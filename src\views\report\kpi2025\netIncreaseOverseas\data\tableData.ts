/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, formatNumber } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const assetRepDownloadTableColumn: TableColumnItem[] = [
    {
        key: 'u1Name',
        label: '中心',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '投顾',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'consCode',
        label: '员工编码',
        width: 110,
        formatter: formatTableValue
    },
    {
        key: 'isKpi',
        label: '是否参与考核',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'hkKpiBuyAmt',
        label: '好买香港KPI申购量(美元)',
        width: 160,
        formatter: ({ hkKpiBuyAmt }: any) => {
            return formatNumber({ num: hkKpiBuyAmt })
        }
    },
    {
        key: 'hkKpiRedeemAmt',
        label: '好买香港KPI赎回量(美元)',
        width: 160,
        formatter: ({ hkKpiRedeemAmt }: any) => {
            return formatNumber({ num: hkKpiRedeemAmt })
        }
    },
    {
        key: 'hkKpiNetIncreaseAmt',
        label: '好买香港KPI净新增(美元)',
        width: 160,
        formatter: ({ hkKpiNetIncreaseAmt }: any) => {
            return formatNumber({ num: hkKpiNetIncreaseAmt })
        }
    },
    {
        key: 'nonAKpiBuyAmt',
        label: '非A类KPI申购量(RMB)',
        width: 150,
        formatter: ({ nonAKpiBuyAmt }: any) => {
            return formatNumber({ num: nonAKpiBuyAmt })
        }
    },
    {
        key: 'nonAKpiRedeemAmt',
        label: '非A类KPI赎回量(RMB)',
        width: 150,
        formatter: ({ nonAKpiRedeemAmt }: any) => {
            return formatNumber({ num: nonAKpiRedeemAmt })
        }
    },
    {
        key: 'nonAKpiNetIncreaseAmt',
        label: '非A类KPI净新增(RMB)',
        width: 150,
        formatter: ({ nonAKpiNetIncreaseAmt }: any) => {
            return formatNumber({ num: nonAKpiNetIncreaseAmt })
        }
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
