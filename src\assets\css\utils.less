/*
 * @Description: less抽取通用方法
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-08-18 20:00:04
 * @FilePath: /dtms-product-web/src/assets/css/utils.less
 *  
 */

// 字体大小设置
.fs(@size) {
    font-size: (@size / 1 - 1) + 1px;
}

// 字体行间距设置
.lh(@space) {
    line-height: (@space / 1 - 1) + 1px;
}

.mr5 {
    margin-right: 5px;
}

.mr8 {
    margin-right: 8px;
}

.ml10 {
    margin-left: 10px;
}

.w180 {
    width: 180px;
}

// 导入字体
// @font-face {
//     font-family: YouSheBiaoTiHei;
//     src: url(../fonts/YouSheBiaoTiHei.ttf);
//     src: url(../fonts/YouSheBiaoTiHei.ttf) format('truetype');
//     font-weight: normal;
//     font-style: normal
// }

// 微软雅黑字体设置
.ff() {
    font-family: 'Microsoft YaHei', '微软雅黑', '宋体', <PERSON><PERSON>, Helvetica, sans-serif;
}

//超出省略-单行
.text-overflow() {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

//超出省略-多行
.text-overflows(@line) {
    display: -webkit-box;
    -webkit-line-clamp: @line;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-box-orient: vertical;
}

.border(@border_color: @border_color_01) {
    position: relative;

    &::after {
        position: absolute;
        top: 0;
        left: 0;
        box-sizing: border-box;
        width: 200%;
        height: 200%;
        pointer-events: none;
        content: '';
        border: 1px solid @border_color;
        transform: scale(0.5);
        transform-origin: left top;
    }
}

.align-center {
    text-align: center;
}
