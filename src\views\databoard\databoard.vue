<!-- eslint-disable eqeqeq -->
<!-- eslint-disable no-undef -->
<!-- eslint-disable vue/v-on-event-hyphenation -->

<template>
    <div class="app-container">
        <!-- 操作栏 -->
        <el-row span="24" style="padding: 2px; margin-top: 5px; border: 2px solid #4874cb">
            <el-col :span="10" style="width: 25%">
                <div>
                    <span
                        style="
                            display: inline-block;
                            padding: 5px;
                            font-size: 13px;
                            background: #b7c0b5;
                        "
                        >所属投顾：</span
                    >
                    <el-cascader
                        ref="consCascader"
                        v-model="orgvalue"
                        :options="options"
                        :props="optionProps"
                        collapse-tags
                        style="width: 50%"
                        :show-all-levels="false"
                        size="small"
                        @change="changeCons"
                    >
                    </el-cascader>
                    <el-select
                        v-model="consultant"
                        filterable
                        size="small"
                        fit-input-width="true"
                        clearable
                        style="width: 30%"
                    >
                        <el-option
                            v-for="item in consultantList"
                            :key="item.conscode"
                            size="small"
                            :label="item.consname"
                            :value="item.conscode"
                            style="width: 100px"
                        ></el-option>
                    </el-select>
                </div>
            </el-col>

            <el-col :span="4" style="width: 25%">
                <div style="margin-left: 0">
                    <span
                        style="
                            display: inline-block;
                            padding: 5px;
                            font-size: 13px;
                            background: #b7c0b5;
                        "
                        >投顾客户号：</span
                    >
                    <el-input
                        v-model="conscustNo"
                        clearable
                        size="small"
                        style="display: inline-block; width: 50%"
                    ></el-input>
                </div>
            </el-col>
            <el-col :span="4" style="width: 30%">
                <div style="margin-left: 0">
                    <span
                        style="
                            display: inline-block;
                            padding: 5px;
                            font-size: 13px;
                            background: #b7c0b5;
                        "
                        >年份：
                    </span>
                    <el-date-picker
                        v-model="tradeYear"
                        type="year"
                        size="small"
                        style="width: 150px; font-size: 12px"
                        placeholder="选择年"
                    >
                    </el-date-picker>
                </div>
            </el-col>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;
            <el-col :span="2">
                <div>
                    <el-button :disabled="submissionFlag" @click="toQuery">查找</el-button>
                </div>
            </el-col>
            <el-col :span="3" class="container">
                <div style="display: flex; justify-content: flex-end">
                    <span style="font-size: 13px; font-weight: bold; color: red">
                        当年数据更新频率: T-1</span
                    >
                </div>
            </el-col>
        </el-row>
        <div class="fgclass3">
            <span >&nbsp;存量</span>
            <span>
                &nbsp;&nbsp;
                <crm-radio
                    v-model="peAmtWay"
                    label-color="white"
                    :option-list="[{ label: '实缴-回款 口径', value: '1' },{ label: '实缴口径（原口径）', value: '2' }]"
                    @change="changePeAmtWay"
                ></crm-radio>
            </span>
            <span style="float: right;">
                统计截止日期:{{
                gdclBarData.gdDataStaticEndDate || lsclBarData.lsDataStaticEndDate
                }}&nbsp;&nbsp;
            </span>
           
        </div>
        <div class="fgclass4">
            <span v-if="peAmtWay == '2'">
                &nbsp;存量说明：① 高端资产规模/高端存量：股权产品使用份额*净值；
                ② 高端投资海外存量：用高端产品存量*该产品海外资产占比上限计算；
                ③ 高端投资国内存量：用高端产品存量*（1-该产品海外资产占比上限）计算
            </span>
            <span v-if="peAmtWay == '1'">
                &nbsp;存量说明：① 高端资产规模/高端存量：股权产品使用【实缴-回款】；
                ② 高端海外存量：用高端产品存量*该产品海外资产占比上限计算；
                ③ 高端国内存量：用高端产品存量*（1-该产品海外资产占比上限）计算
            </span>
        </div>
        <!-- 高端存量 -->
        <el-row span="24" style="height: 60%; border: 1px solid #0e69c7">
            <el-col :span="3">
                <div class="box-card">
                    <div v-for="(item, index) in gdclSumData" :key="index">
                        <span style="display: block; font-size: 15px">{{ item.name + ':' }}</span>
                        <span style="font-size: 14px">{{ item.value }}</span>
                    </div>
                </div>
            </el-col>
            <el-col
                :span="6"
                class="echartBackColor"
                style="min-height: 250px; border: 1px solid cornflowerblue"
            >
                <gdcl-pie-chart-board
                    ref="gdclPieChartBoard"
                    :v-id="gdclPieData.id"
                    :title="gdclPieData.title"
                    :data-item="gdclPieData.dataItem"
                    :int-circle-data="gdclPieData.intCircleData"
                    :out-circle-data="gdclPieData.outCircleData"
                    style="width: 100%; height: 80%"
                ></gdcl-pie-chart-board>

                <!--  style=" width: 100%;height: 80%"-->
                <div style="height: 18%; text-align: center">
                    <div style="height: 10px; margin-bottom: 10px">
                        <span style="font-size: 11px; font-weight: bold">
                            国内存量{{ domesticAmtPer }}
                        </span>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span style="font-size: 11px; font-weight: bold">
                            海外存量{{ overseasAmtPer }}
                        </span>
                    </div>
                    <div style="display: block; width: 120px; height: 20px; margin: auto">
                        <div class="gnClass" :style="{ width: gnwidth }"></div>
                        <div class="hwClass" :style="{ width: hwwidth }"></div>
                    </div>
                </div>
            </el-col>

            <el-col :span="9" class="echartBackColor" style="border: 1px solid cornflowerblue">
                <div style="height: 10%">
                    <div
                        style="
                            float: left;
                            padding: 5px;
                            font-size: 13px;
                            font-weight: bold;
                            color: #ba4949;
                        "
                    >
                        高端存量对比(单位:万)
                    </div>
                    <div style="float: right; padding: 2px">
                        <div class="divLeftClass">
                            <div class="legendLeftClass"></div>
                            <span style="font-size: 10px">
                                {{ gdclBarData.lastYear }}
                            </span>
                        </div>
                        <div class="divRightClass">
                            <div class="legendRightClass"></div>
                            <span style="font-size: 10px">
                                {{ gdclBarData.thisYear }}
                            </span>
                        </div>
                    </div>
                </div>
                <gdcl-bar-chart-board
                    ref="gdclBarChartBoard"
                    :v-id="gdclBarData.id"
                    :title="gdclBarData.title"
                    :last-year="gdclBarData.lastYear"
                    :this-year="gdclBarData.thisYear"
                    :last-year-data="gdclBarData.lastYearData"
                    :this-year-data="gdclBarData.thisYearData"
                    :data-x="gdclBarData.dataX"
                    style="float: left; width: 98%; height: 90%"
                ></gdcl-bar-chart-board>
            </el-col>
            <el-col :span="3" class="echartBackColor" style="border: 1px solid cornflowerblue">
                <gdcjkh-chart-board
                    id="clkhbardiv"
                    :v-id="gdcjkhData.id"
                    :title="gdcjkhData.title"
                    :last-year="gdcjkhData.lastYear"
                    :this-year="gdcjkhData.thisYear"
                    :last-year-data="gdcjkhData.lastYearData"
                    :this-year-data="gdcjkhData.thisYearData"
                    :data-x="gdcjkhData.dataX"
                    style="width: 98%; height: 85%"
                >
                </gdcjkh-chart-board>
                <div style="height: 15%">
                    <div style="float: right; padding: 2px; margin: auto">
                        <div class="divLeftClass">
                            <div class="legendLeftClass"></div>
                            <span style="font-size: 10px">
                                {{ gdclBarData.lastYear }}
                            </span>
                        </div>
                        <div class="divRightClass2">
                            <div class="legendRightClass"></div>
                            <span style="font-size: 10px">
                                {{ gdclBarData.thisYear }}
                            </span>
                        </div>
                    </div>
                </div>
            </el-col>
            <el-col :span="3" style="border: 1px solid cornflowerblue">
                <div id="gdclkhdiv" class="board-s">
                    <div
                        style="
                            width: 100%;
                            height: 60%;
                            padding: 10px 5px 15px 20px;
                            margin-bottom: 2px;
                            background-color: #dae3f5;
                        "
                    >
                        <span
                            style="
                                display: block;
                                padding: 0 0 10px;
                                font-size: 14px;
                                font-weight: bolder;
                            "
                            >二手成交:</span
                        >
                        <span
                            style="
                                display: block;
                                padding: 0 0 10px;
                                font-size: 14px;
                                font-weight: bolder;
                            "
                        >
                            {{ cj2sf }}人</span
                        >

                        <span style="display: block; font-size: 14px; font-weight: bolder"
                            >当年净划入存量（含认缴）:</span
                        >
                        <span
                            style="
                                display: block;
                                padding: 0 0 10px;
                                font-size: 14px;
                                font-weight: bolder;
                            "
                        >
                            {{ netIncreaseAmtRmb }}万</span
                        >
                    </div>
                    <div
                        style="
                            width: 100%;
                            height: 40%;
                            padding: 10px 5px 15px 20px;
                            background-color: #dae3f5;
                        "
                    >
                        <span style="display: block; font-size: 14px; font-weight: bolder"
                            >潜在客户数:</span
                        >
                        <span
                            style="
                                display: block;
                                padding: 0 0 10px;
                                font-size: 14px;
                                font-weight: bolder;
                            "
                        >
                            {{ custqzAll }}人</span
                        >
                    </div>
                </div>
            </el-col>
        </el-row>
        <!-- 零售存量 -->
        <el-row span="24" style="height: 60%; border: 1px solid #0e69c7">
            <el-col :span="3">
                <div class="box-card">
                    <div v-for="(item, index) in lsclSumData" :key="index">
                        <span style="display: block; font-size: 15px">{{ item.name + ':' }}</span>
                        <span style="font-size: 14px">{{ item.value }}</span>
                    </div>
                </div>
            </el-col>
            <el-col
                :span="6"
                class="echartBackColor"
                style="min-height: 250px; border: 1px solid cornflowerblue"
            >
                <lscl-pie-chart-board
                    ref="lsclPieChartBoard"
                    :v-id="lsclPieData.id"
                    :title="lsclPieData.title"
                    :circle-data="lsclPieData.circleData"
                ></lscl-pie-chart-board>
            </el-col>

            <el-col :span="8" class="echartBackColor" style="border: 1px solid cornflowerblue">
                <div style="height: 10%">
                    <div
                        style="
                            float: left;
                            padding: 5px;
                            font-size: 13px;
                            font-weight: bold;
                            color: #ba4949;
                        "
                    >
                        零售存量对比(单位:万)
                    </div>
                    <div style="float: right; padding: 2px">
                        <div class="divLeftClass">
                            <div class="legendLeftClass"></div>
                            <span style="font-size: 10px">
                                {{ gdclBarData.lastYear }}
                            </span>
                        </div>
                        <div class="divRightClass">
                            <div class="legendRightClass"></div>
                            <span style="font-size: 10px">
                                {{ gdclBarData.thisYear }}
                            </span>
                        </div>
                    </div>
                </div>
                <lscl-bar-chart-board
                    ref="lsclBarChartBoard"
                    :v-id="lsclBarData.id"
                    :title="lsclBarData.title"
                    :last-year="lsclBarData.lastYear"
                    :this-year="lsclBarData.thisYear"
                    :last-year-data="lsclBarData.lastYearData"
                    :this-year-data="lsclBarData.thisYearData"
                    :data-x="lsclBarData.dataX"
                    style="float: left; width: 98%; height: 90%"
                ></lscl-bar-chart-board>
            </el-col>
            <el-col :span="7"> </el-col>
        </el-row>
        <div class="fgclass2">
            &nbsp;高净值新增
            <span class="right-aligned"
                >统计截止日期:{{ jxzSumBarData.jxzDataStaticEndDate }}&nbsp;&nbsp;</span
            >
        </div>
        <!-- 净新增 -->
        <el-row span="24" style="border: 1px solid #0e69c7">
            <el-col :span="3">
                <jxz-sum-bar-chart-board
                    :v-id="jxzSumBarData.id"
                    :title="jxzSumBarData.title"
                    :data-y="jxzSumBarData.dataY"
                    :jxz-sum-data="jxzSumBarData.jxzSumData"
                >
                </jxz-sum-bar-chart-board>
            </el-col>
            <el-col
                :span="8"
                class="echartBackColor"
                style="min-height: 250px; border: 1px solid cornflowerblue"
            >
                <div style="height: 10%">
                    <div
                        style="
                            float: left;
                            padding: 5px;
                            font-size: 13px;
                            font-weight: bold;
                            color: #ba4949;
                        "
                    >
                        净新增(单位:万)
                    </div>
                    <div style="float: right; padding: 2px">
                        <el-select
                            v-model="jxzTimeFlag"
                            size="small"
                            fit-input-width="true"
                            placeholder="月度"
                            style="width: 70px"
                            @change="selectJxzTrigger"
                        >
                            <el-option
                                v-for="item in timeGroupList2"
                                :key="item.name"
                                :label="item.name"
                                :value="item.name"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <jxz-bar-chart-board
                    ref="jxzBarChartBoard"
                    :v-id="jxzBarData.id"
                    :title="jxzBarData.title"
                    :month-data-x="jxzBarData.monthDataX"
                    :month-data="jxzBarData.monthData"
                    style="float: left; width: 98%; height: 90%"
                ></jxz-bar-chart-board>
            </el-col>

            <el-col :span="9" class="echartBackColor" style="border: 1px solid cornflowerblue">
                <cpcl-bar-chart-board
                    ref="cpclBarChartBoard"
                    :v-id="cpclBarData.id"
                    :title="cpclBarData.title"
                    :data-x="cpclBarData.dataX"
                    :cpcl-data="cpclBarData.cpclData"
                ></cpcl-bar-chart-board>
            </el-col>
            <el-col :span="4">
                <div class="board-s3">
                    <div
                        id="jxzgykhdiv"
                        style="
                            width: 100%;
                            height: 100%;
                            font-size: 0.2pc;
                            background-color: #dae3f5;
                        "
                    >
                        <span
                            style="display: block; font-size: 14px; font-weight: bolder; color: red"
                        >
                            净新增归因-客户</span
                        >
                        <div style="padding: 5px 0 0 10px; font-size: 12px">
                            <span style="display: inline-block; font-weight: bolder"
                                >1) 老客户:{{ oldcustNum }}人</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;总量:{{ oldcustAmt }}万</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;人均:{{ oldcustAmtAve }}万</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;正贡献人数:{{ oldcustPositive }}人({{
                                    oldcustPositiveRateStr
                                }})</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;负贡献人数:{{ oldcustNegative }}人({{
                                    oldcustNegativeRateStr
                                }})</span
                            >
                        </div>
                        <div style="padding: 5px 0 0 10px; font-size: 12px">
                            <span style="display: block; font-weight: bolder"
                                >2) 新客户:{{ newcustNum }}人</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;总量:{{ newcustAmt }}万</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;人均:{{ newcustAmtAve }}万</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;正贡献人数:{{ newcustPositive }}人({{
                                    newcustPositiveRateStr
                                }})</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;负贡献人数:{{ newcustNegative }}人({{
                                    newcustNegativeRateStr
                                }})</span
                            >
                        </div>
                        <div style="padding: 5px 0 0 10px; font-size: 12px">
                            <span style="display: block; font-weight: bolder"
                                >3) 创新客户:{{ bxcustNum }}人</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;总量:{{ bxcustAmt }}万</span
                            >
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
        <div class="fgclass2">
            &nbsp;高端销量 <span class="right-aligned">统计截止日期:实时更新&nbsp;&nbsp;</span>
        </div>
        <!-- 高端销量 -->
        <el-row v-loading="loading" span="24" style="height: 40%; border: 2px solid #0e69c7">
            <el-col :span="3">
                <div class="box-card">
                    <span style="display: inline-block; font-size: 15px">销量:</span>
                    <span style="padding: 0 0 10px; font-size: 14px"> {{ gdxlAmtSum }}万元</span>
                    <br />
                    <span style="display: inline-block; font-size: 15px">较上年:</span>
                    <span style="padding: 0 0 10px; font-size: 14px"> {{ gdxlPer }}</span>
                </div>
            </el-col>
            <el-col
                :span="8"
                class="echartBackColor"
                style="min-height: 250px; border: 1px solid cornflowerblue"
            >
                <div style="height: 12%">
                    <div
                        style="
                            float: left;
                            padding: 5px;
                            font-size: 13px;
                            font-weight: bold;
                            color: #ba4949;
                        "
                    >
                        销量(单位:万)
                    </div>
                    <div style="float: right; padding: 2px">
                        <el-select
                            v-model="xlTimeFlag"
                            fit-input-width="true"
                            size="small"
                            placeholder="月度"
                            style="width: 70px"
                            @change="selectXlTrigger"
                        >
                            <el-option
                                v-for="item in timeGroupList"
                                :key="item.name"
                                :label="item.name"
                                :value="item.name"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <jxzxl-bar-chart-board
                    ref="jxzxlBarChartBoard"
                    :v-id="jxzxlBarData.id"
                    :title="jxzxlBarData.title"
                    :month-data-x="jxzxlBarData.monthDataX"
                    :month-data="jxzxlBarData.monthData"
                    style="height: 80%"
                ></jxzxl-bar-chart-board>
            </el-col>

            <el-col :span="9" class="echartBackColor" style="border: 1px solid cornflowerblue">
                <cpcl-pie-chart-board
                    ref="cpclPieChartBoard"
                    :v-id="cpclPieData.id"
                    :title="cpclPieData.title"
                    :data-item="cpclPieData.dataItem"
                    :circle-data="cpclPieData.circleData"
                    :first-jd-type="jdCplxData.firstJdType"
                    :first-jd-type-per="jdCplxData.firstJdTypePer"
                    :second-jd-type="jdCplxData.secondJdType"
                    :second-jd-type-per="jdCplxData.secondJdTypePer"
                    :third-jd-type="jdCplxData.thirdJdType"
                    :third-jd-type-per="jdCplxData.thirdJdTypePer"
                    :fourth-jd-type="jdCplxData.fourthJdType"
                    :fourth-jd-type-per="jdCplxData.fourthJdTypePer"
                ></cpcl-pie-chart-board>
            </el-col>
            <el-col :span="4">
                <div class="board-s">
                    <div id="xlgydiv" style="width: 100%; height: 100%; background-color: #dae3f5">
                        <span
                            style="display: block; font-size: 14px; font-weight: bold; color: red"
                        >
                            销量归因-客户</span
                        >
                        <div style="padding: 15px 0 0 10px; font-size: 12px">
                            <span style="display: inline-block; font-weight: bold"
                                >1) 老客户:{{ xlCust.oldCustAmt }}万元({{
                                    xlCust.oldCustAmtRateStr
                                }})</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;客户数:{{ xlCust.oldCustNum }}人</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;客户复购率:{{
                                    xlCust.oldCustRepurchaseRateStr
                                }}</span
                            >
                        </div>
                        <br />
                        <div style="padding: 5px 0 0 10px; font-size: 12px">
                            <span style="display: block; font-weight: bold"
                                >2) 新客户:{{ xlCust.newCustAmt }}万元({{
                                    xlCust.newCustAmtRateStr
                                }})</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;客户数:{{ xlCust.newCustNum }}人</span
                            >
                            <span style="display: block"
                                >&nbsp;&nbsp;&nbsp;&nbsp;客户复购率:{{
                                    xlCust.newCustRepurchaseRateStr
                                }}</span
                            >
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
        <!-- 客户浏览行为、客户认testBoardReport购和申购 -->
        <el-row span="24" style="height: 40%; border: 2px solid #0e69c7">
            <el-col :span="13">
                <scroll-board-left
                    :title="custPvBoard.title"
                    :board-data="custPvBoard.boardData"
                    :grid11-data="custPvBoard.grid11Data"
                    :grid12-data="custPvBoard.grid12Data"
                    :grid13-data="custPvBoard.grid13Data"
                    :grid14-data="custPvBoard.grid14Data"
                    :d1-users-sum="actCrmCust.d1UsersSum"
                    :d14-users-sum="actCrmCust.d14UsersSum"
                    :d30-users-sum="actCrmCust.d30UsersSum"
                    @ievent="getTabData($event)"
                ></scroll-board-left>
            </el-col>
            <el-col :span="11">
                <scroll-board
                    :title="custChgBoard.title"
                    :board-data="custChgBoard.boardData"
                    @ievent="getTabData($event)"
                ></scroll-board>
            </el-col>
        </el-row>
        <!-- 数据表格-->
        <div style="height: 40%; border: 2px solid #0e69c7">
            <span style="font-size: 12px; color: #e1342b">{{ '已选择报表: ' + clickBoard }}</span>
            <detail-table :table-data="custPvTable.data" :table-columns="custPvTable.columns">
            </detail-table>
            <!-- <el-table :data="custPvTable.data"  :columns="custPvTable.columns" height="500">    
             </el-table> -->
            <div
                style="
                    right: 0%;
                    bottom: 0%;
                    margin-right: 5px;
                    margin-bottom: 20px;
                    text-align: right;
                "
            >
                <el-pagination
                    background
                    :pager-count="4"
                    :current-page="pagination.currentPage"
                    :page-sizes="pagination.pageSizes"
                    :page-size="pagination.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                    @size-change="handleSizeChange($event)"
                    @current-change="handleCurrentChange($event)"
                >
                </el-pagination>
            </div>
        </div>
        <!-- 研习社、理财九章操作栏-->
        <el-row span="24" style="padding: 2px; margin-top: 5px; border: 2px solid #0e69c7">
            <el-col :span="10" style="width: 50%">
                <div>
                    <span
                        style="
                            display: inline-block;
                            padding: 5px;
                            font-size: 13px;
                            background: #b7c0b5;
                        "
                        >所属投顾：</span
                    >
                    <el-cascader
                        ref="consCascader2"
                        v-model="orgvalue2"
                        :options="options2"
                        collapse-tags
                        :props="optionProps2"
                        style="width: 45%"
                        :show-all-levels="false"
                        size="small"
                        :check-strictly="true"
                        @change="changeCons2"
                    >
                    </el-cascader>
                    <el-select
                        v-model="consultant2"
                        size="small"
                        filterable
                        fit-input-width="true"
                        clearable
                        style="width: 20%"
                    >
                        <el-option
                            v-for="item in consultantList2"
                            :key="item.conscode"
                            size="small"
                            :label="item.consname"
                            :value="item.conscode"
                        ></el-option>
                    </el-select>
                </div>
            </el-col>
            <el-col :span="10" style="width: 30%">
                <div style="margin-left: 20px">
                    <span
                        style="
                            display: inline-block;
                            padding: 5px;
                            font-size: 13px;
                            background: #b7c0b5;
                        "
                        >时间区间：
                    </span>
                    <el-date-picker
                        v-model="tradeMonth"
                        type="month"
                        size="small"
                        style="width: 150px; font-size: 12px"
                        placeholder="选择月份"
                    >
                    </el-date-picker>
                </div>
            </el-col>

            <el-col :span="4" style="width: 20%">
                <div>
                    <el-button :disabled="submissionFlag" @click="toQuery2">查找</el-button>
                </div>
            </el-col>
        </el-row>
        <!-- 研习社、理财九章访问情况-->
        <el-row span="24" style="padding: 2px; margin-top: 5px; border: 2px solid #0e69c7">
            <el-col :span="12" style="width: 45%">
                <div class="board-s2">
                    <!-- header -->
                    <div class="header-s">
                        <div style="display: inline-block; font-weight: bold; vertical-align: top">
                            <span>线上研习社客户学习数据</span>
                        </div>
                        <div style="display: inline-block; margin-left: 50px">
                            <span style="display: block">学习人数</span>
                            <span style="font-weight: bold">{{ userNumSum }}</span
                            ><span style="">人</span>
                        </div>
                        <div style="display: inline-block; margin-left: 10px">
                            <span style="display: block">学习时长</span>
                            <span style="font-weight: bold">{{ totalDurationSum }}</span
                            ><span style="">分钟</span>
                        </div>
                        <div style="display: inline-block; margin-left: 10px">
                            <span style="display: block">人均学习时长</span>
                            <span style="font-weight: bold">{{ totalDurationAve }}</span
                            ><span style="">分钟</span>
                        </div>
                    </div>
                    <!-- 数据看板 -->
                    <el-row span="24" style="overflow: hidden">
                        <div style="width: 100%; height: 40%; border: 2px solid #0e69c7">
                            <el-table
                                :data="custPvTable2.data"
                                border
                                :header-cell-style="{
                                    background: '#b7c0b5 !important',
                                    color: 'black',
                                    'font-size': '12px !important'
                                }"
                                height="400"
                            >
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="custname"
                                    label="客户姓名"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="conscustNo"
                                    label="投顾客户号"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="custType"
                                    label="客户类型"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="courseNum"
                                    label="学习课程数"
                                >
                                    <template #default="scope">
                                        <el-popover placement="right" :width="600">
                                            <template #reference>
                                                <span style="text-decoration: underline">{{
                                                    scope.row.courseNum
                                                }}</span>
                                            </template>
                                            <span
                                                style="
                                                    font-size: 14px;
                                                    font-weight: bold;
                                                    color: #0f0e0e;
                                                "
                                                >{{
                                                    '客户[' + scope.row.custname + ']课程学习明细'
                                                }}</span
                                            >
                                            <el-table
                                                :data="scope.row.studyInfoTableData"
                                                :height="400"
                                            >
                                                <el-table-column
                                                    width="100"
                                                    property="custName"
                                                    label="客户姓名"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="conscustNo"
                                                    label="投顾客户号"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="courseId"
                                                    label="课程ID"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="courseName"
                                                    label="课程名称"
                                                />
                                                <el-table-column
                                                    width="150"
                                                    property="firstStudyDt"
                                                    label="首次学习日期"
                                                />
                                                <el-table-column
                                                    width="150"
                                                    property="lastStudyDt"
                                                    label="最近学习日期"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="whetherFinish"
                                                    label="是否学完"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="totalDuration"
                                                    label="总学习时长"
                                                />
                                            </el-table>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="totalDuration"
                                    label="学习总时长(分钟)"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="firstStudyDt"
                                    label="首次学习日期"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="firstAckDt"
                                    label="首交日期"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="d30PrebookNum"
                                    label="近30天预约产品个数"
                                >
                                    <template #default="scope">
                                        <el-popover placement="right" :width="600">
                                            <template #reference>
                                                <span style="text-decoration: underline">{{
                                                    scope.row.d30PrebookNum
                                                }}</span>
                                            </template>
                                            <span
                                                style="
                                                    font-size: 14px;
                                                    font-weight: bold;
                                                    color: #0f0e0e;
                                                "
                                                >{{
                                                    '客户[' +
                                                    scope.row.custname +
                                                    ']近30天预约产品明细'
                                                }}</span
                                            >
                                            <el-table
                                                :data="scope.row.prebook30TableData"
                                                :height="400"
                                            >
                                                <el-table-column
                                                    width="100"
                                                    property="custname"
                                                    label="客户姓名"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="conscustNo"
                                                    label="投顾客户号"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="fundName"
                                                    label="近30天预约产品"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="prebookDt"
                                                    label="预约日期"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="realpayAmt"
                                                    label="近30天打款金额"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="realpayDt"
                                                    label="打款日期"
                                                />
                                            </el-table>
                                        </el-popover>
                                    </template>
                                </el-table-column>

                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="d60PrebookNum"
                                    label="近60天预约产品个数"
                                >
                                    <template #default="scope">
                                        <el-popover placement="right" :width="600">
                                            <template #reference>
                                                <span style="text-decoration: underline">{{
                                                    scope.row.d60PrebookNum
                                                }}</span>
                                            </template>
                                            <span
                                                style="
                                                    font-size: 14px;
                                                    font-weight: bold;
                                                    color: #0f0e0e;
                                                "
                                                >{{
                                                    '客户[' +
                                                    scope.row.custname +
                                                    ']近60天预约产品明细'
                                                }}</span
                                            >
                                            <el-table
                                                :data="scope.row.prebook60TableData"
                                                :height="400"
                                            >
                                                <el-table-column
                                                    width="100"
                                                    property="custname"
                                                    label="客户姓名"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="conscustNo"
                                                    label="投顾客户号"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="fundName"
                                                    label="近60天预约产品"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="prebookDt"
                                                    label="预约日期"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="realpayAmt"
                                                    label="近60天打款金额"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="realpayDt"
                                                    label="打款日期"
                                                />
                                            </el-table>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="d90PrebookNum"
                                    label="近90天预约产品个数"
                                >
                                    <template #default="scope">
                                        <el-popover placement="right" :width="600">
                                            <template #reference>
                                                <span style="text-decoration: underline">{{
                                                    scope.row.d90PrebookNum
                                                }}</span>
                                            </template>
                                            <span
                                                style="
                                                    font-size: 14px;
                                                    font-weight: bold;
                                                    color: #0f0e0e;
                                                "
                                                >{{
                                                    '客户[' +
                                                    scope.row.custname +
                                                    ']近90天预约产品明细'
                                                }}</span
                                            >
                                            <el-table
                                                :data="scope.row.prebook90TableData"
                                                :height="400"
                                            >
                                                <el-table-column
                                                    width="100"
                                                    property="custname"
                                                    label="客户姓名"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="conscustNo"
                                                    label="投顾客户号"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="fundName"
                                                    label="近90天预约产品"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="prebookDt"
                                                    label="预约日期"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="realpayAmt"
                                                    label="近90天打款金额"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="realpayDt"
                                                    label="打款日期"
                                                />
                                            </el-table>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div
                                style="
                                    right: 0%;
                                    bottom: 0%;
                                    margin-right: 5px;
                                    margin-bottom: 20px;
                                    text-align: right;
                                "
                            >
                                <el-pagination
                                    background
                                    :pager-count="3"
                                    :current-page="pagination2.currentPage"
                                    :page-sizes="pagination2.pageSizes"
                                    :page-size="pagination2.pageSize"
                                    layout="total, sizes, prev, pager, next"
                                    :total="pagination2.total"
                                    @size-change="handleSizeChange2($event)"
                                    @current-change="handleCurrentChange2($event)"
                                >
                                </el-pagination>
                            </div>
                        </div>
                    </el-row>
                </div>
            </el-col>
            <el-col :span="12" style="width: 45%">
                <div class="board-s2">
                    <!-- header -->
                    <div class="header-s">
                        <div style="display: inline-block; font-weight: bold; vertical-align: top">
                            <span>理财九章客户报名参会数据</span>
                        </div>
                        <div style="display: inline-block; margin-left: 50px">
                            <span style="display: block">报名人数</span>
                            <span style="font-weight: bold">{{ conferenceAppliersSum }}</span
                            ><span style="">人</span>
                        </div>
                        <div style="display: inline-block; margin-left: 10px">
                            <span style="display: block">参会人数</span>
                            <span style="font-weight: bold">{{ conferenceAttendersSum }}</span
                            ><span style="">人</span>
                        </div>
                        <div style="display: inline-block; margin-left: 10px">
                            <span style="display: block">参会率</span>
                            <span style="font-weight: bold">{{ conferenceAttendersPer }}</span>
                        </div>
                    </div>
                    <!--数据看板 -->
                    <el-row span="24" style="overflow: hidden">
                        <div style="width: 100%; height: 40%; border: 2px solid #0e69c7">
                            <el-table
                                :data="custPvTable3.data"
                                border
                                :header-cell-style="{
                                    background: '#b7c0b5 !important',
                                    color: 'black',
                                    'font-size': '12px !important'
                                }"
                                height="400"
                            >
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="custname"
                                    label="客户姓名"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="conscustno"
                                    label="投顾客户号"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="custType"
                                    label="客户类型"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="conferenceCnt"
                                    label="报名预约会议数"
                                >
                                    <template #default="scope">
                                        <el-popover placement="right" :width="600">
                                            <template #reference>
                                                <span style="text-decoration: underline">{{
                                                    scope.row.conferenceCnt
                                                }}</span>
                                            </template>
                                            <span
                                                style="
                                                    font-size: 14px;
                                                    font-weight: bold;
                                                    color: #0f0e0e;
                                                "
                                                >{{
                                                    '客户[' + scope.row.custname + ']预约会议明细'
                                                }}</span
                                            >
                                            <el-table
                                                :data="scope.row.conferenceTableData"
                                                :height="400"
                                            >
                                                <el-table-column
                                                    width="100"
                                                    property="custname"
                                                    label="客户姓名"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="conscustno"
                                                    label="投顾客户号"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="conferenceId"
                                                    label="会议id"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="conferenceName"
                                                    label="会议名称"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="conferenceType"
                                                    label="会议类型"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="conferenceDt"
                                                    label="会议日期"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="registerDatasource"
                                                    label="报名数据来源"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="appointDt"
                                                    label="报名日期"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="isAttender"
                                                    label="是否线下参会"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="attendDt"
                                                    label="参会日期"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="d30PrebookCnt"
                                                    label="参会后30天预约产品个数"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="d30PrebookAmts"
                                                    label="参会后30天预约产品金额"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="d30PrebookFunds"
                                                    label="参会后30天预约产品"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="d90PayCnt"
                                                    label="参会后90天累计打款产品个数"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="d90PayAmts"
                                                    label="参会后90天累计打款产品金额"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="d90PayFunds"
                                                    label="参会后90天累计打款产品"
                                                />
                                            </el-table>
                                        </el-popover>
                                    </template>
                                </el-table-column>

                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="attenderCnt"
                                    label="实际参加会议数"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="attendRatio"
                                    label="参会率"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="firstAppointDt"
                                    label="最近报名时间"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="firstTradeDt"
                                    label="首交日期"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="d30PrebookNum"
                                    label="近30天预约产品个数"
                                >
                                    <template #default="scope">
                                        <el-popover placement="right" :width="600">
                                            <template #reference>
                                                <span style="text-decoration: underline">{{
                                                    scope.row.d30PrebookNum
                                                }}</span>
                                            </template>
                                            <span
                                                style="
                                                    font-size: 14px;
                                                    font-weight: bold;
                                                    color: #0f0e0e;
                                                "
                                                >{{
                                                    '客户[' +
                                                    scope.row.custname +
                                                    ']近30天预约产品明细'
                                                }}</span
                                            >
                                            <el-table
                                                :data="scope.row.lcjzPrebook30TableData"
                                                :height="400"
                                            >
                                                <el-table-column
                                                    width="100"
                                                    property="custname"
                                                    label="客户姓名"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="conscustno"
                                                    label="投顾客户号"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="fundName"
                                                    label="近30天预约产品"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="prebookDt"
                                                    label="预约日期"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="realpayAmt"
                                                    label="近30天打款金额"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="realpayDt"
                                                    label="打款日期"
                                                />
                                            </el-table>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="d60PrebookNum"
                                    label="近60天预约产品个数"
                                >
                                    <template #default="scope">
                                        <el-popover placement="right" :width="600">
                                            <template #reference>
                                                <span style="text-decoration: underline">{{
                                                    scope.row.d60PrebookNum
                                                }}</span>
                                            </template>
                                            <span
                                                style="
                                                    font-size: 14px;
                                                    font-weight: bold;
                                                    color: #0f0e0e;
                                                "
                                                >{{
                                                    '客户[' +
                                                    scope.row.custname +
                                                    ']近60天预约产品明细'
                                                }}</span
                                            >
                                            <el-table
                                                :data="scope.row.lcjzPrebook60TableData"
                                                :height="400"
                                            >
                                                <el-table-column
                                                    width="100"
                                                    property="custname"
                                                    label="客户姓名"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="conscustno"
                                                    label="投顾客户号"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="fundName"
                                                    label="近60天预约产品"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="prebookDt"
                                                    label="预约日期"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="realpayAmt"
                                                    label="近60天打款金额"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="realpayDt"
                                                    label="打款日期"
                                                />
                                            </el-table>
                                        </el-popover>
                                    </template>
                                </el-table-column>

                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="d90PrebookNum"
                                    label="近90天预约产品个数"
                                >
                                    <template #default="scope">
                                        <el-popover placement="right" :width="600">
                                            <template #reference>
                                                <span style="text-decoration: underline">{{
                                                    scope.row.d90PrebookNum
                                                }}</span>
                                            </template>
                                            <span
                                                style="
                                                    font-size: 14px;
                                                    font-weight: bold;
                                                    color: #0f0e0e;
                                                "
                                                >{{
                                                    '客户[' +
                                                    scope.row.custname +
                                                    ']近90天预约产品明细'
                                                }}</span
                                            >
                                            <el-table
                                                :data="scope.row.lcjzPrebook90TableData"
                                                :height="400"
                                            >
                                                <el-table-column
                                                    width="100"
                                                    property="custname"
                                                    label="客户姓名"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="conscustno"
                                                    label="投顾客户号"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="fundName"
                                                    label="近90天预约产品"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="prebookDt"
                                                    label="预约日期"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="realpayAmt"
                                                    label="近90天打款金额"
                                                />
                                                <el-table-column
                                                    width="100"
                                                    property="realpayDt"
                                                    label="打款日期"
                                                />
                                            </el-table>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div
                                style="
                                    right: 0%;
                                    bottom: 0%;
                                    margin-right: 5px;
                                    margin-bottom: 20px;
                                    text-align: right;
                                "
                            >
                                <el-pagination
                                    background
                                    :pager-count="3"
                                    :current-page="pagination3.currentPage"
                                    :page-sizes="pagination3.pageSizes"
                                    :page-size="pagination3.pageSize"
                                    layout="total, sizes, prev, pager, next"
                                    :total="pagination3.total"
                                    @size-change="handleSizeChange3($event)"
                                    @current-change="handleCurrentChange3($event)"
                                >
                                </el-pagination>
                            </div>
                        </div>
                    </el-row>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
    import scrollBoard from './components/scrollBoard.vue'
    import { ElLoading } from 'element-plus'
    // eslint-disable-next-line camelcase
    import {
        getOrgConsList,
        // eslint-disable-next-line camelcase
        getDsConsByOrgcode_json,
        getCustActData,
        getListFavorPv,
        getGdclBoradData,
        getLsclBoardData,
        getJxzBroadData,
        getCustBookingVData,
        getGdOtherInfo,
        getYxsCrmonsultantData,
        getYxsCrmCustData,
        getActCrmLcjzInfoData,
        getActCrmLcjzCustInfoData,
        getActCrmCustUvData,
        getActCrmPvCustInfoData
    } from '@/api/project/databorad/databoard'
    import scrollBoardLeft from './components/scrollBoardLeft.vue'
    import gdclPieChartBoard from './components/gdclPieChartBoard.vue'
    import gdclBarChartBoard from './components/gdclBarChartBoard.vue'
    import gdcjkhChartBoard from './components/gdcjkhChartBoard.vue'
    import jxzSumBarChartBoard from './components/jxzSumBarChartBoard.vue'
    import cpclPieChartBoard from './components/cpclPieChartBoard.vue'
    import cpclBarChartBoard from './components/cpclBarChartBoard.vue'
    import jxzBarChartBoard from './components/jxzBarChartBoard.vue'
    import jxzxlBarChartBoard from './components/jxzxlBarChartBoard.vue'
    import detailTable from './components/detailTable.vue'
    import { ref, toRefs, reactive } from 'vue'

    import lsclPieChartBoard from './components/lsclPieChartBoard.vue'
    import lsclBarChartBoard from './components/lsclBarChartBoard.vue'

    export default {
        name: 'DataBroad',
        components: {
            scrollBoard,
            scrollBoardLeft,
            gdclPieChartBoard,
            gdclBarChartBoard,
            gdcjkhChartBoard,
            jxzxlBarChartBoard,
            jxzSumBarChartBoard,
            cpclPieChartBoard,
            cpclBarChartBoard,
            jxzBarChartBoard,
            lsclPieChartBoard,
            lsclBarChartBoard,
            detailTable
        },
        data() {
            return {
                peAmtWay:'1',
                loading: false,
                jdCplxData: {
                    firstJdType: null,
                    firstJdTypePer: null,
                    secondJdType: null,
                    secondJdTypePer: null,
                    thirdJdType: null,
                    thirdJdTypePer: null,
                    fourthJdType: null,
                    fourthJdTypePer: null
                },
                gnwidth: '0%',
                hwwidth: '100%',
                overseasAmtPer: '0%',
                domesticAmtPer: '100%',
                xlFlag: 1,
                jxzFlag: 1,
                xlTimeFlag: '月度',
                jxzTimeFlag: '月度',
                timeGroupList2: [
                    { name: '月度', value: 1 },
                    { name: '季度', value: 2 }
                ],
                timeGroupList: [
                    { name: '月度', value: 1 },
                    { name: '季度', value: 2 }
                ],
                xlCust: {
                    oldCustAmt: null,
                    newCustAmt: null,
                    oldCustAmtRateStr: null,
                    newCustAmtRateStr: null,
                    newCustNum: null,
                    oldCustNum: null,
                    oldCustRepurchaseRateStr: null,
                    newCustRepurchaseRateStr: null
                },
                newcustAmtAve: null,
                oldcustAmtAve: null,
                oldcustAmt: null,
                oldcustNum: null,
                oldcustPositive: null,
                oldcustPositiveRateStr: '',
                oldcustNegativeRateStr: '',
                newcustPositiveRateStr: '',
                newcustNegativeRateStr: '',
                oldcustNegative: null,
                newcustAmt: null,
                newcustNum: null,
                newcustPositive: null,
                newcustNegative: null,
                bxcustAmt: null,
                bxcustNum: null,
                actCrmCust: {
                    d1UsersSum: null,
                    d14UsersSum: null,
                    d30UsersSum: null
                }, //
                userNumSum: '', //用户学习时长
                totalDurationSum: '', //用户学习时长
                totalDurationAve: '', //平均学习时长
                yxsCrmCust: [], //客户访问列表
                conferenceAttendersPer: '', //参会率
                conferenceAppliersSum: '', //报名人数
                conferenceAttendersSum: '', //参会人数
                actCrmLcjzCustInfo: [], //客户访问列表
                jxzAmtSum: '', //净新增市值
                gdxlAmtSum: '', //销量市值
                gdxlPer: '', //销量占比
                cj2sf: '', //二手成交客户数
                netIncreaseAmtRmb: '', //净划入
                custqzAll: '', //潜在客户数
                yearsList: [],
                tradeYear: '', //交易年
                tradeMonth: '', //时间区间
                consOrg: null,
                consultant: null,
                consultant2: null,
                conscustNo: null,
                options: [], // 投顾所属组织机构
                options2: [], // 投顾所属组织机构
                optionProps: {
                    multiple: true,
                    value: 'id',
                    label: 'text',
                    children: 'children'
                }, // 格式化工单信息
                optionProps2: {
                    multiple: true,
                    value: 'id',
                    label: 'text',
                    children: 'children'
                }, // 格式化工单信息
                vDate: '',
                orgvalue: null,
                orgvalue2: null,
                consultantList: [], // 投顾
                consultantList2: [], // 投顾
                isLoading: false,
                submissionFlag: false,
                componentKey: 0,
                gdclSumData: [
                    { name: '高端资产规模', key: 'gdzcgm', value: '0亿' },
                    { name: '高端海外存量', key: 'gdhwcl', value: '0亿' },
                    { name: '高端国内存量', key: 'gdgncl', value: '0亿' },
                    { name: '高端客户户均', key: 'gdkhhj', value: '0万' },
                    { name: '高端累计收益', key: 'gdljsy', value: '0万(XX%)' },
                    { name: '高端存量客户', key: 'gdclkh', value: '0人' }
                ],
                lsclSumData: [
                    { name: '零售资产规模', key: 'lszcgm', value: '0亿' },
                    { name: '零售客户户均', key: 'lskhhj', value: '0万' },
                    { name: '零售累计收益', key: 'lsljhj', value: '0万(XX%)' },
                    { name: '零售存量客户', key: 'lsclkh', value: '0人' }
                ],
                gdclPieData: {
                    id: 'gdcl-pie-1',
                    title: '高端存量',
                    intCircleData: [],
                    outCircleData: []
                },
                lsclPieData: {
                    id: 'lscl-pie-1',
                    title: '零售存量',
                    circleData: []
                },
                cpclPieData: {
                    id: 'cpcl-pie-1',
                    title: '销量归因-产品策略',
                    dataItem: [
                        '股票',
                        '股权',
                        'CTA',
                        '另类',
                        '\n',
                        '固收与中性',
                        '大类资配FOF'
                    ],
                    circleData: []
                },
                gdclBarData: {
                    id: 'gdcl-bar-1',
                    title: '高端存量对比(单位:万)',
                    thisYear: '2023',
                    lastYear: '2022',
                    gdDataStaticEndDate: '',
                    dataX: [
                        '大类稳健FOF',
                        '大类平衡FOF',
                        '大类进取FOF',
                        '股票型',
                        '股权型',
                        '固收与中性',
                        'CTA策略',
                        '另类策略',
                        '其他'
                    ],
                    thisYearData: [],
                    lastYearData: []
                },
                cpclBarData: {
                    id: 'cpcl-bar-1',
                    title: '净新增产品归因-产品策略(单位:万)',
                    dataX: [
                        '股票',
                        '股权',
                        'CTA',
                        '另类',
                        '固收与中性',
                        '收益型',
                        '非收益型',
                        '大类资配FOF'
                    ],
                    cpclData: []
                },
                jxzxlBarData: {
                    id: 'jxzxl-bar-1',
                    title: '销量(万)',
                    monthDataX: [],
                    monthData: [],
                    monStagingData: [],
                    quaStagingData: []
                },
                jxzBarData: {
                    id: 'jxz-bar-1',
                    title: '净新增(万)',
                    monthDataX: [],
                    monthData: [],
                    monStagingData: [],
                    quaStagingData: []
                    // monthData: [88, 92, -63, 77, 94, 80, 72, -86, 88, 99, 100, 80]
                },
                lsclBarData: {
                    id: 'lscl-bar-1',
                    title: '零售存量对比(单位:万)',
                    thisYear: '2023',
                    lastYear: '2022',
                    lsDataStaticEndDate: '',
                    dataX: ['公募-非货币', '公募-货币', '组合/大V'],
                    // thisYearData: [88, 92, 63],
                    // lastYearData: [93, 60, 61]
                    thisYearData: [],
                    lastYearData: []
                },
                gdcjkhData: {
                    id: 'gdcjkh-bar-1',
                    title: '高端成交客户',
                    thisYear: '2023',
                    lastYear: '2022',
                    dataX: ['存量客户', '0存量客户'],
                    // thisYearData: [88, 92],
                    // lastYearData: [93, 60]
                    thisYearData: [],
                    lastYearData: []
                },
                jxzSumBarData: {
                    id: 'jxzsum-bar-1',
                    title: '净新增:XXXX万元',
                    dataY: ['购买量', '赎回量'],
                    jxzDataStaticEndDate: '',
                    // jxzSumData: [88, 92]
                    jxzSumData: []
                },
                custPvBoard: {
                    title: '客户APP浏览行为',
                    boardData: [
                        // {
                        //     id: 11,
                        //     name: '浏览基金产品',
                        //     value: '0人',
                        //     note: '当月',
                        //     // eslint-disable-next-line camelcase
                        //     top3_Name: '基金访问TOP3'
                        // },
                        // {
                        //     id: 12,
                        //     name: '浏览资讯页面',
                        //     value: '0人',
                        //     note: '当月',
                        //     // eslint-disable-next-line camelcase
                        //     top3_Name: '页面访问TOP3'
                        // },
                        // {
                        //     id: 13,
                        //     name: '浏览视频页面',
                        //     value: '0人',
                        //     note: '当月',
                        //     // eslint-disable-next-line camelcase
                        //     top3_Name: '页面访问TOP3'
                        // },
                        // {
                        //     id: 14,
                        //     name: '浏览自选产品',
                        //     value: '0人',
                        //     note: '当月',
                        //     // eslint-disable-next-line camelcase
                        //     top3_Name: '基金访问TOP3'
                        // }
                    ],
                    grid11Data: [],
                    grid12Data: [],
                    grid13Data: [],
                    grid14Data: []
                },
                custChgBoard: {
                    title: '客户认购/申购',
                    boardData: [
                        // { id: 21, name: '客户赎回明细', value: '0人', note: '当月' },
                        // { id: 22, name: '客户申购明细', value: '0人', note: '当月' },
                        // { id: 23, name: '流失客户', value: '0人', note: '最近3月' },
                        // { id: 24, name: '待定', value: '待定', note: '待定' }
                        // {id:25,name: "测试滚动效果", value: "6人", note: "当月"},
                        // {id:26,name: "测试滚动效果", value: "12人"},
                    ]
                },
                boardId: 11,
                clickBoard: '浏览基金产品',
                custPvTable: {
                    //客户浏览行为表格
                    columns: [
                        {
                            value: 'consCustNo',
                            label: '投顾客户号'
                        },
                        {
                            value: 'consCustName',
                            label: '客户姓名'
                        },
                        {
                            value: 'consName',
                            label: '投顾姓名'
                        },
                        {
                            value: 'outletName',
                            label: '投顾部门'
                        },
                        {
                            value: 'jjdm',
                            label: '访问基金代码'
                        },
                        {
                            value: 'jjjc',
                            label: '基金名称'
                        },
                        {
                            value: 'pv30',
                            label: '最近30天访问量'
                        },
                        {
                            value: 'pv14',
                            label: '最近14天访问量'
                        },
                        {
                            value: 'lastFavoriteDay',
                            label: '最后访问日期'
                        }
                    ],
                    data: [
                        {
                            consCustNo: '',
                            consCustName: '',
                            consName: '',
                            outletName: '',
                            favoriteObject: '',
                            jjjc: '',
                            pv30: '',
                            pv14: '',
                            lastFavoriteDay: ''
                        }
                    ]
                },
                custPvTable2: {
                    //研习社表格
                    columns: [
                        {
                            value: 'custname',
                            label: '客户姓名',
                            minWidth: 400
                        },
                        {
                            value: 'conscustNo',
                            label: '投顾客户号',
                            minWidth: 200
                        },
                        {
                            value: 'custType',
                            label: '客户类型'
                        },
                        {
                            value: 'courseNum',
                            label: '学习课程数'
                        },
                        {
                            value: 'totalDuration',
                            label: '学习总时长(分钟)'
                        },
                        {
                            value: 'firstStudyDt',
                            label: '首次学习日期'
                        },
                        {
                            value: 'firstAckDt',
                            label: '首交日期'
                        },
                        {
                            value: 'd30PrebookNum',
                            label: '近30天预约产品个数'
                        },
                        {
                            value: 'd60PrebookNum',
                            label: '近60天预约产品个数'
                        },
                        {
                            value: 'd90PrebookNum',
                            label: '近90天预约产品个数'
                        }
                    ],
                    data: []
                },
                custPvTable3: {
                    //理财九章表格
                    columns: [
                        {
                            value: 'custname',
                            label: '客户姓名'
                        },
                        {
                            value: 'conscustno',
                            label: '投顾客户号'
                        },
                        {
                            value: 'custType',
                            label: '客户类型'
                        },
                        {
                            value: 'conferenceCnt',
                            label: '报名预约会议数'
                        },
                        {
                            value: 'attenderCnt',
                            label: '实际参加会议数'
                        },
                        {
                            value: 'attendRatio',
                            label: '参会率'
                        },
                        {
                            value: 'firstAppointDt',
                            label: '最近报名时间'
                        },
                        {
                            value: 'firstTradeDt',
                            label: '首交日期'
                        },
                        {
                            value: 'd30PrebookNum',
                            label: '近30天预约产品个数'
                        },
                        {
                            value: 'd60PrebookNum',
                            label: '近60天预约产品个数'
                        },
                        {
                            value: 'd90PrebookNum',
                            label: '近90天预约产品个数'
                        }
                    ],
                    data: []
                },
                pagination: {
                    currentPage: 1,
                    total: 0,
                    pageSize: 10,
                    pageSizes: [10, 30, 100, 500]
                }, // 自定义规则分页配置
                pagination2: {
                    currentPage: 1,
                    total: 0,
                    pageSize: 10,
                    pageSizes: [10, 30, 100, 500]
                }, // 自定义规则分页配置
                pagination3: {
                    currentPage: 1,
                    total: 0,
                    pageSize: 10,
                    pageSizes: [10, 30, 100, 500]
                } // 自定义规则分页配置
            }
        },
        created() {
            //加载效果 显示2s后自动关闭
            const allLoading = ElLoading.service({
                lock: true,
                text: 'Loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            setTimeout(() => {
                allLoading.close()
            }, 2000)
            this.tradeYear = new Date()
            this.gdclBarData.thisYear = this.tradeYear.getFullYear()
            this.gdclBarData.lastYear = this.tradeYear.getFullYear() - 1
            this.lsclBarData.thisYear = this.tradeYear.getFullYear()
            this.lsclBarData.lastYear = this.tradeYear.getFullYear() - 1
            this.gdcjkhData.thisYear = this.tradeYear.getFullYear()
            this.gdcjkhData.lastYear = this.tradeYear.getFullYear() - 1
            this.getOrgConsList() //获取组织架构
            this.getCustActData() //获取客户浏览数据
            this.getGdclBoradData() //高端存量
            this.getGdOtherInfo() //高端其他信息
            this.getLsclBoardData() //零售存量
            this.getJxzBroadData() //净新增
            this.getCustBookingVData() //销量
            this.getActCrmCustUvData() //投顾、部门下客户访问情况
            this.boradId = 11
            this.getListFavorPv() //默认获取基金产品浏览信息 boradId=11
            this.tradeMonth = new Date()
            //研习社，理财九章接口
            this.getYxsCrmonsultantData()
            this.getYxsCrmCustData()
            this.getActCrmLcjzInfoData()
            this.getActCrmLcjzCustInfoData()
        },
        methods: {
            selectXlTrigger(val) {
                if (val === '月度') {
                    if (this.jxzxlBarData.monthData.length !== 0) {
                        this.jxzxlBarData.monthData = []
                    }
                    if (this.jxzxlBarData.monthDataX.length !== 0) {
                        this.jxzxlBarData.monthDataX = []
                    }
                    this.jxzxlBarData.monthDataX.push('1月')
                    this.jxzxlBarData.monthDataX.push('2月')
                    this.jxzxlBarData.monthDataX.push('3月')
                    this.jxzxlBarData.monthDataX.push('4月')
                    this.jxzxlBarData.monthDataX.push('5月')
                    this.jxzxlBarData.monthDataX.push('6月')
                    this.jxzxlBarData.monthDataX.push('7月')
                    this.jxzxlBarData.monthDataX.push('8月')
                    this.jxzxlBarData.monthDataX.push('9月')
                    this.jxzxlBarData.monthDataX.push('10月')
                    this.jxzxlBarData.monthDataX.push('11月')
                    this.jxzxlBarData.monthDataX.push('12月')
                    for (let i = 0; i < this.jxzxlBarData.monStagingData.length; i++) {
                        this.jxzxlBarData.monthData.push(this.jxzxlBarData.monStagingData[i])
                    }
                }
                if (val === '季度') {
                    if (this.jxzxlBarData.monthData.length !== 0) {
                        this.jxzxlBarData.monthData = []
                    }
                    if (this.jxzxlBarData.monthDataX.length !== 0) {
                        this.jxzxlBarData.monthDataX = []
                    }
                    this.jxzxlBarData.monthDataX.push('第一季度')
                    this.jxzxlBarData.monthDataX.push('第二季度')
                    this.jxzxlBarData.monthDataX.push('第三季度')
                    this.jxzxlBarData.monthDataX.push('第四季度')
                    for (let i = 0; i < this.jxzxlBarData.quaStagingData.length; i++) {
                        this.jxzxlBarData.monthData.push(this.jxzxlBarData.quaStagingData[i])
                    }
                }
            },
            selectJxzTrigger(val) {
                if (val === '月度') {
                    if (this.jxzBarData.monthData.length !== 0) {
                        this.jxzBarData.monthData = []
                    }
                    if (this.jxzBarData.monthDataX.length !== 0) {
                        this.jxzBarData.monthDataX = []
                    }
                    this.jxzBarData.monthDataX.push('1月')
                    this.jxzBarData.monthDataX.push('2月')
                    this.jxzBarData.monthDataX.push('3月')
                    this.jxzBarData.monthDataX.push('4月')
                    this.jxzBarData.monthDataX.push('5月')
                    this.jxzBarData.monthDataX.push('6月')
                    this.jxzBarData.monthDataX.push('7月')
                    this.jxzBarData.monthDataX.push('8月')
                    this.jxzBarData.monthDataX.push('9月')
                    this.jxzBarData.monthDataX.push('10月')
                    this.jxzBarData.monthDataX.push('11月')
                    this.jxzBarData.monthDataX.push('12月')
                    for (let i = 0; i < this.jxzBarData.monStagingData.length; i++) {
                        this.jxzBarData.monthData.push(this.jxzBarData.monStagingData[i])
                    }
                }
                if (val === '季度') {
                    if (this.jxzBarData.monthData.length !== 0) {
                        this.jxzBarData.monthData = []
                    }
                    if (this.jxzBarData.monthDataX.length !== 0) {
                        this.jxzBarData.monthDataX = []
                    }
                    this.jxzBarData.monthDataX.push('第一季度')
                    this.jxzBarData.monthDataX.push('第二季度')
                    this.jxzBarData.monthDataX.push('第三季度')
                    this.jxzBarData.monthDataX.push('第四季度')
                    for (let i = 0; i < this.jxzBarData.quaStagingData.length; i++) {
                        this.jxzBarData.monthData.push(this.jxzBarData.quaStagingData[i])
                    }
                }
            },
            //投顾、部门下客户访问情况
            async getActCrmCustUvData() {
                let orgvalue
                if (this.orgvalue) {
                    let orgvalueStr = ''
                    this.$refs.consCascader.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue = orgvalueStr
                }
                const params = {
                    orgCode: orgvalue,
                    consCode: this.consultant,
                    conscustNo: this.conscustNo
                }
                const response = await getActCrmCustUvData(params)
                const pvlist = response.data
                this.actCrmCust.d1UsersSum = pvlist.d1UsersSum
                this.actCrmCust.d14UsersSum = pvlist.d14UsersSum
                this.actCrmCust.d30UsersSum = pvlist.d30UsersSum
            },
            //获取组织架构
            async getOrgConsList() {
                const params = {
                    modules: 20
                }
                const reponse = await getOrgConsList(params)
                const orglist = reponse.data.orgConsJson
                const batchdata = JSON.parse(orglist)
                this.options = this.formatOrgData(batchdata)
                this.options2 = this.options
            },
            formatOrgData(data) {
                for (let i = 0; i < data.length; i++) {
                    if (data[i].children.length < 1) {
                        data[i].children = undefined
                    } else {
                        this.formatOrgData(data[i].children)
                    }
                }
                return data
            },
            //设置日期列表
            initYears() {
                const myDate = new Date()
                const year = myDate.getFullYear() //获取当前年
                this.initSelectYear(year)
            },
            initSelectYear(year) {
                this.yearsList = []
                for (let i = 0; i < 30; i++) {
                    this.yearsList.push({ value: year - i, label: year - i + '年' })
                }
            },
            //获取部门下的所有投顾
            async changeCons(value) {
                this.orgvalue = value
                let orgvalueStr = ''
                this.$refs.consCascader.getCheckedNodes().forEach(element => {
                    if (orgvalueStr === '') {
                        orgvalueStr = element.value
                    } else {
                        orgvalueStr = element.value + ',' + orgvalueStr
                    }
                })
                console.log('orgvalueStr' + orgvalueStr)
                if (this.consultant) {
                    this.consultant = ''
                }
                const params = {
                    selectOrgCode: orgvalueStr,
                    multCheck: '1',
                    module: '20'
                }
                const response = await getDsConsByOrgcode_json(params)
                const conslist = response.data
                this.consultantList = conslist
            },
            filterParentValues(value) {
                return value.filter(item => {
                    if (!item.children || item.children.length === 0) {
                        return true
                    }
                    return item.children.every(child => !value.includes(child))
                })
            },
            //获取客户行为数据
            async getCustActData() {
                //清空参数
                // eslint-disable-next-line eqeqeq
                if (this.custPvBoard.boardData.length != 0) {
                    this.custPvBoard.boardData = []
                }
                // eslint-disable-next-line eqeqeq
                if (this.custChgBoard.boardData.length != 0) {
                    this.custChgBoard.boardData = []
                }
                let orgvalue
                if (this.orgvalue) {
                    let orgvalueStr = ''
                    this.$refs.consCascader.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue = orgvalueStr
                }
                const params = {
                    orgCode: orgvalue,
                    consCode: this.consultant,
                    conscustNo: this.conscustNo
                }
                const response = await getCustActData(params)
                const pvlist = response.data
                //-------- 客户app行为 响应参数赋值  有点多余 ------------
                const appPvCount = pvlist.appPvCount //浏览基金产品人数
                const appPvPgCount = pvlist.appPvPgCount //浏览资讯页面人数
                const appPvSpCount = pvlist.appPvSpCount //浏览视频页面人数
                const listFavorPvCount = pvlist.listFavorPvCount //浏览自选产品人数
                const listBoardRedemTradeCount = pvlist.listBoardRedemTradeCount
                const listBoardAppTradeCount = pvlist.listBoardAppTradeCount
                const listBoardLossProbablyVueCt = pvlist.listBoardLossProbablyVueCt
                const listBoardLossVueCt = pvlist.listBoardLossVueCt
                let redemAmt = 0
                let buyAmt = 0
                if (pvlist.redemAmt) {
                    redemAmt = pvlist.redemAmt
                }
                if (pvlist.buyAmt) {
                    buyAmt = pvlist.buyAmt
                }
                const map33 = pvlist.map33
                const map34 = pvlist.map34
                const map35 = pvlist.map35
                const map36 = pvlist.map36

                //--------客户app行为 浏览页模块赋值 ------------
                this.custPvBoard.boardData.push({
                    id: 11,
                    name: '浏览基金产品',
                    value: appPvCount + '人',
                    note: '当月',
                    // eslint-disable-next-line camelcase
                    top3_Name: '基金访问TOP3'
                })
                this.custPvBoard.boardData.push({
                    id: 12,
                    name: '浏览资讯页面',
                    value: appPvPgCount + '人',
                    note: '当月',
                    // eslint-disable-next-line camelcase
                    top3_Name: '页面访问TOP3'
                })
                this.custPvBoard.boardData.push({
                    id: 13,
                    name: '浏览视频页面',
                    value: appPvSpCount + '人',
                    note: '当月',
                    // eslint-disable-next-line camelcase
                    top3_Name: '页面访问TOP3'
                })
                this.custPvBoard.boardData.push({
                    id: 14,
                    name: '浏览自选产品',
                    value: listFavorPvCount + '人',
                    note: '当月',
                    // eslint-disable-next-line camelcase
                    top3_Name: '基金访问TOP3'
                })
                this.custChgBoard.boardData.push({
                    id: 21,
                    name: '客户赎回',
                    value: listBoardRedemTradeCount + '人',
                    note: '当月',
                    amt: redemAmt
                })
                this.custChgBoard.boardData.push({
                    id: 22,
                    name: '客户申购',
                    value: listBoardAppTradeCount + '人',
                    note: '当月',
                    amt: buyAmt
                })
                this.custChgBoard.boardData.push({
                    id: 23,
                    name: '需关注客户',
                    value: listBoardLossProbablyVueCt + '人',
                    note: '当月'
                })
                this.custChgBoard.boardData.push({
                    id: 24,
                    name: '已流失客户',
                    value: listBoardLossVueCt + '人',
                    note: '最近3月'
                })
                //--------客户app行为 访问前三 ------------
                this.custPvBoard.grid11Data = map33
                this.custPvBoard.grid12Data = map34
                this.custPvBoard.grid13Data = map35
                this.custPvBoard.grid14Data = map36
            },
            //获取客户行为数据  表单明细
            async getListFavorPv() {
                let orgvalue
                if (this.orgvalue) {
                    let orgvalueStr = ''
                    this.$refs.consCascader.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue = orgvalueStr
                }
                const params = {
                    orgCode: orgvalue,
                    consCode: this.consultant,
                    conscustNo: this.conscustNo,
                    pageSize: this.pagination.pageSize,
                    pageNum: this.pagination.currentPage,
                    boardId: this.boardId
                }
                const response = await getListFavorPv(params)
                const pvlist = response.data
                this.custPvTable.data = pvlist.boardAppPvs
                this.pagination.total = pvlist.totalNum
            },
            // 客户访问信息
            async getActCrmPvCustInfoData() {
                let orgvalue
                if (this.orgvalue) {
                    let orgvalueStr = ''
                    this.$refs.consCascader.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue = orgvalueStr
                }

                const params = {
                    orgCode: orgvalue,
                    consCode: this.consultant,
                    conscustNo: this.conscustNo,
                    rows: this.pagination.pageSize,
                    page: this.pagination.currentPage,
                    flag: this.boardId
                }
                const response = await getActCrmPvCustInfoData(params)
                const pvlist = response.data
                this.custPvTable.data = pvlist.rows
                this.pagination.total = pvlist.total
            },
            //获取客户行为数据  变更列名的方法 同时触发明细查询接口
            getTabData(item) {
                this.boardId = item
                if (item === 8) {
                    this.clickBoard = 'APP访问人数近1天'
                }
                if (item === 9) {
                    this.clickBoard = 'APP访问人数近14天'
                }
                if (item === 10) {
                    this.clickBoard = 'APP访问人数近30天'
                }
                // eslint-disable-next-line eqeqeq
                if (item == 10 || item == 9 || item == 8) {
                    this.custPvTable = {
                        columns: [
                            {
                                value: 'conscustNo',
                                label: '投顾客户号',
                                width: '120px'
                            },
                            {
                                value: 'custName',
                                label: '客户姓名'
                            },
                            {
                                value: 'conscode',
                                label: '投顾代码'
                            },
                            {
                                value: 'consname',
                                label: '投顾姓名'
                            },
                            {
                                value: 'u1Name',
                                label: '中心'
                            },
                            {
                                value: 'u2Name',
                                label: '部门'
                            },
                            {
                                value: 'u3Name',
                                label: '分中心'
                            },
                            {
                                value: 'd1Users',
                                label: '最近1天访问量'
                            },
                            {
                                value: 'd14Users',
                                label: '最近14天访问量'
                            },
                            {
                                value: 'd30Users',
                                label: '最近30天访问量'
                            },
                            {
                                value: 'lastVisitDt',
                                label: '最后访问日期'
                            }
                        ]
                    }
                    // eslint-disable-next-line eqeqeq
                }
                // eslint-disable-next-line eqeqeq
                if (item == 11) {
                    this.clickBoard = '浏览基金产品'
                    this.custPvTable.columns = [
                        {
                            value: 'consCustNo',
                            label: '投顾客户号',
                            width: '120px'
                        },
                        {
                            value: 'consCustName',
                            label: '客户姓名'
                        },
                        {
                            value: 'consName',
                            label: '投顾姓名'
                        },
                        {
                            value: 'outletName',
                            label: '投顾部门'
                        },
                        {
                            value: 'jjdm',
                            label: '访问基金代码'
                        },
                        {
                            value: 'jjjc',
                            label: '基金名称'
                        },
                        {
                            value: 'pv30',
                            label: '最近30天访问量'
                        },
                        {
                            value: 'pv14',
                            label: '最近14天访问量'
                        },
                        {
                            value: 'lastFavoriteDay',
                            label: '最后访问日期'
                        }
                    ]
                } else if (item === 12) {
                    this.clickBoard = '浏览资讯页面'
                    this.custPvTable = {
                        columns: [
                            {
                                value: 'consCustNo',
                                label: '投顾客户号',
                                width: '120px'
                            },
                            {
                                value: 'consCustName',
                                label: '客户姓名'
                            },
                            {
                                value: 'consName',
                                label: '投顾姓名'
                            },
                            {
                                value: 'u1Name',
                                label: '一级部门'
                            },
                            {
                                value: 'u2Name',
                                label: '二级部门'
                            },
                            {
                                value: 'appPid',
                                label: '页面ID'
                            },
                            {
                                value: 'appPname',
                                label: '页面名称'
                            },
                            {
                                value: 'appPtype',
                                label: '页面类型'
                            },
                            {
                                value: 'pv30',
                                label: '最近30天访问量'
                            },
                            {
                                value: 'pv14',
                                label: '最近14天访问量'
                            },
                            {
                                value: 'lastFavoriteDay',
                                label: '最后访问日期'
                            }
                        ]
                    }
                    // eslint-disable-next-line eqeqeq
                } else if (item == 13) {
                    this.clickBoard = '浏览视频页面'
                    this.custPvTable = {
                        columns: [
                            {
                                value: 'consCustNo',
                                label: '投顾客户号',
                                width: '120px'
                            },
                            {
                                value: 'consCustName',
                                label: '客户姓名'
                            },
                            {
                                value: 'consName',
                                label: '投顾姓名'
                            },
                            {
                                value: 'u1Name',
                                label: '一级部门'
                            },
                            {
                                value: 'u2Name',
                                label: '二级部门'
                            },
                            {
                                value: 'appPid',
                                label: '页面ID'
                            },
                            {
                                value: 'appPname',
                                label: '页面名称'
                            },
                            {
                                value: 'appPtype',
                                label: '页面类型'
                            },
                            {
                                value: 'pv30',
                                label: '最近30天访问量'
                            },
                            {
                                value: 'pv14',
                                label: '最近14天访问量'
                            },
                            {
                                value: 'lastFavoriteDay',
                                label: '最后访问日期'
                            }
                        ]
                    }
                    // eslint-disable-next-line eqeqeq
                } else if (item == 14) {
                    this.clickBoard = '浏览自选产品'
                    this.custPvTable = {
                        columns: [
                            {
                                value: 'consCustNo',
                                label: '投顾客户号',
                                width: '120px'
                            },
                            {
                                value: 'consCustName',
                                label: '客户姓名'
                            },
                            {
                                value: 'consName',
                                label: '投顾姓名'
                            },
                            {
                                value: 'u1Name',
                                label: '一级部门'
                            },
                            {
                                value: 'u2Name',
                                label: '二级部门'
                            },
                            {
                                value: 'jjdm',
                                label: '基金代码'
                            },
                            {
                                value: 'jjjc',
                                label: '基金名称'
                            },
                            {
                                value: 'isHold',
                                label: '是否持有'
                            },
                            {
                                value: 'fundType',
                                label: '基金类型'
                            },
                            {
                                value: 'hmcpx',
                                label: '产品类型'
                            },
                            {
                                value: 'navDt',
                                label: '最新净值日期'
                            },
                            {
                                value: 'nav',
                                label: '最新净值'
                            },
                            {
                                value: 'hb1y',
                                label: '近一个月业绩'
                            },
                            {
                                value: 'hb3y',
                                label: '近三个月业绩'
                            },
                            {
                                value: 'hb6y',
                                label: '近半年业绩'
                            },
                            {
                                value: 'hb1n',
                                label: '近一年业绩'
                            },
                            {
                                value: 'pv30',
                                label: '近30天访问量'
                            },
                            {
                                value: 'pv60',
                                label: '近60天访问量'
                            },
                            {
                                value: 'pv90',
                                label: '近90天访问量'
                            },
                            {
                                value: 'maxDt',
                                label: '最后访问日期'
                            },
                            {
                                value: 'addDate',
                                label: '添加自选日期'
                            }
                        ]
                    }
                    // eslint-disable-next-line eqeqeq
                } else if (item == 21) {
                    this.clickBoard = '客户赎回报表'
                    this.custPvTable = {
                        columns: [
                            {
                                value: 'consCustNo',
                                label: '投顾客户号',
                                width: '120px'
                            },
                            {
                                value: 'consCustName',
                                label: '客户姓名'
                            },
                            {
                                value: 'consName',
                                label: '投顾姓名'
                            },
                            {
                                value: 'u1Name',
                                label: '一级部门'
                            },
                            {
                                value: 'u2Name',
                                label: '二级部门'
                            },
                            {
                                value: 'ackDt',
                                label: '赎回日期'
                            },
                            {
                                value: 'ackAmtRmb',
                                label: '赎回金额'
                            },
                            {
                                value: 'currentSmVol',
                                label: '当前私募存量'
                            },
                            {
                                value: 'maxSmVol',
                                label: '历史最大私募存量',
                                width: '100px'
                            },
                            {
                                value: 'firstTradeDt',
                                label: '首交日期'
                            },
                            {
                                value: 'firstTradeAmt',
                                label: '首交金额',
                                width: '100px'
                            },
                            {
                                value: 'lastAppDt',
                                label: '最近一次申购日期'
                            },
                            {
                                value: 'lastAppAmt',
                                label: '最近一次申购金额 ',
                                width: '100px'
                            },
                            {
                                value: 'appCount',
                                label: '累计申购次数'
                            },
                            {
                                value: 'appSum',
                                label: '累计申购金额',
                                width: '100px'
                            },
                            {
                                value: 'lastRedemDt',
                                label: '最近一次赎回日期'
                            },
                            {
                                value: 'lastRedemAmt',
                                label: '最近一次赎回金额',
                                width: '100px'
                            },
                            {
                                value: 'redemCount',
                                label: '累计赎回次数'
                            },
                            {
                                value: 'redemSum',
                                label: '累计赎回金额',
                                width: '120px'
                            }
                        ]
                    }
                } else if (item === 22) {
                    this.clickBoard = '客户申购报表'
                    this.custPvTable = {
                        columns: [
                            {
                                value: 'consCustNo',
                                label: '投顾客户号',
                                width: '120px'
                            },
                            {
                                value: 'consCustName',
                                label: '客户姓名'
                            },
                            {
                                value: 'consName',
                                label: '投顾姓名'
                            },
                            {
                                value: 'u1Name',
                                label: '一级部门'
                            },
                            {
                                value: 'u2Name',
                                label: '二级部门'
                            },
                            {
                                value: 'ackDt',
                                label: '申购日期'
                            },
                            {
                                value: 'ackAmtRmb',
                                label: '申购金额'
                            },
                            {
                                value: 'currentSmVol',
                                label: '当前私募存量',
                                width: '100px'
                            },
                            {
                                value: 'maxSmVol',
                                label: '历史最大私募存量',
                                width: '120px'
                            },
                            {
                                value: 'firstTradeDt',
                                label: '首交日期'
                            },
                            {
                                value: 'firstTradeAmt',
                                label: '首交金额'
                            },
                            {
                                value: 'lastAppDt',
                                label: '最近一次申购日期'
                            },
                            {
                                value: 'lastAppAmt',
                                label: '最近一次申购金额 '
                            },
                            {
                                value: 'appCount',
                                label: '累计申购次数'
                            },
                            {
                                value: 'appSum',
                                label: '累计申购金额',
                                width: '120px'
                            },
                            {
                                value: 'lastRedemDt',
                                label: '最近一次赎回日期'
                            },
                            {
                                value: 'lastRedemAmt',
                                label: '最近一次赎回金额',
                                width: '100px'
                            },
                            {
                                value: 'redemCount',
                                label: '累计赎回次数'
                            },
                            {
                                value: 'redemSum',
                                label: '累计赎回金额',
                                width: '120px'
                            }
                        ]
                    }
                    // eslint-disable-next-line eqeqeq
                } else if (item == 23) {
                    this.clickBoard = '需关注客户'
                    this.custPvTable = {
                        columns: [
                            {
                                value: 'consCustNo',
                                label: '投顾客户号',
                                width: '120px'
                            },
                            {
                                value: 'consCustName',
                                label: '客户姓名'
                            },
                            {
                                value: 'consName',
                                label: '投顾姓名'
                            },
                            {
                                value: 'u1Name',
                                label: '一级部门'
                            },
                            {
                                value: 'u2Name',
                                label: '二级部门'
                            },
                            {
                                value: 'flagReason',
                                label: '关注理由',
                                popText:'topFeature'
                            },
                            {
                                value: 'lastRedemDt',
                                label: '最近一次赎回日期'
                            },
                            {
                                value: 'lastRedemAmt',
                                label: '最近一次赎回金额'
                            },
                            {
                                value: 'heGpdcAmtCur',
                                label: '当前剩余私募二级存量'
                            },
                            {
                                value: 'currentSmVol',
                                label: '当前剩余私募总存量'
                            },
                            {
                                value: 'maxSmVol',
                                label: '私募历史最大存量'
                            },
                            {
                                value: 'firstTradeDt',
                                label: '私募首交日期 '
                            },
                            {
                                value: 'firstTradeAmt',
                                label: '私募首交金额'
                            }
                        ]
                    }
                } else if (item === 24) {
                    this.clickBoard = '已流失客户'
                    this.custPvTable = {
                        columns: [
                            {
                                value: 'consCustNo',
                                label: '投顾客户号',
                                width: '120px'
                            },
                            {
                                value: 'consCustName',
                                label: '客户姓名'
                            },
                            {
                                value: 'consName',
                                label: '投顾姓名'
                            },
                            {
                                value: 'u1Name',
                                label: '一级部门'
                            },
                            {
                                value: 'u2Name',
                                label: '二级部门'
                            },
                            {
                                value: 'lastRedemDt',
                                label: '最近一次赎回日期'
                            },
                            {
                                value: 'lastRedemAmt',
                                label: '最近一次赎回金额'
                            },
                            {
                                value: 'heGpdcAmtCur',
                                label: '当前剩余私募二级存量'
                            },
                            {
                                value: 'currentSmVol',
                                label: '当前剩余私募总存量'
                            },
                            {
                                value: 'maxSmVol',
                                label: '历史最大存量'
                            },
                            {
                                value: 'firstTradeDt',
                                label: '首交日期 '
                            },
                            {
                                value: 'firstTradeAmt',
                                label: '首交金额'
                            }
                        ]
                    }
                }
                if (item === 10 || item === 9 || item === 8) {
                    this.getActCrmPvCustInfoData()
                } else {
                    this.getListFavorPv()
                }
            },
            handleSizeChange(size) {
                this.pagination.pageSize = size
                if (this.boardId === 10 || this.boardId === 9 || this.boardId === 8) {
                    this.getActCrmPvCustInfoData()
                } else {
                    this.getListFavorPv()
                }
            },
            handleCurrentChange(currentPage) {
                this.pagination.currentPage = currentPage
                if (this.boardId === 10 || this.boardId === 9 || this.boardId === 8) {
                    this.getActCrmPvCustInfoData()
                } else {
                    this.getListFavorPv()
                }
            },
            handleSizeChange2(size) {
                this.pagination2.pageSize = size
                this.getYxsCrmCustData()
            },
            handleCurrentChange2(currentPage) {
                this.pagination2.currentPage = currentPage
                this.getYxsCrmCustData()
            },
            handleSizeChange3(size) {
                this.pagination3.pageSize = size
                this.getActCrmLcjzCustInfoData()
            },
            handleCurrentChange3(currentPage) {
                this.pagination3.currentPage = currentPage
                this.getActCrmLcjzCustInfoData()
            },

            //字段格式转换
            formatCash(cash) {
                // eslint-disable-next-line camelcase
                const str_cash = cash + '' //转换成字符串
                // eslint-disable-next-line camelcase
                let ret_cash = ''
                let counter = 0
                // eslint-disable-next-line camelcase
                for (let i = str_cash.length - 1; i >= 0; i--) {
                    // eslint-disable-next-line camelcase
                    ret_cash = str_cash.charAt(i) + ret_cash
                    // eslint-disable-next-line camelcase, eqeqeq
                    if (str_cash.charAt(i) == '.') {
                        counter = 0
                        continue
                    }
                    counter++
                    // eslint-disable-next-line eqeqeq
                    if (counter == 20) {
                        counter = 0
                        // eslint-disable-next-line eqeqeq, camelcase
                        if (
                            // eslint-disable-next-line eqeqeq
                            i != 0 &&
                            // eslint-disable-next-line camelcase, eqeqeq
                            str_cash.charAt(i - 1) != '.' &&
                            // eslint-disable-next-line eqeqeq, camelcase
                            str_cash.charAt(i - 1) != '-'
                        ) {
                            // eslint-disable-next-line camelcase
                            ret_cash = ',' + ret_cash
                        }
                    }
                }
                // eslint-disable-next-line camelcase
                return ret_cash
            },
            //获取高端存量数据
            async getGdclBoradData() {
                //清除上一次查询的数据
                //当年高端
                if (this.gdclBarData.thisYearData.length !== 0) {
                    this.gdclBarData.thisYearData = []
                }
                //上一年
                if (this.gdclBarData.lastYearData.length !== 0) {
                    this.gdclBarData.lastYearData = []
                }
                if (this.gdclPieData.intCircleData.length !== 0) {
                    this.gdclPieData.intCircleData = []
                }
                if (this.gdclPieData.outCircleData.length !== 0) {
                    this.gdclPieData.outCircleData = []
                }
                if (this.gdcjkhData.thisYearData.length !== 0) {
                    this.gdcjkhData.thisYearData = []
                }
                if (this.gdcjkhData.lastYearData.length !== 0) {
                    this.gdcjkhData.lastYearData = []
                }
                let orgvalue
                if (this.orgvalue) {
                    let orgvalueStr = ''
                    this.$refs.consCascader.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue = orgvalueStr
                }
                const params = {
                    orgCode: orgvalue,
                    consCode: this.consultant,
                    conscustNo: this.conscustNo,
                    tradeYear: this.tradeYear,
                    peAmtWay: this.peAmtWay
                }
                const response = await getGdclBoradData(params)
                const pvlist = response.data
                //高端存量汇总数据赋值
                for (let i = 0; i < this.gdclSumData.length; i++) {
                    const obj = this.gdclSumData[i]
                    // eslint-disable-next-line eqeqeq
                    if (obj.key == 'gdzcgm') {
                        //高端资产规模
                        if (pvlist.thisYearGdclData.totalAmt < 10000) {
                            obj.value = pvlist.thisYearGdclData.totalAmt + '万'
                        } else {
                            obj.value =
                                ((pvlist.thisYearGdclData.totalAmt * 1.0) / 10000).toFixed(2) + '亿'
                        }
                    }
                    if (obj.key === 'gdhwcl') {
                        //高端海外存量
                        if (pvlist.thisYearGdclData.overseasAmt < 10000) {
                            obj.value = pvlist.thisYearGdclData.overseasAmt + '万'
                        } else {
                            obj.value =
                                ((pvlist.thisYearGdclData.overseasAmt * 1.0) / 10000).toFixed(2) + '亿'
                        }
                    }
                    if (obj.key === 'gdgncl') {
                        //高端国内存量
                        if (pvlist.thisYearGdclData.domesticAmt < 10000) {
                            obj.value = pvlist.thisYearGdclData.domesticAmt + '万'
                        } else {
                            obj.value =
                                ((pvlist.thisYearGdclData.domesticAmt * 1.0) / 10000).toFixed(2) + '亿'
                        }
                    }
                    // eslint-disable-next-line eqeqeq
                    if (obj.key == 'gdkhhj') {
                        //高端客户户均
                        obj.value = pvlist.thisYearGdclData.custAmtAve + '万'
                    }
                    // eslint-disable-next-line eqeqeq
                    if (obj.key == 'gdljsy') {
                        //高端累计收益
                        obj.value =
                            pvlist.thisYearGdclData.accumIncomeExFee +
                            '万' +
                            '(' +
                            pvlist.thisYearGdclData.accumIncomePer +
                            ')'
                    }
                    // eslint-disable-next-line eqeqeq
                    if (obj.key == 'gdclkh') {
                        //高端存量客户
                        obj.value = pvlist.thisYearGdclData.assertUers + '人'
                    }
                }
                const thisYearGdclData = pvlist.thisYearGdclData
                const lastYearGdclData = pvlist.lastYearGdclData
                this.gdclBarData.gdDataStaticEndDate = pvlist.dataStaticEndDate
                //     ------    高端存量对比柱状图    --------------
                this.gdclBarData.thisYearData.push(thisYearGdclData.fof1Amt)
                this.gdclBarData.thisYearData.push(thisYearGdclData.fof2Amt)
                this.gdclBarData.thisYearData.push(thisYearGdclData.fof3Amt)
                this.gdclBarData.thisYearData.push(thisYearGdclData.sharesAmt)
                this.gdclBarData.thisYearData.push(thisYearGdclData.peAmt)
                this.gdclBarData.thisYearData.push(thisYearGdclData.fixedAmt)
                this.gdclBarData.thisYearData.push(thisYearGdclData.ctaAmt)
                this.gdclBarData.thisYearData.push(thisYearGdclData.alterAmt)
                this.gdclBarData.thisYearData.push(thisYearGdclData.otherAmt)

                this.gdclBarData.lastYearData.push(lastYearGdclData.fof1Amt)
                this.gdclBarData.lastYearData.push(lastYearGdclData.fof2Amt)
                this.gdclBarData.lastYearData.push(lastYearGdclData.fof3Amt)
                this.gdclBarData.lastYearData.push(lastYearGdclData.sharesAmt)
                this.gdclBarData.lastYearData.push(lastYearGdclData.peAmt)
                this.gdclBarData.lastYearData.push(lastYearGdclData.fixedAmt)
                this.gdclBarData.lastYearData.push(lastYearGdclData.ctaAmt)
                this.gdclBarData.lastYearData.push(lastYearGdclData.alterAmt)
                this.gdclBarData.lastYearData.push(lastYearGdclData.otherAmt)
                //     ------    高端存量饼图    --------------

                this.gdclPieData.intCircleData.push({
                    name: '大类稳健FOF',
                    value: thisYearGdclData.fof1Amt
                })
                this.gdclPieData.intCircleData.push({
                    name: '大类平衡FOF',
                    value: thisYearGdclData.fof2Amt
                })
                this.gdclPieData.intCircleData.push({
                    name: '大类进取FOF',
                    value: thisYearGdclData.fof3Amt
                })
                this.gdclPieData.intCircleData.push({
                    name: '股票型',
                    value: thisYearGdclData.sharesAmt
                })
                this.gdclPieData.intCircleData.push({
                    name: '股权型',
                    value: thisYearGdclData.peAmt
                })
                this.gdclPieData.intCircleData.push({
                    name: '固收与中性',
                    value: thisYearGdclData.fixedAmt
                })
                this.gdclPieData.intCircleData.push({
                    name: 'CTA策略',
                    value: thisYearGdclData.ctaAmt
                })
                this.gdclPieData.intCircleData.push({
                    name: '另类策略',
                    value: thisYearGdclData.alterAmt
                })
                this.gdclPieData.intCircleData.push({
                    name: '其他',
                    value: thisYearGdclData.otherAmt
                })
                this.gdclPieData.outCircleData.push({
                    name: '单策略',
                    value: thisYearGdclData.singlePolicy
                })
                this.gdclPieData.outCircleData.push({
                    name: '多策略',
                    value: thisYearGdclData.multiPolicy
                })
                //国内外占比
                this.overseasAmtPer = thisYearGdclData.overseasAmtPer
                this.domesticAmtPer = thisYearGdclData.domesticAmtPer
                this.gnwidth = thisYearGdclData.domesticAmtPer
                this.hwwidth = thisYearGdclData.overseasAmtPer

                //     ------    高端存量对比柱状图    --------------

                this.gdcjkhData.thisYearData.push(thisYearGdclData.assertUers)
                this.gdcjkhData.thisYearData.push(thisYearGdclData.noAssetUsers)

                this.gdcjkhData.lastYearData.push(lastYearGdclData.assertUers)
                this.gdcjkhData.lastYearData.push(lastYearGdclData.noAssetUsers)
            },
            async getGdOtherInfo() {
                let orgvalue
                if (this.orgvalue) {
                    let orgvalueStr = ''
                    this.$refs.consCascader.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue = orgvalueStr
                }
                const params = {
                    orgCode: orgvalue,
                    consCode: this.consultant,
                    conscustNo: this.conscustNo,
                    tradeYear: this.tradeYear
                }
                const response2 = await getGdOtherInfo(params)
                const data = response2.data
                ;(this.cj2sf = data.cj2sf),
                    (this.netIncreaseAmtRmb = data.netIncreaseAmtRmb),
                    (this.custqzAll = data.custqzAll)
            },
            //获取零售存量数据
            async getLsclBoardData() {
                //清除上一次查询的数据
                if (this.lsclPieData.circleData.length !== 0) {
                    this.lsclPieData.circleData = []
                }
                if (this.lsclBarData.thisYearData.length !== 0) {
                    this.lsclBarData.thisYearData = []
                }
                if (this.lsclBarData.lastYearData.length !== 0) {
                    this.lsclBarData.lastYearData = []
                }

                let orgvalue
                if (this.orgvalue) {
                    let orgvalueStr = ''
                    this.$refs.consCascader.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue = orgvalueStr
                }
                const params = {
                    orgCode: orgvalue,
                    consCode: this.consultant,
                    conscustNo: this.conscustNo,
                    tradeYear: this.tradeYear
                }
                const response = await getLsclBoardData(params)
                const lslist = response.data
                //零售存量汇总数据赋值
                for (let i = 0; i < this.lsclSumData.length; i++) {
                    const obj = this.lsclSumData[i]
                    // eslint-disable-next-line eqeqeq
                    if (obj.key == 'lszcgm') {
                        //零售资产规模
                        if (lslist.thisYearLsclData.totalAmtSum < 10000) {
                            obj.value = lslist.thisYearLsclData.totalAmtSum + '万'
                        } else {
                            obj.value =
                                ((lslist.thisYearLsclData.totalAmtSum * 1.0) / 10000).toFixed(2) +
                                '亿'
                        }
                    }
                    // eslint-disable-next-line eqeqeq
                    if (obj.key == 'lskhhj') {
                        //零售客户户均
                        obj.value = lslist.thisYearLsclData.custAmtAve + '万'
                    }
                    // eslint-disable-next-line eqeqeq
                    if (obj.key == 'lsljhj') {
                        //零售累计收益
                        obj.value =
                            lslist.thisYearLsclData.accumIncomeSum +
                            '万' +
                            '(' +
                            lslist.thisYearLsclData.accumIncomePer +
                            ')'
                    }
                    // eslint-disable-next-line eqeqeq
                    if (obj.key == 'lsclkh') {
                        //零售存量客户
                        obj.value = lslist.thisYearLsclData.assetUsersSum + '人'
                    }
                }
                const thisYearLsclData = lslist.thisYearLsclData
                const lastYearLsclData = lslist.lastYearLsclData
                this.lsclBarData.lsDataStaticEndDate = lslist.dataStaticEndDate
                //零售存量饼图
                this.lsclPieData.circleData.push({
                    name: '公募-非货币市值',
                    value: thisYearLsclData.fhbAmtSum
                })
                this.lsclPieData.circleData.push({
                    name: '公募-货币市值',
                    value: thisYearLsclData.hbAmtSum
                })
                this.lsclPieData.circleData.push({
                    name: '组合/大V市值',
                    value: thisYearLsclData.zhAmtSum
                })
                //  ----------   零售存量柱状图-----------
                //当年
                this.lsclBarData.thisYearData.push(thisYearLsclData.fhbAmtSum)
                this.lsclBarData.thisYearData.push(thisYearLsclData.hbAmtSum)
                this.lsclBarData.thisYearData.push(thisYearLsclData.zhAmtSum)

                //上一年
                this.lsclBarData.lastYearData.push(lastYearLsclData.fhbAmtSum)
                this.lsclBarData.lastYearData.push(lastYearLsclData.hbAmtSum)
                this.lsclBarData.lastYearData.push(lastYearLsclData.zhAmtSum)
            },
            //获取销量数据
            async getCustBookingVData() {
                this.loading = true
                this.xlTimeFlag = '月度'
                let orgvalue
                if (this.orgvalue) {
                    let orgvalueStr = ''
                    this.$refs.consCascader.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue = orgvalueStr
                }
                const params = {
                    orgCode: orgvalue,
                    consCode: this.consultant,
                    conscustNo: this.conscustNo,
                    tradeYear: this.tradeYear
                }
                const response = await getCustBookingVData(params)
                const pvlist = response.data
                //清除上一次查询出的数据
                if (this.jxzxlBarData.monthData.length !== 0) {
                    this.jxzxlBarData.monthData = []
                }
                if (this.jxzxlBarData.monthDataX.length !== 0) {
                    this.jxzxlBarData.monthDataX = []
                }
                if (this.jxzxlBarData.monStagingData.length !== 0) {
                    this.jxzxlBarData.monStagingData = []
                }
                if (this.jxzxlBarData.quaStagingData.length !== 0) {
                    this.jxzxlBarData.quaStagingData = []
                }
                if (this.cpclPieData.circleData.length !== 0) {
                    this.cpclPieData.circleData = []
                }
                this.gdxlAmtSum = pvlist.gdxlAmtSum
                this.gdxlPer = pvlist.gdxlPer

                this.jxzxlBarData.monthDataX.push('1月')
                this.jxzxlBarData.monthDataX.push('2月')
                this.jxzxlBarData.monthDataX.push('3月')
                this.jxzxlBarData.monthDataX.push('4月')
                this.jxzxlBarData.monthDataX.push('5月')
                this.jxzxlBarData.monthDataX.push('6月')
                this.jxzxlBarData.monthDataX.push('7月')
                this.jxzxlBarData.monthDataX.push('8月')
                this.jxzxlBarData.monthDataX.push('9月')
                this.jxzxlBarData.monthDataX.push('10月')
                this.jxzxlBarData.monthDataX.push('11月')
                this.jxzxlBarData.monthDataX.push('12月')
                for (let i = 0; i < pvlist.monthXlArray.length; i++) {
                    this.jxzxlBarData.monthData.push(pvlist.monthXlArray[i])
                }
                for (let i = 0; i < pvlist.monthXlArray.length; i++) {
                    this.jxzxlBarData.monStagingData.push(pvlist.monthXlArray[i])
                }
                for (let i = 0; i < pvlist.quarterXlArray.length; i++) {
                    this.jxzxlBarData.quaStagingData.push(pvlist.quarterXlArray[i])
                }
                for (let i = 0; i < pvlist.typeXLPieList.length; i++) {
                    this.cpclPieData.circleData.push(pvlist.typeXLPieList[i])
                }
                if (pvlist.custXl === '' || pvlist.custXl === null || pvlist.custXl === undefined) {
                    this.xlCust.oldCustAmt = null
                    this.xlCust.newCustAmt = null
                    this.xlCust.oldCustAmtRateStr = null
                    this.xlCust.newCustAmtRateStr = null
                    this.xlCust.newCustNum = null
                    this.xlCust.oldCustNum = null
                    this.xlCust.oldCustRepurchaseRateStr = null
                    this.xlCust.newCustRepurchaseRateStr = null
                }
                if (
                    pvlist.jdCplxList === '' ||
                    pvlist.jdCplxList === null ||
                    pvlist.jdCplxList === undefined ||
                    pvlist.jdCplxList.length < 1
                ) {
                    this.jdCplxData.firstJdType = null
                    this.jdCplxData.firstJdTypePer = null
                    this.jdCplxData.secondJdType = null
                    this.jdCplxData.secondJdTypePer = null
                    this.jdCplxData.thirdJdType = null
                    this.jdCplxData.thirdJdTypePer = null
                    this.jdCplxData.fourthJdType = null
                    this.jdCplxData.fourthJdTypePer = null
                }
                this.xlCust.oldCustAmt = pvlist.custXl.oldCustAmt
                this.xlCust.newCustAmt = pvlist.custXl.newCustAmt
                this.xlCust.oldCustAmtRateStr = pvlist.custXl.oldCustAmtRateStr
                this.xlCust.newCustAmtRateStr = pvlist.custXl.newCustAmtRateStr
                this.xlCust.newCustNum = pvlist.custXl.newCustNum
                this.xlCust.oldCustNum = pvlist.custXl.oldCustNum
                this.xlCust.oldCustRepurchaseRateStr = pvlist.custXl.oldCustRepurchaseRateStr
                this.xlCust.newCustRepurchaseRateStr = pvlist.custXl.newCustRepurchaseRateStr
                //获取各季度主要产品类型 以及占比情况
                for (let i = 0; i < pvlist.jdCplxList.length; i++) {
                    if ('第一季度' === pvlist.jdCplxList[i].jd) {
                        this.jdCplxData.firstJdType = pvlist.jdCplxList[i].type
                        this.jdCplxData.firstJdTypePer = pvlist.jdCplxList[i].typePer
                    }
                    if ('第二季度' === pvlist.jdCplxList[i].jd) {
                        this.jdCplxData.secondJdType = pvlist.jdCplxList[i].type
                        this.jdCplxData.secondJdTypePer = pvlist.jdCplxList[i].typePer
                    }
                    if ('第三季度' === pvlist.jdCplxList[i].jd) {
                        this.jdCplxData.thirdJdType = pvlist.jdCplxList[i].type
                        this.jdCplxData.thirdJdTypePer = pvlist.jdCplxList[i].typePer
                    }
                    if ('第四季度' === pvlist.jdCplxList[i].jd) {
                        this.jdCplxData.fourthJdType = pvlist.jdCplxList[i].type
                        this.jdCplxData.fourthJdTypePer = pvlist.jdCplxList[i].typePer
                    }
                }
                this.loading = false
            },
            //获取净新增数据
            async getJxzBroadData() {
                this.jxzTimeFlag = '月度'

                let orgvalue
                if (this.orgvalue) {
                    let orgvalueStr = ''
                    this.$refs.consCascader.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue = orgvalueStr
                }
                const params = {
                    orgCode: orgvalue,
                    consCode: this.consultant,
                    conscustNo: this.conscustNo,
                    tradeYear: this.tradeYear
                }
                const response = await getJxzBroadData(params)
                const pvlist = response.data
                //清除上次查询的数据
                if (this.jxzBarData.monthData.length !== 0) {
                    this.jxzBarData.monthData = []
                }
                if (this.jxzBarData.monthDataX.length !== 0) {
                    this.jxzBarData.monthDataX = []
                }
                if (this.jxzBarData.monStagingData.length !== 0) {
                    this.jxzBarData.monStagingData = []
                }
                if (this.jxzBarData.quaStagingData.length !== 0) {
                    this.jxzBarData.quaStagingData = []
                }
                if (this.jxzSumBarData.jxzSumData.length !== 0) {
                    this.jxzSumBarData.jxzSumData = []
                }
                if (this.cpclBarData.cpclData.length !== 0) {
                    this.cpclBarData.cpclData = []
                }

                this.jxzSumBarData.jxzDataStaticEndDate = pvlist.dataStaticEndDate
                this.jxzSumBarData.title = '净新增:' + pvlist.jxzAmtSum + '万元'
                this.jxzSumBarData.jxzSumData.push(pvlist.buyAmtSum)
                this.jxzSumBarData.jxzSumData.push(pvlist.redemAmtSum)
                this.jxzBarData.monthDataX.push('1月')
                this.jxzBarData.monthDataX.push('2月')
                this.jxzBarData.monthDataX.push('3月')
                this.jxzBarData.monthDataX.push('4月')
                this.jxzBarData.monthDataX.push('5月')
                this.jxzBarData.monthDataX.push('6月')
                this.jxzBarData.monthDataX.push('7月')
                this.jxzBarData.monthDataX.push('8月')
                this.jxzBarData.monthDataX.push('9月')
                this.jxzBarData.monthDataX.push('10月')
                this.jxzBarData.monthDataX.push('11月')
                this.jxzBarData.monthDataX.push('12月')
                for (let i = 0; i < pvlist.monthJxzArray.length; i++) {
                    this.jxzBarData.monthData.push(pvlist.monthJxzArray[i])
                }
                for (let i = 0; i < pvlist.monthJxzArray.length; i++) {
                    this.jxzBarData.monStagingData.push(pvlist.monthJxzArray[i])
                }
                for (let i = 0; i < pvlist.quarterJxzArray.length; i++) {
                    this.jxzBarData.quaStagingData.push(pvlist.quarterJxzArray[i])
                }
                for (let i = 0; i < pvlist.typeJxzArray.length; i++) {
                    this.cpclBarData.cpclData.push(pvlist.typeJxzArray[i])
                }
                //净新增-客户归因
                if (
                    pvlist.custJxz === '' ||
                    pvlist.custJxz === null ||
                    pvlist.custJxz === undefined
                ) {
                    this.oldcustAmt = null
                    this.oldcustNum = null
                    this.oldcustPositive = null
                    this.oldcustPositiveRateStr = null
                    this.oldcustNegativeRateStr = null
                    this.newcustPositiveRateStr = null
                    this.newcustNegativeRateStr = null
                    this.oldcustNegative = null
                    this.newcustAmt = null
                    this.newcustNum = null
                    this.newcustPositive = null
                    this.newcustNegative = null
                    this.bxcustAmt = null
                    this.bxcustNum = null
                    this.oldcustAmtAve = null
                    this.newcustAmtAve = null
                } else {
                    this.oldcustAmt = pvlist.custJxz.oldcustAmt
                    this.oldcustNum = pvlist.custJxz.oldcustNum
                    this.oldcustPositive = pvlist.custJxz.oldcustPositive
                    this.oldcustPositiveRateStr = pvlist.custJxz.oldcustPositiveRateStr
                    this.oldcustNegativeRateStr = pvlist.custJxz.oldcustNegativeRateStr
                    this.newcustPositiveRateStr = pvlist.custJxz.newcustPositiveRateStr
                    this.newcustNegativeRateStr = pvlist.custJxz.newcustNegativeRateStr
                    this.oldcustNegative = pvlist.custJxz.oldcustNegative
                    this.newcustAmt = pvlist.custJxz.newcustAmt
                    this.newcustNum = pvlist.custJxz.newcustNum
                    this.newcustPositive = pvlist.custJxz.newcustPositive
                    this.newcustNegative = pvlist.custJxz.newcustNegative
                    this.bxcustAmt = pvlist.custJxz.bxcustAmt
                    this.bxcustNum = pvlist.custJxz.bxcustNum
                    this.oldcustAmtAve = pvlist.custJxz.oldcustAmtAve
                    this.newcustAmtAve = pvlist.custJxz.newcustAmtAve
                }
            },
            //点击查询时调用
            toQuery() {
                this.pageSize = 10
                this.pageNum = 1
                this.submissionFlag = true
                if (this.tradeYear) {
                    this.gdclBarData.thisYear = this.tradeYear.getFullYear()
                    this.gdclBarData.lastYear = this.tradeYear.getFullYear() - 1
                    this.lsclBarData.thisYear = this.tradeYear.getFullYear()
                    this.lsclBarData.lastYear = this.tradeYear.getFullYear() - 1
                    this.gdcjkhData.thisYear = this.tradeYear.getFullYear()
                    this.gdcjkhData.lastYear = this.tradeYear.getFullYear() - 1
                    if (
                        this.conscustNo === '' ||
                        this.conscustNo === null ||
                        this.conscustNo === undefined
                    ) {
                        document.getElementById('gdclkhdiv').style.display = '' //高端其他信息
                        document.getElementById('clkhbardiv').style.display = '' //存量客户柱状图
                        document.getElementById('jxzgykhdiv').style.display = '' //净新增归因-客户
                        document.getElementById('xlgydiv').style.display = '' //净新增归因-客户
                    } else {
                        document.getElementById('gdclkhdiv').style.display = 'none' //高端其他信息
                        document.getElementById('clkhbardiv').style.display = 'none' //存量客户柱状图
                        document.getElementById('jxzgykhdiv').style.display = 'none' //净新增归因-客户
                        document.getElementById('xlgydiv').style.display = 'none' //净新增归因-客户
                    }
                    this.getCustActData() //获取客户浏览数据
                    this.getGdclBoradData() //高端存量
                    this.getGdOtherInfo() //高端其他信息
                    this.getLsclBoardData() //零售存量
                    this.getJxzBroadData() //净新增
                    this.getCustBookingVData() //销量
                    this.getActCrmCustUvData() //投顾、部门下客户访问情况
                    this.boradId = 11
                    this.getListFavorPv() //默认获取基金产品浏览信息 boradId=11
                } else {
                    alert('请选择年份')
                }
                this.submissionFlag = false
            },
            async changePeAmtWay(val) {
                console.log('changePeAmtWay:',val)
                this.getGdclBoradData()
            },
            //点击查询时调用

            //    ---------------------研习社理财九章方法-----------------------------

            //获取部门下的所有投顾
            async changeCons2(label, value) {
                let orgvalue2
                if (this.orgvalue2) {
                    let orgvalueStr = ''
                    this.$refs.consCascader2.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue2 = orgvalueStr
                }
                if (this.consultant2) {
                    this.consultant2 = ''
                }
                const params = {
                    selectOrgCode: orgvalue2,
                    multCheck: '1',
                    module: '20'
                }
                const response = await getDsConsByOrgcode_json(params)
                const conslist = response.data
                this.consultantList2 = conslist
            },
            toQuery2() {
                if (this.tradeMonth) {
                    this.getYxsCrmonsultantData()
                    this.getYxsCrmCustData()
                    this.getActCrmLcjzInfoData()
                    this.getActCrmLcjzCustInfoData()
                } else {
                    alert('请选择时间区间')
                }
            },
            //研习社 投顾/部门名下客户学习情况统计
            async getYxsCrmonsultantData() {
                let orgvalue2
                if (this.orgvalue2) {
                    let orgvalueStr = ''
                    this.$refs.consCascader2.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue2 = orgvalueStr
                }
                const params = {
                    orgCode: orgvalue2,
                    consCode: this.consultant2,
                    tradeYearMonth: this.tradeMonth,
                    rows: this.pagination.pageSize,
                    page: this.pagination.currentPage
                }
                const response = await getYxsCrmonsultantData(params)
                const pvlist = response.data
                this.userNumSum = pvlist.userNumSum
                this.totalDurationSum = pvlist.totalDurationSum
                this.totalDurationAve = pvlist.totalDurationAve
            },
            //研习社 客户学习情况总览
            async getYxsCrmCustData() {
                let orgvalue2
                if (this.orgvalue2) {
                    let orgvalueStr = ''
                    this.$refs.consCascader2.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue2 = orgvalueStr
                }
                const params = {
                    orgCode: orgvalue2,
                    consCode: this.consultant2,
                    tradeYearMonth: this.tradeMonth,
                    rows: this.pagination2.pageSize,
                    page: this.pagination2.currentPage
                }
                const response = await getYxsCrmCustData(params)
                const pvlist = response.data
                this.custPvTable2.data = pvlist.rows
                this.pagination2.total = pvlist.total
            },
            //理财九章 投顾报名人数 汇总
            async getActCrmLcjzInfoData() {
                let orgvalue2
                if (this.orgvalue2) {
                    let orgvalueStr = ''
                    this.$refs.consCascader2.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue2 = orgvalueStr
                }
                const params = {
                    orgCode: orgvalue2,
                    consCode: this.consultant2,
                    tradeYearMonth: this.tradeMonth
                }
                const response = await getActCrmLcjzInfoData(params)
                const pvlist = response.data
                this.conferenceAttendersPer = pvlist.conferenceAttendersPer
                this.conferenceAppliersSum = pvlist.conferenceAppliersSum
                this.conferenceAttendersSum = pvlist.conferenceAttendersSum
            },
            //理财九章 某客户参会情况
            async getActCrmLcjzCustInfoData() {
                let orgvalue2
                if (this.orgvalue2) {
                    let orgvalueStr = ''
                    this.$refs.consCascader2.getCheckedNodes().forEach(element => {
                        if (orgvalueStr === '') {
                            orgvalueStr = element.value
                        } else {
                            orgvalueStr = element.value + ',' + orgvalueStr
                        }
                    })
                    orgvalue2 = orgvalueStr
                }
                const params = {
                    orgCode: orgvalue2,
                    consCode: this.consultant2,
                    tradeYearMonth: this.tradeMonth,
                    rows: this.pagination3.pageSize,
                    page: this.pagination3.currentPage
                }
                const response = await getActCrmLcjzCustInfoData(params)
                const pvlist = response.data
                this.custPvTable3.data = pvlist.rows
                this.pagination3.total = pvlist.total
            }
        }
    }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
    .container {
        display: flex;
        align-items: center; /* 在交叉轴上居中 */
        justify-content: center; /* 在主轴上居中 */
    }

    .right-aligned {
        text-align: right;
    }

    .fgclass2 {
        display: grid;
        grid-template-columns: auto auto;
        width: 100%;
        height: 20px;
        font-size: 14px;
        color: white;
        background: #4874cb;
    }

    .right-aligned {
        justify-self: end;
    }

    .fgclass {
        width: 100%;
        height: 18px;
        font-size: 14px;
        color: white;
        background: #4874cb;
    }

    .fgclass3 {
        grid-template-columns: auto auto;
        width: 100%;
        height: 28px;
        font-size: 14px;
        color: white;
        background: #4874cb;

    }
    
    .fgclass4 {
        font-size: 14px;
        color: white;
        background: #4874cb;
    }

    .echartBackColor {
        background-color: #dae3f5;
        border: 2px solid white;
    }

    .divLeftClass {
        float: left;
        margin: 0 4px;
    }

    .divRightClass {
        float: right;
        margin: 0 4px;
    }

    .divRightClass2 {
        float: right;
        padding: 0 30px 0 4px;
        margin: 0 4px;
    }

    .legendLeftClass {
        float: left;
        width: 6px;
        height: 6px;
        margin: 10px 4px 0 0;
        background-color: #bfbfbf;
    }

    .legendRightClass {
        float: left;
        width: 6px;
        height: 6px;
        margin: 10px 4px 0 0;
        background-color: #e54c5e;
    }

    /* .divLeftClass{
        float: left;
          margin: 0 4px 0 4px;
    }
    .divRightClass{
        float: right;
        margin: 0 4px 0 4px;
    }
    .legendLeftClass {
        float: left;
        height: 5px;
        width: 5px;
        margin: 12px 4px 0 0;
        background-color: #bfbfbf;
    }
    .legendRightClass {
        float: left;
        height: 5px;
        width: 5px;
        margin: 12px 4px 0 0;
        background-color: #e54c5e;
    } */
    .gnClass {
        float: left;
        height: 12px;
        background-color: #0000d0;
    }

    .hwClass {
        float: right;
        height: 12px;
        background-color: #5ad000;
    }

    h1,
    h2 {
        font-weight: normal;
    }

    .board-s {
        height: 98%;
        margin: 2px;
    }

    .board-s3 {
        height: 98%;
        margin: 2px;
    }

    .box-card {
        height: 100%;
        padding: 15px 0 0 8px;
        font-size: 12px;
        color: white;
        background: #4874cb;
        border: 2px solid white;
    }

    ul {
        padding: 0;
        list-style-type: none;
    }

    li {
        display: inline-block;
        margin: 0 10px;
    }

    a {
        color: #42b983;
    }

    .board-s2 {
        height: 95%;
        margin: 5px;
        border: 1px solid cornflowerblue;
    }

    .header-s {
        height: 10%;
        padding: 2px;
        margin: 2px;
        font-size: 16px;
        background-color: #d9edf7;
    }
</style>

<style lang="less">
.crm_radio.el-radio-group {
    .el-radio{
        .el-radio__label {
            height: 18px;
            padding-left: 5px;
            font-family: 'Microsoft YaHei', '微软雅黑';
            font-size: 14px;
            font-weight: 400;
            line-height: 17px;
            color: white;
        }
        &.is-checked {

            .el-radio__label {
                height: 18px;
                font-family: 'Microsoft YaHei', '微软雅黑';
                font-size: 14px;
                font-weight: 400;
                line-height: 17px;
                color: white;
            }
        }
    }
}
</style>
