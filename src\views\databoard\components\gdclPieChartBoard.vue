﻿<!-- eslint-disable no-undef -->
<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="board-s">
        <div :id="vId" style="width: 100%; height: 100%"></div>
    </div>
</template>

<script>
    import * as echarts from 'echarts'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'GdclPieChartBoard',
        props: {
            // eslint-disable-next-line vue/require-default-prop
            vId: {
                type: String
            },
            title: {
                type: String,
                default: 'title'
            },
            // eslint-disable-next-line vue/require-default-prop
            gdclPieData: {
                type: Object,
                default: () => {
                    return {
                        id: '',
                        title: '',
                        intCircleData: [],
                        outCircleData: []
                    }
                }
            },
            // eslint-disable-next-line vue/require-default-prop
            dataItem: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            intCircleData: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            outCircleData: {
                type: Array
            }
        },
        data() {
            return {}
        },
        watch: {
            intCircleData: {
                handler(newVal) {
                    if (newVal) {
                        this.renderPieChart()
                    }
                },
                immediate: true,
                deep: true
            }
        },
        mounted() {
            this.renderPieChart()
        },
        methods: {
            renderPieChart() {
                const option = {
                    title: {
                        text: this.title,
                        left: 'left',
                        textStyle: {
                            fontSize: 13,
                            color: '#ba4949'
                        }
                    },
                    //下载图片
                    toolbox: {
                        show: true,
                        y: 'center',
                        x: 'right',
                        feature: {
                            saveAsImage: {
                                pixelRatio: 15
                            } //值越大分辨率越高
                        }
                    },
                    tooltip: {
                        trigger: 'item',
                        showContent: true,
                        textStyle: {
                            fontSize: 14
                        }
                    },
                    legend: {
                        orient: 'vertical',
                        y: 'center',
                        x: '57%',
                        itemWidth: 8,
                        itemHeight: 8,
                        itemGap: 8,
                        selected: false,
                        textStyle: {
                            fontSize: 9
                        }
                    },
                    series: [
                        //外圈
                        {
                            name: '高端存量(单位:万)',
                            type: 'pie',
                            radius: ['60%', '75%'],
                            center: ['30%', '55%'],
                            avoidLabelOverlap: false,
                            color: ['#ffeede', '#e5e7eb'],
                            label: {
                                normal: {
                                    show: true,
                                    position: 'inner', //center
                                    formatter: function (param) {
                                        return param.name + ':\n' + Math.round(param.percent) + '%'
                                    },
                                    textStyle: {
                                        // color: '#fff',
                                        fontWeight: 'bold',
                                        fontSize: 7
                                    }
                                },
                                emphasis: {
                                    show: true,
                                    textStyle: {
                                        fontSize: '7',
                                        fontWeight: 'bold'
                                    }
                                }
                            },
                            labelLine: {
                                normal: {
                                    smooth: true,
                                    lineStyle: {
                                        width: 2
                                    }
                                }
                            },
                            itemStyle: {
                                normal: {
                                    borderRadius: 4,
                                    borderWidth: 1,
                                    borderColor: 'rgba(255, 255, 255, 1 )'
                                }
                            },
                            data: this.outCircleData
                        },
                        //内圈
                        {
                            name: '高端存量(单位:万)',
                            type: 'pie',
                            radius: ['40%', '60%'],
                            center: ['30%', '55%'],
                            color: [
                                '#da9f9f',
                                '#a065a8',
                                '#ea7f25',
                                '#bb5469',
                                '#cf8080',
                                '#a1afcf'
                            ],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderColor: '#fff',
                                borderRadius: 8,
                                borderWidth: 1
                            },
                            label: {
                                normal: {
                                    show: true,
                                    position: 'inner', //center
                                    formatter: '{d}%', //{d}
                                    textStyle: {
                                        // color: '#fff',
                                        fontWeight: 'bold',
                                        fontSize: 7
                                    }
                                },
                                emphasis: {
                                    show: true,
                                    textStyle: {
                                        fontSize: '10',
                                        fontWeight: 'bold'
                                    }
                                }
                            },
                            labelLine: {
                                normal: {
                                    show: false
                                }
                            },
                            // data: this.intCircleData
                            data: this.intCircleData
                        }
                    ]
                }
                this.$nextTick(() => {
                    if (document.getElementById(this.vId)) {
                        const pieChart = echarts.init(document.getElementById(this.vId))
                        console.log('thisoption' + JSON.stringify(option))
                        pieChart.setOption(option)
                    }
                })
            }
        }
    })
</script>

<style scoped>
    .board-s {
        height: 95%;
        margin: 2px;
    }
</style>
