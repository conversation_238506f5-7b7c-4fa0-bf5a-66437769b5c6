interface ResultWithColor {
    result: string
    resultChangeColor: boolean
}

export interface FollowStatisticRecord {
    u1Name: string // 中心
    u2Name: string // 区域
    u3Name: string // 分公司
    allocationConsName: string // 分配投顾姓名
    custType: string // 客户类型
    consCustNo: string // 投顾客户号
    custName: string // 客户姓名
    allocationDt: string // 分配日期
    allocationMarket: string // 分配日存量
    allocationCustState: string // 分配日客户状态
    followStage: string // 跟进阶段
    followFlag: string // 继续跟进
    followCount: string // 跟进次数
    startDt: string // 开始时间
    endDt: string // 结束时间
    phoneVisit: {
        date: string
        result: string
        resultChangeColor: boolean
    }
    wechatBind: {
        date: string
        result: string
        resultChangeColor: boolean
    }
    wechatComm: {
        advisorCount: string
        customerCount: string
        result: string
        resultChangeColor: boolean
    }
    faceVisit: {
        date: string
        accompany: string
        result: string
        resultChangeColor: boolean
    }
    offlineActivity: {
        count: string
        result: string
        resultChangeColor: boolean
    }
    ips: {
        date: string
        result: string
        resultChangeColor: boolean
    }
    trade: {
        rmbAmt: string
        usAmt: string
        netIncrease: string
        redemptionAmount: string
        ipsHealthResult: string
        result: string
        resultChangeColor: boolean
    }
    forecastResult: string // 预计结果
    feedbackIsSatisfied: string // 客服回访-是否满意
    finalResult: string // 最终结果
    remark: string // 备注
}

export interface QueryFollowStatisticResponse {
    rows: FollowStatisticRecord[]
    total: number
    curPage: string
    size: string
}

export interface ExportFollowStatisticResponse {
    data: {
        fileByte: string
        name: string
    }
}

export interface FollowStatisticInitResponse {
    lastUpdateTime: string // 最后更新时间
    dataDeadline: string // 统计截止时间
}
