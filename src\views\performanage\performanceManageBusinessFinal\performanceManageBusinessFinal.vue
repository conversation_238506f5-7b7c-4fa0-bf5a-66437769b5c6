<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 管理人员 -->
                <label-item label="管理人员" class-name="text-left">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <!-- 考核周期 -->
                <label-item :label="periodExplain.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.periodExplain"
                        :placeholder="periodExplain.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="periodExplain.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 预计考核结果 -->
                <label-item :label="exaimneResult.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.exaimneResult"
                        :placeholder="exaimneResult.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="exaimneResult.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 非观察分公司 -->
                <label-item :label="qualifiedDepartment.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.qualifiedDepartment"
                        :placeholder="qualifiedDepartment.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="qualifiedDepartment.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 合格分公司 -->
                <label-item :label="competentDepartment.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.competentDepartment"
                        :placeholder="competentDepartment.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="competentDepartment.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 非观察分公司-预计 -->
                <label-item :label="qualifiedDepartmentNew.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.qualifiedDepartmentNew"
                        :placeholder="qualifiedDepartmentNew.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="qualifiedDepartmentNew.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 合格分公司-预计 -->
                <label-item :label="competentDepartmentNew.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.competentDepartmentNew"
                        :placeholder="competentDepartmentNew.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="competentDepartmentNew.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 考核节点 -->
                <label-item :label="exaimneNode.label" class-name="text-left">
                    <date-range
                        v-model="queryForm.exaimneNode"
                        show-format="YYYY-MM-DD"
                        :placeholder="exaimneNode.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 是否TP -->
                <label-item :label="istp.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.istp"
                        :placeholder="istp.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="istp.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 最终考核结果 -->
                <label-item :label="exaimneEndresult.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.exaimneEndresult"
                        :placeholder="exaimneEndresult.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="exaimneEndresult.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 投顾code -->
                <label-item :label="userId.label" class-name="text-left">
                    <crm-input
                        v-model="queryForm.userId"
                        :placeholder="userId.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 截止时间(计算日期) -->
                <label-item label="截止时间" class-name="text-left">
                    <el-date-picker
                        v-model="queryForm.calcMonth"
                        type="month"
                        size="small"
                        format="YYYYMM"
                        value-format="YYYYMM"
                        :disabled="calcMonthDisabled"
                        @change="handleCalcMonth"
                    ></el-date-picker>
                </label-item>
            </template>
            <template #operationBtns>
                <crm-button size="small" :radius="true" :icon="Download" plain @click="exportHandle"
                    >导出</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    :pops="{ selectFixed: 'left' }"
                    @selectionChange="handleSelectionChange"
                >
                </base-table>
            </template>
        </table-wrapper>
    </div>
</template>

<script lang="ts" setup>
    import { Plus, Download, InfoFilled } from '@element-plus/icons-vue'
    import { fetchRes, messageBox } from '@/utils'
    import { dataList } from './data/labelData'
    import { ElLoading, ElMessage } from 'element-plus'

    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import { performanceManageBusinessTableColumn, showTableColumn } from './data/tableData'
    import {
        performanceManageBusinessFinalQuery,
        performanceManageBusinessFinalExport
    } from '@/api/project/performanage/performanceManageBusinessFinal/performanceManageBusinessFinal'
    import { MANAGE_SUB_TOTAL_FINAL_OPER_PERMISSION } from '@/constant/performanceConst'
    import { getMenuPermission } from '@/api/project/common/common'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import LabelItem from '@/components/moduleBase/LabelItem.vue'

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const {
        userId,
        periodExplain,
        exaimneResult,
        qualifiedDepartment,
        competentDepartment,
        qualifiedDepartmentNew,
        competentDepartmentNew,
        exaimneNode,
        istp,
        exaimneEndresult
    } = dataList

    const handleRowClick = (row: any) => {
        console.log(row)
    }
    const listLoading = ref<boolean>(false)
    const consStatus = ref<string>('0') //包含离职人员

    // 是否禁用截止时间 默认禁用，有权限的时候启用
    const calcMonthDisabled = ref<boolean>(false)
    //批量修改选中的id
    let checkIds = [] as string[]
    let consNames = [] as string[]

    const module = ref<string>('B140705')
    const handleCalcMonth = (val: string) => {
        queryForm.calcMonth = val
    }

    /**
     * @description: 关闭弹框刷新数据
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleClose = (): void => {
        queryList()
    }

    const handleSelectionChange = (sel: any): void => {
        checkIds = sel.map((item: any) => item.id)
        consNames = sel.map((item: any) => item.consName)
        console.log(checkIds)
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        userId = ''
        periodExplain = ''
        competentDepartment = ''
        qualifiedDepartment = ''
        competentDepartmentNew = ''
        qualifiedDepartmentNew = ''
        istp = ''
        exaimneResult = ''
        exaimneEndresult = ''
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        exaimneNode = {
            startDate: '',
            endDate: ''
        }
        calcMonth = ''
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            performanceManageBusinessTableColumn.map(item => item.key),
            performanceManageBusinessTableColumn
        )
    })
    //查詢
    const queryList = async () => {
        listLoading.value = true
        if (!queryForm.calcMonth) {
            ElMessage.error('请选择查询时间')
            listLoading.value = false
            return
        }
        const params = {
            flag: '0',
            userId: queryForm.userId,
            periodExplain: queryForm.periodExplain,
            competentDepartment: queryForm.competentDepartment,
            qualifiedDepartment: queryForm.qualifiedDepartment,
            competentDepartmentNew: queryForm.competentDepartmentNew,
            qualifiedDepartmentNew: queryForm.qualifiedDepartmentNew,
            istp: queryForm.istp,
            exaimneResult: queryForm.exaimneResult,
            exaimneEndresult: queryForm.exaimneEndresult,
            exaimneNodeStart: queryForm.exaimneNode.startDate,
            exaimneNodeEnd: queryForm.exaimneNode.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            calcMonth: queryForm.calcMonth
        }
        const allLoading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)'
        })
        fetchRes(performanceManageBusinessFinalQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { list } = resObj
                tableData.value = list
                allLoading.close()
            },
            errorCB: () => {
                listLoading.value = false
                allLoading.close()
            },
            catchCB: () => {
                listLoading.value = false
                allLoading.close()
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        if (!queryForm.calcMonth) {
            ElMessage.error('请选择查询时间')
            return
        }
        const params = {
            flag: '0',
            userId: queryForm.userId,
            periodExplain: queryForm.periodExplain,
            competentDepartment: queryForm.competentDepartment,
            qualifiedDepartment: queryForm.qualifiedDepartment,
            competentDepartmentNew: queryForm.competentDepartmentNew,
            qualifiedDepartmentNew: queryForm.qualifiedDepartmentNew,
            istp: queryForm.istp,
            exaimneResult: queryForm.exaimneResult,
            exaimneEndresult: queryForm.exaimneEndresult,
            exaimneNodeStart: queryForm.exaimneNode.startDate,
            exaimneNodeEnd: queryForm.exaimneNode.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            calcMonth: queryForm.calcMonth
        }
        const allLoading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)'
        })
        const res: any = await performanceManageBusinessFinalExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
        allLoading.close()
    }

    const initData = async () => {
        queryForm.calcMonth = getLastMonth()
        const params = {
            menuCode: module.value
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode ===
                                MANAGE_SUB_TOTAL_FINAL_OPER_PERMISSION.CALC_MONTH &&
                            item.display === '1'
                        ) {
                            calcMonthDisabled.value = false
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    const getLastMonth = () => {
        // 获取当前时间
        let now = new Date()
        // 获取当前月份
        let month = now.getMonth()
        // 获取当前年份
        let year = now.getFullYear()

        // 计算上个月的月份和年份
        if (month === 0) {
            month = 11
            year -= 1
        } else {
            month -= 1
        }

        // 使用计算出的年月创建新的Date对象，为了避免日期越界问题，这里直接使用1号
        now = new Date(year, month, 1)

        // 格式化日期为 yyyyMM 格式
        return (
            now.getFullYear().toString().padStart(4, '0') +
            (now.getMonth() + 1).toString().padStart(2, '0')
        )
    }

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [orgCodeDefault, consCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.orgvalue.orgCode = orgCodeDefault.value
                queryForm.orgvalue.consCode = consCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        console.log('onMounted')
        initData()
        fetchConsOrgList('', module.value)
    })
</script>
<style lang="less" scoped></style>
