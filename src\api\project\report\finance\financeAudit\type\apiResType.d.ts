/*
 * @Description: reportEditTop 返回参数类型
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-03-25 23:23:39
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:39:29
 * @FilePath: /crm-template/src/api/project/reportList/type/apiResType.d.ts
 *
 */
export {}
declare module './apiResType' {
    interface ResFinceVO {
        /**
         * 主键id
         */
        id: String
        /**
         * 产品代码
         */
        fundCode: String
        /**
         * 产品名称
         */
        fundName: String
        /**
         * 管理人
         */
        manager: String
        /**
         * 天数
         */
        holdDays: number
        /**
         * 持仓份额
         */
        balanceVol: number
        /**
         * 结算开始日期
         */
        startDt: String
        /**
         * 结算结束日期
         */
        endDt: String
        /**
         * 费用类型
         */
        feeType: String
        /**
         * 结算费用
         */
        feeTax: number
        /**
         * 去税结算费用
         */
        fee: number
        /**
         * 协议主体
         */
        ageSubject: String
    }

    export { ResFinceVO }
}
