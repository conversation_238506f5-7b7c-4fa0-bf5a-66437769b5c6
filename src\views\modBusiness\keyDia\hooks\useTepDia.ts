/*
 * @Description: 设置模版弹框方法
 * @Author: chaohui.wu
 * @Date: 2024-10-12 15:24:28
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-14 10:24:07
 * @FilePath: /ds-report-web/src/views/modBusiness/keyDia/hooks/useTepDia.ts
 *  
 */
// utils函数
import {
    getAllTemplate,
    addTemplate,
    modifyTemplate,
    deleteTemplate,
    setColumnTemplate,
    queryModelDetail
} from '../api'
import { getString, flatten, message, messageBox } from '@/utils'
// 自定义hooks方法
export function useTepDia({ 
    props, 
    emit, 
    flatTableColumns, 
    rankListHasNoChildren, 
    tree, 
    resetChecked, 
    filterText,
    handleClose
 }: any) {
    
    const modelId = ref<number | string | any>(null)
    const modelVisible = ref(false)
    const modelTitle = ref('编辑模板')
    const localAllModels = ref<Array<any>>([])
    const activeModelItem = ref<any>({})
        // Methods
    /**
     * @description: 获取模版
     * @param {*} type
     * @return {*}
     */
    const queryModelList = (type?: string) => {
        if (!props.supportModel) {
            return
        }
        // 获取模版接口
        getAllTemplate({ moduleName: props.modelType }).then((res:any) => {
            localAllModels.value = res?.body
            if (type === 'delete') {
                modelId.value = localAllModels.value[0].templateId
                searchModelDetail()
            }
        })
    }

    /**
     * @description: 重置模版
     * @return {*}
     */
    const searchModelDetail = () => {
        queryModelDetail({
            moduleName: props.modelType,
            templateId: modelId.value
        }).then((res:any) => {
            // 结构不同需要调整
            const resColumns = res.body.column || []
            const flatTableColumnsValue = flatTableColumns.value || []
            const rankListArr: Array<any> = []
            const disabledKeys: Array<any> = []
            const keyList1: Array<any> = []
            const keyList2: Array<any> = []

            const coumnMap = flatTableColumnsValue.reduce((acc:any, cur:any) => {
                acc.set(cur.key, cur)
                return acc
            }, new Map())

            resColumns.forEach((key:any) => {
                const item = coumnMap.get(key)
                if (item) {
                    rankListArr.push(item)
                    keyList1.push(key)
                }
            })

            const disabledList = flatTableColumnsValue.filter(
                (item:any) => item?.disabled && !resColumns.includes(item.key)
            )
            disabledList.forEach((item:any) => {
                disabledKeys.push(item)
                keyList2.push(item.key)
            })

            rankListHasNoChildren.value = [...rankListArr, ...disabledKeys]

            if(tree.value) {
                tree.value?.setCheckedKeys([...keyList1, ...keyList2])
            }
        })
    }

    /**
     * @description: 模版提交
     * @return {*}
     */
    const submitModelEdit = () => {
        if (activeModelItem.value.templateId) {
            modifyTemplate({
                moduleName: props.modelType,
                ...activeModelItem.value
            }).then(() => {
                message({
                    message: '修改模板名称成功',
                    type: 'success'
                })
                queryModelList()
            })
            modelVisible.value = false
        } else {
            addTemplate({
                moduleName: props.modelType,
                templateName: activeModelItem.value.templateName
            }).then((res:any) => {
                message({
                    message: '新增模板成功',
                    type: 'success'
                })
                activeModelItem.value.templateId = res.body
                modelId.value = res.body
                localAllModels.value.push(activeModelItem.value)
                resetChecked()
                queryModelList()
            })
            modelVisible.value = false
        }
    }

    /**
     * @description: 删除模版
     * @return {*}
     */
    const deleteModel = () => {
        messageBox(
            {
                content: `<div>
                    <p>您是否确认要删除${activeModelItem.value.templateName}吗?</p>
                    <p>删除后将不可恢复</p>
                </div>`,
                confirmBtn: '确定',
                cancelBtn: '取消',
            },
            () => {
                // successCB
                deleteTemplate({
                    templateId: activeModelItem.value.templateId
                }).then(() => {
                    message({
                        message: '删除模板成功',
                        type: 'success'
                    })
                    queryModelList('delete')
                })
                modelVisible.value = false
            },
            () => {
                message({
                    message: '已取消删除',
                    type: 'info'
                })
            }
        )
    }

    /**
     * @description: 选择操作的模版
     * @param {*} type
     * @param {*} item
     * @return {*}
     */
    const checkOption = (type: string, item: any = {}) => {
        modelTitle.value = type === 'edit' ? '编辑模板' : '新增模板'
        activeModelItem.value = { ...item }
        modelVisible.value = true
    }

    /**
     * @description: 选择模版
     * @param {*} item
     * @return {*}
     */
    const changeModelId = (item: any) => {
        filterText.value = ''
        if (item === '新增模板') {
            checkOption('new')
        } else {
            searchModelDetail()
        }
    }

    /**
     * @description: 应用模版
     * @return {*}
     */
    const promiseResetTableColumns = () => {
        if (rankListHasNoChildren.value?.length === 0) {
            return new Promise((_, reject) => {
                message({
                    message: '至少选择一个指标',
                    type: 'error',
                    duration: 1000
                })
                reject('')
            })
        }
        let columns = ''
        rankListHasNoChildren.value.forEach((item:any) => {
            columns = columns ? `${columns},${item.key}` : item.key
        })
        
        return new Promise((resolve, reject) => {
            emit('update:modelValue', modelId.value)
            emit('change', columns.split(','))
            message({
                message: '应用模版成功',
                type: 'success'
            })
            handleClose()
            resolve('')
        })
        // 应用接口本次不对接
        // return setColumnTemplate({
        //     moduleName: props.modelType,
        //     templateId: modelId.value,
        //     columns
        // }).then(() => {
        //     emit('update:modelValue', modelId.value)
        //     emit('change', columns.split(','))
        //     handleClose()
        // })
    }

    /**
     * @description: 模版取消或关闭
     * @return {*}
     */
    const cancelAdd = () => {
        modelVisible.value = false
    }
    // 导出方法
    return {
        localAllModels,
        modelId,
        activeModelItem,
        modelVisible, 
        modelTitle,
        queryModelList,
        searchModelDetail,
        submitModelEdit,
        deleteModel,
        checkOption,
        changeModelId,
        promiseResetTableColumns,
        cancelAdd
    }
}
        