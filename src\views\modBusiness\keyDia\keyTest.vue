<!--
 * @Description: 组件使用案例
 * @Author: chao<PERSON>.wu
 * @Date: 2024-09-27 10:48:16
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-12 17:34:25
 * @FilePath: /ds-report-web/src/views/modBusiness/keyTest.vue
 *  待完善，目前还没搞定
-->
<!--
* 设置表格显示字段
* @props title: 弹框标题
* @props dialogVisible: 显示或隐藏
-->
<template>
    <ModuleKeyDia
        v-model="dialogId" 
        v-model:visible="showDialog"
        model-type="bsConfMsg"
        title="指标设置"
        :table-columns="[
            {
                label: '基本信息',
                children: [
                    {
                        key: 'fundName',
                        label: '基金简称',
                        disabled: true
                    },
                    {
                        key: 'fundId',
                        label: '基金ID'
                    }
                ]
            },
            {
                key: 'detail',
                label: '业务描述'
            },
            {
                key: 'newsTemplateId',
                label: '消息模板id'
            },
            {
                key: 'infoTemplateId',
                label: '模板消息标识'
            }
        ]"
        :check-list="['fundName']"
        :support-model="false"
        @change="handleChange"
        >
    </ModuleKeyDia>
</template>

<script setup lang="ts">
    import ModuleKeyDia from './moduleKeyDia.vue'
    const dialogId = ref('1111')
    const showDialog = ref(true)

    /**
     * @description: 关键字事件回调
     * @param {*} val
     * @return {*}
     */
    const handleChange = (val: any) => {
        console.log(val)
    }
</script>
<style lang="less" scoped></style>

