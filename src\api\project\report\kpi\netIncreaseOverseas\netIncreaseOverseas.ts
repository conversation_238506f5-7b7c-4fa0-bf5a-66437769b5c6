import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { NetIncreaseOverseasParam } from './type/apiReqType.js'

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const NetIncreaseOverseasQuery = (params: NetIncreaseOverseasParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreaseoverseas/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const NetIncreaseOverseasExport = (params: NetIncreaseOverseasParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreaseoverseas/export',
            method: 'post',
            data: params
        })
    )
}


/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const NetIncreaseOverseasDetailQuery = (params: NetIncreaseOverseasParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreaseoverseas/querydetail',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const NetIncreaseOverseasDetailExport = (params: NetIncreaseOverseasParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreaseoverseas/exportdetail',
            method: 'post',
            data: params
        })
    )
}


/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const NetIncreaseOverseasInit = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreaseoverseas/initdata',
            method: 'post',
            data: null
        })
    )
}
