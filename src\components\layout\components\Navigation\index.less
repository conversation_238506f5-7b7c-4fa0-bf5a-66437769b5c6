.crm-web-app {
    .navigationBar {
        box-sizing: border-box;
        flex: 1;
        height: 100%;
        padding: 0 20px;
        overflow: hidden;
        user-select: none;

        .menuBar {
            height: 100%;
            background-color: transparent;
            border-bottom: none;

            .el-menu-item,
            .el-sub-menu .el-sub-menu__title {
                box-sizing: border-box;
                height: @header_bar_height;
                padding: 0 6px;
                padding-top: 2px;
                line-height: @header_bar_height;
                color: #ffffff;
                border-bottom: 2px solid transparent;
                border-left: none;

                &.is-active {
                    color: #ffffff !important;
                    background-color: rgba(255, 255, 255, 0.1) !important;
                    border-bottom-color: #606ecc !important;
                }

                &:hover {
                    color: #ffffff !important;
                    background-color: rgba(255, 255, 255, 0.1) !important;
                    border-bottom: 2px solid #606ecc !important;
                    border-left: none !important;
                }

                i {
                    margin-right: 2px;
                    color: inherit;
                }

                .el-sub-menu__icon-arrow {
                    margin-left: 4px;
                }
            }

            .el-sub-menu {
                &.is-active {
                    .el-sub-menu__title {
                        color: #ffffff;
                        background-color: rgba(255, 255, 255, 0.1);
                        border-bottom-color: #606ecc;
                    }
                }
            }
        }
    }

    .el-menu--horizontal {
        color: #ffffff;

        i {
            color: inherit;
        }

        .el-menu {
            background-color: #3b3e5b;

            .el-menu-item {
                height: 38px;
                padding: 0;
                line-height: 38px;
                color: inherit;
                background-color: #3b3e5b;
                border-left: 2px solid transparent;

                &.is-active {
                    color: inherit;
                    background-color: rgba(255, 255, 255, 0.1);
                    border-left-color: #606ecc;
                }

                &:hover {
                    color: inherit;
                    background-color: rgba(255, 255, 255, 0.1);
                    border-left-color: #606ecc;
                }
            }
        }

        .el-sub-menu {
            color: #ffffff;

            &:hover {
                .el-sub-menu__title {
                    color: inherit;
                    background-color: rgba(255, 255, 255, 0.1);
                    border-left-color: #606ecc;
                }
            }

            .el-sub-menu__title {
                height: 38px;
                padding-left: 8px;
                line-height: 38px;
                color: inherit;
                background-color: #3b3e5b;
                border-left: 2px solid transparent;

                &.is-active {
                    color: inherit;
                    background-color: rgba(255, 255, 255, 0.1);
                    border-left-color: #606ecc;
                }
            }

            &.is-active {
                .el-sub-menu__title {
                    color: #ffffff !important;
                    background-color: rgba(255, 255, 255, 0.1);

                    &.is-active {
                        color: inherit;
                        background-color: rgba(255, 255, 255, 0.1);
                        border-left-color: #606ecc;
                    }
                }

                &:hover {
                    .el-sub-menu__title {
                        color: inherit;
                        background-color: rgba(255, 255, 255, 0.1);
                        border-left-color: #606ecc;
                    }
                }
            }
        }

        .navMenuItem {
            display: block;
            width: 100%;
            height: 100%;
            padding: 0 10px 0 8px;
            color: #ffffff;
            user-select: none;

            &:hover,
            &:active,
            &:visited {
                color: #ffffff;
            }
        }

        .badge-item {
            // height: 38px;
            // line-height: 38px;
            vertical-align: top;

            .el-badge__content {
                top: 10px;
                right: 2px;
                background-color: @theme_main;
                border: none;
            }
        }
    }
}
