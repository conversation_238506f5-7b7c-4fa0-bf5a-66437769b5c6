<template>
    <transition name="el-zoom-in-top">
        <div v-show="visible" class="more-menu-list">
            <div v-for="item in hideMenuList" :key="item.id" class="mm-list">
                <div class="mm-title">{{ item.title }}</div>
                <div class="mm-item">
                    <div v-for="subItem in item.children" :key="subItem.id" class="mm-sub-title">
                        <span
                            class="text-overflow"
                            :title="subItem.title"
                            @click="handleToPage(subItem)"
                        >
                            {{ subItem.title }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</template>

<script>
    import { defineComponent } from 'vue'
    import { openUrl } from '@/utils/openUrl'
    export default defineComponent({
        name: 'MoreMenu',
        props: {
            menuList: {
                type: Array,
                default: () => []
            },
            visible: {
                type: Boolean,
                default: false
            }
        },
        emits: ['update:visible'],
        data() {
            return {
                hideMenuList: []
            }
        },
        watch: {
            menuList: {
                handler(list) {
                    if (list && list.length > 0) {
                        this.hideMenuList = list
                    }
                },
                deep: true,
                immediate: true
            }
        },
        methods: {
            // 点击导航跳转到对应的路由
            handleToPage(item) {
                if (item.openNewPageType === '1') {
                    // 新开tag标签展示ams页面
                    // 用name解析出path
                    const { href } = this.$router.resolve({ name: item.name })
                    openUrl({ type: 1, path: href.replace('#/', '/') })
                    return false
                } else if (item.openNewPageType === '2') {
                    // iframe打开外链
                    this.$router.push({
                        name: item.name
                    })
                    return false
                } else if (item.openNewPageType === '3') {
                    // 直接跳转到外链
                    openUrl({ type: 3, path: item.link })
                    return false
                }

                this.openRouterName(item)
                return false
            },

            // 路由方式跳转
            openRouterName(item) {
                let params = {}
                // 动态路由参数 (形式：a=1,b=2）
                if (item.dynamicParams) {
                    try {
                        item.dynamicParams.split(',').forEach(v => {
                            const [key, value] = v.split('=')
                            params[key] = value
                        })
                    } catch (e) {
                        params = {}
                    }
                }

                this.$router.push({
                    name: item.name,
                    params
                })
            }
        }
    })
</script>
<style lang="less" scoped>
    .more-menu-list {
        position: fixed;
        top: 52px;
        right: 200px;
        left: 200px;
        z-index: 9999;
        padding: 0 20px;
        color: @font_color_05;
        white-space: normal;
        background-color: @font_color_01;
        border-radius: 3px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);

        .mm-list {
            // overflow: hidden;
            display: flex;
            width: 100%;
            padding: 15px 0;
            border-bottom: 1px solid #f3f3f3;

            &:last-child {
                border-bottom: none;
            }

            .mm-title {
                position: relative;
                flex: 0 0 100px;
                height: 24px;
                padding-left: 14px;
                font-size: 14px;
                font-weight: 600;
                line-height: 24px;

                &::before {
                    position: absolute;
                    top: 10px;
                    left: 0;
                    width: 4px;
                    height: 4px;
                    content: '';
                    background-color: @theme_main;
                }
            }

            .mm-item {
                flex: 1;
                height: auto;
                line-height: 24px;
            }

            .mm-sub-title {
                display: inline-block;
                width: 120px;
                height: 24px;
                margin: 0 15px;
                font-size: 13px;
                font-weight: normal;
                line-height: 24px;
                color: #3b3f5b;
                cursor: pointer;

                span {
                    display: inline-block;
                    width: 100%;

                    &:hover {
                        color: @theme_main;
                        text-decoration: underline;
                    }
                }

                :deep(.badge-item) {
                    height: 26px;
                    line-height: 26px;
                    vertical-align: top;

                    .el-badge__content {
                        top: 2px;
                        right: 3px;
                        background-color: @theme_main;
                    }
                }
            }
        }
    }
</style>
