/*
 * @Description: reportEditTop 返回参数类型
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-25 23:23:39
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:39:29
 * @FilePath: /crm-template/src/api/project/reportList/type/apiResType.d.ts
 *
 */
export {}
declare module './apiResType' {
    interface ResVO {
        taskId: string // 任务id
        fundCode: string // 基金code
        fundName: string // 基金名称
        company: string // 公司
        subRate: number // 认购费率
        manageRate: number // 管理费率
        manageFormula: string // 管理费公式
        manageRemark: string // 管理费备注
        performanceRate: Number // 业绩报酬费率
        performanceShareRate: Number // 业绩报酬分成费率
        redemRate: string // 赎回费率
        performanceDate: string // 业绩报酬提取日
        redemDate: string //赎回日 1-是2-否
        settleDate: string //清算日 1-是2-否
        shareDate: string //分红日 1-是2-否
        performanceFormula: string //业绩报酬公式
        performanceRemark: string //业绩报酬备注
        redemFormula: string // 赎回费公式
        redemRemark: string // 赎回费备注
        productManage: string // 产品经理
        signDate: string // 签约日期
        signSubject: string // 好买签约主体
        oppositeContact: string // 对方联系人
    }

    export { ResVO }
}
