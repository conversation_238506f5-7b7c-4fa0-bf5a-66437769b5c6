export const followTableColumn = [
    {
        label: '中心',
        key: 'u1Name',
        minWidth: 100
    },
    {
        label: '区域',
        key: 'u2Name',
        minWidth: 90
    },
    {
        label: '分公司',
        key: 'u3Name',
        minWidth: 100
    },
    {
        label: '分配投顾姓名',
        key: 'allocationConsName',
        minWidth: 95
    },
    {
        label: '客户类型',
        key: 'custType',
        minWidth: 90
    },
    {
        label: '投顾客户号',
        key: 'consCustNo',
        minWidth: 100
    },
    {
        label: '客户姓名',
        key: 'custName',
        minWidth: 90
    },
    {
        label: '分配日期',
        key: 'allocationDt',
        minWidth: 90
    },
    {
        label: '分配日存量',
        key: 'allocationMarket',
        minWidth: 100
    },
    {
        label: '分配日客户状态',
        key: 'allocationCustState',
        minWidth: 105
    },
    {
        label: '跟进阶段',
        key: 'followStage',
        minWidth: 85
    },
    {
        label: '继续跟进',
        key: 'followFlag',
        minWidth: 70
    },
    {
        label: '跟进次数',
        key: 'followCount',
        minWidth: 70
    },
    {
        label: '开始时间',
        key: 'startDt',
        minWidth: 90
    },
    {
        label: '结束时间',
        key: 'endDt',
        minWidth: 90
    },
    {
        label: '电话拜访',
        key: 'phoneVisit',
        minWidth: 100,
        type: 'multi',
        align: 'center',
        subColumns: [
            {
                label: '日期',
                key: 'phoneVisit.date',
                minWidth: 90
            },
            {
                label: '结果',
                key: 'phoneVisit.result',
                minWidth: 60
            }
        ]
    },
    {
        label: '企微绑定',
        key: 'wechatBind',
        minWidth: 120,
        type: 'multi',
        align: 'center',
        subColumns: [
            {
                label: '日期',
                key: 'wechatBind.date',
                minWidth: 90
            },
            {
                label: '结果',
                key: 'wechatBind.result',
                minWidth: 60
            }
        ]
    },
    {
        label: '企微沟通',
        key: 'wechatComm',
        minWidth: 120,
        type: 'multi',
        align: 'center',
        subColumns: [
            {
                label: '投顾次数',
                key: 'wechatComm.advisorCount',
                minWidth: 80
            },
            {
                label: '客户次数',
                key: 'wechatComm.customerCount',
                minWidth: 80
            },
            {
                label: '结果',
                key: 'wechatComm.result',
                minWidth: 60
            }
        ]
    },
    {
        label: '面访',
        key: 'faceVisit',
        minWidth: 120,
        type: 'multi',
        align: 'center',
        subColumns: [
            {
                label: '日期',
                key: 'faceVisit.date',
                minWidth: 90
            },
            {
                label: '陪访人',
                key: 'faceVisit.accompany',
                minWidth: 100
            },
            {
                label: '结果',
                key: 'faceVisit.result',
                minWidth: 60
            }
        ]
    },
    {
        label: '线下活动',
        key: 'offlineActivity',
        minWidth: 120,
        type: 'multi',
        align: 'center',
        subColumns: [
            {
                label: '参会次数',
                key: 'offlineActivity.count',
                minWidth: 80
            },
            {
                label: '结果',
                key: 'offlineActivity.result',
                minWidth: 60
            }
        ]
    },
    {
        label: 'IPS',
        key: 'ips',
        minWidth: 120,
        type: 'multi',
        align: 'center',
        subColumns: [
            {
                label: '日期',
                key: 'ips.date',
                minWidth: 90
            },
            {
                label: '结果',
                key: 'ips.result',
                minWidth: 60
            }
        ]
    },
    {
        label: '成交',
        key: 'trade',
        minWidth: 120,
        type: 'multi',
        align: 'center',
        subColumns: [
            {
                label: '成交人民币',
                key: 'trade.rmbAmt',
                minWidth: 80
            },
            {
                label: '成交美元',
                key: 'trade.usAmt',
                minWidth: 80
            },
            {
                label: '净新增',
                key: 'trade.netIncrease',
                minWidth: 80
            },
            {
                label: '赎回量',
                key: 'trade.redemptionAmount',
                minWidth: 80
            },
            {
                label: 'IPS健康度',
                key: 'trade.ipsHealthResult',
                minWidth: 80
            },
            {
                label: '结果',
                key: 'trade.result',
                minWidth: 60
            }
        ]
    },
    {
        label: '预计结果',
        key: 'forecastResult',
        minWidth: 100
    },
    {
        label: '客服回访-是否满意',
        key: 'feedbackIsSatisfied',
        minWidth: 125
    },
    {
        label: '最终结果',
        key: 'finalResult',
        minWidth: 100
    },
    {
        label: '备注',
        key: 'remark',
        minWidth: 120
    }
]
