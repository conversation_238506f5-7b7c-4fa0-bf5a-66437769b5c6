<!--
 * @Description: readme
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-29 12:50:53
 * @FilePath: /dtms-product-web/README.md
 *
-->

# Vue 3 + TypeScript + Vite + ElPlus【pnpm ｜ yarn | npm】

## 常用命令

-   包管理工具使用 pnpm 安装依赖前确保安装过 pnpm \*
-   `pnpm i`：安装依赖
-   `pnpm run dev`：本地开发
-   `pnpm run build`：生产环境打包
-   `pnpm run preview`：在线预览｜需先打包
-   `pnpm run lint`：格式化
-   `pnpm run prettier`：格式化

-   包管理工具使用 yarn 安装依赖前确保安装过 yarn \*
-   `yarn`：安装依赖
-   `yarn dev`：本地开发
-   `yarn build`：生产环境打包
-   `yarn preview`：在线预览｜需先打包
-   `yarn lint`：格式化
-   `yarn prettier`：格式化
-   `yarn analyzer`：可视化分析打包内容

## 项目结构说明

```text
├── build                             # 编译和打包的相关配置
├── husky                             # git提交校验
├── public                            # 公共配置及宙斯参数
├── federation                        # federation联邦配置 待扩展
├── src                               # 程序源文件
│   ├── api                           # 接口定义
│   ├── assets                        # 静态资源（img,css,font,icon）
│   ├── components                    # 可复用的直观组件(UI Components)
│   ├── constant                      # 全局常量
│   ├── containers                    # 页面容器
│   ├── page                          # 页面通用组件
│   ├── routers                       # 路由配置
│   ├── stores                        # pina数据状态管理
│   ├── type                          # ts类型定义
│   ├── utils                         # 公共方法
│   ├── App.vue                       # 入口组件
│   ├── vite-env.d.ts                 # vite自定义模块
│   └── main.ts                       # 入口js
├── package.json                      # node.js依赖配置
├── tsconfig.json                     # ts配置
├── commitlint.config.js              # git 提交校验关键字配置
├── tsconfig.node.json                # node ts配置
├── vite.config.ts                    # vite基础配置
├── sync-nacos.js                     # 宙斯配置项
├── eslint-auto-import.json           # eslint自动导入组件设置 可删除
├── auto-imports.d.ts                 # 自动导入组件设置 可删除
├── components.d.ts                   # 自动注册组件设置 可删除
├── vite.config.ts                    # vite基础配置
├── README.md                         # 项目说明文档
└── yarn.lock                         # 项目依赖的具体版本信息锁定文件 提交时需删除
```

This template should help get you started developing with Vue 3 and TypeScript in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

## Recommended IDE Setup

-   [VS Code](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur) + [TypeScript Vue Plugin (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.vscode-typescript-vue-plugin).

## Type Support For `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [TypeScript Vue Plugin (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.vscode-typescript-vue-plugin) to make the TypeScript language service aware of `.vue` types.

If the standalone TypeScript plugin doesn't feel fast enough to you, Volar has also implemented a [Take Over Mode](https://github.com/johnsoncodehk/volar/discussions/471#discussioncomment-1361669) that is more performant. You can enable it by the following steps:

1. Disable the built-in TypeScript Extension
    1. Run `Extensions: Show Built-in Extensions` from VSCode's command palette
    2. Find `TypeScript and JavaScript Language Features`, right click and select `Disable (Workspace)`
2. Reload the VSCode window by running `Developer: Reload Window` from the command palette.
