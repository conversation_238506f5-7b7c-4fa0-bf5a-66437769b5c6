/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const assetGdHkTableColumn: TableColumnItem[] = [
    {
        key: 'mon',
        label: '交易月份',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'dt',
        label: '存量日期',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'fundCode',
        label: '基金代码',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'fundName',
        label: '基金名称',
        width: 300,
        formatter: formatTableValue
    },
    {
        key: 'holdCust',
        label: '存量客户数',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'balanceVol',
        label: '存量份额',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'marketCap',
        label: '存量市值',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
