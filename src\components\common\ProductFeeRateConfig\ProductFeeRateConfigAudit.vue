<!--
 * @Description: 产品费率配置通用审核弹框组件
 * @Author: hongdong.xie
 * @Date: 2025-06-06 09:42:20
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 09:42:20
 * @FilePath: /ds-report-web/src/components/common/ProductFeeRateConfig/ProductFeeRateConfigAudit.vue
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1200px"
        height="600px"
        :border="true"
        :title="title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div class="fee-rate-audit-form-container">
            <FormTabs
                v-model="activeTab"
                :tabs-list="formTabs"
                class="form-tabs-container"
            >
                <!-- 基础属性 -->
                <template #basic>
                    <el-form
                        ref="basicFormRef"
                        :model="formData"
                        label-width="140px"
                        class="form-section"
                    >
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="备案代码">
                                    <crm-input
                                        v-model="formData.filingCode"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="产品code（好买内部）">
                                    <crm-input
                                        v-model="formData.productCode"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="份额代码">
                                    <crm-input
                                        v-model="formData.shareCode"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="产品全称">
                                    <crm-input
                                        v-model="formData.productFullName"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="仅限FOF">
                                    <crm-select
                                        v-model="formData.fofOnlyFlag"
                                        :option-list="selectOptions.fofOnlyFlag"
                                        label-format="label"
                                        value-format="key"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="付款方全称">
                                    <crm-input
                                        v-model="formData.payerFullName"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="产品经理">
                                    <crm-input
                                        v-model="formData.productManager"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="签约日期">
                                    <crm-date-picker
                                        v-model="formData.signingDate"
                                        show-format="YYYY-MM-DD"
                                        value-format="YYYYMMDD"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="好买签约主体">
                                    <crm-input
                                        v-model="formData.howbuySigningEntity"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="对方联系人">
                                    <crm-input
                                        v-model="formData.counterpartContact"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="存续D系数">
                                    <el-input-number
                                        v-model="formData.durationDCoefficient"
                                        :precision="6"
                                        :disabled="true"
                                        style="width: 100%"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="存续D备注">
                                    <crm-input
                                        v-model="formData.durationDRemark"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="费率开始日期">
                                    <crm-date-picker
                                        v-model="formData.rateStartDate"
                                        show-format="YYYY-MM-DD"
                                        value-format="YYYYMMDD"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="费率结束日期">
                                    <crm-date-picker
                                        v-model="formData.rateEndDate"
                                        show-format="YYYY-MM-DD"
                                        value-format="YYYYMMDD"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>

                <!-- 阶梯费率 -->
                <template #tiered>
                    <el-form
                        ref="tieredFormRef"
                        :model="formData"
                        label-width="140px"
                        class="form-section"
                    >
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="费率配置类型">
                                    <crm-select
                                        v-model="formData.tieredRateType"
                                        :option-list="selectOptions.tieredRateType"
                                        label-format="label"
                                        value-format="key"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="费率配置生效日">
                                    <crm-select
                                        v-model="formData.rateEffectiveType"
                                        :option-list="selectOptions.rateEffectiveType"
                                        label-format="label"
                                        value-format="key"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row v-if="showConfigLimits" :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="配置下限">
                                    <el-input-number
                                        v-model="formData.configLowerLimit"
                                        :disabled="true"
                                        style="width: 100%"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="配置上限">
                                    <el-input-number
                                        v-model="formData.configUpperLimit"
                                        :disabled="true"
                                        style="width: 100%"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row v-if="showRateEffectiveDates" :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="费率生效日期">
                                    <crm-date-picker
                                        v-model="formData.rateEffectiveStartDate"
                                        show-format="YYYY-MM-DD"
                                        value-format="YYYYMMDD"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="费率结束日期">
                                    <crm-date-picker
                                        v-model="formData.rateEffectiveEndDate"
                                        show-format="YYYY-MM-DD"
                                        value-format="YYYYMMDD"
                                        :disabled="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>

                <!-- 交易管理费率 -->
                <template #tradingManagement>
                    <el-form
                        ref="tradingManagementFormRef"
                        :model="formData"
                        label-width="140px"
                        class="form-section"
                    >
                        <!-- 认申购费率区域 -->
                        <div class="rate-section">
                            <div class="rate-section-title">认申购费率</div>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="认申购费率">
                                        <el-input-number
                                            v-model="formData.subscriptionRate"
                                            :precision="6"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="认申购费备注">
                                        <crm-input
                                            v-model="formData.subscriptionRemark"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>

                        <!-- 管理费率区域 -->
                        <div class="rate-section">
                            <div class="rate-section-title">管理费率</div>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="管理费率">
                                        <el-input-number
                                            v-model="formData.managementRate"
                                            :precision="6"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="管理费公式">
                                        <crm-input
                                            v-model="formData.managementFormula"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="管理费备注">
                                        <crm-input
                                            v-model="formData.managementRemark"
                                            type="textarea"
                                            :rows="3"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>

                        <!-- 赎回费率区域 -->
                        <div class="rate-section">
                            <div class="rate-section-title">赎回费率</div>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="赎回费率1">
                                        <el-input-number
                                            v-model="formData.redemptionRate1"
                                            :precision="6"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="赎回费持有天数1">
                                        <el-input-number
                                            v-model="formData.redemptionHoldingDays1"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="赎回费率2">
                                        <el-input-number
                                            v-model="formData.redemptionRate2"
                                            :precision="6"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="赎回费持有天数2">
                                        <el-input-number
                                            v-model="formData.redemptionHoldingDays2"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="赎回费率3">
                                        <el-input-number
                                            v-model="formData.redemptionRate3"
                                            :precision="6"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="赎回费持有天数3">
                                        <el-input-number
                                            v-model="formData.redemptionHoldingDays3"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="赎回费率4">
                                        <el-input-number
                                            v-model="formData.redemptionRate4"
                                            :precision="6"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="赎回费持有天数4">
                                        <el-input-number
                                            v-model="formData.redemptionHoldingDays4"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="赎回费公式">
                                        <crm-input
                                            v-model="formData.redemptionFormula"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="赎回费持有天数计算规则">
                                        <crm-select
                                            v-model="formData.redemptionHoldingCalcRule"
                                            :option-list="selectOptions.redemptionHoldingCalcRule"
                                            label-format="label"
                                            value-format="key"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="赎回费特例">
                                        <crm-select
                                            v-model="formData.redemptionSpecialRule"
                                            :option-list="selectOptions.redemptionSpecialRule"
                                            label-format="label"
                                            value-format="key"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="赎回费备注">
                                        <crm-input
                                            v-model="formData.redemptionRemark"
                                            type="textarea"
                                            :rows="3"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-form>
                </template>

                <!-- 业绩报酬 -->
                <template #performance>
                    <el-form
                        ref="performanceFormRef"
                        :model="formData"
                        label-width="140px"
                        class="form-section"
                    >
                        <!-- 业绩报酬分成费率区域 -->
                        <div class="rate-section">
                            <div class="rate-section-title">业绩报酬分成费率</div>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="业绩报酬分成费率1">
                                        <el-input-number
                                            v-model="formData.performanceSharingRate1"
                                            :precision="6"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="业绩报酬费率1">
                                        <el-input-number
                                            v-model="formData.performanceRate1"
                                            :precision="6"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="业绩报酬计提基准1">
                                        <crm-input
                                            v-model="formData.performanceBenchmark1"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <!-- 业绩报酬费率2 -->
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="业绩报酬分成费率2">
                                        <el-input-number
                                            v-model="formData.performanceSharingRate2"
                                            :precision="6"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="业绩报酬费率2">
                                        <el-input-number
                                            v-model="formData.performanceRate2"
                                            :precision="6"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="业绩报酬计提基准2">
                                        <crm-input
                                            v-model="formData.performanceBenchmark2"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <!-- 业绩报酬费率3 -->
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="业绩报酬分成费率3">
                                        <el-input-number
                                            v-model="formData.performanceSharingRate3"
                                            :precision="6"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="业绩报酬费率3">
                                        <el-input-number
                                            v-model="formData.performanceRate3"
                                            :precision="6"
                                            :disabled="true"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="业绩报酬计提基准3">
                                        <crm-input
                                            v-model="formData.performanceBenchmark3"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <!-- 业绩报酬配置 -->
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="业绩报酬计提类型">
                                        <crm-select
                                            v-model="formData.performanceAccrualType"
                                            :option-list="selectOptions.performanceAccrualType"
                                            label-format="label"
                                            value-format="key"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="业绩报酬计提形式">
                                        <crm-select
                                            v-model="formData.performanceAccrualForm"
                                            :option-list="selectOptions.performanceAccrualForm"
                                            label-format="label"
                                            value-format="key"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="业绩报酬备注">
                                        <crm-input
                                            v-model="formData.performanceRemark"
                                            type="textarea"
                                            :rows="3"
                                            :disabled="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-form>
                </template>

                <!-- 审核操作 -->
                <template #audit>
                    <el-form
                        ref="auditFormRef"
                        :model="auditForm"
                        :rules="auditRules"
                        label-width="140px"
                        class="form-section"
                    >
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item label="审核备注" prop="auditRemark">
                                    <crm-input
                                        v-model="auditForm.auditRemark"
                                        type="textarea"
                                        placeholder="请输入审核备注"
                                        :rows="4"
                                        :clearable="true"
                                        maxlength="500"
                                        show-word-limit
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>
            </FormTabs>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <crm-button @click="handleCancel">取消</crm-button>
                <crm-button
                    type="danger"
                    :loading="submitLoading"
                    @click="handleReject"
                >
                    审核不通过
                </crm-button>
                <crm-button
                    type="primary"
                    :loading="submitLoading"
                    @click="handleApprove"
                >
                    审核通过
                </crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { computed, reactive, ref, watch } from 'vue'
    import { ElMessage } from 'element-plus'
    import type { FormRules } from 'element-plus'
    import type { ProductFeeRateConfig, BaseAuditParam } from './types'
    import { useProductFeeRateForm } from './composables/useProductFeeRateForm'
    import { useProductFeeRateApi } from './composables/useProductFeeRateApi'

    // 组件props定义
    interface Props {
        visible?: boolean
        config: ProductFeeRateConfig
        auditData?: any
    }

    const props = withDefaults(defineProps<Props>(), {
        visible: false,
        auditData: null
    })

    // 组件emits定义
    const emit = defineEmits<{
        'update:visible': [value: boolean]
        'success': []
    }>()

    // 使用表单逻辑hooks（只读模式）
    const {
        // 响应式数据
        activeTab,
        basicFormRef,
        tieredFormRef,
        tradingManagementFormRef,
        performanceFormRef,
        formData,
        selectOptions,

        // 计算属性
        showConfigLimits,
        showRateEffectiveDates,

        // 方法
        resetForm,
        fillFormData
    } = useProductFeeRateForm(props.config.rateType)

    // 使用API操作hooks
    const { submitLoading, auditData: auditApiData } = useProductFeeRateApi(props.config)

    // 审核表单引用
    const auditFormRef = ref()

    // 审核表单数据
    const auditForm = reactive({
        auditRemark: ''
    })

    // 审核表单校验规则
    const auditRules: FormRules = {
        auditRemark: [
            { required: true, message: '请输入审核备注', trigger: 'blur' },
            { min: 1, max: 500, message: '审核备注长度在1到500个字符', trigger: 'blur' }
        ]
    }

    // 表单标签页配置（包含审核标签页）
    const formTabs = [
        { label: '基础属性', name: 'basic', slotName: 'basic' },
        { label: '阶梯费率', name: 'tiered', slotName: 'tiered' },
        { label: '交易管理费率', name: 'tradingManagement', slotName: 'tradingManagement' },
        { label: '业绩报酬费率', name: 'performance', slotName: 'performance' },
        { label: '审核操作', name: 'audit', slotName: 'audit' }
    ]

    // 弹框显示状态
    const dialogVisible = computed({
        get: () => props.visible,
        set: (value: boolean) => {
            emit('update:visible', value)
        }
    })

    // 弹框标题
    const title = computed(() => {
        return props.config.pageConfig.dialogTitle.audit
    })

    // 监听审核数据变化
    watch(
        () => props.auditData,
        (newData) => {
            console.log('📝 审核数据监听调试信息:')
            console.log('传入数据 newData:', newData)

            if (newData && props.visible) {
                fillFormData(newData, 'audit')
            }
        },
        { immediate: true, deep: true }
    )

    // 监听弹框显示状态
    watch(
        () => props.visible,
        (newVisible) => {
            if (newVisible) {
                if (props.auditData) {
                    fillFormData(props.auditData, 'audit')
                }
                // 重置审核表单
                auditForm.auditRemark = ''
                auditFormRef.value?.clearValidate()
                // 默认切换到审核操作标签页
                activeTab.value = 'audit'
            } else {
                // 弹框关闭时重置表单
                resetForm()
                auditForm.auditRemark = ''
            }
        }
    )

    /**
     * @description: 取消操作
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @return {void}
     */
    const handleCancel = (): void => {
        dialogVisible.value = false
    }

    /**
     * @description: 审核通过
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @return {Promise<void>}
     */
    const handleApprove = async (): Promise<void> => {
        await handleAudit('2')
    }

    /**
     * @description: 审核不通过
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @return {Promise<void>}
     */
    const handleReject = async (): Promise<void> => {
        await handleAudit('3')
    }

    /**
     * @description: 执行审核操作
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {string} auditStatus 审核状态 2-通过 3-不通过
     * @return {Promise<void>}
     */
    const handleAudit = async (auditStatus: string): Promise<void> => {
        console.log('🔍 开始审核操作...')
        console.log('审核状态:', auditStatus)
        console.log('审核数据:', props.auditData)

        // 验证审核表单
        try {
            await auditFormRef.value?.validate()
        } catch (error) {
            console.log('❌ 审核表单验证失败')
            return
        }

        if (!props.auditData?.id) {
            ElMessage({
                type: 'error',
                message: '审核数据异常，请重试'
            })
            return
        }

        // 构建审核参数
        const auditParam: BaseAuditParam = {
            id: props.auditData.id,
            auditStatus: auditStatus,
            rateType: props.config.rateType,
            auditRemark: auditForm.auditRemark
        }

        console.log('📤 审核参数:', auditParam)

        // 调用审核API
        const success = await auditApiData(auditParam)
        if (success) {
            console.log('✅ 审核成功')
            dialogVisible.value = false
            emit('success')
        }
    }
</script>

<style lang="less" scoped>
.fee-rate-audit-form-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .form-tabs-container {
        flex: 1;
        overflow: hidden;
    }

    .form-section {
        padding: 20px;
        height: 100%;
        overflow-y: auto;
    }

    .rate-section {
        margin-bottom: 30px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 20px;

        .rate-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e4e7ed;
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid #e4e7ed;
}

// 禁用状态样式
:deep(.el-input.is-disabled .el-input__inner) {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
}

:deep(.el-select.is-disabled .el-input__inner) {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
}

:deep(.el-input-number.is-disabled .el-input__inner) {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
}

:deep(.el-textarea.is-disabled .el-textarea__inner) {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
}

// 表单项样式调整
:deep(.el-form-item) {
    margin-bottom: 20px;
}

:deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
}
</style>
