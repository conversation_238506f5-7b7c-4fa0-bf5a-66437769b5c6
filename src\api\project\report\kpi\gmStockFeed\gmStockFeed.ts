import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { gmStockFeedQueryParam } from './type/apiReqType.js'

/**
 * @description:查询接口
 * @return {*}
 */
export const gmStockFeedQuery = (params: gmStockFeedQueryParam): any => {
    return axiosRequest(
        paramsMerge({
            timeout:40000,
            url: '/api/report/jobassessment/gmstockfeed/gmstockfeedquery',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 导出接口
 * @return {*}
 */
export const gmStockFeedExport = (params: gmStockFeedQueryParam) => {
    return axiosRequest(
        paramsMerge({
            timeout:60000,
            url: '/api/report/jobassessment/gmstockfeed/gmstockfeedexport',
            method: 'post',
            data: params
        })
    )
}
