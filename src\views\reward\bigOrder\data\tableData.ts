import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

export const bigOrderTableColumn: TableColumnItem[] = [
    { key: 'preId', label: '预约ID', width: 100, formatter: formatTableValue },
    { key: 'tradeDt', label: '交易日期', width: 100, formatter: formatTableValue },
    { key: 'settlementDt', label: '预计结算日期', width: 100, formatter: formatTableValue },
    { key: 'custName', label: '客户名称', width: 100, formatter: formatTableValue },
    { key: 'consCustNo', label: '投顾客户号', width: 100, formatter: formatTableValue },
    { key: 'consCode', label: '投顾code', width: 100, formatter: formatTableValue },
    { key: 'consName', label: '所属投顾', width: 100, formatter: formatTableValue },
    { key: 'u1Name', label: '所属中心', width: 100, formatter: formatTableValue },
    { key: 'u2Name', label: '所属区域', width: 100, formatter: formatTableValue },
    { key: 'u3Name', label: '所属部门', width: 100, formatter: formatTableValue },
    { key: 'fundCode', label: '产品代码', width: 100, formatter: formatTableValue },
    { key: 'fundName', label: '产品名称', width: 100, formatter: formatTableValue },
    { key: 'accountProductType', label: '核算产品类型', width: 100, formatter: formatTableValue },
    { key: 'bigOrderProductType', label: '大单产品类型', width: 100, formatter: formatTableValue },
    { key: 'stockFeeD', label: '存续D系数', width: 100, formatter: formatTableValue },
    { key: 'availVol', label: '剩余份额', width: 100, formatter: formatTableValue },
    { key: 'nav', label: '确认净值', width: 100, formatter: formatTableValue },
    { key: 'availCost', label: '剩余本金', width: 100, formatter: formatTableValue },
    { key: 'commissionRate', label: '佣金率(%)', width: 100, formatter: formatTableValue },
    { key: 'settlementStatus', label: '结算状态', width: 100, formatter: formatTableValue },
    { key: 'achieveCoeff', label: '业绩系数', width: 100, formatter: formatTableValue },
    { key: 'manageCoeff', label: '管理系数', width: 100, formatter: formatTableValue },
    {
        key: 'manageCoeffCommission',
        label: '管理奖金基数',
        width: 100,
        formatter: formatTableValue
    },
    { key: 'modor', label: '修改人', width: 100, formatter: formatTableValue },
    {
        key: 'modBigOrderProductTypeFlag',
        label: '大单产品类型修改标识',
        visible: true
    },
    {
        key: 'modAvailVolFlag',
        label: '剩余份额修改标识',
        visible: true
    },
    {
        key: 'modAvailCostFlag',
        label: '剩余本金修改标识',
        visible: true
    },
    {
        key: 'modCommissionRateFlag',
        label: '佣金率修改标识',
        visible: true
    },
    {
        key: 'modAchieveCoeffFlag',
        label: '业绩系数修改标识',
        visible: true
    },
    {
        key: 'modManageCoeffCommissionFlag',
        label: '管理奖金基数修改标识',
        visible: true
    }
]

export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.visible) {
            return false
        }
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
