const NacosConfigClient = require('nacos').NacosConfigClient
const fs = require('fs')
const path = require('path')

const app_name = 'ds-report-web'
const br_name = 'web4.0.0'

const config_file_path = './public/msEnv.js'

const configClient = new NacosConfigClient({
    serverAddr: 'nacos1.inner.howbuy.com:8848',
    namespace: 'dev'
})

configClient.getConfig(app_name + '.properties', br_name).then(res => {
    const res_list = res.split('\n')
    const new_list = []
    for (const row in res_list) {
        if (!res_list[row].startsWith('#') && res_list[row] !== '') {
            const row_list = res_list[row].split('=')
            row_list.slice(1)
            new_list.push('var ' + row_list[0] + '=' + '"' + row_list.slice(1).join('=') + '"')
        }
    }
    const file = path.resolve(__dirname, config_file_path)

    fs.writeFileSync(file, new_list.join('\n'), { encoding: 'utf8' })
    process.exit()
})
