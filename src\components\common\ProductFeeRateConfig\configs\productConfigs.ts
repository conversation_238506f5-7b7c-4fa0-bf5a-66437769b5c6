/*
 * @Description: 产品费率配置各产品类型配置
 * @Author: hongdong.xie
 * @Date: 2025-06-06 09:42:20
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 09:42:20
 * @FilePath: /ds-report-web/src/components/common/ProductFeeRateConfig/configs/productConfigs.ts
 */

import type { ProductFeeRateConfig } from '../types'

/**
 * @description: FOF产品费率配置
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 */
export const fofProductConfig: ProductFeeRateConfig = {
    productType: 'fof',
    rateType: '3',
    apiConfig: {
        queryUrl: '/feerateconfig/fof/query',
        addUrl: '/feerateconfig/fof/add',
        updateUrl: '/feerateconfig/fof/update',
        deleteUrl: '/feerateconfig/fof/delete',
        auditUrl: '/feerateconfig/fof/audit',
        exportUrl: '/feerateconfig/fof/export',
        authUrl: '/feerateconfig/fof/auth'
    },
    pageConfig: {
        title: 'FOF产品费率配置',
        dialogTitle: {
            add: '新增FOF产品费率配置',
            edit: '修改FOF产品费率配置',
            copyAdd: '复制新增FOF产品费率配置',
            audit: '审核FOF产品费率配置'
        },
        exportFileName: 'FOF产品费率配置导出文件.xlsx'
    }
}

/**
 * @description: 固收产品费率配置
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 */
export const fixedIncomeProductConfig: ProductFeeRateConfig = {
    productType: 'fixedIncome',
    rateType: '1',
    apiConfig: {
        queryUrl: '/feerateconfig/fixedincome/query',
        addUrl: '/feerateconfig/fixedincome/add',
        updateUrl: '/feerateconfig/fixedincome/update',
        deleteUrl: '/feerateconfig/fixedincome/delete',
        auditUrl: '/feerateconfig/fixedincome/audit',
        exportUrl: '/feerateconfig/fixedincome/export',
        authUrl: '/feerateconfig/fixedincome/auth'
    },
    pageConfig: {
        title: '固收产品费率配置',
        dialogTitle: {
            add: '新增固收产品费率配置',
            edit: '修改固收产品费率配置',
            copyAdd: '复制新增固收产品费率配置',
            audit: '审核固收产品费率配置'
        },
        exportFileName: '固收产品费率配置导出文件.xlsx'
    }
}

/**
 * @description: 二级产品费率配置
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 */
export const secondaryProductConfig: ProductFeeRateConfig = {
    productType: 'secondary',
    rateType: '2',
    apiConfig: {
        queryUrl: '/feerateconfig/secondary/query',
        addUrl: '/feerateconfig/secondary/add',
        updateUrl: '/feerateconfig/secondary/update',
        deleteUrl: '/feerateconfig/secondary/delete',
        auditUrl: '/feerateconfig/secondary/audit',
        exportUrl: '/feerateconfig/secondary/export',
        authUrl: '/feerateconfig/secondary/auth'
    },
    pageConfig: {
        title: '二级产品费率配置',
        dialogTitle: {
            add: '新增二级产品费率配置',
            edit: '修改二级产品费率配置',
            copyAdd: '复制新增二级产品费率配置',
            audit: '审核二级产品费率配置'
        },
        exportFileName: '二级产品费率配置导出文件.xlsx'
    }
}

/**
 * @description: 海外人民币产品费率配置
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 */
export const overseasRmbProductConfig: ProductFeeRateConfig = {
    productType: 'overseasRmb',
    rateType: '4',
    apiConfig: {
        queryUrl: '/feerateconfig/overseasrmb/query',
        addUrl: '/feerateconfig/overseasrmb/add',
        updateUrl: '/feerateconfig/overseasrmb/update',
        deleteUrl: '/feerateconfig/overseasrmb/delete',
        auditUrl: '/feerateconfig/overseasrmb/audit',
        exportUrl: '/feerateconfig/overseasrmb/export',
        authUrl: '/feerateconfig/overseasrmb/auth'
    },
    pageConfig: {
        title: '海外人民币产品费率配置',
        dialogTitle: {
            add: '新增海外人民币产品费率配置',
            edit: '修改海外人民币产品费率配置',
            copyAdd: '复制新增海外人民币产品费率配置',
            audit: '审核海外人民币产品费率配置'
        },
        exportFileName: '海外人民币产品费率配置导出文件.xlsx'
    }
}

/**
 * @description: 根据产品类型获取配置
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {string} productType 产品类型
 * @return {ProductFeeRateConfig} 配置对象
 */
export const getProductConfig = (productType: string): ProductFeeRateConfig => {
    switch (productType) {
        case 'fof':
            return fofProductConfig
        case 'fixedIncome':
            return fixedIncomeProductConfig
        case 'secondary':
            return secondaryProductConfig
        case 'overseasRmb':
            return overseasRmbProductConfig
        default:
            throw new Error(`未知的产品类型: ${productType}`)
    }
}

/**
 * @description: 获取所有产品配置
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @return {ProductFeeRateConfig[]} 所有配置数组
 */
export const getAllProductConfigs = (): ProductFeeRateConfig[] => {
    return [
        fofProductConfig,
        fixedIncomeProductConfig,
        secondaryProductConfig,
        overseasRmbProductConfig
    ]
}
