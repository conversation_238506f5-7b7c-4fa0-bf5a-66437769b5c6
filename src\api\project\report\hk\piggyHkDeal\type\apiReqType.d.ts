export {}
declare module './apiReqType' {
    type queryPiggyHkDealParam = {
        /**
         * dealNo
         */
        dealNo?: string
        /**
         * 香港客户号
         */
        hkCustNo?: string
        /**
         * 手机号码
         */
        mobile?: string

        /**
         * 证件号码
         */
        idNo?: string

        /**
         * ebrokerID
         */
        ebrokerId?: string

        /**
         * 一账通号
         */
        hboneNo?: string

        /**
         * 投顾客户号
         */
        conscustno?: string

        /**
         * 审核状态
         */
        checkState?: string
        /**
         * 业务类型
         */
        busiName?: string

        /**
         * 客户类型
         */
        invstType?: string

        /**
         * 投资者类型（香港）
         */
        investorQualification?: string

        /**
         * 签署/终止时间
         */
        signCancelTimeStart?: string
        /**
         * 签署/终止时间
         */
        signCancelTimeEnd?: string

        /**
         * 签署/终止方式
         */
        signCancelType?: string

        /**
         * 签署/终止渠道
         */
        channel?: string

        /**
         * 所属部门
         */
        orgCode?: string

        /**
         * 当前投顾代码
         */
        consCode?: string
    }

    type viewDetailParam = {
        /**
         * 香港客户号
         */
        dealNo: string
    }

    export { queryPiggyHkDealParam, viewDetailParam }
}
