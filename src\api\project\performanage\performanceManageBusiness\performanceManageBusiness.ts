import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import {
    performanceManageBusinessParam,
    performanceManageSubTotalBatchUpdateParam,
    performanceManageSubTotalSaveParam,
    performanceManageSubTotalCalSaveDSumParam,
    querySaveRecordParam
} from './type/apiReqType.js'

/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const performanceManageBusinessQuery = (params: performanceManageBusinessParam): any => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/manage/node/conf/subtotal/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 工作日接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const performanceManageBusinessCurrWorkDays = (): any => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/manage/node/conf/subtotal/currWorkDays',
            method: 'post'
        })
    )
}

/**
 * @description: 导出接口
 * @return {*}
 */
export const performanceManageBusinessExport = (params: performanceManageBusinessParam) => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/manage/node/conf/subtotal/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 批量修改
 * @return {*}
 */
export const performanceManageBusinessBatchUpdate = (
    params: performanceManageSubTotalBatchUpdateParam
) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/subtotal/batchupdate',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 批量修改初始化
 * @return {*}
 */
export const performanceManageBusinessBatchUpdateInitData = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/subtotal/batchupdateinitdata',
            method: 'post'
            //    data: params
        })
    )
}

/**
 * @description: 保存
 * @return {*}
 */
export const performanceManageBusinessSave = (params: performanceManageSubTotalSaveParam) => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/manage/node/conf/subtotal/save',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 操作记录查询
 * @return {*}
 */
export const performanceManageBusinessQuerySaveRecord = (params: querySaveRecordParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/subtotal/querysaverecord',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 分总列表页初始化数据
 * @return {*}
 */
export const performanceManageGetSubTotalListInitData = (): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/subtotal/subtotallistinitdata',
            method: 'post'
            //    data: params
        })
    )
}
/**
 * @description: 汇总并保存存续D 数据
 * @return {*}
 */
export const performanceManageCalSaveManageDSumData = (
    params: performanceManageSubTotalCalSaveDSumParam
): any => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/manage/node/conf/subtotal/calsavemanagedsumdata',
            method: 'post',
            data: params
        })
    )
}
