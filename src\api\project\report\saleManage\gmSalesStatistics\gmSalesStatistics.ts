import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { GmSalesStatisticsParam } from './type/apiReqType.js'

/**
 * @description: 将所有 二级分类 = [好买投顾]的产品名称展示出来，可以多选和选择全部，默认为全部
 * @return {*}
 */
export const FundListQuery = (): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/salemanage/gmsalesstatistics/getfundlist',
            method: 'get',
            data: null
        })
    )
}

/**
 * @description: 公募销量统计-查询统计数据
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const GmSalesStatisticsQuery = (params: GmSalesStatisticsParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/salemanage/gmsalesstatistics/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 公募销量统计-导出明细
 * @return {*}
 */
export const GmSalesStatisticsDetailExport = (params: GmSalesStatisticsParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/salemanage/gmsalesstatistics/exportdetails',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 公募销量统计-查询明细
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const GmSalesStatisticsDetailQuery = (params: GmSalesStatisticsParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/salemanage/gmsalesstatistics/querydetails',
            method: 'post',
            data: params
        })
    )
}
