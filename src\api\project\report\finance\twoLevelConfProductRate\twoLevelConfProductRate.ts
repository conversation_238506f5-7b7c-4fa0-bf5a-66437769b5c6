import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '../../../../mock.js'
import {
    TwoLevelConfProductRateParam,
    TwoLevelConfProductRateAddOrUpdateParam,
    BatchDeleteId,
    AduitProduct
} from './type/apiReqType.js'
/**
 * @description: 二级产品费率配置查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const twoLevelConfProductRateQuery = (params: TwoLevelConfProductRateParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/twoLevelConfProductRate/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 二级产品费率配置导出接口
 * @return {*}
 */
export const twoLevelConfProductRateExport = (params: TwoLevelConfProductRateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/twoLevelConfProductRate/export',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 二级产品费率配置新增接口
 * @return {*}
 */
export const twoLevelConfProductRateInsert = (params: TwoLevelConfProductRateAddOrUpdateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/twoLevelConfProductRate/insert',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 二级产品费率配置修改接口
 * @return {*}
 */
export const twoLevelConfProductRateUpdate = (params: TwoLevelConfProductRateAddOrUpdateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/twoLevelConfProductRate/update',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 二级产品费率配置删除接口
 * @return {*}
 */
export const twoLevelConfProductRateDelete = (params: BatchDeleteId) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/twoLevelConfProductRate/delete',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 二级产品费率配置复核接口
 * @return {*}
 */
export const twoLevelConfProductRateReview = (params: AduitProduct) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/twoLevelConfProductRate/review',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核-init
 * @param {object} params
 * @return {*}
 */
export const getAuth = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/twoLevelConfProductRate/getAuth',
            method: 'post'
        })
    )
}
