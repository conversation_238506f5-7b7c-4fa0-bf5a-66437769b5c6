<!-- table顶部操作按钮-导入 -->
<template>
    <crm-dialog
        v-model="chooseFileVisible"
        title="导入文件"
        :close-on-click-modal="true"
        :append-to-body="true"
        :before-close="onClose"
    >
        <div>
            <el-upload
                ref="uploadFiles"
                class="upload-demo"
                name="file"
                :action="actionUploadUrl"
                :multiple="false"
                :show-file-list="true"
                :on-success="handleSuccess"
                :on-error="handleError"
                :before-upload="beforeUpload"
                :with-credentials="true"
                :accept="'.pdf,.PDF'"
                :headers="{ Authorization: getLocalItem('hb_crm_token') }"
            >
                <crm-button size="small" type="primary" :loading="importExcelLoading"
                    >导入文件</crm-button
                >
            </el-upload>
            <div v-for="(item, index) in tipText" :key="index" style="margin-top: 10px">
                {{ item.name }} --
                <span :style="computedRedColor(item.desc)">{{ item.desc }}</span>
                <span>{{ item.type }}</span>
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <crm-button type="primary" plain size="small" @click="onClose">关 闭</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script>
    import { UploadFilled, Upload, ArrowDown } from '@element-plus/icons-vue'
    import { getLocalItem } from '@/utils/index.js'
    import { responseCode } from '@/constant/index'
    import { ElMessage, ElMessageBox } from 'element-plus'
    export default defineComponent({
        name: 'CrmInportFile',
        model: {
            prop: 'show',
            event: 'change'
        },
        props: {
            show: {
                type: Boolean,
                default: false
            },
            uploadUrl: {
                type: String,
                required: true
            },
            type: {
                type: String,
                default: ''
            },
            title: {
                type: String,
                default: '导入'
            },
            fileName: {
                type: String,
                default: ''
            },
            logInfo: {
                type: Object,
                default: null
            }
        },
        emits: ['update:modelValue', 'reloading', 'upload:fileName'],
        setup() {
            return {
                UploadFilled,
                Upload,
                ArrowDown
            }
        },
        data() {
            return {
                chooseFileVisible: false,
                tipText: [],
                importExcelLoading: false
            }
        },
        computed: {
            actionUploadUrl() {
                return window._msApiPrefix + this.uploadUrl
            },
            computedRedColor(item) {
                return function (item) {
                    if (item?.includes('错误')) {
                        return 'color: #c82d30;'
                    } else if (item?.includes('成功')) {
                        return 'color: #018800;'
                    }
                }
            }
        },
        watch: {
            show(newValue, oldValue) {
                this.chooseFileVisible = newValue
            }
        },
        methods: {
            getLocalItem,
            onClose() {
                this.$emit('update:modelValue', false)
                this.tipText = []
                this.$refs.uploadFiles.clearFiles()
            },
            beforeUpload(file) {
                this.tipText = []
                this.importExcelLoading = true
                if (file !== '') {
                    const isExcel = file.name.substring(
                        file.name.lastIndexOf('.'),
                        file.name.length
                    )
                    const isValid = isExcel === '.pdf' || isExcel === '.PDF'
                    if (!isValid) {
                        ElMessage({
                            type: 'error',
                            message: '上传文件只能是.pdf格式!'
                        })
                    }
                    return isValid
                }
            },
            handleSuccess(response, file, fileList) {
                if (response !== '' && file !== '') {
                    const { code } = response
                    switch (code) {
                        case responseCode.SUCCESS:
                        case responseCode.CRMSUCCESS:
                            this.tipText.push({
                                name: file.name,
                                type: file?.data || '导入成功~~~',
                                desc: response?.description || ''
                            })
                            break
                        case responseCode.SYS_ERROR:
                        case responseCode.CRMSYS_FILED:
                            this.tipText.push({
                                name: file.name,
                                type: response.data || '导入失败，请重试~~~',
                                desc: response?.description || ''
                            })
                            break
                        default:
                        case '0011':
                        case 'C010011':
                            this.tipText.push({
                                name: file.name,
                                type: response.data || '导入失败，请重试~~~',
                                desc: response?.description || ''
                            })
                            break
                    }
                    this.importExcelLoading = false
                    if (this.logInfo) {
                        const { businessKey, operationKey, detail, ...logParms } = this.logInfo
                        this.$OperationLogs.addLogFromButton(
                            businessKey,
                            operationKey,
                            detail,
                            logParms
                        )
                    }
                    console.log(response.data.fileName)
                    this.$refs.uploadFiles.clearFiles()
                    this.$emit('reloading')
                    this.$emit('upload:fileName', response.data.fileName, response.data.filePath)
                    // if (!this.type) this.$OperationLogs.addFromKey('lhjhc', 'import') // 导入机会池的时候才记录日志
                } else {
                    ElMessage({
                        type: 'error',
                        message: '上传文件失败~'
                    })
                    this.importExcelLoading = false
                }
            },
            handleError(response, file, fileList) {
                if (response !== '') {
                    ElMessage({
                        type: 'error',
                        message: '上传文件失败~'
                    })
                    this.importExcelLoading = false
                } else {
                    this.importExcelLoading = false
                }
            }
        }
    })
</script>
<style lang="less"></style>
