<!--
 * @Description: 半环图
 * @Author: chaohui.wu
 * @Date: 2023-04-03 16:04:41
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-19 11:22:35
 * @FilePath: /crm-asset-web/src/components/charts/SemiCircle.vue
 *  
-->

<template>
    <chart-wrapper :width="width" :height="height" :options="chartOptions" v-bind="$attrs" />
</template>
<script setup lang="ts">
    import { deepMergeObj } from './scripts/methods'
    import { chartColors } from './scripts/chartColors'
    import { formaAccountingNumber } from '@/utils/index'

    /**
     * @description: 传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            width?: string
            height?: string
            chartData?: any[]
        }>(),
        {
            width: '100%',
            height: '300px',
            chartData: () => {
                return [
                    { value: 1048, name: '好买内资产' },
                    { value: 735, name: '外部资产' }
                ]
            }
        }
    )

    /**
     * @description: 持仓汇总
     * @param {*} computed
     * @return {*}
     */
    const total = computed(() => {
        return props.chartData.reduce((pre, curItem) => (pre += Number(curItem.value)), 0)
    })

    /**
     * @description: 当前展示数据
     * @return {*}
     */
    const curData = computed(() => {
        return [
            ...props.chartData,
            {
                value: total.value,
                itemStyle: {
                    color: 'none',
                    decal: {
                        symbol: 'none'
                    }
                },
                label: {
                    show: false
                }
            }
        ]
    })

    /**
     * @description: 配置选项
     * @return {*}
     */
    const chartOptions = ref<any>({})
    const getChartOptions = () => {
        chartOptions.value = deepMergeObj(
            {},
            {
                color: chartColors.simiCircleColors,
                tooltip: {
                    show: false
                },
                grid: {
                    bottom: '10%',
                    z: 10
                },
                legend: {
                    orient: 'horizontal',
                    icon: 'circle',
                    itemWidth: 7,
                    bottom: 2,
                    itemGap: 22,
                    selectedMode: false,
                    textStyle: {
                        color: '#9497A7',
                        fontSize: 10,
                        fontFamily: 'Microsoft YaHei,微软雅黑'
                    }
                },
                graphic: [
                    {
                        type: 'text',
                        left: 'center',
                        bottom: 66,
                        style: {
                            text: `总资产(元)`,
                            textAlign: 'center',
                            fill: '#333',
                            fontSize: 14,
                            textLineHeight: 19,
                            fontFamily: 'Microsoft YaHei'
                        }
                    },
                    {
                        type: 'text',
                        left: 'center',
                        bottom: 34,
                        style: {
                            text: formaAccountingNumber({ num: total.value }),
                            textAlign: 'center',
                            fill: '#333',
                            fontSize: 21,
                            textLineHeight: 27,
                            // fontWeight: 'bold',
                            fontFamily: 'Microsoft YaHei'
                        }
                    }
                ],
                series: [
                    {
                        name: 'Access From',
                        type: 'pie',
                        radius: ['120%', '150%'],
                        center: ['50%', 105],
                        startAngle: 180,
                        emphasis: {
                            scale: false
                        },
                        label: {
                            show: true,
                            position: 'inside',
                            formatter(param: any) {
                                // correct the percentage
                                return param.percent * 2 + '%'
                            }
                        },
                        data: curData.value
                    }
                ]
            }
        )
        // }
        return chartOptions
    }

    /**
     * @description: 监听数据变化
     * @return {*}
     */
    watch(
        [() => props.chartData],
        (newVal, oldVal) => {
            if (newVal) {
                getChartOptions()
            } else {
                chartOptions.value = []
            }
        },
        {
            immediate: true,
            deep: true
        }
    )
</script>
<style lang="less">
    @import './styles/index';
</style>
