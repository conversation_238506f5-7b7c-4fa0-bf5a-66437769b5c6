export enum MANAGE_NODE_CONF_OPER_PERMISSION {
    EXPORT = '1',
    BATCH_MODIFY = '2',
    MODIFY = '3',
    ADJUST_STATUS = '4',
    SAVE = '5'
}

export enum MANAGE_SUB_TOTAL_OPER_PERMISSION {
    EXPORT = '1',
    BATCH_MODIFY = '2',
    CAL_D_SUM = '3',
    SAVE_RECORD = '4',
    SAVE = '5'
}

export enum MANAGE_SUB_TOTAL_FINAL_OPER_PERMISSION {
    CALC_MONTH = '1'
}

export enum MANAGE_REGION_TOTAL_OPER_PERMISSION {
    EXPORT = '1',
    BATCH_MODIFY = '2',
    SAVE_RECORD = '3',
    SAVE = '4'
}

export enum MANAGE_CONSULT_FORECAST_OPER_PERMISSION {
    EXPORT = '1',
    BATCH_MODIFY = '3',
    CAL_D_SUM = '5',
    SAVE_RECORD = '6',
    SAVE = '4'
}

export enum MANAGE_CONSULT_FINAL_OPER_PERMISSION {
    EXPORT = '1'
}
