import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { NetIncreaseCustParam } from './type/apiReqType.js'

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const NetIncreaseCustQuery = (params: NetIncreaseCustParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreasecust2025/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const NetIncreaseCustExport = (params: NetIncreaseCustParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreasecust2025/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const NetIncreaseCustInit = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreasecust2025/initData',
            method: 'post',
            data: null
        })
    )
}
