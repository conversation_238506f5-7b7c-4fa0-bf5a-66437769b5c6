/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const tradeGdHkTableColumn: TableColumnItem[] = [
    {
        key: 'mon',
        label: '交易月份',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundCode',
        label: '基金代码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundName',
        label: '基金名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'buyCust',
        label: '认申购客户数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'buyAckVol',
        label: '认申购量',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'buyAckAmtRmb',
        label: '认申购金额RMB',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'buyAckAmt',
        label: '认申购金额',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'buyPct',
        label: '认申购金额占比',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'redeemCust',
        label: '赎回客户数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'redeemAckVol',
        label: '赎回量',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'redeemAckAmtRmb',
        label: '赎回金额RMB',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'redeemAckAmt',
        label: '赎回金额',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'redeemPct',
        label: '赎回金额占比',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
