<!--
 * @Description: 审核产品弹框
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-24 21:07:35
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-27 19:14:16
 * @FilePath: /crm-template/src/views/components/dialogCommon/adjustAmt/adjustAmtIndex.vue
 * @ 当前未解耦，有时间解耦后可提取到dialogCommomn中
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        height="500px"
        :border="true"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <!-- 产品费率的弹框 -->
            <el-form
                v-if="isAssetHandConfAdd"
                ref="ruleFormRef"
                :model="formList"
                label-width="100px"
                :rules="rules"
                status-icon
            >
                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="busitype"
                            label="业务类型"
                            style="margin-left: 40%"
                            :class="{ 'custom-label': busitypeClass }"
                        >
                            <crm-select
                                v-model="formList.busitype"
                                :placeholder="busitype.placeholder"
                                filterable
                                clearable
                                disabled
                                label-format="label"
                                value-format="key"
                                :option-list="busitype.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="conslevel"
                            label="层级"
                            style="margin-left: 18%"
                            :class="{ 'custom-label': conslevelClass }"
                        >
                            <crm-select
                                v-model="formList.conslevel"
                                :placeholder="conslevel.placeholder"
                                filterable
                                clearable
                                disabled
                                label-format="label"
                                value-format="key"
                                :option-list="conslevel.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item
                            prop="formerConstObj"
                            label="管理层/投顾"
                            style="margin-top: 25px; margin-bottom: 50px; margin-left: 16.6%"
                            :class="{ 'custom-label': formerConstObjClass }"
                        >
                            <ReleatedSelect
                                v-model="formList.formerConstObj"
                                disabled
                                :organization-list="formerOrganizationList"
                                :cons-list-default="[]"
                                :default-org-code="formerOrgCode"
                                :default-cons-code="formerConsCode"
                            ></ReleatedSelect>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="custInfo"
                            label="客户"
                            style="margin-left: 40%"
                            :class="{ 'custom-label': custInfoClass }"
                        >
                            <crm-select
                                v-model="formList.custInfo"
                                label-format="custInfo"
                                value-format="custInfo"
                                filterable
                                clearable
                                disabled
                                remote
                                remote-show-suffix
                                :remote-method="getAllCustINFO"
                                :option-list="custList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item
                            prop="firstSource"
                            label="第一来源"
                            style="margin-left: 40%"
                            :class="{ 'custom-label': firstSourceClass }"
                        >
                            <crm-select
                                v-model="formList.firstSource"
                                :placeholder="firstSource.placeholder"
                                filterable
                                clearable
                                disabled
                                label-format="label"
                                value-format="key"
                                :option-list="firstSource.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item
                            prop="foldcoeff"
                            label="客户折算系数"
                            label-width="auto"
                            style="margin-left: 39%"
                            :class="{ 'custom-label': foldcoeffClass }"
                        >
                            <crm-input
                                v-model="formList.foldcoeff"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="managecoeff"
                            label="管理系数"
                            style="margin-left: 40%"
                            :class="{ 'custom-label': managecoeffClass }"
                        >
                            <crm-input
                                v-model="formList.managecoeff"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="assetcalcstartdt"
                            label-width="auto"
                            label="存D计算起始月"
                            style="margin-left: 36%"
                            :class="{ 'custom-label': assetcalcstartdtClass }"
                        >
                            <el-date-picker
                                v-model="formList.assetcalcstartdt"
                                class="popperClass"
                                type="date"
                                size="small"
                                disabled
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="fundname"
                            label="产品名称"
                            style="margin-left: 18%"
                            :class="{ 'custom-label': fundnameClass }"
                        >
                            <crm-input
                                v-model="formList.fundname"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="scale"
                            label="规模(万)"
                            style="margin-left: 18%"
                            :class="{ 'custom-label': scaleClass }"
                        >
                            <crm-input
                                v-model="formList.scale"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="stockfeed"
                            label-width="auto"
                            label="产品存续D系数"
                            style="margin-left: 15%"
                            :class="{ 'custom-label': stockfeedClass }"
                        >
                            <crm-input
                                v-model="formList.stockfeed"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="otherrate"
                            label="调整比例"
                            style="margin-left: 18%"
                            :class="{ 'custom-label': otherrateClass }"
                        >
                            <crm-input
                                v-model="formList.otherrate"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="assetcalcenddt"
                            label-width="auto"
                            label="存D计算结束月"
                            style="margin-left: 15%"
                            :class="{ 'custom-label': assetcalcenddtClass }"
                        >
                            <el-date-picker
                                v-model="formList.assetcalcenddt"
                                class="popperClass"
                                type="date"
                                size="small"
                                disabled
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item
                            prop="remark"
                            label="备注"
                            style="margin-top: 25px; margin-bottom: 50px; margin-left: 16.6%"
                            :class="{ 'custom-label': remarkClass }"
                        >
                            <crm-input
                                v-model="formList.remark"
                                :clearable="true"
                                disabled
                                :style="{ width: '335px', height: '25px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item
                            prop="reviewAdvice"
                            label="审核意见"
                            style="margin-top: 25px; margin-bottom: 50px; margin-left: 16.6%"
                        >
                            <crm-input
                                v-model="formList.reviewAdvice"
                                :clearable="true"
                                :style="{ width: '335px', height: '25px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <span
                    >注： <br />
                    1、必填项:「业务类型」~「存D计算结束月」 <br />
                    2、录入数据前
                    <span style="font-weight: bold; color: red">请复核</span> 业务数据是否已录入
                </span>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 55%">
                            <el-button type="primary" @click="handleisReview(1)">
                                审核通过
                            </el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item style="margin-left: 10%">
                            <el-button type="primary" @click="handleisReview(2)"
                                >审核不通过</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { fetchRes } from '@/utils/index'
    import { dataList } from './data/labelData'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import {
        performanceAssetHandConfReview,
        getAllCust,
        queryEdit
    } from '@/api/project/performanage/performanceAssetHandConf/performanceAssetHandConf'
    const { busitype, firstSource, conslevel } = dataList
    const loadingFlag = ref<boolean>(false)
    //字段颜色控制
    const busitypeClass = ref<boolean>(false)
    const conslevelClass = ref<boolean>(false)
    const custInfoClass = ref<boolean>(false)
    const formerConstObjClass = ref<boolean>(false)
    const fundnameClass = ref<boolean>(false)
    const scaleClass = ref<boolean>(false)
    const stockfeedClass = ref<boolean>(false)
    const firstSourceClass = ref<boolean>(false)
    const foldcoeffClass = ref<boolean>(false)
    const managecoeffClass = ref<boolean>(false)
    const otherrateClass = ref<boolean>(false)
    const assetcalcstartdtClass = ref<boolean>(false)
    const assetcalcenddtClass = ref<boolean>(false)
    const remarkClass = ref<boolean>(false)
    class FormList {
        busitype = ''
        conslevel = ''
        custInfo = ''
        formerConstObj = {
            orgCode: '',
            consCode: ''
        }
        fundname = ''
        scale = ''
        stockfeed = ''
        firstSource = ''
        foldcoeff = ''
        managecoeff = ''
        otherrate = ''
        assetcalcstartdt = ''
        assetcalcenddt = ''
        remark = ''
        reviewAdvice = ''
    }

    const formList = reactive<any>(new FormList())

    const ruleFormRef = ref<FormInstance>()

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            formerConsCode?: string
            formerOrgCode?: string
            formerConsultList?: any[]
            formerOrganizationList?: any[]
            consultList?: any[]
            transData?: {
                title: string
                type: string
                id: string
            }
            isAssetHandConfAdd?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    title: '审核',
                    type: 'edit',
                    id: ''
                }
            },
            formerConsCode: '',
            formerOrgCode: '',
            formerConsultList: () => [],
            consultList: () => [],
            formerOrganizationList: () => [],
            isAssetHandConfAdd: false
        }
    )

    // 弹窗标题配置
    const title = computed(() => {
        if (props.isAssetHandConfAdd) {
            return '新增'
        }
    })

    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })
    const custList = ref<object[]>()
    const getAllCustINFO = async (query: string) => {
        const custVO = {
            queryStr: query,
            page: 1,
            rows: 10
        }
        if (query !== '') {
            fetchRes(getAllCust(custVO), {
                successCB: (res: any) => {
                    const { rows } = res
                    custList.value = rows
                },
                errorCB: (res: any) => {
                    dialogVisible.value = false
                    ElMessage({
                        type: 'error',
                        message: res?.description || '请求失败'
                    })
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        }
    }

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()
    const rules = reactive<FormRules<FormList>>({
        busitype: [{ required: true, message: '请选择业务类型', trigger: 'blur' }],
        conslevel: [{ required: true, message: '请选择层级', trigger: 'blur' }],
        custInfo: [{ required: true, message: '请输入客户信息', trigger: 'blur' }],
        formerConstObj: [{ required: true, message: '请选择管理层/投顾', trigger: 'blur' }],
        scale: [{ required: true, message: '请输入规模', trigger: 'blur' }],
        stockfeed: [{ required: true, message: '请输入产品存续D系数', trigger: 'blur' }],
        foldcoeff: [{ required: true, message: '请输入客户折算系数', trigger: 'blur' }],
        managecoeff: [{ required: true, message: '请输入管理系数', trigger: 'blur' }],
        otherrate: [{ required: true, message: '请输入调整比例', trigger: 'blur' }],
        firstSource: [{ required: true, message: '请选择第一来源', trigger: 'blur' }],
        fundname: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        assetcalcstartdt: [{ required: true, message: '请选择存D计算起始月', trigger: 'blur' }],
        assetcalcenddt: [{ required: true, message: '请选择存D计算结束月', trigger: 'blur' }]
    })

    //提交方法

    //重置方法
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        formEl.resetFields()
    }

    // const handleisReview = (val: any): void => {
    //     performanceAssetHandConfSubmit(val)
    // }
    /**
     * 产品费率新增方法
     * @param params
     */
    const handleisReview = (val: any): void => {
        const requestParams = {
            id: props.transData.id,
            auditState: val,
            reviewAdvice: formList.reviewAdvice
        }
        fetchRes(performanceAssetHandConfReview(requestParams), {
            successCB: (res: any) => {
                loadingFlag.value = false
                dialogVisible.value = false
                return emit('callback')
            },
            errorCB: (res: any) => {
                loadingFlag.value = false
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => {
                loadingFlag.value = false
            },
            successTxt: '操作成功',
            failTxt: '',
            fetchKey: ''
        })
    }

    /**
     * @description: 编辑初始化
     * @return {*}
     */
    const fetchList = () => {
        // 初始化
        fetchRes(queryEdit({ id: props.transData.id }), {
            successCB: (res: any) => {
                // 编辑初始化
                const {
                    busitype,
                    conslevel,
                    conscode,
                    fundname,
                    scale,
                    stockfeed,
                    firstSource,
                    foldcoeff,
                    managecoeff,
                    otherrate,
                    assetcalcstartdt,
                    assetcalcenddt,
                    remark,
                    orgCode,
                    custInfo,
                    reviewAdvice,
                    editFields
                } = res || {}
                formList.busitype = busitype
                formList.conslevel = conslevel
                formList.fundname = fundname
                formList.scale = scale
                formList.stockfeed = stockfeed
                formList.firstSource = firstSource
                formList.foldcoeff = foldcoeff
                formList.managecoeff = managecoeff
                formList.otherrate = otherrate
                formList.assetcalcstartdt = assetcalcstartdt
                formList.assetcalcenddt = assetcalcenddt
                formList.remark = remark
                formList.custInfo = custInfo
                formList.formerConstObj.orgCode = orgCode
                formList.formerConstObj.consCode = conscode
                formList.reviewAdvice = reviewAdvice
                //修改字段使用红色字体
                if (editFields !== null) {
                    for (let i = 0; i < editFields.length; i++) {
                        if (editFields[i] === 'conscode' || editFields[i] === 'orgCode') {
                            formerConstObjClass.value = true
                        }
                        if (editFields[i] === 'fundname') {
                            fundnameClass.value = true
                        }
                        if (editFields[i] === 'scale') {
                            scaleClass.value = true
                        }
                        if (editFields[i] === 'stockfeed') {
                            stockfeedClass.value = true
                        }
                        if (editFields[i] === 'firstSource') {
                            firstSourceClass.value = true
                        }
                        if (editFields[i] === 'foldcoeff') {
                            foldcoeffClass.value = true
                        }
                        if (editFields[i] === 'managecoeff') {
                            managecoeffClass.value = true
                        }
                        if (editFields[i] === 'otherrate') {
                            otherrateClass.value = true
                        }
                        if (editFields[i] === 'assetcalcstartdt') {
                            assetcalcstartdtClass.value = true
                        }
                        if (editFields[i] === 'assetcalcenddt') {
                            assetcalcenddtClass.value = true
                        }
                        if (editFields[i] === 'remark') {
                            remarkClass.value = true
                        }
                    }
                }
            },
            errorCB: () => {
                formList.value = new FormList()
            },
            catchCB: () => {
                formList.value = new FormList()
            },
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }
    onBeforeMount(() => {
        fetchList()
    })
</script>

<style lang="less">
    .custom-label .el-form-item__label {
        color: red; // 设置label的颜色
    }

    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
