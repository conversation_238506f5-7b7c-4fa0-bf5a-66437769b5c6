import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import {
    performanceAssetHandConfParam,
    performanceAssetHandConfAddOrUpdateParam,
    aduitParam,
    deleteParam,
    defaultInfoParam,
    CustVO
} from './type/apiReqType.js'
/**
 * @description: 手工存续D查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const performanceAssetHandConfQuery = (params: performanceAssetHandConfParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConf/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 手工存续D导出接口
 * @return {*}
 */
export const performanceAssetHandConfExport = (params: performanceAssetHandConfParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConf/export',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 手工存续D新增接口
 * @return {*}
 */
export const performanceAssetHandConfInsert = (
    params: performanceAssetHandConfAddOrUpdateParam
) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConf/insert',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 手工存续D修改接口
 * @return {*}
 */
export const performanceAssetHandConfUpdate = (
    params: performanceAssetHandConfAddOrUpdateParam
) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConf/update',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 手工存续D删除接口
 * @return {*}
 */
export const performanceAssetHandConfDelete = (params: deleteParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConf/delete',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 手工存续D复核接口  ,
 * @return {*}
 */
export const performanceAssetHandConfReview = (params: aduitParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConf/review',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 获取客户信息
 * @return {*}
 */
export const getAllCust = (params: CustVO) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConf/getAllCust',
            method: 'post',
            data: params,
            loadingParams: {
                target: '.elForm'
            }
        })
    )
}

/**
 * @description: 编辑-init
 * @param {object} params
 * @return {*}
 */
export const queryEdit = (params: { id: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConf/queryEdit',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核-init
 * @param {object} params
 * @return {*}
 */
export const queryDealLog = (params: { id: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConf/dealLog',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核-init
 * @param {object} params
 * @return {*}
 */
export const getDefaultInfo = (params: defaultInfoParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConf/getDefaultInfo',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 审核-init
 * @param {object} params
 * @return {*}
 */
export const getAuth = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConf/getAuth',
            method: 'post'
        })
    )
}
