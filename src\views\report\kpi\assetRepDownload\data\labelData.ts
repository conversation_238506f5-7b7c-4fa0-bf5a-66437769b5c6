/*
 * @Description: 定义搜索的label列表
 * @Author: jian<PERSON><PERSON>.yang
 * @Date: 2024-04-01 10:40:30
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-04-01 10:40:30
 * @FilePath: /src/views/report/kpi/assetRepDownload/data/labelData.ts
 *
 */
export const dataList = {
    endDt: {
        label: '产品名称',
        placeholder: '输入结束时间'
    },
    // 0-家办、1-综拓、2-创新、3-份额转让、4-其他
    busitype: {
        label: '业务类型',
        placeholder: '请选择业务类型',
        selectList: [
            {
                key: '0',
                label: '家办'
            },
            {
                key: '1',
                label: '综拓'
            },
            {
                key: '2',
                label: '创新'
            },
            {
                key: '3',
                label: '份额转让'
            },
            {
                key: '4',
                label: '其他'
            }
        ]
    },
    // 0-离职，1-在职
    workState: {
        label: '在职状态',
        placeholder: '请选择在职状态',
        selectList: [
            {
                key: '0',
                label: '离职'
            },
            {
                key: '1',
                label: '在职'
            }
        ]
    },
    userId: {
        label: '员工编码',
        placeholder: '请输入员工编码'
    },
    orgData: {
        label: '投顾管理层',
        placeholder: '请选择所在组织'
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
