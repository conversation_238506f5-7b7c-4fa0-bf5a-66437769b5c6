/*
 * @Description: 返回结果通用处理逻辑
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-28 16:47:02
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 16:49:47
 * @FilePath: /crm-template/src/utils/resBase.ts
 * fetchRes(saveMaxInvest({
            conscustno,
            amount: topInfoListChange.value.maxInvest
        }),
        {
            successCB: () => getTopInfo(),
            errorCB: null,
            successTxt: '',
            failTxt: '最大可投资金额保存失败',
            fetchKey: ''
        }
    )
 */
import { MsgFetchRes } from '@/type/index'
import { ElMessage, ElMessageBox } from 'element-plus'
import { returnCodeRes, responseCode } from '@/constant/index'

// const returnCodeMap = {
//     SUCCESS: 'C020000',
//     FAIL: 'C020001',
//     PARAM_ERROR: 'C020002',
//     REPORT_NOT_EXIST: 'C020016'
// }

// const oldReturnCodeMap = {
//     SUCCESS: '0000',
//     FAIL: '0001',
//     PARAM_ERROR: '0002',
//     REPORT_NOT_EXIST: '0016'
// }

/**
 * @description: 接口返回通用处理逻辑
 * @param {any} saveRes
 * @param {MsgFetchRes} fetchResObj
 * @return {*}
 */
export const fetchRes = async (saveRes: any, fetchResObj: MsgFetchRes) => {
    try {
        const resObj = await saveRes
        const { code, data, description } = resObj || {}
        if (code === 'ERR_BAD_RESPONSE') {
            ElMessage({
                type: 'error',
                message: '接口请求失败'
            })
            fetchResObj.errorCB && fetchResObj.errorCB()
        }
        switch (code) {
            case returnCodeRes.SUCCESS:
            case returnCodeRes.CRM_SUCCESS:
            case responseCode.CRM_SUCCESS:
                if (fetchResObj.successTxt) {
                    ElMessage({
                        type: 'success',
                        message: fetchResObj.successTxt || '请求成功'
                    })
                }
                fetchResObj.successCB && fetchResObj.successCB(data)
                break

            case returnCodeRes.PARAM_ERROR:
            case returnCodeRes.CRM_PARAM_ERROR:
                ElMessage({
                    type: 'error',
                    message: description || '参数错误'
                })
                fetchResObj.errorCB && fetchResObj.errorCB(resObj)
                break
            case returnCodeRes.SYS_FILED:
            case returnCodeRes.CRM_SYS_FILED:
                fetchResObj.errorCB && fetchResObj.errorCB(resObj)
                if (fetchResObj.failTxt) {
                    ElMessage({
                        type: 'error',
                        message: fetchResObj.failTxt || '请求失败'
                    })
                }
                break
            default:
                ElMessage({
                    type: 'error',
                    message: description || data?.description || '请求失败'
                })
                fetchResObj.errorCB && fetchResObj.errorCB(resObj)
                break
        }
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e?.message || '请求失败'
        })
        console.log(e)
        fetchResObj.catchCB && fetchResObj.catchCB(e)
        // throw new Error(`请求失败${fetchResObj?.fetchKey || ''}`)
    }
}

/**
 * @description: 接口返回利用promise容器
 * @param {any} saveRes
 * @param {MsgFetchRes} fetchResObj
 * @return {*}
 */
export const fetchSync = (saveRes: any, fetchResObj: MsgFetchRes) => {
    return new Promise((resolve, reject) => {
        saveRes
            .then((resObj: any) => {
                const { code, data, body } = resObj
                const { returnCode } = data || body
                const codeStr = returnCode || code
                if (code === 'ERR_BAD_RESPONSE') {
                    ElMessage({
                        type: 'error',
                        message: '接口请求失败'
                    })
                    fetchResObj.errorCB && fetchResObj.errorCB()
                    reject()
                }
                switch (codeStr) {
                    case returnCodeRes.SUCCESS:
                    case returnCodeRes.CRM_SUCCESS:
                    case responseCode.CRM_SUCCESS:
                        if (fetchResObj.successTxt) {
                            ElMessage({
                                type: 'success',
                                message: fetchResObj.successTxt || '请求成功'
                            })
                        }
                        fetchResObj.successCB && fetchResObj.successCB(data ?? body)
                        resolve(data)
                        break
                    case returnCodeRes.SYS_FILED:
                    case returnCodeRes.CRM_SYS_FILED:
                        if (fetchResObj.failTxt) {
                            ElMessage({
                                type: 'error',
                                message: fetchResObj.failTxt || '请求失败'
                            })
                        }
                        fetchResObj.errorCB && fetchResObj.errorCB()
                        reject()
                        break
                    case returnCodeRes.PARAM_ERROR:
                    case returnCodeRes.CRM_PARAM_ERROR:
                        ElMessage({
                            type: 'error',
                            message: '参数错误'
                        })
                        fetchResObj.errorCB && fetchResObj.errorCB()
                        reject()
                        break
                    default:
                        ElMessage({
                            type: 'error',
                            message: '接口请求失败'
                        })
                        fetchResObj.errorCB && fetchResObj.errorCB()
                        reject()
                        break
                }
            })
            .catch(() => {
                ElMessage({
                    type: 'error',
                    message: '接口请求失败'
                })
                fetchResObj.catchCB && fetchResObj.catchCB()
                reject()
            })
    })
}

/**
 * @description: 接口promise.all返回通用处理逻辑
 * @param {any} saveRes
 * @param {MsgFetchRes} fetchResObj
 * @return {*}
 */
export const fetchAll = (saveRes: any, fetchResObj: MsgFetchRes) => {
    if (saveRes?.length > 6) {
        throw new Error(`并发接口不建议大于6个,会导致浏览器性能问题`)
    }
    return Promise.all(saveRes)
        .then((resObj: any) => {
            fetchResObj.successCB && fetchResObj.successCB()
        })
        .catch(() => {
            ElMessage({
                type: 'error',
                message: '请求失败'
            })
            fetchResObj.catchCB && fetchResObj.catchCB()
            throw new Error(`请求失败${fetchResObj?.fetchKey || ''}`)
        })
}
