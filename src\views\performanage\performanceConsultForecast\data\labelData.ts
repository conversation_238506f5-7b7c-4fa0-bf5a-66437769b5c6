/*
 * @Description: 定义搜索的label列表
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:36:23
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/labelData.ts
 *
 */
export const dataList = {
    orgData: {
        label: '投顾',
        placeholder: '请选择'
    },
    // 考核周期
    periodExplain: {
        label: '考核周期',
        placeholder: '请选择',
        selectList: [
            {
                key: '1',
                label: '试用-3M'
            },
            {
                key: '2',
                label: '试用-6M'
            },
            {
                key: '3',
                label: '跟踪-12M'
            },
            {
                key: '4',
                label: '正式-年中'
            },
            {
                key: '5',
                label: '正式-年末'
            },
            {
                key: '6',
                label: '观察期-12M以上'
            },
            {
                key: '7',
                label: '观察期-12M以内'
            },
            {
                key: '8',
                label: '理1B'
            }
        ]
    },
    // 预计考核结果
    exaimneResult: {
        label: '预计考核结果',
        placeholder: '请选择',
        selectList: [
            {
                key: '5',
                label: '晋升'
            },
            {
                key: '1',
                label: '维持'
            },
            {
                key: '2',
                label: '降级'
            },
            {
                key: '4',
                label: '降职或淘汰'
            },
            {
                key: '3',
                label: '进观察期'
            },
            {
                key: '6',
                label: '出观察期'
            }
        ]
    },
    // 层级
    userLevel: {
        label: '层级',
        placeholder: '请选择',
        selectList: [
            {
                key: '1',
                label: '投资顾问'
            },
            {
                key: '3',
                label: '分总'
            },
            {
                key: '4',
                label: '区域执行副总'
            },
            {
                key: '5',
                label: '区域总'
            }
        ]
    },
    // 职级
    curMonthLevel: {
        label: '职级',
        placeholder: '请选择'
    },
    // 非观察人力
    qualifiedStaff: {
        label: '非观察人力',
        placeholder: '请选择',
        selectList: [
            {
                key: '1',
                label: '1'
            },
            {
                key: '0',
                label: '0'
            }
        ]
    },
    // 合格人力
    competentStaff: {
        label: '合格人力',
        placeholder: '请选择',
        selectList: [
            {
                key: '1',
                label: '1'
            },
            {
                key: '0',
                label: '0'
            }
        ]
    },
    //考核节点
    exaimneNode: {
        label: '考核节点',
        placeholder: ['开始日期', '结束日期']
    },
    //预计新职级
    newRank: {
        label: '预计新职级',
        placeholder: '请选择'
    },
    //是否TP
    istp: {
        label: '是否TP',
        placeholder: '请选择',
        selectList: [
            {
                key: '1',
                label: '是'
            },
            {
                key: '0',
                label: '否'
            }
        ]
    },
    // 最终考核结果
    exaimneEndresult: {
        label: '最终考核结果',
        placeholder: '请选择',
        selectList: [
            {
                key: '5',
                label: '晋升'
            },
            {
                key: '1',
                label: '维持'
            },
            {
                key: '2',
                label: '降级'
            },
            {
                key: '4',
                label: '降职或淘汰'
            },
            {
                key: '3',
                label: '进观察期'
            },
            {
                key: '6',
                label: '出观察期'
            }
        ]
    },
    // 投顾code
    userId: {
        label: '投顾code',
        placeholder: '投顾code'
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
