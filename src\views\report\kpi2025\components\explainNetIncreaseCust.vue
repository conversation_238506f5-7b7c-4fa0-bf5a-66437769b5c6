<!--
 * @Description: 统计说明
 * @Author: gang.zou
 * @Date: 2024-04-12 19:46:04
 * @LastEditors: gang.zou
 * @LastEditTime: 2024-04-12 19:46:04
 * @FilePath: /src/views/report/kpi/components/explainNetIncreaseCust.vue
 *  
-->
<template>
    <crm-dialog
        width="824px"
        title="统计说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <template #default>
            <div style="height: 300px">
                <b>统计2025年发生交易的客户是否满足2025年KPI-新增客户（新增香港持仓客户）要求。</b
                ><br />
                <span>①2025年1月1日之后首次打款香港业务（含基金、创新）</span><br />
                <span
                    >②全年最高存量不低于5万美金。含所有香港业务（不管是否投海外）。海外创新按总保费计算</span
                ><br />
            </div>
        </template>

        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">确定</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { useVisible } from '@/views/common/scripts/useVisible'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()
    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })
</script>

<style lang="less" scoped>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 8px;
        text-align: left;
        border: 1px solid black;
    }

    th {
        background-color: #f2f2f2;
    }

    .middle-content {
        padding: 10px 0;
    }
</style>
