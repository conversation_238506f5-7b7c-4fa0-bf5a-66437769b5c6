<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 投顾 -->
                <label-item label="投顾" class-name="text-left">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        code-width="120px"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <!-- 员工编码 -->
                <label-item :label="userId.label" class-name="text-left">
                    <crm-input
                        v-model="queryForm.userId"
                        :placeholder="userId.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <!-- 员工状态 -->
                <label-item :label="worktype.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.worktype"
                        :placeholder="worktype.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="worktype.selectList"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <!-- 统计日期 -->
                <label-item :label="sdate.label" class-name="text-left">
                    <el-date-picker
                        v-model="queryForm.sdate"
                        type="date"
                        format="YYYYMMDD"
                        :clearable="false"
                        :editable="false"
                        value-format="YYYYMMDD"
                        style="width: 150px; height: 24px"
                        :placeholder="sdate.placeholder"
                    />
                </label-item>
                <!-- 是否参与统计 -->
                <label-item :label="isKpi.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.isKpi"
                        :placeholder="isKpi.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="isKpi.selectList"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <!-- 层级 -->
                <label-item :label="userLevel.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.userLevel"
                        :placeholder="userLevel.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="userLevel.selectList"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <!-- 目标达标状态 -->
                <label-item :label="isReach.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.isReach"
                        :placeholder="isReach.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="isReach.selectList"
                        :style="{ width: '150px' }"
                    />
                </label-item>
            </template>
            <template #operationBtns>
                <Text type="info" size="small" style="font-size: 14px"
                    >最后更新时间：
                    <span class="last-update-time">{{ lastUpdateTimeView }}</span>
                    , 截止统计日期:
                    <span class="last-update-time">{{ dataDeadlineView }}</span>
                </Text>
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    plain
                    @click="exportHandle"
                    >导出</crm-button
                >
                <crm-button size="small" :radius="true" plain @click="handleExplain"
                    >说明</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    :pops="{ selectFixed: 'left' }"
                    @sortChange="handleSortChange"
                >
                    <template #custCount="{ scope }">
                        <el-text v-if="scope.row.gdClCnt <= 0 || !detailShow" size="small">{{
                            scope.row.gdClCnt
                        }}</el-text>
                        <el-button
                            v-if="detailShow && scope.row.gdClCnt > 0"
                            size="small"
                            :text="true"
                            link
                            @click="handleCustDetail(scope.row)"
                            >{{ scope.row.gdClCnt }}</el-button
                        >
                    </template>
                    <template #healthCount="{ scope }">
                        <el-text v-if="scope.row.healthCnt <= 0 || !detailShow" size="small">{{
                            scope.row.healthCnt
                        }}</el-text>
                        <el-button
                            v-if="detailShow && scope.row.healthCnt > 0"
                            size="small"
                            :text="true"
                            link
                            @click="handleHealthDetail(scope.row)"
                            >{{ scope.row.healthCnt }}</el-button
                        >
                    </template>
                </base-table>
            </template>
            <!-- <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template> -->
        </table-wrapper>
        <Explain v-model="explainDialogVisiable"></Explain>
    </div>
</template>

<script lang="ts" setup>
    import { fetchRes } from '@/utils'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import { ElMessage, ElLoading } from 'element-plus'
    import { customerHealthTableColumn } from './data/tableData'
    import { dataList } from './data/labelData'
    import {
        queryCustomerHealthList,
        exportCustomerHealthList,
        healthInitData
    } from '@/api/project/report/health/customerHealth/customerHealth'
    import Explain from '@/views/report/health/components/explainHealthOverview.vue'
    import { getMenuPermission } from '@/api/project/common/common'
    import { HEALTH_CUSTOMER_OPER_PERMISSION } from '@/constant/reportConst'
    import { SortOrderCumstom } from '@/type/index'

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const listLoading = ref<boolean>(false)
    const exportShow = ref<boolean>(false)
    // 是否显示详情 默认显示不配权限
    const detailShow = ref<boolean>(true)
    const consStatus = ref<string>('1') //不包含离职人员
    const module = ref<string>('140901')
    const lastUpdateTimeView = ref<string>()
    const dataDeadlineView = ref<string>()

    // 表格数据
    const tableData = ref<object[]>([])

    // 从labelData中获取下拉选项
    const { userId, sdate, worktype, isKpi, userLevel, isReach } = dataList

    // 表格列配置
    const tableColumn = computed(() => {
        return customerHealthTableColumn
    })

    // 查询条件
    class QueryForm {
        userId = ''
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        sdate = ''
        worktype = ''
        isKpi = ''
        userLevel = ''
        isReach = ''
        sort = ''
        order = 'descending'
    }
    const queryForm = reactive(new QueryForm())

    // 分页数据
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    // 排序值映射
    const transOrder = (key: string) => {
        switch (key) {
            case 'ascending':
                return 'ASC'
            case 'descending':
                return 'DESC'
            default:
                return 'DESC'
        }
    }

    interface SortParams {
        order: SortOrderCumstom
        prop: string
    }

    /**
     * @description: 说明展示隐藏
     * @param explainDialogVisiable 展示隐藏
     * @method handleExplain 触发方法
     * @return {*}
     */
    const explainDialogVisiable = ref<boolean>(false)
    const handleExplain = () => {
        explainDialogVisiable.value = true
    }

    const handleCustDetail = (val: any): void => {
        const { conscode, sdate, userLevel } = val || {}
        console.log('handleCustDetail' + conscode)
        window.open(
            `${
                window.location.origin + window.location.pathname
            }#/report/custDetail?consCode=${conscode}&sdate=${sdate}&userLevel=${userLevel}`
        )
    }

    const handleHealthDetail = (val: any): void => {
        const { conscode, sdate, userLevel } = val || {}
        console.log('handleHealthDetail' + conscode)
        window.open(
            `${
                window.location.origin + window.location.pathname
            }#/report/healthDetail?consCode=${conscode}&sdate=${sdate}&userLevel=${userLevel}`
        )
    }

    // 排序联动
    const handleSortChange = (val: SortParams) => {
        queryForm.order = val.order
        queryForm.sort = val.prop
        nextTick(() => {
            queryList()
        })
    }

    // 查询方法
    const queryList = async () => {
        listLoading.value = true
        const params = {
            userId: queryForm.userId,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            sdate: queryForm.sdate,
            worktype: queryForm.worktype,
            isKpi: queryForm.isKpi,
            userLevel: queryForm.userLevel,
            isReach: queryForm.isReach
            // page: pageObj.value.page,
            // rows: pageObj.value.size,
            // sort: queryForm.sort,
            // order: transOrder(queryForm.order)
        }

        fetchRes(queryCustomerHealthList(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total, lastUpdateTime } = resObj
                tableData.value = rows
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    // 导出方法
    const exportHandle = async () => {
        const allLoading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)'
        })

        const params = {
            userId: queryForm.userId,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            sdate: queryForm.sdate,
            worktype: queryForm.worktype,
            isKpi: queryForm.isKpi,
            userLevel: queryForm.userLevel,
            isReach: queryForm.isReach,
            sort: queryForm.sort,
            order: transOrder(queryForm.order)
        }

        const res: any = await exportCustomerHealthList(params)
        const { fileByte, name, errorMsg } = res.data
        if (errorMsg) {
            ElMessage({
                message: errorMsg,
                type: 'warning',
                duration: 2000
            })
            allLoading.close()
            return
        }
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
        allLoading.close()
    }

    // 初始化
    const handleLoading = () => {
        queryList()
    }

    // 分页联动
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    // 初始化数据
    const initData = async () => {
        const params = {
            menuCode: module.value
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode === HEALTH_CUSTOMER_OPER_PERMISSION.EXPORT &&
                            item.display === '1'
                        ) {
                            exportShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
        fetchRes(healthInitData(), {
            successCB: (resObj: any) => {
                const { lastUpdateTime, dataDeadline } = resObj
                lastUpdateTimeView.value = lastUpdateTime
                dataDeadlineView.value = dataDeadline
                queryForm.sdate = dataDeadline
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    // 监听默认值变化
    watch(
        [orgCodeDefault, consCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.orgvalue.orgCode = orgCodeDefault.value
                queryForm.orgvalue.consCode = consCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    onMounted(() => {
        fetchConsOrgList('', module.value, '1')
        initData()
    })
</script>

<style lang="less" scoped>
    .table-cust-select {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        tr {
            background-color: #ffffff;

            td {
                border: 1px solid #dddddd;
            }
        }
    }
</style>
@/api/project/report/health/customerHealth/customerHealth
