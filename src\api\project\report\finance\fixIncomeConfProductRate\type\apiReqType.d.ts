export {}
declare module './apiReqType' {
    type TwoLevelConfProductRateParam = {
        /**
         * 开始日期
         */
        startDate?: string
        /**
         * 结束日期
         */
        endDate?: string
        /**
         * 基金代码
         */
        fundCode?: string
        /**
         * 产品名称
         */
        fundName?: string
        /**
         * 审核状态
         */
        reviewState?: string
        /**
         * 是否审核通过
         */
        isReview?: string
    }
    type TwoLevelConfProductRateAddOrUpdateParam = {
        fundCode: ?string // 基金code
        fundName: ?string // 基金名称
        company: ?string // 公司
        subRate: ?number // 认购费率
        manageRate: ?number // 管理费率
        manageFormula: ?string // 管理费公式
        manageRemark: ?string // 管理费备注
        performanceRate: ?number // 业绩报酬费率
        performanceShareRate: ?number // 业绩报酬分成费率
        redemRate: ?string // 赎回费率
        performanceDate: ?string // 业绩报酬提取日
        redemDate: ?string //赎回日 1-是2-否
        settleDate: ?string //清算日 1-是2-否
        shareDate: ?string //分红日 1-是2-否
        performanceFormula: ?string //业绩报酬公式
        performanceRemark: ?string //业绩报酬备注
        redemFormula: ?string // 赎回费公式
        redemRemark: ?string // 赎回费备注
        productManage: ?string // 产品经理
        signDate: ?string // 签约日期
        signSubject: ?string // 好买签约主体
        oppositeContact: ?string // 对方联系人
    }

    // 批量删除
    type BatchDeleteId = {
        /**
         * id列表
         */
        taskIdList?: string[]
    }
    // 批量删除
    type AduitProduct = {
        /**
         * 任务id
         */
        taskId?: string
        /**
         * 审核状态
         */
        reviewState?: string
    }

    export {
        TwoLevelConfProductRateParam,
        TwoLevelConfProductRateAddOrUpdateParam,
        BatchDeleteId,
        AduitProduct
    }
}
