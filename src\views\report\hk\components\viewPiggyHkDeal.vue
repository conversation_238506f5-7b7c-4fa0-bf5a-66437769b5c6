<!--
 * @Description: 查看详情弹框
 * @Author: jianjian.yang
 * @Date: 2024-09-29 16:47:35
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-09-29 16:47:35
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        height="500px"
        :border="true"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <!-- 产品费率的弹框 -->
            <el-form :model="formList" label-width="180px" status-icon>
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="dealNo" label="业务订单号" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.dealNo"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="hkCustNo" label="香港客户号" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.hkCustNo"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="hboneNo" label="一账通号" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.hboneNo"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="conscustno" label="投顾客户号" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.conscustno"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="checkState" label="审核状态" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.custStat"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="busiName" label="业务类型" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.busiName"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="signCancelTime"
                            label="签署/终止时间"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.signCancelTime"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="signCancelType"
                            label="签署/终止方式"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.signCancelType"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="channel" label="签署/终止渠道" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.channel"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="invstType" label="客户类型" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.invstType"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="investorQualification"
                            label="投资者类型（香港）"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.investorQualification"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="signState"
                            label="储蓄罐签约状态(当前)"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.signState"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="custStat"
                            label="香港客户状态(当前)"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.custStat"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="consname" label="当前投顾" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.consname"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="u1Name"
                            label="当前投顾一级组织架构"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.u1Name"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="u2Name"
                            label="当前投顾二级组织架构"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.u2Name"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="u3Name"
                            label="当前投顾三级组织架构"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.u3Name"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="createConsname"
                            label="当时投顾"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.createConsname"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="createU1Name"
                            label="当时投顾一级组织架构"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.createU1Name"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="createU2Name"
                            label="当时投顾二级组织架构"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.createU2Name"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="createU3Name"
                            label="当时投顾三级组织架构"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.createU3Name"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="statDate" label="统计日期" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.statDate"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="popTime" label="数据生成时间" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.popTime"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="createTime"
                            label="数据创建时间"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.createTime"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="isSign"
                            label="历史是否签署过海外储蓄罐"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.isSign"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <br />
                <el-row>
                    <el-col>
                        <el-form-item style="margin-left: 25%">
                            <el-button type="primary" @click="handleClose()"> 关闭 </el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { fetchRes } from '@/utils/index'
    import { viewDetail } from '@/api/project/report/hk/piggyHkDeal/piggyHkDeal'

    class FormList {
        dealNo = ''
        hkCustNo = ''
        hboneNo = ''
        conscustno = ''
        busiName = ''
        checkState = ''
        signCancelType = ''
        signCancelTime = ''
        channel = ''
        invstType = ''
        investorQualification = ''
        signState = ''
        custStat = ''
        consname = ''
        u1Name = ''
        u2Name = ''
        u3Name = ''
        createConsname = ''
        createU1Name = ''
        createU2Name = ''
        createU3Name = ''
        statDate = ''
        popTime = ''
        createTime = ''
        isSign = ''
    }

    const formList = reactive<any>(new FormList())

    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })
    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                title: string
                type: string
                dealNo: string
            }
            isAssetHandConfAdd?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    title: '审核',
                    type: 'edit',
                    dealNo: ''
                }
            }
        }
    )

    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (): any => {
        dialogVisible.value = false
    }

    /**
     * @description: 编辑初始化
     * @return {*}
     */
    const fetchList = () => {
        // 初始化
        fetchRes(viewDetail({ dealNo: props.transData.dealNo }), {
            successCB: (res: any) => {
                // 初始化
                formList.dealNo = res.dealNo
                formList.hkCustNo = res.hkCustNo
                formList.hkCustName = res.hkCustName
                formList.custName = res.custName
                formList.busiName = res.busiName
                formList.checkState = res.checkState
                formList.signCancelType = res.signCancelType
                formList.signCancelTime = res.signCancelTime
                formList.channel = res.channel
                formList.custStat = res.custStat
                formList.signState = res.signState
                formList.hboneNo = res.hboneNo
                formList.conscustno = res.conscustno
                formList.invstType = res.invstType
                formList.investorQualification = res.investorQualification
                formList.consname = res.consname
                formList.u1Name = res.u1Name
                formList.u2Name = res.u2Name
                formList.u3Name = res.u3Name
                formList.createConsname = res.createConsname
                formList.createU1Name = res.createU1Name
                formList.createU2Name = res.createU2Name
                formList.createU3Name = res.createU3Name
                formList.statDate = res.statDate
                formList.popTime = res.popTime
                formList.createTime = res.createTime
                formList.isSign = res.isSign
            },
            errorCB: () => {
                formList.value = new FormList()
            },
            catchCB: () => {
                formList.value = new FormList()
            },
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }
    onBeforeMount(() => {
        fetchList()
    })
</script>

<style lang="less"></style>
