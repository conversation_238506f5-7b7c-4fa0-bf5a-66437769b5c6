<!--
 * @Description: 统计说明
 * @Author: gang.zou
 * @Date: 2024-04-15 19:46:04
 * @LastEditors: gang.zou
 * @LastEditTime: 2024-04-15 19:46:04
 * @FilePath: /src/views/report/kpi/components/explainAssetRep.vue
 *  
-->
<template>
    <crm-dialog
        width="824px"
        title="统计说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <template #default>
            <div style="height: 300px">
                <b>此报表用于统计投顾、分公司、区域、中心 2025年的创新继续率的达成情况。</b><br />
                <span>①投顾：续缴的保单在2024年12月31日对应的投顾</span><br />
                <span>②继续率=实收保费（RMB）/ 应收保费（RMB）</span><br />
                <span>③细部规则请以2025年KPI文件为准</span>
            </div>
        </template>

        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">确定</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { useVisible } from '@/views/common/scripts/useVisible'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()
    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })
</script>

<style lang="less" scoped>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 8px;
        text-align: left;
        border: 1px solid black;
    }

    th {
        background-color: #f2f2f2;
    }

    .middle-content {
        padding: 10px 0;
    }
</style>
