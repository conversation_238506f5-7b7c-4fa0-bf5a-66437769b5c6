<!--
 * @Description: 查看详情弹框
 * @Author: jianjian.yang
 * @Date: 2024-09-29 16:47:35
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-09-29 16:47:35
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        height="500px"
        :border="true"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <!-- 产品费率的弹框 -->
            <el-form :model="formList" label-width="180px" status-icon>
                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="hkCustName"
                            label="客户姓名(香港)"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.hkCustName"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="hkCustNo" label="香港客户号" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.hkCustNo"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="hboneNo" label="一账通号" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.hboneNo"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="conscustno" label="投顾客户号" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.conscustno"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="invstType" label="客户类型" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.invstType"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="investorQualification"
                            label="投资者类型（香港）"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.investorQualification"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="custStat" label="香港客户状态" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.custStat"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="regTime" label="注册日期" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.regTime"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="closeDate" label="销户日期" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.closeDate"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="dormantDate" label="休眠日期" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.dormantDate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="openDate" label="香港开户日期" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.openDate"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="openChannel" label="开户渠道" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.openChannel"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="submitTime"
                            label="线上开户资料提交时间"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.submitTime"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="txChkFlag"
                            label="线上开户资料审核状态"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.txChkFlag"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="passDt"
                            label="线上开户资料审核通过时间"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.passDt"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="accountTime"
                            label="开户申请成功日期"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.accountTime"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="depositFundChannel"
                            label="入金渠道"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.depositFundChannel"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="depositSubmitTime"
                            label="开户入金审核提交时间"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.depositSubmitTime"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="depositPassDt"
                            label="开户入金审核通过时间"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.depositPassDt"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="firstAckDt"
                            label="首次打款不低1万美元日期"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.firstAckDt"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="riskToleranceLevel"
                            label="风险等级"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.riskToleranceLevel"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="derivativeKnowledge"
                            label="是否有衍生工具知识"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.derivativeKnowledge"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="kycDt" label="风险测评日期" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.kycDt"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="kycExpireDt"
                            label="风险测评到期日期"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.kycExpireDt"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="investorQualificationDate"
                            label="投资者资质认证日期"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.investorQualificationDate"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="assetCertDate"
                            label="资产证明日期"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.assetCertDate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="riskToleranceTerm"
                            label="资产证明到期日期"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.riskToleranceTerm"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="signState"
                            label="海外储蓄罐签约状态"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.signState"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="agreementSignDt"
                            label="海外储蓄罐签约日期"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.agreementSignDt"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="agreementSignType"
                            label="海外储蓄罐签署方式"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.agreementSignType"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="signChannel"
                            label="海外储蓄罐签署渠道"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.signChannel"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="agreementSignExpiredDt"
                            label="海外储蓄罐签约有效期"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.agreementSignExpiredDt"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="agreementCancelDt"
                            label="海外储蓄罐协议终止日期"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.agreementCancelDt"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="agreementCancelType"
                            label="海外储蓄罐终止方式"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.agreementCancelType"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="cancelChannel"
                            label="海外储蓄罐终止渠道"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.cancelChannel"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="consname" label="当前投顾" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.consname"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="conscode" label="当前投顾代码" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.conscode"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="u1Name"
                            label="一级组织架构(当前投顾)"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.u1Name"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="u2Name"
                            label="二级组织架构(当前投顾)"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.u2Name"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="u3Name"
                            label="三级组织架构(当前投顾)"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.u3Name"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="kpi2024Dt" label="KPI达标日期" style="margin-left: 10%">
                            <crm-input
                                v-model="formList.kpi2024Dt"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="consnameKpi"
                            label="投顾(KPI达标日)"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.consnameKpi"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="u1NameKpi"
                            label="一级组织架构(KPI)"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.u1NameKpi"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="u2NameKpi"
                            label="二级组织架构(KPI)"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.u2NameKpi"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                disabled
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="u3NameKpi"
                            label="三级组织架构(KPI)"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="formList.u3NameKpi"
                                :clearable="true"
                                disabled
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <br />
                <el-row>
                    <el-col>
                        <el-form-item style="margin-left: 25%">
                            <el-button type="primary" @click="handleClose()"> 关闭 </el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage, ElMessageBox } from 'element-plus'
    import { fetchRes } from '@/utils/index'
    import { viewDetail, viewRealText } from '@/api/project/report/hk/custHkInfo/custHkInfo'
    const loadingFlag = ref<boolean>(false)

    class FormList {
        hkCustNo = ''
        hkCustName = ''
        hboneNo = ''
        conscustno = ''
        openDate = ''
        custStat = ''
        closeDate = ''
        invstType = ''
        investorQualification = ''
        investorQualificationDate = ''
        riskToleranceLevel = ''
        kycDt = ''
        conscode = ''
        consname = ''
        u1Name = ''
        u2Name = ''
        u3Name = ''
        assetCertDate = ''
        assetCertExpiredDate = ''
        derivativeKnowledge = ''
        regTime = ''
        openChannel = ''
        accountTime = ''
        submitTime = ''
        passDt = ''
        txChkFlag = ''
        depositSubmitTime = ''
        depositPassDt = ''
        depositFundChannel = ''
        dormantDate = ''
        firstAckDt = ''
        kpi2024Dt = ''
        conscodeKpi = ''
        consnameKpi = ''
        u1NameKpi = ''
        u2NameKpi = ''
        u3NameKpi = ''
    }

    const formList = reactive<any>(new FormList())

    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })
    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                title: string
                type: string
                hkCustNo: string
            }
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    title: '审核',
                    type: 'edit',
                    hkCustNo: ''
                }
            }
        }
    )

    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = () => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        dialogVisible.value = false
    }

    const viewReal = (type: string) => {
        console.log(formList.hkCustNo)
        let text
        if (type === '2') {
            text = formList.idNoCipher
        } else {
            text = formList.mobileCipher
        }
        const params = {
            hkCustNo: props.transData.hkCustNo,
            text: text,
            type: type
        }
        fetchRes(viewRealText(params), {
            successCB: (res: any) => {
                console.log(res)
                ElMessageBox.alert(res, '', {
                    center: true
                })
            },
            errorCB: null,
            catchCB: null,
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 编辑初始化
     * @return {*}
     */
    const fetchList = () => {
        // 初始化
        fetchRes(viewDetail({ hkCustNo: props.transData.hkCustNo }), {
            successCB: (res: any) => {
                // 编辑初始化
                const { detailVO } = res || {}
                formList.hkCustNo = detailVO.hkCustNo
                formList.hkCustName = detailVO.hkCustName
                formList.hboneNo = detailVO.hboneNo
                formList.conscustno = detailVO.conscustno
                formList.openDate = detailVO.openDate
                formList.custStat = detailVO.custStat
                formList.closeDate = detailVO.closeDate
                formList.invstType = detailVO.invstType
                formList.investorQualification = detailVO.investorQualification
                formList.investorQualificationDate = detailVO.investorQualificationDate
                formList.riskToleranceLevel = detailVO.riskToleranceLevel
                formList.kycDt = detailVO.kycDt
                formList.conscode = detailVO.conscode
                formList.consname = detailVO.consname
                formList.u1Name = detailVO.u1Name
                formList.u2Name = detailVO.u2Name
                formList.u3Name = detailVO.u3Name
                formList.assetCertDate = detailVO.assetCertDate
                formList.assetCertExpiredDate = detailVO.assetCertExpiredDate
                formList.derivativeKnowledge = detailVO.derivativeKnowledge
                formList.regTime = detailVO.regTime
                formList.openChannel = detailVO.openChannel
                formList.accountTime = detailVO.accountTime
                formList.submitTime = detailVO.submitTime
                formList.passDt = detailVO.passDt
                formList.txChkFlag = detailVO.txChkFlag
                formList.depositSubmitTime = detailVO.depositSubmitTime
                formList.depositPassDt = detailVO.depositPassDt
                formList.depositFundChannel = detailVO.depositFundChannel
                formList.dormantDate = detailVO.dormantDate
                formList.firstAckDt = detailVO.firstAckDt
                formList.kpi2024Dt = detailVO.kpi2024Dt
                formList.conscodeKpi = detailVO.conscodeKpi
                formList.consnameKpi = detailVO.consnameKpi
                formList.u1NameKpi = detailVO.u1NameKpi
                formList.u2NameKpi = detailVO.u2NameKpi
                formList.u3NameKpi = detailVO.u3NameKpi
                formList.riskToleranceTerm = detailVO.riskToleranceTerm
                formList.signState = detailVO.signState
                formList.agreementSignDt = detailVO.agreementSignDt
                formList.agreementSignType = detailVO.agreementSignType
                formList.signChannel = detailVO.signChannel
                formList.agreementSignExpiredDt = detailVO.agreementSignExpiredDt
                formList.agreementCancelDt = detailVO.agreementCancelDt
                formList.agreementCancelType = detailVO.agreementCancelType
                formList.cancelChannel = detailVO.cancelChannel
            },
            errorCB: () => {
                formList.value = new FormList()
            },
            catchCB: () => {
                formList.value = new FormList()
            },
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }
    onBeforeMount(() => {
        fetchList()
    })
</script>

<style lang="less"></style>
