<!--
 * @Description: 单选按钮组件
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-19 11:11:19
 * @FilePath: /crm-asset-web/src/components/moduleBase/CrmRadio.vue
 * 基于el-plus 
-->
<template>
    <el-radio-group class="crm_radio" v-bind="$attrs" size="small">
        <slot />
        <el-radio v-for="(item, index) in optionList" :key="index" :label="item[valueFormat]">{{
            item[labelFormat]
        }}</el-radio>
    </el-radio-group>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmRadio',
        props: {
            optionList: {
                type: Array,
                default: () => []
            },
            labelFormat: {
                type: String,
                default: 'label'
            },
            valueFormat: {
                type: String,
                default: 'value'
            }
        }
    })
</script>
<style lang="less">
    .crm_radio.el-radio-group {
        .el-radio {
            margin-right: 20px;

            &.el-radio--small {
                height: 26px;
            }

            &.is-checked {
                .el-radio__inner {
                    background-color: @theme_main;
                    border-color: @theme_main;
                }

                .el-radio__label {
                    height: 18px;
                    font-family: 'Microsoft YaHei', '微软雅黑';
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 17px;
                    color: @font_color;
                }
            }

            .el-radio__input {
                .el-radio__inner {
                    width: 12px;
                    height: 12px;

                    &:hover {
                        border-color: @theme_main;
                    }
                }
            }

            .el-radio__label {
                height: 18px;
                padding-left: 5px;
                font-family: 'Microsoft YaHei', '微软雅黑';
                font-size: 12px;
                font-weight: 400;
                line-height: 17px;
                color: @font_color;
            }
        }
    }
</style>
