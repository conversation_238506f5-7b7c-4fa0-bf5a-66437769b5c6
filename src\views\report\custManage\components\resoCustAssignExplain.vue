<!--
 * @Description: 说明弹框
 * @Author: danpeng.gao
 * @Date: 2023-03-23 13:26:04
 * @LastEditors: danpeng.gao
 * @LastEditTime: 2023-09-21 13:39:50
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="800px"
        title="统计说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <template #default>
            <b>此报表用于统计公司分配的leads客户、公募20万客户，在分配给投顾的跟进情况。</b><br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span>
                ①首次拜访日期：分给第一任投顾后首次联系的时间（取自沟通记录）</span
            >
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span>
                ②是否及时处理：是（分给第一任投顾后在一小时内有联系客户）；否（分给第一任投顾后在一小时内未联系客户）</span
            >
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span>
                ③成交：是（有发生高端成交，且交易已确认）；否（未发生高端成交，或交易未确认）</span
            >
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span> ④首次成交日期：私募首次成交的确认日期</span>
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span> ⑤成交周期（天）=首次高端成交日期-分配日期</span>
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span>
                ⑥成交数据均取自高端交易记录表，如成交金额为外币，转换为人民币</span
            >
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span>
                ⑦是否重复：客户分配后，是否存在因为重复客户原因被划走</span
            >
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span>
                ⑧几手：一手（分配投顾=当前投顾）；二手（分配投顾≠当前投顾）</span
            >
            <br />
        </template>

        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">关闭</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { useVisible } from '@/views/common/scripts/useVisible'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()

    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })
</script>

<style lang="less" scoped>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 8px;
        text-align: left;
        border: 1px solid black;
    }

    th {
        background-color: #f2f2f2;
    }

    .middle-content {
        padding: 10px 0;
    }
</style>
