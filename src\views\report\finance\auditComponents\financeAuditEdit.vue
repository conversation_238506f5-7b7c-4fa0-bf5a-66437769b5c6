<!--
 * @Description: 添加产品弹框
 * @Author: chaohui.wu
 * @Date: 2023-03-24 21:07:35
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-27 19:14:16
 * @FilePath: /crm-template/src/views/components/dialogCommon/adjustAmt/adjustAmtIndex.vue
 * @ 当前未解耦，有时间解耦后可提取到dialogCommomn中
 400px
-->
<template>
    <crm-dialog
        v-model="editDialogVisible"
        width="600px"
        height="500px"
        :border="true"
        :title="title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <el-form
            v-if="financeAuditEdit"
            ref="ruleFormRef"
            :model="ruleForm"
            label-width="150px"
            :rules="rules"
            status-icon
        >
            <el-row>
                <el-col :span="24">
                    <el-form-item prop="fundCode" label="产品代码" style="margin-left: 10%">
                        <crm-input
                            v-model="ruleForm.fundCode"
                            :clearable="false"
                            :style="{ width: '200px', opacity: 0.5, cursor: 'not-allowed' }"
                            readonly
                        ></crm-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <br />
            <el-row>
                <el-col :span="24">
                    <el-form-item prop="fundCode" label="产品名称" style="margin-left: 10%">
                        <crm-input
                            v-model="ruleForm.fundName"
                            :clearable="false"
                            :style="{ width: '200px', opacity: 0.5, cursor: 'not-allowed' }"
                            readonly
                        ></crm-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <br />
            <el-row>
                <el-col :span="24">
                    <el-form-item prop="fundCode" label="所投子基金名称" style="margin-left: 10%">
                        <crm-input
                            v-model="ruleForm.subFundName"
                            :clearable="false"
                            :style="{ width: '200px', opacity: 0.5, cursor: 'not-allowed' }"
                            readonly
                        ></crm-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <br />
            <el-row>
                <el-col :span="24">
                    <el-form-item prop="feeType" label="费用类型" style="margin-left: 10%">
                        <crm-input
                            v-model="ruleForm.feeType"
                            :clearable="false"
                            :style="{ width: '200px', opacity: 0.5, cursor: 'not-allowed' }"
                            readonly
                        ></crm-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <br />
            <el-row>
                <el-col :span="24">
                    <el-form-item prop="preAmountTax" label="计提金额" style="margin-left: 10%">
                        <crm-input
                            v-model="ruleForm.preAmountTax"
                            :clearable="false"
                            :style="{ width: '200px', opacity: 0.5, cursor: 'not-allowed' }"
                            readonly
                        ></crm-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <br />
            <el-row>
                <el-col :span="24">
                    <el-form-item prop="preAmount" label="去税计提金额" style="margin-left: 10%">
                        <crm-input
                            v-model="ruleForm.preAmount"
                            :clearable="false"
                            :style="{ width: '200px', opacity: 0.5, cursor: 'not-allowed' }"
                            readonly
                        ></crm-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <br />
            <el-row>
                <el-col :span="24">
                    <el-form-item
                        prop="realAmountTax"
                        label="实际结算金额"
                        style="margin-left: 10%"
                    >
                        <crm-input
                            v-model="ruleForm.realAmountTax"
                            :clearable="true"
                            :style="{ width: '200px' }"
                            @input="updateDeviation"
                        ></crm-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <br />
            <el-row>
                <el-col :span="24">
                    <el-form-item
                        prop="realAmount"
                        label="去税实际结算金额"
                        style="margin-left: 10%"
                    >
                        <crm-input
                            v-model="computedRealAmount"
                            :clearable="false"
                            readonly
                            :style="{ width: '120px', opacity: 0.5, cursor: 'not-allowed' }"
                        ></crm-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <br />
            <el-row>
                <el-col :span="24">
                    <el-form-item prop="deviation" label="误差(%)" style="margin-left: 10%">
                        <el-input
                            v-model="computedDeviation.value"
                            :input-style="{
                                color: computedDeviation.color,
                                width: '120px',
                                opacity: 0.5,
                                cursor: 'not-allowed'
                            }"
                            :clearable="false"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <br />
            <el-row>
                <el-col :span="24">
                    <el-form-item prop="preStartdt" label="计提日期区间" style="margin-left: 10%">
                        <crm-input
                            v-model="ruleForm.preStartdt"
                            :clearable="false"
                            :style="{
                                width: 'calc(50% - 10%)',
                                opacity: 0.5,
                                cursor: 'not-allowed'
                            }"
                            readonly
                        />
                        <span style="margin-right: 10px; margin-left: 10px">至</span>
                        <crm-input
                            v-model="ruleForm.preEnddt"
                            :clearable="false"
                            :style="{
                                width: 'calc(50% - 10%)',
                                opacity: 0.5,
                                cursor: 'not-allowed'
                            }"
                            readonly
                        />
                    </el-form-item>
                </el-col>
            </el-row>
            <br />
            <el-row>
                <el-col :span="24">
                    <el-form-item prop="settlePeriod" label="结算周期" style="margin-left: 10%">
                        <crm-input
                            v-model="ruleForm.settlePeriod"
                            :clearable="true"
                            readonly
                            :style="{ width: '200px', opacity: 0.5, cursor: 'not-allowed' }"
                        ></crm-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <br />
            <el-row>
                <el-col :span="24">
                    <el-form-item prop="remark" label="备注" style="margin-left: 10%">
                        <crm-input
                            v-model="ruleForm.remark"
                            :clearable="true"
                            :style="{ width: '200px' }"
                        ></crm-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24"> </el-col>
            </el-row>
            <br />
            <br />
            <el-row>
                <el-col :span="12">
                    <el-form-item style="margin-left: 5%">
                        <el-button type="primary" @click="submitForm(ruleFormRef)">
                            提交
                        </el-button>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item style="margin-left: 5%">
                        <el-button type="primary" @click="resetForm(ruleFormRef)">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { fetchRes } from '@/utils/index'
    import { dataList } from './data/labelData'
    import {
        financeAuditInsert,
        financeAuditUpdate
    } from '@/api/project/report/finance/financeAudit/financeAudit'
    import { CSSProperties } from 'vue/dist/vue'
    const {
        productType,
        fundCode,
        fundName,
        feeType,
        preAmount,
        preAmountTax,
        realAmount,
        realAmountTax,
        deviation,
        preStartdt,
        preEnddt,
        settlePeriod,
        settleYear,
        settlePeriodDetail,
        remark
    } = dataList
    const loadingFlag = ref<boolean>(false)
    // 表单数据共用的实体类
    interface RuleForm {
        id: string
        productType: string
        fundCode: string
        fundName: string
        subFundName: string
        feeType: string
        preAmount: string
        preAmountTax: string
        realAmount: string
        realAmountTax: string
        deviation: string
        preStartdt: string
        preEnddt: string
        settlePeriod: string
        settleYear: string
        settlePeriodDetail: string
        remark: string
        settleStartDt: string
        settleEndDt: string
    }

    const ruleFormRef = ref<FormInstance>()
    const ruleForm = reactive<RuleForm>({
        id: '',
        productType: '',
        fundCode: '',
        fundName: '',
        subFundName: '',
        feeType: '',
        preAmount: '',
        preAmountTax: '',
        realAmount: '',
        realAmountTax: '',
        deviation: '',
        preStartdt: '',
        preEnddt: '',
        settlePeriod: '',
        settleYear: '',
        settlePeriodDetail: '',
        remark: '',
        settleStartDt: '',
        settleEndDt: ''
    })
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            financeAuditData?: {
                id: string
                productType: string
                fundCode: string
                fundName: string
                subFundName: string
                feeType: string
                preAmount: string
                preAmountTax: string
                realAmount: string
                realAmountTax: string
                deviation: string
                preStartdt: string
                preEnddt: string
                settlePeriod: string
                settlePeriodType: string
                settleYear: string
                settlePeriodDetail: string
                remark: string
                settleStartDt: string
                settleEndDt: string
            }
            financeAuditEdit?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            financeAuditData: () => {
                return {
                    id: '',
                    productType: '',
                    fundCode: '',
                    fundName: '',
                    subFundName: '',
                    feeType: '',
                    preAmount: '',
                    preAmountTax: '',
                    realAmount: '',
                    realAmountTax: '',
                    deviation: '',
                    preStartdt: '',
                    preEnddt: '',
                    settlePeriod: '',
                    settlePeriodType: '',
                    settleYear: '',
                    settlePeriodDetail: '',
                    remark: '',
                    settleStartDt: '',
                    settleEndDt: ''
                }
            },
            financeAuditEdit: false
        }
    )

    watchEffect(() => {
        console.log(props.financeAuditData.productType)
        ruleForm.id = props.financeAuditData.id
        ruleForm.productType = props.financeAuditData.productType
        ruleForm.fundCode = props.financeAuditData.fundCode
        ruleForm.fundName = props.financeAuditData.fundName
        ruleForm.subFundName = props.financeAuditData.subFundName
        ruleForm.feeType = props.financeAuditData.feeType
        ruleForm.preAmount = props.financeAuditData.preAmount
        ruleForm.preAmountTax = props.financeAuditData.preAmountTax
        ruleForm.realAmount = props.financeAuditData.realAmount
        ruleForm.realAmountTax = props.financeAuditData.realAmountTax
        ruleForm.deviation = props.financeAuditData.deviation
        ruleForm.preStartdt = props.financeAuditData.preStartdt
        ruleForm.preEnddt = props.financeAuditData.preEnddt
        const settlePeriodQueryType = props.financeAuditData.settlePeriodType
        let settlePeriodDetailQuery = props.financeAuditData.settlePeriodDetail
        if (
            props.financeAuditData.settlePeriodDetail == '1' &&
            settlePeriodQueryType == 'quarterly'
        ) {
            settlePeriodDetailQuery = '一季度'
        }
        if (
            props.financeAuditData.settlePeriodDetail == '2' &&
            settlePeriodQueryType == 'quarterly'
        ) {
            settlePeriodDetailQuery = '二季度'
        }
        if (
            props.financeAuditData.settlePeriodDetail == '3' &&
            settlePeriodQueryType == 'quarterly'
        ) {
            settlePeriodDetailQuery = '三季度'
        }
        if (
            props.financeAuditData.settlePeriodDetail == '4' &&
            settlePeriodQueryType == 'quarterly'
        ) {
            settlePeriodDetailQuery = '四季度'
        }
        ruleForm.settlePeriod = props.financeAuditData.settlePeriod
        ruleForm.remark = props.financeAuditData.remark
        ruleForm.settleStartDt = props.financeAuditData.settleStartDt
        ruleForm.settleEndDt = props.financeAuditData.settleEndDt
    })

    // 在父组件的 <script> 部分
    const getColor = (deviation: string) => {
        const parsedDeviation = parseFloat(deviation)
        return isNaN(parsedDeviation) || Math.abs(parsedDeviation) <= 5 ? 'green' : 'red'
    }
    // 弹窗标题配置
    const title = computed(() => {
        if (props.financeAuditEdit) {
            return '修改审核'
        }
    })

    // 计算属性：去税实际结算金额
    const computedRealAmount = computed({
        get: () =>
            ruleForm.realAmountTax ? Number(Number(ruleForm.realAmountTax) / 1.06).toFixed(2) : '', // 计算去税金额
        set: newValue => {
            ruleForm.realAmountTax = newValue ? (Number(newValue) * 1.06).toFixed(2) : '' // 设置实际结算金额
        }
    })

    // 误差计算属性
    const computedDeviation = computed(() => {
        const preAmountTaxValue = parseFloat(ruleForm.preAmountTax) || 0
        const realAmountTaxValue = parseFloat(ruleForm.realAmountTax) || 0
        if (realAmountTaxValue === 0) {
            return { value: '0.00', color: 'black' }
        } // 避免除以零
        const deviation = (
            ((preAmountTaxValue - realAmountTaxValue) / realAmountTaxValue) *
            100
        ).toFixed(2)
        // 确定颜色
        const color = Math.abs(parseFloat(deviation)) > 5 ? 'red' : 'green'
        return { value: deviation, color }
    })

    // 更新偏差值的方法
    const updateDeviation = () => {
        // 确保 computedDeviation 计算属性被调用，以更新偏差值
        const deviationData = computedDeviation.value
        ruleForm.deviation = deviationData.value // 更新 ruleForm.deviation
    }

    const inputStyle: CSSProperties = {
        color: computedDeviation.value.color,
        width: '120px',
        opacity: 0.5 as any, // TypeScript 需要将 number 断言为 any 来赋值给 CSSProperties
        cursor: 'not-allowed'
    }

    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const editDialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })
    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()
    const rules = reactive<FormRules<RuleForm>>({
        fundCode: [
            { required: false, message: '请输入产品代码', trigger: 'blur' },
            { pattern: /^.{0,6}$/, message: '产品代码不能超过6个字符', trigger: 'blur' }
        ]
    })
    //提交方法
    const submitForm = async (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        await formEl.validate((valid, fields) => {
            if (valid) {
                financeAuditSubmit(ruleForm)
            } else {
                console.log('error submit!', fields)
            }
        })
    }
    //重置方法
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        formEl.resetFields()
    }
    /**
     * 审核新增方法
     * @param params
     */
    function financeAuditSubmit(params: any) {
        console.log('params' + JSON.stringify(params))
        fetchRes(financeAuditUpdate(params), {
            successCB: (res: any) => {
                loadingFlag.value = false
                editDialogVisible.value = false
                return emit('callback')
            },
            errorCB: (res: any) => {
                loadingFlag.value = false
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => {
                loadingFlag.value = false
            },
            successTxt: '修改成功',
            failTxt: '',
            fetchKey: ''
        })
    }
    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        editDialogVisible.value = false
        // formEl.resetFields()
    }
</script>

<style lang="less" scoped>
    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }

    .error-message {
        font-size: 12px;
        color: red;
    }
</style>
