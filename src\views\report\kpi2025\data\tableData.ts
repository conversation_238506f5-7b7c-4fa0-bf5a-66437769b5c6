/*
 * @Description: IPS面访记录表格列定义
 * @Author: AI Assistant
 * @Date: 2024-12-19
 * @FilePath: /src/views/report/kpi2025/data/tableData.ts
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: IPS面访记录表格数据
 * @return {*}
 */
export const ipsVisitTableColumn: TableColumnItem[] = [
    {
        label: '投顾客户号',
        key: 'conscustno',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '客户姓名',
        key: 'custName',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '25年初IPS存量',
        key: 'initialIpsAmt',
        minWidth: 140,
        formatter: formatTableValue
    },
    {
        label: '25年初关联的主账户',
        key: 'initialMainAccount',
        minWidth: 160,
        formatter: formatTableValue
    },
    {
        label: '是否完成IPS面访',
        key: 'isIpsVisitComplete',
        minWidth: 140,
        formatter: formatTableValue
    },
    {
        label: '完成日期',
        key: 'completeDate',
        minWidth: 120,
        formatter: formatTableValue,
        sortable: 'custom'
    },
    {
        label: '完成时的存量',
        key: 'completeAmt',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '完成投顾code',
        key: 'completeConsCode',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '完成投顾',
        key: 'completeConsName',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '完成中心',
        key: 'completeCenter',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '完成区域',
        key: 'completeArea',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '完成分公司',
        key: 'completeBranch',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '25年初投顾code',
        key: 'initialConsCode',
        minWidth: 140,
        formatter: formatTableValue
    },
    {
        label: '25年初投顾',
        key: 'initialConsName',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '25年初中心',
        key: 'initialCenter',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '25年初区域',
        key: 'initialArea',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '25年初分公司',
        key: 'initialBranch',
        minWidth: 120,
        formatter: formatTableValue
    }
]
