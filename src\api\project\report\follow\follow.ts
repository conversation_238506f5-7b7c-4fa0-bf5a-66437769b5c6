import { axiosRequest } from '@/utils/request'
import type {
    QueryFollowStatisticRequest,
    ExportFollowStatisticRequest,
    ModifyFollowStatisticRequest
} from './type/request'
import type { FollowStatisticInitResponse } from './type/response'

// 跟进统计列表查询
export function queryFollowStatistic(data: QueryFollowStatisticRequest) {
    return axiosRequest({
        url: '/api/report/followstatistic/query',
        method: 'post',
        data
    })
}

// 跟进统计列表导出
export function exportFollowStatistic(data: ExportFollowStatisticRequest) {
    return axiosRequest({
        url: '/api/report/followstatistic/export',
        method: 'post',
        data,
        responseType: 'json'
    })
}

// 跟进统计初始化
export function followStatisticInit() {
    return axiosRequest({
        url: '/api/report/followstatistic/initdata',
        method: 'post'
    })
}

// 跟进统计修改
export function modifyFollowStatistic(data: ModifyFollowStatisticRequest) {
    return axiosRequest({
        url: '/api/report/followstatistic/modify',
        method: 'post',
        data
    })
}
