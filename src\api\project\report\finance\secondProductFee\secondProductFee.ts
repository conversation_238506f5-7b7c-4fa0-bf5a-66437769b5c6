import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { SecondProductFeeParam } from './type/apiReqType.js'

/**
 * @description: 二级产品的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const SecondProductFeeQuery = (params: SecondProductFeeParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/secondProductFee/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 二级产品的导出接口
 * @return {*}
 */
export const SecondProductFeeExport = (params: SecondProductFeeParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/secondProductFee/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 详细页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const SecondProductFeeDetailQuery = (params: SecondProductFeeParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/secondProductFee/queryDetail',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 明细页面的导出
 * @return {*}
 */
export const SecondProductFeeDetailExport = (params: SecondProductFeeParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/secondProductFee/exportdetail',
            method: 'post',
            data: params
        })
    )
}
