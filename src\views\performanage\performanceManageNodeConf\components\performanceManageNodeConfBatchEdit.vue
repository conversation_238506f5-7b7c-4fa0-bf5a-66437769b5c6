<!--
 * @Description: 修改弹框
 * @Author: jianjian.yang
 * @Date: 2024-07-26 10:07:35
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="500px"
        height="900px"
        :border="true"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            
            <!-- 编辑的弹框 -->
            <el-form
                ref="ruleFormRef"
                :model="formList"
                :rules="rules"
                status-icon
            >
               
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="promoteDate"
                            style="margin-top: 15px; margin-bottom: 15px;"
                        >
                            <el-checkbox v-model="promoteDateVisible" label="管理日期" style="width: 140px;margin-right: 10px;" :true-value="true" :false-value="false"/>
                            <el-date-picker
                                v-if="promoteDateVisible"
                                v-model="formList.promoteDate"
                                class="popperClass"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                                @change="handleExaimneMonth"
                            >
                            </el-date-picker>
                        </el-form-item>
                     </el-col>
                </el-row>
                        
                
                <el-row>
                    <el-col>
                        <el-form-item 
                            prop="periodExplain" 
                            style="margin-top: 15px; margin-bottom: 15px;">
                            <el-checkbox v-model="periodExplainVisible" label="考核周期" style="width: 140px;margin-right: 10px;" :true-value="true" :false-value="false"/>
                            <crm-select
                                v-if="periodExplainVisible"
                                v-model="formList.periodExplain"
                                :placeholder="periodExplain.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="periodExplain.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="startDt"
                            style="margin-top: 15px; margin-bottom: 15px;"
                        >
                            <el-checkbox v-model="startDtVisible" label="开始时间" style="width: 140px;margin-right: 10px;" :true-value="true" :false-value="false"/>
                            <el-date-picker
                                v-if="startDtVisible"
                                v-model="formList.startDt"
                                class="popperClass"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item 
                            prop="exaimneNode" 
                            style="margin-top: 15px; margin-bottom: 15px;">
                            <el-checkbox v-model="exaimneNodeVisible" label="考核节点" style="width: 140px;margin-right: 10px;" :true-value="true" :false-value="false"/>
                            <el-date-picker
                                v-if="exaimneNodeVisible"
                                v-model="formList.exaimneNode"
                                class="popperClass"
                                size="small"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                                @change="handleExaimneMonth"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="calcDepartment"
                            style="margin-top: 15px; margin-bottom: 15px;"
                        >
                        <el-checkbox v-model="calcDepartmentVisible" label="计入分公司" style="width: 140px;margin-right: 10px;" :true-value="true" :false-value="false"/>
                        <crm-select
                                v-if="calcDepartmentVisible"
                                v-model="formList.calcDepartment"
                                :placeholder="calcDepartment.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="calcDepartment.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col>
                        <el-form-item
                            prop="calcNewDepartment"
                            style="margin-top: 15px; margin-bottom: 15px;"
                        >
                        <el-checkbox v-model="calcNewDepartmentVisible" label="计入净新增分公司" style="width: 140px;margin-right: 10px;" :true-value="true" :false-value="false"/>
                        <crm-select
                                v-if="calcNewDepartmentVisible"
                                v-model="formList.calcNewDepartment"
                                :placeholder="calcNewDepartment.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="calcNewDepartment.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item
                            prop="remark"
                            style="margin-top: 15px; margin-bottom: 40px;"
                        >
                            <el-checkbox 
                            v-model="remarkVisible"
                            label="备注" 
                            style="width: 140px;margin-right: 10px;" 
                            :true-value="true" 
                            :false-value="false"/>
                            <crm-input
                                v-if="remarkVisible"
                                v-model="formList.remark"
                                :clearable="true"
                                :style="{ width: '250px', height: '25px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row>
                    <el-col>
                        <el-form-item
                            style="margin-bottom: 25px;"
                        >
                            <Text
                                type="info"
                                size="small" 
                                style="font-size: 14px;margin-bottom: 25px;line-height: 20px;">
                                说明：
                                选择修改「管理日期、开始时间、备注」信息，保存时未选择/填写内容，按清除字段原有的数据处理
                            </Text>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 45%;">
                            <el-button type="primary" @click="submitForm(ruleFormRef)">
                                确认
                            </el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 55%">
                            <el-button type="primary" @click="handleClose(ruleFormRef)"
                                >取消</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { dataList } from '../data/labelData'
    import {
        performanceManageNodeConfBatchUpdate
    } from '@/api/project/performanage/performanceManageNodeConf/performanceManageNodeConf'
    const { calcDepartment, calcNewDepartment, periodExplain } = dataList
    const loadingFlag = ref<boolean>(false)
    
    const promoteDateVisible = ref<boolean>(false)
    const periodExplainVisible = ref<boolean>(false)
    const startDtVisible = ref<boolean>(false)
    const exaimneNodeVisible = ref<boolean>(false)
    const calcDepartmentVisible = ref<boolean>(false)
    const calcNewDepartmentVisible = ref<boolean>(false)
    const remarkVisible = ref<boolean>(false)

    class FormList {
        promoteDate = ''
        periodExplain = ''
        startDt = ''
        exaimneNode = ''
        calcDepartment = ''
        calcNewDepartment = ''
        remark = ''
    }

    const formList = reactive<any>(new FormList())

    const ruleFormRef = ref<FormInstance>()

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                ids: string[]
                title: string
            }
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    ids: [] as string[],
                    type: '',
                    title: ''
                }
            },
        }
    )


    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()
    const rules = reactive<FormRules<FormList>>({
    })

    //提交方法
    const submitForm = async (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }

        await formEl.validate((valid, fields) => {
            if (valid) {
                console.log('submit!', fields)
                performanceManageNodeConfSubmit()
            } else {
                console.log('error submit!', fields)
            }
        })
    }
    //重置方法
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        formEl.resetFields()
    }

    /**
     * 提交方法
     * @param params
     */
    const performanceManageNodeConfSubmit = async () => {
        
        if(!periodExplainVisible.value 
        && !promoteDateVisible.value 
        && !calcDepartmentVisible.value 
        && !calcNewDepartmentVisible.value
        && !startDtVisible.value 
        && !exaimneNodeVisible.value 
        && !remarkVisible.value) {
            ElMessage({
                message: '请勾选要修改的字段',
                type: 'warning',
                duration: 2000
            })
            return
        }
        
        if(periodExplainVisible.value
            && !formList.periodExplain) {
                ElMessage({
                message: '考核周期不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if(exaimneNodeVisible.value
            && !formList.exaimneNode) {
                ElMessage({
                message: '考核节点不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if(calcDepartmentVisible.value
            && !formList.calcDepartment) {
                ElMessage({
                message: '计入分公司字段不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if(calcNewDepartmentVisible.value
            && !formList.calcNewDepartment) {
                ElMessage({
                message: '计入净新增分公司字段不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }
        const requestParams = {
            ids: props.transData.ids,
            promoteDate: promoteDateVisible.value ? formList.promoteDate : null,
            periodExplain: periodExplainVisible.value ? formList.periodExplain : null,
            startDt: startDtVisible.value ? formList.startDt : null,
            exaimneNode: exaimneNodeVisible.value ? formList.exaimneNode : null,
            calcDepartment: calcDepartmentVisible.value ? formList.calcDepartment : null,
            calcNewDepartment: calcNewDepartmentVisible.value ? formList.calcNewDepartment : null,
            remark: remarkVisible.value ? formList.remark : null
        }

        const res: any = await performanceManageNodeConfBatchUpdate(requestParams)
        if (res.code === 'C030000') {
            ElMessage({
                type: 'success',
                message: res.description
            })
            loadingFlag.value = false
            dialogVisible.value = false
            return emit('callback')
        }
        if (res.code !== 'C030000') {
            ElMessage({
                type: 'error',
                message: res?.description || '请求失败'
            })
        }
    }

    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        dialogVisible.value = false
        // formEl.resetFields()
    }

    

    const handleExaimneMonth = (value: string) => {
        changePerfomanceMonth(formList.promoteDate, formList.exaimneNode)
    }

    const changePerfomanceMonth = (promoteDate: string, exaimneNode: string) => {
        if(!promoteDate || !exaimneNode) {
            formList.exaimneMonth = null
            return 
        }
        const promoteYear = promoteDate.substring(0, 4)
        const promoteMonth = promoteDate.substring(4, 6)
        const exaimneNodeYear = exaimneNode.substring(0, 4)
        const exaimneNodeMonth = exaimneNode.substring(4, 6)
        let month = (Number(exaimneNodeYear) - Number(promoteYear)) * 12 + (Number(exaimneNodeMonth) - Number(promoteMonth))
        const day = promoteDate.substring(6, 8)
        if(promoteYear < '2023') {
            console.log('promoteYear', promoteYear)
            if(Number(day) > 10) {
                month -= 1
            }
        }else if(Number(day) > 15) {
            month -= 1
        }
        formList.exaimneMonth = month + Number(formList.adjustManageServingMonth)
    }
    
    // onBeforeMount(() => {
        
    // })
</script>

<style lang="less" scoped>
    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
