<!--
 * @Description: 带连续点的折线图,面积图，平滑曲线图
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-21 19:36:37
 * @FilePath: /crm-asset-web/src/components/charts/DotLineChart.vue
 *  
-->
<template>
    <chart-wrapper :width="width" :height="height" :options="chartOptions" v-bind="$attrs" />
</template>
<script setup lang="ts">
    import { watch, ref } from 'vue'
    import { deepMergeObj } from './scripts/methods'
    import { lineChartConfig } from '@/components/charts/scripts/chartOptions.js'
    import { dateTrans, addUnit } from '@/utils/index'

    interface Props {
        width?: string
        height?: string
        chartData?: any
    }
    const props = withDefaults(defineProps<Props>(), {
        width: '100%',
        height: '300px',
        chartData: () => {
            return {
                seriesData: [
                    {
                        name: '推荐配置',
                        data: [150, 230, 224, 218, 135, 147, 260],
                        type: 'line',
                        symbol: 'none',
                        showArea: false
                    },
                    {
                        name: '沪深300',
                        data: [115, 220, 285, 310, 170, 165, 240],
                        type: 'line',
                        symbol: 'none'
                    },
                    {
                        name: '优化后配置',
                        data: [185, 290, 245, 320, 170, 215, 200],
                        type: 'line',
                        symbol: 'none'
                    }
                ],
                xData: [
                    '20140102',
                    '20150102',
                    '20170621',
                    '20170816',
                    '20191231',
                    '20200305',
                    '20230403'
                ]
            }
        }
    })

    const xLen = computed(() => {
        return xData.value.length
    })
    const yLen = computed(() => {
        const val0: any = seriesData?.value[0] ?? { data: [] }
        return val0?.data?.length ?? 0
    })
    // // 需要对比3条线找到其中的最大最小值
    // const yDataObj = computed(() => {
    //     const dataTpl = props.chartData?.seriesData.map((item: any) => {
    //         const dataTpl = item.data.map((sotre: any) => Number(sotre))
    //         return {
    //             max: Math.max(...dataTpl),
    //             min: Math.min(...dataTpl)
    //         }
    //     })
    //     // 获取其中最大的值
    //     const maxTpl = dataTpl.reduce((curItem: number, item: any) => {
    //         return curItem > item.max ? curItem : item.max
    //     }, 0)
    //     // 获取其中最小的值
    //     const minTpl = dataTpl.reduce((curItem: number, item: any) => {
    //         return curItem < item.min ? curItem : item.min
    //     }, 0)
    //     const maxNum = maxTpl > 0 ? maxTpl * 1.1 : maxTpl * 0.9
    //     const minNum = minTpl > 0 ? minTpl * 0.9 : minTpl * 1.1
    //     // return {
    //     //     max: Math.floor(maxTpl / maxNum) * maxNum,
    //     //     min: Math.ceil(minTpl / minNum) * minNum
    //     // }
    //     return {
    //         maxNum,
    //         minNum,
    //         max: Math.floor(maxNum / 20) * 20,
    //         min: Math.ceil(minNum / 20) * 20
    //     }
    // })

    // x轴分段展示算法
    const splitArrayIntoChunksWithIndices = (arr: any[], chunkSize: number) => {
        const chunks = []
        const indices = []
        for (let i = 0; i < arr.length; i += chunkSize) {
            const chunk = arr.slice(i, i + chunkSize)
            chunks.push(chunk)
            indices.push([i, i + chunk.length - 1])
        }
        return [chunks, indices]
    }
    const showArr: any = ref([])
    const initData = () => {
        const cutNum = 5
        const [resultChunks, resultIndices] = splitArrayIntoChunksWithIndices(
            xData.value,
            Math.floor(xLen.value / cutNum)
        )
        // 等于%0时展示
        if (xLen.value % cutNum === 0) {
            showArr.value = [
                resultIndices[0][0],
                resultIndices[1][0],
                resultIndices[2][0],
                resultIndices[2][1],
                resultIndices[3][1],
                resultIndices[4][1]
            ]
        } else if (resultIndices && resultIndices[0]) {
            showArr.value = [
                resultIndices[0][0],
                resultIndices[1][0],
                resultIndices[2][0],
                resultIndices[3][0],
                resultIndices[4][0],
                resultIndices[4][1]
            ]
        }
    }

    /**
     * @description: 监听树值变化并更新数据
     * @return {*}
     */
    const xData = ref([])
    const seriesData = ref([])
    watchEffect(() => {
        xData.value = props.chartData.xData
        seriesData.value = props.chartData.seriesData.map((item: any, index: number) => {
            if (item.showArea) {
                item = {
                    ...item,
                    areaStyle: {
                        origin: 'end',
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: 'rgba(208, 2, 27, 0.2)' // 100% 处的颜色
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(208, 2, 27, 0.2)' // 0% 处的颜色
                                }
                            ],
                            global: false // 缺省为 false
                        }
                    }
                }
            }
            return item
        })
        initData()
    })

    const chartOptions = ref()
    const getChartOptions = () => {
        initData()
        chartOptions.value = deepMergeObj(lineChartConfig, {
            grid: {
                left: 40,
                right: 10,
                top: 30,
                bottom: 20
            },
            legend: {
                orient: 'horizontal',
                icon: 'roundRect',
                itemWidth: 12,
                itemHeight: 2,
                top: 2,
                left: 0,
                itemGap: 22,
                textStyle: {
                    color: '#9497A7',
                    fontSize: 12,
                    fontFamily: 'Microsoft YaHei,微软雅黑'
                }
            },
            xAxis: {
                type: 'category',
                data: xData,
                // splitNumber: 6,
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: '#e0e0e3',
                        width: 1
                    }
                },
                axisLabel: {
                    interval: (index: number, value: string) => {
                        // 需要获取当前列表的长度并计算中值的下标
                        if (showArr.value.includes(index)) {
                            return true
                        }
                        if (xLen.value <= 6) {
                            return true
                        }
                        if (index === xLen.value - 1) {
                            return true
                        }
                    },
                    formatter: (value: any, index: number) => {
                        if (index === 0) {
                            return `{a|${dateTrans(value, 'yyyy-MM')}}`
                        }
                        if (index === xLen.value - 1) {
                            return `{b|${dateTrans(value, 'yyyy-MM')}}`
                        }
                        return `{c|${dateTrans(value, 'yyyy-MM')}}`
                    },
                    rich: {
                        a: {
                            fontSize: 12,
                            fontFamily: 'Microsoft YaHei,微软雅黑',
                            color: '#999999',
                            padding: [0, 0, 0, 45]
                        },
                        b: {
                            fontSize: 12,
                            fontFamily: 'Microsoft YaHei,微软雅黑',
                            color: '#999999',
                            padding: [0, 45, 0, 0]
                        },
                        c: {
                            fontSize: 12,
                            fontFamily: 'Microsoft YaHei,微软雅黑',
                            color: '#999999'
                        }
                    }
                }
            },
            yAxis: {
                type: 'value',
                splitNumber: 4,
                // max: yDataObj.value.maxNum,
                // min: yDataObj.value.minNum,
                // interval: (yDataObj.value.max - yDataObj.value.min) / 5,
                axisLabel: {
                    formatter: (value: any, index: number) => {
                        if (index === 0) {
                            return `{a|${value.toFixed(0)}%}`
                        } else if (index === 5) {
                            return `{b|${value.toFixed(0)}%}`
                        }
                        return `{c|${value.toFixed(0)}%}`
                    },
                    rich: {
                        a: {
                            fontSize: 12,
                            fontFamily: 'Microsoft YaHei,微软雅黑',
                            color: '#999999',
                            padding: [0, 0, 8, 0]
                        },
                        b: {
                            fontSize: 12,
                            fontFamily: 'Microsoft YaHei,微软雅黑',
                            color: '#999999',
                            padding: [10, 0, 0, 0]
                        },
                        c: {
                            fontSize: 12,
                            fontFamily: 'Microsoft YaHei,微软雅黑',
                            color: '#999999'
                        }
                    }
                }
            },
            series: seriesData
        })
        return chartOptions
    }
    watch(
        [() => props.chartData],
        (newVal, oldVal) => {
            if (newVal) {
                getChartOptions()
            } else {
                chartOptions.value = null
            }
        },
        {
            immediate: true
            // deep: true
        }
    )
</script>
<style lang="less" scoped></style>
