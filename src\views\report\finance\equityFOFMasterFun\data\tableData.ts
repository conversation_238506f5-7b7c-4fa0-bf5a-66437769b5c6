/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const equityFOFMasterFunOrderTableColumn: TableColumnItem[] = [
    {
        key: 'zlid',
        label: '投资指令ID',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'cpdm',
        label: 'FOF基金代码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'cpmc',
        label: 'FOF基金名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'cpjc',
        label: 'FOF基金简称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'tzjjdm',
        label: '所投子基金代码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'tzjjmc',
        label: '所投子基金名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'tzjjjc',
        label: '所投子基金简称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'jyrq',
        label: '交易日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'kfr',
        label: '开放日',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'tzlx',
        label: '交易类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'jz',
        label: '净值',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'tzfe',
        label: '交易份额',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'qrje',
        label: '交易确认金额',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'sgf',
        label: '申购费',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'shf',
        label: '赎回费',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'yjbc',
        label: '业绩报酬',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
