<template>
    <el-select-v2
        :class="`crm_select_v2 ${selectClass}`"
        popper-class="crm_select_v2_popper crm_select_popper_v2_vue3"
        size="small"
        :style="`width: ${width};z-index: 20;`"
        :collapse-tags="$attrs.multiple"
        v-bind="$attrs"
        :options="
            optionList?.map(item => {
                return {
                    ...item,
                    label: item[labelFormat],
                    value: item[valueFormat]
                }
            })
        "
    >
    </el-select-v2>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmSelectV2',
        props: {
            optionList: {
                type: Array,
                default: () => []
            },
            labelFormat: {
                type: String,
                default: 'label'
            },
            valueFormat: {
                type: String,
                default: 'value'
            },
            width: {
                type: String,
                default: '100%'
            },
            // 样式类型，可选默认、fund两种样式，fund为基金研究里面的样式
            styleType: {
                type: String,
                default: ''
            }
        },
        computed: {
            selectClass() {
                switch (this.styleType) {
                    case 'fund':
                        return 'crm_select_fund_v2'
                    case 'tableHeader':
                        return 'crm_table_select_v2'
                    default:
                        return ''
                }
            }
        }
    })
</script>

<style lang="less" scoped>
    .crm_select_v2 {
        min-width: 135px;
        width: 100%;
        height: 24px;

        // 基金研究里面的下拉选择样式
        &.crm_select_fund_v2 {
            display: inline-flex;
            align-items: center;
            height: 20px;
            padding-left: 11px;
            margin: 0;
            line-height: 20px;
        }
        &.table_select {
            :deep(.el-select) {
                --el-input-border-color: transparent;

                text-align: right;

                &__wrapper {
                    background-color: transparent;
                }

                &__inner {
                    padding-right: 12px;
                    text-align: center;

                    &:focus {
                        border-color: @border_focus;
                    }

                    &::placeholder {
                        font-size: 12px;
                        // font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                        // font-weight: bold;
                        color: @font_color;
                    }
                }

                .el-input__suffix {
                    right: 12px;
                }
            }
        }
    }

    .crm_select_v2_popper {
        .el-select-dropdown__item {
            height: 30px;
            padding: 0 15px;
            font-size: 12px;
            line-height: 30px;
        }

        .el-select-dropdown__item.selected {
            color: @theme_main;
        }

        .el-popper__arrow {
            display: none;
        }
    }

    .crm_select_popper_v2_vue3 {
        &.el-select-dropdown {
            position: relative;
            margin: 0;
            border: none;
        }
    }

    .el-select-v2__wrapper {
        border: none !important;
    }
</style>

<style lang="less">
    .crm_select_v2 {
        .el-select-v2__placeholder {
            width: calc(100% - 42px) !important;
            color: #434549 !important;
        }
    }

    .crm_select_v2_popper {
        .el-select-dropdown__option-item {
            padding-left: 15px;
            padding-right: 15px;
        }
        .el-select-dropdown .el-select-dropdown__option-item.is-selected::after {
            width: 0;
        }
    }

    .crm_table_select_v2 {
        .is-focused,
        .is-hovering {
            border-color: var(--el-border-color) !important;
        }
        .el-select-v2__wrapper {
            border-color: transparent;
        }
    }

    .el-select {
        --el-select-input-focus-border-color: @border_focus;
    }

    .el-select-dropdown__item {
        &.selected {
            color: @theme_main;
            background-color: @bg_main_selected;
        }

        &:not(.is-disabled):hover,
        &:not(.is-disabled).hover {
            color: @theme_main;
            background-color: @bg_main_hover;
        }
    }
</style>
