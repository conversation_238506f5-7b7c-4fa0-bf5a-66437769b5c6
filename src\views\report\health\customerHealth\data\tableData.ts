import { formatTableValue } from '@/utils/index.js'
export const customerHealthTableColumn = [
    { key: 'sdate', label: '统计日期', width: 100, formatter: formatTableValue },
    { key: 'u1Name', label: '中心', width: 120, formatter: formatTableValue },
    { key: 'u2Name', label: '区域', width: 120, formatter: formatTableValue },
    { key: 'u3Name', label: '分公司', width: 120, formatter: formatTableValue },
    { key: 'consname', label: '投顾姓名', width: 100, formatter: formatTableValue },
    { key: 'conscode', label: '员工编码', width: 100, formatter: formatTableValue },
    { key: 'userLevel', label: '层级', width: 80, formatTableValue },
    { key: 'isKpi', label: '是否参与统计', width: 100, formatter: formatTableValue },
    { key: 'worktype', label: '员工状态', width: 80, formatter: formatTableValue },
    {
        key: 'gdClCnt',
        label: '24年底高端存量客户数',
        width: 150,
        type: 'slot',
        slotName: 'custCount',
        formatter: formatTableValue
    },
    {
        key: 'healthCnt',
        label: '健康客户数',
        width: 100,
        type: 'slot',
        slotName: 'healthCount',
        formatter: formatTableValue
    },
    { key: 'kpiResult', label: '考核结果比例', width: 120, formatter: formatTableValue },
    { key: 'kpiResultCnt', label: '考核结果人数', width: 120, formatter: formatTableValue },
    { key: 'kpiTarget', label: '考核目标比例', width: 120, formatter: formatTableValue },
    { key: 'kpiTargetCnt', label: '考核目标人数', width: 120, formatter: formatTableValue },
    { key: 'isReach', label: '目标达标状态', width: 120, formatter: formatTableValue }
]

export const showTableColumn = (keyArr: string[], tableColumnList: any[]) => {
    return tableColumnList.filter(item => {
        if (item.visible) {
            return false
        }
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
