<!--
 * @Author: xing.zhou01
 * @Date: 2022-03-17 15:13:37
 * @LastEditTime: 2022-03-27 18:02:05
 * @LastEditors: xing.zhou01
 * @Description:
-->
<!--
* 带有label的多选
* @eg:
    <crm-checkbox-item
        v-model="data.dqdm"
        label="所属地区"
        :option-list="locationOptions"
        value-format="key"
        need-all
        @checkAll="(val) => {data.dqdm = val }"
    />
-->
<template>
    <div class="crm-input-item crm_checkbox_item">
        <div v-if="label" class="label">{{ label ? `${label}：` : '' }}</div>
        <div class="value">
            <crm-checkbox v-bind="$attrs" />
        </div>
        <slot name="suffix" />
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmCheckboxItem',
        props: {
            label: {
                type: String,
                default: ''
            }
        }
    })
</script>
<style lang="less" scoped>
    .crm-input-item {
        display: flex;
        align-items: flex-start;
        margin: 15px 30px 0 0;

        .label {
            min-width: 72px;
            font-size: 12px;
            line-height: 26px;
            color: @font_color;
            text-align: right;
            white-space: nowrap;
        }

        .value {
            flex: 1;
            font-size: 12px;
        }
    }

    .crm_checkbox_item {
        width: 100%;
    }
</style>
