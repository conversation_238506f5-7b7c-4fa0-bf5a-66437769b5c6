<template>
    <div class="report-list-module">
        <table-wrap-cust
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <table class="table-cust-select">
                    <tbody>
                        <tr>
                            <td>
                                <!-- 投顾 -->
                                <LabelItemCust label="投顾" minwidth="160px">
                                    <ReleatedSelect
                                        ref="newlySelect"
                                        v-model="queryForm.orgvalue"
                                        :organization-list="organizationList"
                                        :cons-list-default="consultList"
                                        :default-org-code="orgCodeDefault"
                                        :default-cons-code="consCodeDefault"
                                        :cons-status="consStatus"
                                        :module="module"
                                        code-width="120px"
                                        :add-all="true"
                                    ></ReleatedSelect>
                                </LabelItemCust>
                            </td>
                            <!-- 核算产品类型 -->
                            <td>
                                <label-item-cust :label="accountProductType.label" minwidth="160px">
                                    <crm-select
                                        v-model="queryForm.accountProductType"
                                        :placeholder="accountProductType.placeholder"
                                        filterable
                                        clearable
                                        label-format="label"
                                        value-format="key"
                                        :option-list="accountProductTypeSelectList"
                                        :style="{ width: '150px' }"
                                    />
                                </label-item-cust>
                            </td>
                            <!-- 客户号 -->
                            <td colspan="2">
                                <LabelItemCust
                                    :label="consCustNo.label"
                                    minwidth="160px"
                                    labelwidth="120px"
                                >
                                    <crm-input
                                        v-model="queryForm.consCustNo"
                                        :placeholder="consCustNo.placeholder"
                                        :clearable="true"
                                        :style="{ width: '150px' }"
                                    />
                                    <div style="margin-left: 100px">
                                        <el-checkbox
                                            v-model="queryForm.containRelationAccount"
                                            label="查询关联账户"
                                        />
                                    </div>
                                </LabelItemCust>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <!-- 产品名称 -->
                                <LabelItemCust label="产品名称" minwidth="160px">
                                    <autocomplete
                                        v-model="queryForm.label"
                                        :search-func="autoAllProd"
                                        :style="{ width: '325px' }"
                                        @handleSet="handleProdCode"
                                    />
                                </LabelItemCust>
                            </td>
                            <td>
                                <!-- 组合code -->
                                <LabelItemCust label="组合代码" minwidth="160px">
                                    <crm-input
                                        v-model="queryForm.combCode"
                                        placeholder="请输入"
                                        :clearable="true"
                                        :style="{ width: '150px' }"
                                    />
                                </LabelItemCust>
                            </td>
                            <!-- 产品分类 -->
                            <td>
                                <label-item-cust
                                    label="产品分类"
                                    minwidth="160px"
                                    labelwidth="120px"
                                >
                                    <crm-select
                                        v-model="queryForm.fundType"
                                        :placeholder="fundType.placeholder"
                                        filterable
                                        clearable
                                        label-format="label"
                                        value-format="key"
                                        :option-list="fundType.selectList"
                                        :style="{ width: '150px' }"
                                    />
                                </label-item-cust>
                            </td>
                            <td>
                                <!-- 预约ID -->
                                <LabelItemCust :label="preId.label" minwidth="160px">
                                    <crm-input
                                        v-model="queryForm.preId"
                                        :placeholder="preId.placeholder"
                                        :clearable="true"
                                        :style="{ width: '150px' }"
                                    />
                                </LabelItemCust>
                            </td>
                        </tr>
                        <tr>
                            <!-- 在职状态 -->
                            <td>
                                <label-item-cust :label="workType.label" minwidth="160px">
                                    <crm-select
                                        v-model="queryForm.workType"
                                        :placeholder="workType.placeholder"
                                        filterable
                                        clearable
                                        label-format="label"
                                        value-format="key"
                                        :option-list="workType.selectList"
                                        :style="{ width: '150px' }"
                                    />
                                </label-item-cust>
                            </td>
                            <td>
                                <!-- 投顾code -->
                                <LabelItemCust :label="userId.label" minwidth="160px">
                                    <crm-input
                                        v-model="queryForm.userId"
                                        :placeholder="userId.placeholder"
                                        :clearable="true"
                                        :style="{ width: '150px' }"
                                    />
                                </LabelItemCust>
                            </td>
                            <!-- 是否计入成交次数 -->
                            <td>
                                <label-item-cust
                                    label="是否计入成交次数"
                                    minwidth="160px"
                                    labelwidth="120px"
                                >
                                    <crm-select
                                        v-model="queryForm.markFlag"
                                        :placeholder="markFlag.placeholder"
                                        filterable
                                        clearable
                                        label-format="label"
                                        value-format="key"
                                        :option-list="markFlag.selectList"
                                        :style="{ width: '150px' }"
                                    />
                                </label-item-cust>
                            </td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </template>
            <template #operationBtns>
                <button-cust size="small" :radius="true" plain @click.stop="queryList"
                    >查询</button-cust
                >
                <button-cust
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    plain
                    @click="exportHandle"
                    >导出</button-cust
                >
            </template>
            <template #tableContentMiddle>
                <base-table-cust
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                >
                </base-table-cust>
            </template>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrap-cust>
    </div>
</template>

<script lang="ts" setup>
    import { fetchRes } from '@/utils'
    import { dataList } from './data/labelData'
    import { tradeNumTableColumn } from './data/tableData'

    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import {
        // eslint-disable-next-line camelcase
        TradeNumQuery,
        TradeNumExport
    } from '@/api/project/reward/tradeNum/tradeNum'

    import { getMenuPermission } from '@/api/project/common/common'
    import { autoAllProd, fetchConstant } from '@/api/project/common/common'
    import Autocomplete from '@/components/moduleBase/Autocomplete.vue'
    import { REWARD_TRADE_NUM_OPER_PERMISSION } from '@/constant/rewardConst'
    import BaseTableCust from '@/views/modBusiness/pageModule/components/BaseTableCust.vue'
    import LabelItemCust from '@/views/modBusiness/pageModule/components/LabelItemCust.vue'
    import TableWrapCust from '@/views/modBusiness/pageModule/components/TableWrapCust.vue'
    import ButtonCust from '@/views/modBusiness/pageModule/components/ButtonCust.vue'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import { defineComponent } from 'vue'
    import { ACCOUNT_PRODUCT_TYPE_NEW } from '@/constant/hbConstant'

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)
    const { accountProductType, consCustNo, preId, workType, userId, fundType, markFlag } = dataList
    const module = ref<string>('071614')
    const exportShow = ref<boolean>(false)

    const listLoading = ref<boolean>(false)
    const consStatus = ref<string>('0') //包含离职人员

    const accountProductTypeSelectList = ref<object[]>([])

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        accountProductType = ''
        consCustNo = ''
        containRelationAccount = false
        preId = ''
        productCode = ''
        workType = ''
        userId = ''
        label = ''
        combCode = ''
        fundType = ''
        markFlag = ''
    }
    const queryForm = reactive(new QueryForm())

    const handleProdCode = (val: { label: string; productCode: string }) => {
        queryForm.productCode = val.productCode
        queryForm.label = val.label
    }
    /**
     * @description: 分页数据
     * @return {*}
     */
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 100
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return tradeNumTableColumn
    })

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            orgCode: queryForm.orgvalue.orgCode,
            consCode: queryForm.orgvalue.consCode,
            consCustNo: queryForm.consCustNo,
            accountProductType: queryForm.accountProductType,
            containRelationAccount: queryForm.containRelationAccount ? '1' : '0',
            productCode: queryForm.productCode,
            preId: queryForm.preId,
            workType: queryForm.workType,
            userId: queryForm.userId,
            markFlag: queryForm.markFlag,
            combCode: queryForm.combCode,
            fundType: queryForm.fundType,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(TradeNumQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                tableData.value = rows
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const params = {
            orgCode: queryForm.orgvalue.orgCode,
            consCode: queryForm.orgvalue.consCode,
            consCustNo: queryForm.consCustNo,
            accountProductType: queryForm.accountProductType,
            containRelationAccount: queryForm.containRelationAccount ? '1' : '0',
            productCode: queryForm.productCode,
            preId: queryForm.preId,
            workType: queryForm.workType,
            userId: queryForm.userId,
            markFlag: queryForm.markFlag,
            combCode: queryForm.combCode,
            fundType: queryForm.fundType
        }
        const res: any = await TradeNumExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }

    const initData = async () => {
        const params = {
            menuCode: module.value
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode === REWARD_TRADE_NUM_OPER_PERMISSION.EXPORT &&
                            item.display === '1'
                        ) {
                            exportShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })

        fetchRes(fetchConstant({ typeCodeList: [ACCOUNT_PRODUCT_TYPE_NEW] }), {
            successCB: (resObj: any) => {
                const { constantTypeMap } = resObj
                accountProductTypeSelectList.value = constantTypeMap[ACCOUNT_PRODUCT_TYPE_NEW]
            },
            errorCB: () => {},
            catchCB: () => {},
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }
    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        initData()
        fetchConsOrgList('', module.value)
    })
</script>
<style lang="less" scoped>
    .table-cust-select {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        tr {
            background-color: #ffffff;

            td {
                border: 1px solid #dddddd;
            }
        }
    }
</style>
