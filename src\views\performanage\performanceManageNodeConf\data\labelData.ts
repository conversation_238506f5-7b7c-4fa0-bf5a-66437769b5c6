/*
 * @Description: 定义搜索的label列表
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:36:23
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/labelData.ts
 *
 */
export const dataList = {
    // 投顾code
    userId: {
        label: '投顾code',
        placeholder: '投顾code'
    },
    // 0-否 1-是
    status: {
        label: '是否考核',
        placeholder: '请选择是否考核',
        selectList: [
            {
                key: '1',
                label: '是'
            },
            {
                key: '0',
                label: '否'
            }
        ]
    },
    // 考核周期
    periodExplain: {
        label: '考核周期',
        placeholder: '请选择考核周期',
        selectList: [
            {
                key: '1',
                label: '试用-3M'
            },
            {
                key: '2',
                label: '试用-6M'
            },
            {
                key: '3',
                label: '跟踪-12M'
            },
            {
                key: '4',
                label: '正式-年中'
            },
            {
                key: '5',
                label: '正式-年末'
            },
            {
                key: '6',
                label: '观察期-12M以上'
            },
            {
                key: '7',
                label: '观察期-12M以内'
            },
            {
                key: '8',
                label: '理1B'
            }
        ]
    },
    // 计入分公司
    calcDepartment: {
        label: '计入分公司',
        placeholder: '请选择计入分公司',
        selectList: [
            {
                key: '1',
                label: '是'
            },
            {
                key: '0',
                label: '否'
            }
        ]
    },
    // 计入净新增分公司
    calcNewDepartment: {
        label: '计入净新增分公司',
        placeholder: '请选择计入净新增分公司',
        selectList: [
            {
                key: '1',
                label: '是'
            },
            {
                key: '0',
                label: '否'
            }
        ]
    },
    // 层级
    conslevel: {
        label: '层级',
        placeholder: '请选择层级',
        selectList: [
            {
                key: '3',
                label: '分总'
            },
            {
                key: '4',
                label: '区域执行副总'
            },
            {
                key: '5',
                label: '区域总'
            }
        ]
    },
    exaimneNode: {
        label: '考核节点',
        placeholder: ['开始日期', '结束日期']
    },
    curMonthLevel:{
        label: '职级',
        placeholder: '请选择职级',
        selectList: ref([]),
    },
    orgData: {
        label: '管理人员',
        placeholder: '请选择管理人员'
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
