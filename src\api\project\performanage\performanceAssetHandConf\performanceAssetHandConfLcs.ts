import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { performanceAssetHandConfParam } from './type/apiReqType.js'
/**
 * @description: 手工存续D查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const performanceAssetHandConfLcsQuery = (params: performanceAssetHandConfParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConfLcs/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 手工存续D导出接口
 * @return {*}
 */
export const performanceAssetHandConfLcsExport = (params: performanceAssetHandConfParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConfLcs/export',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 审核-init
 * @param {object} params
 * @return {*}
 */
export const getAuth = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConfLcs/getAuth',
            method: 'post'
        })
    )
}
