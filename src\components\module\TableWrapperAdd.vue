<!--
* 通用table列表页面容器
* author: cleardWu
* @slot searchArea, 搜索条件区域
* @slot operationBtns, 操作按钮栏
* @slot tableContentMiddle, 表格列表区域
* @slot tableContentBottom, table列表底部区域, 分页器等 
* @link PrivateEquity 

-->
<template>
    <div class="crm-table-wrapper-add">
        <div :class="['crm_table_top', { pack_up: !topAreaExpand }]">
            <div v-if="showSearchArea" class="search_area">
                <div class="top">
                    <section class="search-input-top">
                        <slot name="searchInputTop" />
                        <div class="search-btn">
                            <el-button plain @click="$emit('searchFn')">查询</el-button>
                        </div>
                        <slot name="introTxt" />
                        <!-- <p class="intro-txt">
                            *添加产品范围：好买投顾组合、公募30池产品及在售的公募QDII产品
                        </p> -->
                    </section>
                    <section>
                        <div class="reset-btn">
                            <el-button :text="true" :icon="RefreshRight" @click="$emit('resetFn')"
                                >重置</el-button
                            >
                        </div>
                    </section>
                </div>
                <div ref="searchArea" class="left">
                    <slot name="searchArea" />
                </div>
                <div v-show="showPackupBtn" class="pack_up_btn" @click="handleExpand">
                    <el-icon size="12px"><arrow-up /></el-icon>
                </div>
            </div>
            <div v-if="showOperationBtns" class="operation_btns">
                <slot name="operationBtns" />
            </div>
        </div>
        <div class="crm_table_middle">
            <div class="crm_table_list">
                <slot name="tableContentMiddle" />
            </div>
        </div>
        <div class="crm_table_bottom">
            <slot name="tableContentBottom" />
        </div>
        <slot />
    </div>
</template>

<script>
    import { Search, ArrowUp, RefreshRight } from '@element-plus/icons-vue'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'TableWrapperAdd',
        components: { ArrowUp },
        props: {
            // 搜索条目标签名称的宽度
            labelWidth: {
                type: String,
                default: ''
            },
            // 是否展示操作按钮模块，默认展示
            showOperationBtns: {
                type: Boolean,
                default: true
            },
            showSearchArea: {
                type: Boolean,
                default: true
            }
        },
        emits: ['searchFn', 'resetFn'],
        setup() {
            return { Search, RefreshRight }
        },
        data() {
            return {
                showPackupBtn: true, // 是否显示收起展开按钮
                topAreaExpand: true // 顶部区域是否展开
            }
        },
        watch: {
            labelWidth: {
                handler(newVal) {
                    if (newVal) {
                        this.$nextTick(() => {
                            const labelList =
                                this.$refs.searchArea.querySelectorAll('.crm-input-item .label') ||
                                []
                            labelList.forEach(item => {
                                item.style.width = newVal
                            })
                        })
                    }
                },
                immediate: true
            }
        },
        mounted() {
            this.resizeFn()
            window.addEventListener('resize', this.resizeFn)
        },
        methods: {
            handleExpand() {
                this.topAreaExpand = !this.topAreaExpand
            },
            resizeFn() {
                if (this.$refs.searchArea) {
                    // this.showPackupBtn = this.$refs.searchArea.offsetHeight > 56
                }
            },
            beforeUnmount() {
                window.removeEventListener('resize', this.resizeFn)
            }
        }
    })
</script>

<style lang="less">
    .crm-table-wrapper-add {
        display: flex;
        flex-direction: column;
        width: 100%;
        // height: 100%;
        height: calc(~'@{max_dialog_height} - 56px - 27px');

        .crm_table_top {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            padding: 0;
            // background-color: @bg_main;

            &.pack_up {
                .search_area {
                    .left {
                        height: 0;
                        padding: 0;
                        margin-bottom: 0;
                    }

                    .top {
                        margin-bottom: 17px;
                    }
                }

                .pack_up_btn {
                    i {
                        transform: rotate(180deg);
                    }
                }
            }

            .pack_up_btn {
                position: absolute;
                right: 0;
                bottom: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 16px;
                cursor: pointer;
                background-color: #b5b5bc;

                &:hover {
                    background-color: @theme_main_hover;
                }

                i {
                    color: @font_color_01;
                    transition: all 0.2s;
                }
            }

            .search_area {
                position: relative;
                display: flex;
                flex-flow: column wrap;
                width: 100%;
                overflow: hidden;
                background-color: @font_color_01;
                box-shadow: 0 0 12px 0 rgba(210, 211, 224, 0.47);

                .left {
                    display: flex;
                    flex-wrap: wrap;
                    align-items: flex-start;
                    padding: 5px 5px 0;
                    margin-bottom: 15px;
                    background: linear-gradient(
                        180deg,
                        rgba(181, 180, 186, 0.11) 0%,
                        @font_color_01 100%
                    );
                    transition: height 0.2s;

                    .crm-input-item,
                    .crm-ratio-item {
                        margin-top: 0;

                        .label {
                            min-width: 100px;
                            height: 18px;
                            padding-left: 0;
                            margin-top: 3px;
                            font-family: 'Microsoft YaHei', '微软雅黑';
                            font-size: 12px;
                            font-weight: 400;
                            line-height: 17px;
                            color: @font_color;
                            text-align: left;
                        }
                    }
                    // .crm-ratio-item {
                    //     width: 100%;
                    // }
                }

                .top {
                    display: flex;
                    flex-wrap: wrap;
                    align-items: flex-start;
                    justify-content: space-between;
                    transition: height 0.2s;

                    .search-input-top {
                        display: flex;
                        padding-left: 5px;

                        .intro-txt {
                            height: 22px;
                            padding: 0 8px 0 5px;
                            margin: 12px 0 0 12px;
                            font-family: 'Microsoft YaHei', '微软雅黑';
                            font-size: 11px;
                            font-weight: 400;
                            line-height: 21px;
                            color: #d0021b;
                            background: linear-gradient(
                                360deg,
                                @font_color_01 0%,
                                rgba(208, 2, 27, 0.1) 100%
                            );
                        }

                        .search-btn {
                            height: 22px;
                        }

                        .reset-btn {
                            height: 22px;
                        }

                        .crm-input-item,
                        .crm-ratio-item {
                            padding-bottom: 13px;
                            margin-top: 12px;
                            margin-right: 10px;

                            .label {
                                min-width: auto;
                                height: 18px;
                                padding-left: 0;
                                margin-top: 3px;
                                font-family: 'Microsoft YaHei', '微软雅黑';
                                font-size: 12px;
                                font-weight: 400;
                                line-height: 17px;
                                color: @font_color;
                                text-align: left;
                            }
                        }
                    }
                }

                .right {
                    position: relative;
                    display: inline-flex;
                    align-items: center;
                    width: 100px;

                    &::before {
                        display: block;
                        width: 1px;
                        height: 42px;
                        margin-right: 16px;
                        content: '';
                        background: linear-gradient(
                            to top,
                            @font_color_01,
                            #b5b4b9,
                            @font_color_01
                        );
                    }

                    .el-button {
                        height: 28px;
                        font-size: 12px;
                        line-height: 28px;
                        color: #797979;
                        background-color: @font_color_01;
                        border-color: #e1e0e0;
                        border-radius: 14px;

                        &:hover {
                            color: #686767;
                            background-color: @bg_main;
                            border-color: #c4c4c4;
                        }

                        &:active {
                            color: #4a4a4d;
                            background-color: #e2e4ea;
                            border-color: #c4c4c4;
                        }

                        span {
                            margin-left: 2px;
                        }
                    }
                }

                .crm-input-item {
                    .value {
                        width: 190px;
                    }
                }
            }

            .search-btn {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                height: 22px;
                margin-top: 12px;
                background: @font_color_01;

                .el-button {
                    height: 22px;
                    padding: 2px 10px;
                    font-family: 'Microsoft YaHei', '微软雅黑';
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 22px;
                    color: @font_color;
                    text-shadow: 0 2px 4px rgba(146, 146, 146, 0.47);
                    border-radius: 0;
                    border-radius: 4px;

                    &.is-plain {
                        --el-button-active-border-color: rgba(113, 113, 123, 0.75);
                        --el-button-hover-border-color: rgba(113, 113, 123, 0.75);

                        box-shadow: 0 2px 4px 0 rgba(146, 146, 146, 0.47);
                    }
                }
            }

            .reset-btn {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                height: 22px;
                margin-top: 12px;
                background: @font_color_01;

                .el-button {
                    height: 22px;
                    font-family: 'Microsoft YaHei', '微软雅黑';
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 22px;
                    color: @font_link;
                    border-radius: 0;

                    &.is-plain {
                        --el-button-active-border-color: rgba(113, 113, 123, 0.75);
                        --el-button-hover-border-color: rgba(113, 113, 123, 0.75);

                        box-shadow: 0 2px 4px 0 rgba(146, 146, 146, 0.47);
                    }
                }
            }
        }

        .crm_table_middle {
            flex: 1;
            overflow: hidden;
            background-color: @font_color_01;
            // 通用table列表
            .crm_table_list {
                display: flex;
                height: 100%;
                min-height: 100px;

                .el-table__body {
                    td {
                        border: none;
                    }
                }
            }
        }

        .crm_table_bottom {
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: 99;
            width: 100%;
        }
    }
</style>
