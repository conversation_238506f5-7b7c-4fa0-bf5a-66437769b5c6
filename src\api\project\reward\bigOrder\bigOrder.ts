import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { QueryBigOrderParams } from './type/apiReqType.js'

/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const queryBigOrderList = (params: QueryBigOrderParams): any => {
    return axiosRequest(
        paramsMerge({
            timeout: 60000,
            url: '/reward/bigorder/queryList',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 导出接口
 * @return {*}
 */
export const exportBigOrderList = (params: QueryBigOrderParams) => {
    return axiosRequest(
        paramsMerge({
            timeout: 120000,
            url: '/reward/bigorder/export',
            method: 'post',
            data: params
        })
    )
}
export const bigOrderBatchUpdate = (params: any) => {
    return axiosRequest(
        paramsMerge({
            url: '/reward/bigorder/batchUpdate',
            method: 'post',
            data: params
        })
    )
}

export const bigOrderBatchSettlement = (params: any) => {
    return axiosRequest(
        paramsMerge({
            url: '/reward/bigorder/batchSettlement',
            method: 'post',
            data: params
        })
    )
}
