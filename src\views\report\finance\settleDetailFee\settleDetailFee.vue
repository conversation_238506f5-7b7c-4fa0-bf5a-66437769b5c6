<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            :show-export-btn="true"
            @searchFn="handleLoading"
            @exportFn="exportHandle"
        >
            <template #searchArea>
                <label-item :label="startDt.label">
                    <el-date-picker
                        v-model="queryForm.startDt"
                        type="date"
                        placeholder="选择日期"
                        format="YYYYMMDD"
                        value-format="YYYYMMDD"
                        :style="{ width: '150px' }"
                        @change="handleStartDt"
                    ></el-date-picker>
                </label-item>
                <label-item :label="endDt.label">
                    <el-date-picker
                        v-model="queryForm.endDt"
                        type="date"
                        placeholder="选择日期"
                        format="YYYYMMDD"
                        value-format="YYYYMMDD"
                        :style="{ width: '150px' }"
                        @change="handleEndDt"
                    ></el-date-picker>
                </label-item>
                <label-item :label="fundMan.label">
                    <crm-input
                        v-model="queryForm.fundMan"
                        :placeholder="fundMan.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <label-item :label="fundCode.label">
                    <crm-input
                        v-model="queryForm.fundCode"
                        :placeholder="fundCode.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <label-item :label="fundName.label">
                    <crm-input
                        v-model="queryForm.fundName"
                        :placeholder="fundName.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <label-item :label="subFundName.label">
                    <crm-input
                        v-model="queryForm.subFundName"
                        :placeholder="subFundName.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <label-item :label="custName.label">
                    <crm-input
                        v-model="queryForm.custName"
                        :placeholder="custName.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <label-item :label="conscustNo.label">
                    <crm-input
                        v-model="queryForm.conscustNo"
                        :placeholder="conscustNo.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <label-item :label="productType.label">
                    <crm-select
                        v-model="queryForm.productType"
                        :placeholder="productType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="productType.selectList"
                        :style="{ width: '120px' }"
                        @change="handleProductType"
                    />
                </label-item>
                <label-item :label="feeType.label">
                    <crm-select
                        v-model="queryForm.feeType"
                        :placeholder="feeType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="feeTypeList"
                        :style="{ width: '120px' }"
                        @change="handleFeeType"
                    />
                </label-item>
                <!-- 部门 -->
                <label-item label="部门">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
            </template>

            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                >
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>
    </div>
</template>

<script lang="ts" setup>
    import { downloadFile, fetchRes, message } from '@/utils'
    const stockListStore = useStockListData()
    const { getPageInit } = stockListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(stockListStore)
    import { dataList } from './data/labelData'
    import { settleDetailFeeTableColumn, showTableColumn } from './data/tableData'
    import moment from 'moment' // 假设您使用了 dayjs 来简化日期处理
    import {
        SettleDetailFeeExport,
        SettleDetailFeeQuery
    } from '@/api/project/report/finance/settleDetailFee/settleDetailFee'
    import { useStockListData } from '@/views/common/scripts/stockListData'
    import LabelItem from '@/components/moduleBase/LabelItem.vue'
    import { ElMessage } from 'element-plus'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'

    const {
        startDt,
        endDt,
        fundCode,
        fundMan,
        fundName,
        subFundName,
        custName,
        conscustNo,
        productType,
        feeType
    } = dataList

    const module = ref<string>('B071219')
    const consStatus = ref<string>('1') //不包含离职人员
    const exportShow = ref<boolean>(false)
    const detailShow = ref<boolean>(false)

    const listLoading = ref<boolean>(false)
    const dialogVisible = ref<boolean>(false)

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        startDt = moment().format('YYYYMMDD')
        endDt = moment().add(30, 'day').format('YYYYMMDD')
        fundCode = ''
        fundMan = ''
        productType = '1'
        feeType = '1'
        fundName = ''
        subFundName = ''
        custName = ''
        conscustNo = ''
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
    }
    const queryForm = reactive(new QueryForm())
    /**
     * @description: 上一次点了查询的条件列表
     * @return {*}
     */
    const queryFormAction = new QueryForm()
    const feeTypeList = ref<object[]>(feeType.fixedList)

    const getDefaultQuarterDates = () => {
        const now = new Date()
        const currentYear = now.getFullYear()
        let startMonth, endMonth

        // 根据当前月份确定上个季度的月份范围
        if (now.getMonth() < 3) {
            // 第四季度
            startMonth = 9 // 1月
            endMonth = 11 // 3月
        } else if (now.getMonth() < 6) {
            // 第一季度
            startMonth = 0 // 4月
            endMonth = 2 // 6月
        } else if (now.getMonth() < 9) {
            // 第二季度
            startMonth = 3 // 7月
            endMonth = 5 // 9月
        } else {
            // 第三季度
            startMonth = 6 // 10月
            endMonth = 8 // 12月
        }

        // 创建日期对象数组
        const startDate = new Date(currentYear, startMonth, 1)
        const endDate = new Date(currentYear, endMonth + 1, 0) // 加1 并用0作为天数可以获取到月底的日期
        return { startDate, endDate: endDate }
    }

    // 格式化日期为 YYYYMMDD 格式
    const formatDate = (date: any) => {
        const year = date.getFullYear()
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        return `${year}${month}${day}`
    }

    // 初始化日期范围
    const { startDate, endDate } = getDefaultQuarterDates()
    queryForm.startDt = formatDate(startDate) // 格式化日期函数
    queryForm.endDt = formatDate(endDate)

    const handleStartDt = (val: string) => {
        queryForm.startDt = val
    }
    const handleEndDt = (val: string) => {
        queryForm.endDt = val
    }

    const handleProductType = (val: string) => {
        queryForm.productType = val
        if (val === '1') {
            feeTypeList.value = feeType.fixedList
            queryForm.feeType = '1'
        }
        if (val === '2') {
            feeTypeList.value = feeType.secondList
            queryForm.feeType = '4'
        }
        if (val === '3') {
            feeTypeList.value = feeType.stockList
            queryForm.feeType = '7'
        }
    }
    const handleFeeType = (val: string) => {
        queryForm.feeType = val
    }

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const panelActiveName = ref<string>('myReport')
    const tableData = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            settleDetailFeeTableColumn.map(item => item.key),
            settleDetailFeeTableColumn
        )
    })

    const checkDatepicker = (modelValue: any) => {
        if (!modelValue) {
            ElMessage.error('该日期字段是必填的')
            return false
        }
        return true
    }

    //查詢
    const queryList = async () => {
        if (!checkDatepicker(queryForm.startDt)) {
            return
        }
        if (!checkDatepicker(queryForm.endDt)) {
            return
        }
        listLoading.value = true
        const params = {
            startDt: queryForm.startDt,
            endDt: queryForm.endDt,
            fundCode: queryForm.fundCode,
            fundMan: queryForm.fundMan,
            productType: queryForm.productType,
            feeType: queryForm.feeType,
            fundName: queryForm.fundName,
            subFundName: queryForm.subFundName,
            custName: queryForm.custName,
            conscustNo: queryForm.conscustNo,
            orgCode: queryForm.orgvalue.orgCode,
            consCode: queryForm.orgvalue.consCode,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(SettleDetailFeeQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                // debugger
                tableData.value = rows
                // TODO list接口待添加分页数据
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const pdfUrl = ref<string>('')
    const exportList = async () => {
        const params = {
            startDt: queryForm.startDt,
            endDt: queryForm.endDt,
            fundCode: queryForm.fundCode,
            fundMan: queryForm.fundMan,
            productType: queryForm.productType,
            feeType: queryForm.feeType,
            fundName: queryForm.fundName,
            subFundName: queryForm.subFundName,
            custName: queryForm.custName,
            conscustNo: queryForm.conscustNo,
            orgCode: queryForm.orgvalue.orgCode,
            consCode: queryForm.orgvalue.consCode
        }
        const res: any = await SettleDetailFeeExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }

    /**
     * @description 提示
     */
    onBeforeMount(() => {
        // fetchUserPermissions()
        // queryList()
    })
    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        getPageInit(module.value)
    })
</script>
<style scoped>
    /* 父组件的样式 */
    .ReleatedSelect .crm-select-v2 {
        display: none;
    }
</style>
