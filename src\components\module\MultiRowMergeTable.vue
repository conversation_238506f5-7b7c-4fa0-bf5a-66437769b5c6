<!--
 * @Description: table 表格合并
 * @Author: chaohui.wu
 * @Date: 2023-04-17 18:19:47
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-04-25 09:25:11
 * @FilePath: /crm-asset-web/src/components/module/MultiRowMergeTable.vue
 *  
-->
<template>
    <el-table
        class="theme-table"
        :data="tableData"
        style="width: 100%"
        :span-method="objectSpanMethod"
        :border="true"
    >
        <el-table-column
            v-for="(item, index) in tableHeader"
            :key="index"
            :prop="item.prop"
            :label="item.label"
        ></el-table-column>
    </el-table>
</template>

<script>
    export default {
        name: 'MultiRowMergeTable',
        props: {
            tableHeader: {
                type: [Array],
                default: () => {
                    return [
                        {
                            prop: 'strLabel',
                            label: '策略'
                        },
                        {
                            prop: 'strRatio',
                            label: '占比'
                        },
                        {
                            prop: 'assetTypeLabel',
                            label: '产品类型'
                        },
                        {
                            prop: 'assetTypeValue',
                            label: '占比'
                        },
                        {
                            prop: 'ratioLabel',
                            label: '国内海外'
                        },
                        {
                            prop: 'ratioValue',
                            label: '占比'
                        }
                    ]
                }
            },
            tableData: {
                type: [Array],
                default: () => {
                    return [
                        {
                            strLabel: '多策略',
                            strKey: '0',
                            strRatio: 50,
                            assetTypeLabel: '大类稳健FOF',
                            assetTypeKey: 'fof104',
                            assetTypeValue: 27,
                            ratioLabel: '国内',
                            ratioKey: 'GN',
                            ratioValue: 12,
                            className: 'font-small'
                        },
                        {
                            strLabel: '多策略',
                            strKey: '0',
                            strRatio: 50,
                            assetTypeLabel: '大类稳健FOF',
                            assetTypeKey: 'fof104',
                            assetTypeValue: 27,
                            ratioLabel: '海外',
                            ratioKey: 'HW',
                            ratioValue: 15,
                            className: 'font-small'
                        },
                        {
                            strLabel: '多策略',
                            strKey: '0',
                            strRatio: 50,
                            assetTypeLabel: '大类平衡FOF',
                            assetTypeKey: 'fof106',
                            assetTypeValue: 15,
                            ratioLabel: '海外',
                            ratioKey: 'HW',
                            ratioValue: 15,
                            className: 'font-small'
                        },
                        {
                            strLabel: '多策略',
                            strKey: '0',
                            strRatio: 50,
                            assetTypeLabel: '大类平衡FOF',
                            assetTypeKey: 'fof106',
                            assetTypeValue: 15,
                            ratioLabel: '国内',
                            ratioKey: 'GN',
                            ratioValue: 12,
                            className: 'font-small'
                        },
                        {
                            strLabel: '单策略',
                            strKey: '1',
                            strRatio: 50,
                            assetTypeLabel: '股票型',
                            assetTypeKey: '101',
                            assetTypeValue: 15,
                            ratioLabel: '海外',
                            ratioKey: 'HW',
                            ratioValue: 15,
                            className: 'font-small'
                        },
                        {
                            strLabel: '单策略',
                            strKey: '1',
                            strRatio: 50,
                            assetTypeLabel: '股票型',
                            assetTypeKey: '101',
                            assetTypeValue: 15,
                            ratioLabel: '国内',
                            ratioKey: 'GN',
                            ratioValue: 12,
                            className: 'font-small'
                        },
                        {
                            strLabel: '单策略',
                            strKey: '1',
                            strRatio: 50,
                            assetTypeLabel: '固收与中性',
                            assetTypeKey: '301',
                            assetTypeValue: 15.1,
                            ratioLabel: '国内',
                            ratioKey: 'GN',
                            ratioValue: 12,
                            className: 'font-small'
                        }
                    ]
                }
            },
            mergedColumns: {
                type: [Array],
                default: () => {
                    return ['strLabel', 'strRatio', 'assetTypeLabel', 'assetTypeValue']
                }
            }
        },
        data() {
            return {
                spanMap: {}
            }
        },
        created() {
            this.getSpanArr(this.tableData)
        },
        methods: {
            getSpanArr(data) {
                for (let i = 0; i < data.length; i++) {
                    if (i === 0) {
                        this.mergedColumns.forEach(column => {
                            this.spanMap[column] = {
                                spanArr: [1],
                                pos: 0
                            }
                        })
                    } else {
                        this.mergedColumns.map(column => {
                            if (
                                data[i][column] === data[i - 1][column] &&
                                data[i].strKey === data[i - 1].strKey
                            ) {
                                this.spanMap[column].spanArr[this.spanMap[column].pos] += 1
                                this.spanMap[column].spanArr.push(0)
                            } else {
                                this.spanMap[column].spanArr.push(1)
                                this.spanMap[column].pos = i
                            }
                            return column
                        })
                    }
                }
            },
            objectSpanMethod({ row, column, rowIndex, columnIndex }) {
                if (this.spanMap[column.property]) {
                    const _row = this.spanMap[column.property].spanArr[rowIndex]
                    const _col = _row > 0 ? 1 : 0
                    return {
                        rowspan: _row,
                        colspan: _col
                    }
                }
            }
        }
    }
</script>
