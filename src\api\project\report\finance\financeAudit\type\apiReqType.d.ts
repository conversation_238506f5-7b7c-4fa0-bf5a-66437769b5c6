import { financeAuditBatchReview } from '@/api/project/report/finance/financeAudit/financeAudit'

export {}
declare module './apiReqType' {
    type FinanceAuditAddOrUpdateParam = {
        /**
         * 表主键id
         */
        id: ?number
        /**
         * 产品类型 1.固收产品  2.二级产品 3. 股权产品
         */
        productType?: string
        /**
         * 基金code
         */
        fundCode?: string
        /**
         * 基金code
         */
        fundName?: string
        /**
         * 基金code
         */
        subFundName?: string
        /**
         * 费用类型  1.咨询费-普通口径 2.管理费-存量口径 3.退出费-赎回口径
         * 4. 赎回费(二级产品)  5.管理费(二级产品)  6. 业绩报酬 (二级产品)
         * 7.认购费/设立服务费(股权产品) 8.管理费/咨询服务费 (股权产品) 9.业绩报酬 (股权产品)
         */
        feeType?: string
        /**
         * 计提金额
         */
        preAmount?: BigDecimal
        /**
         * 去税计提金额
         */
        preAmountTax?: BigDecimal
        /**
         * 实际结算金额
         */
        realAmount?: BigDecimal
        /**
         * 去税实际结算金额
         */
        realAmountTax?: BigDecimal
        /**
         * 误差
         */
        deviation?: BigDecimal
        /**
         * 计提开始日期
         */
        preStartdt?: string
        /**
         * 计提结束日期
         */
        preEnddt?: string
        /**
         * 结算周期
         */
        settlePeriod?: string
        /**
         * 结算开始日期
         */
        settleStartDt?: string
        /**
         * 结算结束日期
         */
        settleEndDt?: string
        /**
         * 备注
         */
        remark?: string
        /**
         * 审核状态 0-未提交 1-待审核 2-通过 3-驳回
         */
        auditState?: string
        /**
         * 是否有效 0-无效，1-有效
         */
        recState?: string
        /**
         * 审核人
         */
        auditor?: string
        /**
         * 审核时间
         */
        auditTime?: string
        /**
         * 创建人
         */
        creator?: string
        /**
         * 创建时间
         */
        createTime?: string
        /**
         * 修改人
         */
        modor?: string
        /**
         * 修改时间
         */
        updateTime?: string
    }
    type FeeParam = {
        /**
         * 开始日期
         */
        startDt?: string
        /**
         * 结束日期
         */
        endDt?: string
        /**
         * 基金代码
         */
        fundCode?: string
        /**
         * 基金管理人
         */
        fundMan?: string
        /**
         * 费用类型
         */
        feeType?: string
        /**
         * 产品名称
         */
        fundName?: string
        /**
         * 所投子基金名称
         */
        subFundName?: string
        /**
         * 客户姓名
         */
        custName?: string
        /**
         * 投顾客户号
         */
        conscustNo?: string
        /**
         * 周期
         */
        settlePeriod?: string
        /**
         * 页数
         */
        page?: number
        /**
         * 行数
         */
        rows?: number
    }

    // 批量删除
    type DeleteById = {
        /**
         * id列表
         */
        id?: string
        /**
         * 是否有效 0-无效，1-有效
         */
        recState?: string
    }
    // 通过驳回
    type FinanceAudit = {
        /**
         * 任务id
         */
        id?: string
        /**
         * 审核状态
         */
        auditState?: string
    }

    // 批量删除
    type BatchDeleteId = {
        /**
         * id列表
         */
        taskIdList?: string[]
    }
    // 批量删除
    type BatchReviewId = {
        /**
         * id列表
         */
        taskIdList?: string[]
        /**
         * 审核状态
         */
        auditState?: string
    }
    export {
        FinanceAuditAddOrUpdateParam,
        DeleteById,
        BatchDeleteId,
        BatchReviewId,
        FinanceAudit,
        FeeParam
    }
}
