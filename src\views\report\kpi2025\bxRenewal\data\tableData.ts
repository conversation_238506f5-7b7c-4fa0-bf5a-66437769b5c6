/*
 * @Description: table表格展示
 * @Author: gang.zou
 * @Date: 2024-04-17 17:14:42
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, formatNumber } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const assetRepDownloadTableColumn: TableColumnItem[] = [
    {
        key: 'u1Name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consCode',
        label: '员工编码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'isKpi',
        label: '是否参与考核',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'shouldPayamount',
        label: '应收保费(RMB)',
        width: 120,
        formatter: ({ shouldPayamount }: any) => {
            return formatNumber({ num: shouldPayamount })
        }
    },
    {
        key: 'realPayamount',
        label: '实收保费(RMB)',
        width: 120,
        formatter: ({ realPayamount }: any) => {
            return formatNumber({ num: realPayamount })
        }
    },
    {
        key: 'payPctStr',
        label: '继续率',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
