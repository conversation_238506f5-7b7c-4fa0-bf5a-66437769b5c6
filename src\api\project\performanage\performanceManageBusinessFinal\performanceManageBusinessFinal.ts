import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { performanceManageBusinessFinalParam } from './type/apiReqType.js'

/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const performanceManageBusinessFinalQuery = (
    params: performanceManageBusinessFinalParam
): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/subtotalfinal/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 导出接口
 * @return {*}
 */
export const performanceManageBusinessFinalExport = (
    params: performanceManageBusinessFinalParam
) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/subtotalfinal/export',
            method: 'post',
            data: params
        })
    )
}
