/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const productCoefficientHisTableColumn: TableColumnItem[] = [
    {
        key: 'fundCode',
        label: '产品代码',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'productName',
        label: '产品名称',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'accountProductType',
        label: '核算产品类型',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'foldCoefficient',
        label: '折标系数',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'commissionRate',
        label: '佣金率（%）',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'annuallySetAward',
        label: '年度配置奖',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'secondStockCoeff',
        label: '二级存量折标',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'stockFeeA',
        label: '存续D',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'stockFeeB',
        label: '存续B',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'sfhscxf',
        label: '是否核算存续费',
        width: 110,
        formatter: formatTableValue
    },
    {
        key: 'startDt',
        label: '起始日期',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'endDt',
        label: '结束日期',
        width: 100,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.visible) {
            return false
        }
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
