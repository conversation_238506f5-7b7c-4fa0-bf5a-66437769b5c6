import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { KpiConsulantSumParam } from './type/apiReqType.js'

/**
 * @description:查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const KpiConsulantSumQuery = (params: KpiConsulantSumParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/kpiconsulantsum2025/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 查询接口
 * @return {*}
 */
export const KpiConsulantSumExport = (params: KpiConsulantSumParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/kpiconsulantsum2025/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 查询接口
 * @return {*}
 */
export const KpiConsulantSumInit = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/kpiconsulantsum2025/initdata',
            method: 'post',
            data: null
        })
    )
}
