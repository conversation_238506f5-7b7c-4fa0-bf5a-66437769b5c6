export {}
declare module './apiReqType' {
    type QueryBigOrderParams = {
        /**
         * 交易日期开始
         */
        tradeYear?: string
        /**
         * 交易日期结束
         */
        tradeQuarter?: string
        /**
         * 结算日期开始
         */
        settlementYear?: string
        /**
         * 结算日期结束
         */
        settlementQuarter?: string
        /**
         * 部门编码
         */
        orgCode?: string
        /**
         * 投顾编码 所属投顾控件
         */
        consCode?: string
        /**
         * 投顾编码 输入框
         */
        userId?: string
        /**
         * 核算产品类型
         */
        accountProductType?: string
        /**
         * 产品代码
         */
        productCode?: string
        /**
         * 产品名称
         */
        productName?: string
        /**
         * 大单产品类型
         */
        bigOrderProductType?: string
        /**
         * 客户姓名
         */
        custName?: string
        /**
         * 客户号
         */
        consCustNo?: string
        /**
         * 是否结算
         */
        isSettlement?: string
        /**
         * 预约ID
         */
        preId?: string
        /**
         * 页码
         */
        page?: number
        /**
         * 数据行数
         */
        rows?: number
    }

    type BigOrderBatchUpdateParams = {
        /**
         * 记录ID列表
         */
        ids: string[]
        /**
         * 大单产品类型
         */
        bigOrderProductType?: string
        /**
         * 剩余份额
         */
        availVol?: number
        /**
         * 剩余本金
         */
        availCost?: number
        /**
         * 佣金率(%)
         */
        commissionRate?: number
        /**
         * 业绩系数
         */
        achieveCoeff?: number
        /**
         * 管理奖金基数
         */
        manageCoeffCommission?: number
    }

    export { QueryBigOrderParams, BigOrderBatchUpdateParams }
}
