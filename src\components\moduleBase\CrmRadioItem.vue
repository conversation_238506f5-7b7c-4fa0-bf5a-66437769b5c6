<!--
* 带label的输入框
* @eg:
    <crm-radio-item
        v-model="data.cpmc"
        label="产品名称"
        placeholder="输入代码或名称模糊搜索"
    />
-->
<template>
    <div class="crm-ratio-item" :style="{ width }">
        <div class="label">{{ label ? `${label}：` : '' }}</div>
        <div class="value">
            <crm-radio v-bind="$attrs" />
        </div>
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmRadioItem',
        props: {
            label: {
                type: String,
                default: ''
            },
            width: {
                type: String,
                default: '100%'
            }
        }
    })
</script>

<style lang="less">
    .crm-ratio-item {
        display: flex;
        align-items: flex-start;
        margin: 10px 30px 0 0;
        // width: 100%;

        .label {
            min-width: 72px;
            padding-left: 10px;
            font-size: 12px;
            line-height: 26px;
            color: @font_color;
            text-align: right;
            white-space: nowrap;
        }

        .value {
            flex: 1;
            font-size: 12px;
        }
    }
</style>
