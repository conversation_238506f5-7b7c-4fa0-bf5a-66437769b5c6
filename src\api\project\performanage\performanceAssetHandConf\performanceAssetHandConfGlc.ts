import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { performanceAssetHandConfParam } from './type/apiReqType.js'
/**
 * @description: 手工存续D查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const performanceAssetHandConfGclQuery = (params: performanceAssetHandConfParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConfGlc/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 手工存续D导出接口
 * @return {*}
 */
export const performanceAssetHandConfGclExport = (params: performanceAssetHandConfParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConfGlc/export',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 审核-init
 * @param {object} params
 * @return {*}
 */
export const getAuth = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/assetHandConfGlc/getAuth',
            method: 'post'
        })
    )
}
