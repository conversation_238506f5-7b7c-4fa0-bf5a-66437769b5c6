﻿<!-- eslint-disable no-undef -->
<!-- eslint-disable vue/require-default-prop -->
<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="box-card2" style="height: 100%">
        <div :id="vId" style="width: 100%; height: 100%"></div>
    </div>
</template>

<script>
    import * as echarts from 'echarts'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'JxzSumBarChartBoard',
        props: {
            // eslint-disable-next-line vue/require-default-prop
            vId: {
                type: String
            },
            title: {
                type: String,
                default: 'title'
            },
            // eslint-disable-next-line vue/require-default-prop
            dataY: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            jxzSumData: {
                type: Array
            }
        },
        data() {
            return {
                // option: {
                //     title: {
                //         text: this.title,
                //         textStyle: {
                //             fontSize: 10
                //         }
                //     },
                //     grid: {
                //         left: '2%',
                //         right: '10%',
                //         bottom: '50%',
                //         top: '20%',
                //         containLabel: true
                //     },
                //     legend: {
                //         y: 'bottom',
                //         x: 'center',
                //         itemWidth: 6,
                //         itemHeight: 6,
                //         itemGap: 10
                //     },
                //     xAxis: {
                //         type: 'value',
                //         axisLabel: {
                //             interval: 0,
                //             textStyle: {
                //                 fontSize: 6
                //             }
                //         }
                //     },
                //     yAxis: {
                //         type: 'category',
                //         axisLabel: {
                //             interval: 0,
                //             textStyle: {
                //                 fontSize: 6
                //             }
                //         },
                //         data: this.dataY
                //     },
                //     series: [
                //         {
                //             type: 'bar',
                //             barwidth: '40%', //柱的宽度
                //             barGap: 0,
                //             label: {
                //                 show: true, // 是否可见
                //                 rotate: 0, // 旋转角度
                //                 position: 'top', // 显示位置
                //                 textStyle: {
                //                     fontSize: 6
                //                 }
                //             },
                //             // data: [88, 92]
                //             data: this.jxzSumData
                //         }
                //     ]
                // }
            }
        },
        watch: {
            jxzSumData: {
                handler(newVal) {
                    if (newVal) {
                        this.$nextTick(() => {
                            this.renderGaugeChart()
                        })
                    }
                },
                immediate: true,
                deep: true
            }
        },
        mounted() {
            this.renderGaugeChart()
        },
        methods: {
            renderGaugeChart() {
                const option = {
                    title: {
                        text: this.title,
                        padding: 2,
                        textStyle: {
                            fontSize: 13,
                            color: 'white'
                        }
                    },
                    grid: {
                        left: '0%',
                        right: '10%',
                        bottom: '50%',
                        top: '20%',
                        containLabel: true
                    },
                    legend: {
                        y: 'bottom',
                        x: 'center',
                        itemWidth: 6,
                        itemHeight: 6,
                        itemGap: 10
                    },
                    //下载图片
                    toolbox: {
                        show: true,
                        feature: {
                            saveAsImage: {
                                pixelRatio: 15
                            } //值越大分辨率越高
                        }
                    },
                    // 提示框
                    tooltip: {
                        trigger: 'axis',
                        position: [125, 20],
                        axisPointer: {
                            type: 'shadow'
                        },
                        showContent: true,
                        // 自定义提示  显示千位符
                        formatter(params) {
                            let relVal = params[0].name + '(单位:万)'
                            for (let i = 0, l = params.length; i < l; i++) {
                                relVal += `<br/>${params[i].marker}${'净值'} : ${params[
                                    i
                                ].value.toLocaleString()}`
                            }
                            return relVal
                        }
                    },
                    xAxis: {
                        type: 'value',
                        show: false,
                        axisLabel: {
                            interval: 0,
                            textStyle: {
                                fontSize: 10
                            }
                        }
                    },
                    yAxis: {
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            textStyle: {
                                fontSize: 10,
                                color: 'white'
                            }
                        },
                        data: this.dataY
                    },
                    series: [
                        {
                            type: 'bar',
                            barwidth: '30%', //柱的宽度
                            barGap: 0,
                            label: {
                                show: true, // 是否可见
                                rotate: 0, // 旋转角度
                                position: 'top', // 显示位置
                                textStyle: {
                                    fontSize: 8
                                }
                            },
                            //配置样式
                            itemStyle: {
                                //每个柱子显示不同的颜色
                                normal: {
                                    color: function (params) {
                                        const colorList = ['#e54c5e', '#d9d9d9']
                                        return colorList[params.dataIndex]
                                    }
                                }
                            },

                            data: this.jxzSumData
                        }
                    ]
                }
                const gaugeChart = echarts.init(document.getElementById(this.vId))
                gaugeChart.setOption(option)
            }
        }
    })
</script>

<style scoped>
    .board-s {
        height: 95%;
        margin: 2px;
    }

    .box-card2 {
        height: 100%;
        padding: 15px 0 0 2px;
        font-size: 12px;
        color: white;
        background: #4874cb;
        border: 2px solid white;
    }

    .card-title {
        display: block;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }
</style>
