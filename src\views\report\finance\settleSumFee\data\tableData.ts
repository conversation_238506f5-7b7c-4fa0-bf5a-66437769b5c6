/*
 * @Description: table表格展示
 * @Author: gang.zou
 * @Date: 2024-05-09 17:14:42
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const settleSumFeeTableColumn: TableColumnItem[] = [
    {
        key: 'u1Name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '部门',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundMan',
        label: '管理人',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'settleFeeTax',
        label: '最终入账金额',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'settleFee',
        label: '去税最终入账金额',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
