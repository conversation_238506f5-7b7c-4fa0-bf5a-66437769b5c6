import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { SettleSumFeeParam } from './type/apiReqType.js'

/**
 * @description: 股权产品的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const SettleSumFeeQuery = (params: SettleSumFeeParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/settleSumFee/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 股权产品的导出接口
 * @return {*}
 */
export const SettleSumFeeExport = (params: SettleSumFeeParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/settleSumFee/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 详细页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const SettleSumFeeDetailQuery = (params: SettleSumFeeParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/settleSumFee/queryDetail',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 明细页面的导出
 * @return {*}
 */
export const SettleSumFeeDetailExport = (params: SettleSumFeeParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/settleSumFee/exportdetail',
            method: 'post',
            data: params
        })
    )
}
