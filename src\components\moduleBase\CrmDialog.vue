<!--
 * @Description: 弹窗
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-01 18:09:53
 * @FilePath: /crm-asset-web/src/components/moduleBase/CrmDialog.vue
 * @crmClass 1.crm-tabs-card1 定制tabs样式groupProducu
-->

<template>
    <el-dialog :class="`crm-dialog ${crmClass}`" v-bind="$attrs">
        <template v-for="(slotName, index) in slotList" :key="`dialog-slot${index}`" #[slotName]>
            <slot v-if="slotName" :key="slotName" :name="slotName" />
        </template>
    </el-dialog>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmDialog',
        props: {
            slotList: {
                type: Array,
                default: () => {
                    // header、footer、default
                    return ['default', 'footer']
                }
            },
            crmClass: {
                type: String,
                default: ''
            }
        }
    })
</script>
<style lang="less">
    .crm-dialog,
    .el-dialog {
        min-width: 360px;

        .el-dialog__header {
            width: 100%;
            padding: 12px 20px;
            margin-right: 0;
            background-color: @bg_main_01;

            .el-dialog__title {
                font-size: 15px;
                font-weight: 700;
            }
        }

        .el-dialog__headerbtn {
            top: 0;
            right: 0;
            font-size: 18px;

            &:hover {
                i {
                    color: @theme_main_hover;
                }
            }

            i {
                color: #303133;
                transition: color 0.2s;
            }
        }

        .el-dialog__body {
            box-sizing: border-box;
            max-height: @max_dialog_height;
            padding: 20px;
            overflow-y: auto;
        }

        .el-dialog__footer {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 100%;
            padding: 10px 20px;
            background-color: @bg_main_01;

            .el-button {
                height: 30px;
                line-height: 30px;
            }
        }

        &.dialog-default {
            .el-dialog__body {
                box-sizing: border-box;
                max-height: @max_dialog_height;
                padding: 20px;
                overflow-y: auto;
            }
        }

        .crm_single_form {
            .el-form-item__label {
                padding-right: 4px;
                color: @font_color_02;
                text-align: left;
            }

            .el-form-item {
                margin-bottom: 0;
            }
        }
    }
</style>
