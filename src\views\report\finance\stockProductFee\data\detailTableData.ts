/*
 * @Description: table表格展示
 * @Author: gang.zou
 * @Date: 2024-05-09 17:14:42
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, formatNumber } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const stockProductFeeDetailTableColumn: TableColumnItem[] = [
    {
        key: 'fundCode',
        label: '产品代码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundName',
        label: '产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'manager',
        label: '管理人',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'productYpe',
        label: '产品类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ageSubject',
        label: '公司协议主体',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'subFundName',
        label: '底层产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'saleAmt',
        label: '销量(认缴)',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'feeType',
        label: '费用类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'mfeeRate',
        label: '结算频率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'startDt',
        label: '结算起始日',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'endDt',
        label: '结算结束日',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'finDt',
        label: '结算日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'feeRate',
        label: '费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'feeTax',
        label: '计提金额',
        width: 120,
        formatter: ({ feeTax }: any) => {
            return formatNumber({ num: feeTax })
        }
    },
    {
        key: 'fee',
        label: '去税计提金额',
        width: 120,
        formatter: ({ fee }: any) => {
            return formatNumber({ num: fee })
        }
    },
    {
        key: 'feeTax1',
        label: '切分金额(含税)',
        width: 120,
        formatter: ({ feeTax1 }: any) => {
            return formatNumber({ num: feeTax1 })
        }
    },
    {
        key: 'fee1',
        label: '切分金额(不含税)',
        width: 120,
        formatter: ({ fee1 }: any) => {
            return formatNumber({ num: fee1 })
        }
    },
    {
        key: 'subject',
        label: '切分主体',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u1Name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '部门1',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '部门2',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'remark',
        label: '备注',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ackDt',
        label: '交易确认日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'isFof',
        label: '自营fof',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'hboneNo',
        label: '一账通号',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custName',
        label: '客户姓名',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consname',
        label: '所属投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'conscustNo',
        label: '投顾客户号',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
