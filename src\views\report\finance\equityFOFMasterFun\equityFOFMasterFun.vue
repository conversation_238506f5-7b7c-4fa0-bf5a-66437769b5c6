<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            :show-export-btn="true"
            @searchFn="handleLoading"
            @exportFn="exportHandle"
        >
            <template #searchArea>
                <label-item :label="cpdm.label">
                    <crm-input
                        v-model="queryForm.cpdm"
                        :placeholder="cpdm.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
                <label-item :label="cpjc.label">
                    <crm-input
                        v-model="queryForm.cpjc"
                        :placeholder="cpjc.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <label-item :label="tzjjdm.label">
                    <crm-input
                        v-model="queryForm.tzjjdm"
                        :placeholder="tzjjdm.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <label-item :label="tzjjmc.label">
                    <crm-input
                        v-model="queryForm.tzjjmc"
                        :placeholder="tzjjmc.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
                <label-item :label="jyrq.label">
                    <date-range
                        v-model="queryForm.jyrq"
                        show-format="YYYY-MM-DD"
                        :placeholder="jyrq.placeholder"
                        style-type="fund"
                    />
                </label-item>
            </template>

            <template #tableContentMiddle>
                <!-- <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :no-select="true"
                    :stripe="true"
                    height="100%"
                    :border="true"
                >
                </base-table> -->
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                >
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>
        <!-- <riskyc-index
            v-if="dialogVisible"
            v-model="dialogVisible"
            :cons-cust-no="amountTransData?.conscustNo"
            @callBack="handleAdd"
        ></riskyc-index>
        <adjust-amt-index
            v-if="amountDialogVisible"
            v-model="amountDialogVisible"
            :trans-data="amountTransData"
            @callBack="handleAdd"
        ></adjust-amt-index> -->
    </div>
</template>

<script lang="ts" setup>
    import { downloadFile, fetchRes, message } from '@/utils'

    import { dataList } from './data/labelData'
    import { equityFOFMasterFunOrderTableColumn, showTableColumn } from './data/tableData'

    import {
        // eslint-disable-next-line camelcase
        equityFOFMasterFunOrder_Json,
        equityFOFMasterFunOrderExport
    } from '@/api/project/report/finance/equityFOFMasterFun/equityFOFMasterFun'

    const { cpdm, cpjc, tzjjdm, tzjjmc, jyrq } = dataList

    const listLoading = ref<boolean>(false)
    const dialogVisible = ref<boolean>(false)

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        cpdm = ''
        cpjc = ''
        tzjjdm = ''
        tzjjmc = ''
        jyrq = {
            startDate: '',
            endDate: ''
        }
        // sort = 'creDt'
        // order = 'descending'
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const panelActiveName = ref<string>('myReport')
    const tableData = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            equityFOFMasterFunOrderTableColumn.map(item => item.key),
            equityFOFMasterFunOrderTableColumn
        )
    })

    // // 默认排序
    // const defaultSort = computed(() => {
    //     return { prop: queryForm.sort, order: queryForm.order }
    // })
    // interface SortParams {
    //     order: SortOrderCumstom
    //     prop: string
    // }
    // // 排序联动
    // const handleSortChange = (val: SortParams) => {
    //     queryForm.order = val.order
    //     nextTick(() => {
    //         queryList()
    //     })
    // }

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            cpdm: queryForm.cpdm,
            cpjc: queryForm.cpjc,
            tzjjdm: queryForm.tzjjdm,
            tzjjmc: queryForm.tzjjmc,
            startDate: queryForm.jyrq.startDate,
            endDate: queryForm.jyrq.endDate,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(equityFOFMasterFunOrder_Json(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                // debugger
                tableData.value = rows
                // TODO list接口待添加分页数据
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const pdfUrl = ref<string>('')
    const exportList = async () => {
        const params = {
            cpdm: queryForm.cpdm,
            cpjc: queryForm.cpjc,
            tzjjdm: queryForm.tzjjdm,
            tzjjmc: queryForm.tzjjmc,
            startDate: queryForm.jyrq.startDate,
            endDate: queryForm.jyrq.endDate
        }
        const res: any = await equityFOFMasterFunOrderExport(params)
        const { fileByte } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        // 生成Blob的URL
        pdfUrl.value = URL.createObjectURL(blob)
        // 创建一个临时链接
        const url = URL.createObjectURL(blob)

        // 打开新的页面预览对应的pdf
        window.open(url)
    }

    /**
     * @description 提示
     */
    onBeforeMount(() => {
        // fetchUserPermissions()
        // queryList()
    })
</script>
<style lang="less" scoped></style>
