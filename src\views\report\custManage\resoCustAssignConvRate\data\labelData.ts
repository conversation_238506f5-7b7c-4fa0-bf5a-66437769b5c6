/*
 * @Description: 定义搜索的label列表
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:36:23
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/labelData.ts
 *
 */
export const dataList = {
    resType: {
        label: '资源类型',
        placeholder: '请选择资源类型',
        selectList: [
            {
                key: '6',
                label: 'lead分配'
            },
            {
                key: '7',
                label: '20w手动划转'
            }
        ]
    },
    custSource: {
        label: '客户来源',
        placeholder: '请选择客户来源',
        selectList: [
            {
                key: '1',
                label: 'howbuy400'
            },
            {
                key: '2',
                label: '储蓄罐app'
            },
            {
                key: '3',
                label: '好买网站'
            },
            {
                key: '4',
                label: '内部活动'
            },
            {
                key: '5',
                label: '其他合作'
            },
            {
                key: '6',
                label: '其他来源'
            },
            {
                key: '7',
                label: '腾讯'
            },
            {
                key: '8',
                label: '好买微信'
            },
            {
                key: '9',
                label: '掌上基金app'
            },
            {
                key: '10',
                label: '百度'
            },
            {
                key: '11',
                label: 'MGM'
            },
            {
                key: '12',
                label: '投顾开发'
            },
            {
                key: '13',
                label: '一致行动人'
            }
        ]
    },
    isDeal: {
        label: '是否成交',
        placeholder: '请选择是否成交',
        selectList: [
            {
                key: '1',
                label: '是'
            },
            {
                key: '0',
                label: '否'
            }
        ]
    },
    isWork: {
        label: '是否在职',
        placeholder: '请选择是否在职',
        selectList: [
            {
                key: '1',
                label: '是'
            },
            {
                key: '0',
                label: '否'
            }
        ]
    },
    isBindWeiXin: {
        label: '添加企微',
        placeholder: '请选择是否添加企微',
        selectList: [
            {
                key: '1',
                label: '是'
            },
            {
                key: '0',
                label: '否'
            }
        ]
    },
    secondHand: {
        label: '几手',
        placeholder: '请选择几手',
        selectList: [
            {
                key: '0',
                label: '一手'
            },
            {
                key: '1',
                label: '二手'
            }
        ]
    },
    radioNodeList: {
        label: '统计维度',
        placeholder: '统计维度',
        selectList: [
            {
                label: '分配投顾',
                value: '0'
            },
            {
                label: '当前投顾',
                value: '1'
            }
        ]
    },
    assignTime: {
        label: '分配时间',
        placeholder: ['起始日期', '结束日期']
    },
    curAssignTime: {
        label: '当前投顾分配时间',
        placeholder: ['起始日期', '结束日期']
    },
    conscustno: {
        label: '投顾客户号',
        placeholder: '输入投顾客户号'
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
