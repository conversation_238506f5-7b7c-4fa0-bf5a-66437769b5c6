<!--
* author: cleardWu
* props 与 emit与el-input一致
* 详见文档 https://element.eleme.cn/2.13/#/zh-CN/component/input
* 通用输入框组件
 -->
<template>
    <el-input class="crm-input" :class="[className]" v-bind="$attrs" size="small">
        <template v-for="slotName in dataList" #[slotName]>
            <slot v-if="slotName" :key="slotName" :name="slotName" />
        </template>
    </el-input>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmInput',
        props: {
            slotList: {
                type: Array,
                default: () => {
                    return []
                }
            },
            className: {
                type: String,
                default: ''
            }
        },
        data() {
            return {
                dataList: this.slotList
            }
        },
        watch: {
            slotList: {
                handler(newVal) {
                    this.dataList = newVal || []
                },
                deep: true,
                immediate: true
            }
        }
    })
</script>
<style lang="less" scoped>
    .crm-input {
        width: 100%;
        font-size: 12px;
        line-height: 24px;
        --el-input-focus-border-color: @border_focus;

        &.el-input--small {
            .el-input__inner {
                height: 24px;
                padding: 0 8px;

                &::placeholder {
                    font-size: 12px;
                }
            }

            .el-input__icon {
                line-height: 24px;
            }
        }

        .el-input__wrapper {
            padding: 1px 5px 1px 1px;
            border-radius: 2px;

            &.is-focus,
            &:focus {
                border-color: @border_focus;
            }
        }

        &.is-disabled {
            .el-input__inner {
                color: @font_color_02;
            }
        }

        .el-input__inner {
            color: @font_color_02;
            border-radius: 2px;

            &:focus {
                border-color: @border_focus;
            }
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
        }

        input[type='number'] {
            appearance: textfield;
        }
    }
</style>
<style lang="less">
    .el-tabs__item {
        &.is-active {
            color: @theme_main;
        }

        &.is-focus {
            color: @theme_main_hover;
        }

        &:hover {
            color: @theme_main_hover;
        }
    }

    .el-input {
        --el-input-text-color: @font_color_02;
        --el-input-focus-border-color: @border_focus;
        --el-select-input-focus-border-color: @border_focus;
    }
</style>
