import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import type {
    QueryIpsVisitRequest,
    ExportIpsVisitRequest
} from './type/request'

/**
 * @description: IPS面访记录查询接口
 * @return {*}
 */
export const queryIpsVisitList = (params: QueryIpsVisitRequest): any => {
    return axiosRequest(
        paramsMerge({
            url: '/report/kpi2025/ipsVisit/list',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: IPS面访记录导出接口
 * @return {*}
 */
export const exportIpsVisitList = (params: ExportIpsVisitRequest): any => {
    return axiosRequest(
        paramsMerge({
            url: '/report/kpi2025/ipsVisit/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: IPS面访记录初始化接口
 * @return {*}
 */
export const getIpsVisitInitData = (): any => {
    return axiosRequest(
        paramsMerge({
            url: '/report/kpi2025/ipsVisit/initData',
            method: 'post'
        })
    )
}
