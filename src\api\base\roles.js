/*
 * @Author: xing.zhou
 * @Date: 2021-12-13 14:48:00
 * @LastEditTime: 2023-04-02 07:38:26
 * @LastEditors: chaohui.wu
 * @Description: 角色及权限配置
 */

import { axiosRequest } from '@/utils/index'

export function getRoleList() {
    return axiosRequest({
        url: '/role/list',
        method: 'get'
    })
}

export function add(data) {
    return axiosRequest({
        url: '/role/add',
        method: 'put',
        data
    })
}
export function update(data) {
    return axiosRequest({
        url: '/role/update',
        method: 'post',
        data
    })
}
export function removeRole(data) {
    return axiosRequest({
        url: '/role/remove',
        method: 'delete',
        params: data
    })
}

// 获取用户组权限
export function getRolePermission(data) {
    return axiosRequest({
        url: '/role/permission/get',
        method: 'get',
        params: data
    })
}

// 保存用户组权限
export function saveRolePermission(data) {
    return axiosRequest({
        url: '/role/permission/save',
        method: 'put',
        data
    })
}

// 删除权限组绑定的用户
export function removeUser(data) {
    return axiosRequest({
        url: '/role/user/remove',
        method: 'delete',
        params: data
    })
}

// export function getRoleListByFid () {
//     return axiosRequest({
//         url: '/role/list/fid',
//         method: 'get'
//     })
// }

// 角色及权限配置 —— 添加用户
export function roleAddUser(data) {
    return axiosRequest({
        url: '/role/add/user',
        method: 'put',
        data
    })
}
