﻿<!-- eslint-disable no-undef -->
<!-- eslint-disable vue/require-default-prop -->
<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="board-s">
        <div :id="vId" style="width: 100%; height: 100%"></div>
    </div>
</template>

<script>
    import * as echarts from 'echarts'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'JxzBarChartBoard',
        props: {
            // eslint-disable-next-line vue/require-default-prop
            vId: {
                type: String
            },
            title: {
                type: String,
                default: 'title'
            },
            // eslint-disable-next-line vue/require-default-prop
            monthDataX: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            monthData: {
                type: Array
            }
        },
        data() {
            return {}
        },
        watch: {
            monthData: {
                handler(newVal) {
                    if (newVal) {
                        this.$nextTick(() => {
                            console.log('monthData++++++' + JSON.stringify(this.monthData))
                            this.renderGaugeChart()
                        })
                    }
                },
                immediate: true,
                deep: true
            }
        },
        mounted() {
            this.renderGaugeChart()
        },
        methods: {
            renderGaugeChart() {
                const option = {
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '10%',
                        top: '8%',
                        containLabel: true
                    },
                    //下载图片
                    toolbox: {
                        show: true,
                        feature: {
                            saveAsImage: {
                                pixelRatio: 15
                            } //值越大分辨率越高
                        }
                    },
                    legend: {
                        y: 'bottom',
                        x: 'center',
                        itemWidth: 6,
                        itemHeight: 6,
                        itemGap: 10
                    },
                    // 提示框
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        showContent: true,
                        // 自定义提示  显示千位符
                        formatter(params) {
                            let relVal = params[0].name + '(单位:万)'
                            for (let i = 0, l = params.length; i < l; i++) {
                                relVal += `<br/>${params[i].marker}${'净值'} : ${params[
                                    i
                                ].value.toLocaleString()}`
                            }
                            return relVal
                        }
                    },
                    xAxis: {
                        type: 'category',
                        splitLine: {
                            //去除网格线
                            show: false
                        },
                        axisLabel: {
                            interval: 0,
                            textStyle: {
                                fontSize: 9
                            }
                        },
                        //data: ['1月', '2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月']
                        data: this.monthDataX
                    },
                    yAxis: {
                        type: 'value',
                        splitLine: {
                            //去除网格线
                            show: false
                        },
                        splitArea: { show: false },
                        axisLabel: {
                            textStyle: {
                                fontSize: 9
                            }
                        }
                    },
                    series: [
                        {
                            type: 'bar',
                            barwidth: '40%', //柱的宽度
                            barGap: 0,
                            color: '#e54c5e',
                            data: this.monthData.map(item => {
                                return {
                                    value: item,
                                    label: {
                                        // 设置显示label
                                        show: true,
                                        // 设置label的位置
                                        position: item >= 0 ? 'top' : 'bottom',
                                        fontSize: 8
                                    }
                                }
                            })
                        }
                    ]
                }
                const gaugeChart = echarts.init(document.getElementById(this.vId))
                gaugeChart.setOption(option)
            }
        }
    })
</script>

<style scoped>
    .board-s {
        height: 95%;
        margin: 2px;

        /* border: 1px solid cornflowerblue; */
    }

    .box-card {
        height: 95%;
        font-size: 12px;
        background: #8cc5ff;
    }

    .card-title {
        display: block;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }
</style>
