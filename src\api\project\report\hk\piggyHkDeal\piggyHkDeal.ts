import { axiosRequest } from '@/utils/index'
import { queryPiggyHkDealParam, viewDetailParam } from './type/apiReqType.js'

export const queryPiggyHkDeal = (params: queryPiggyHkDealParam): any => {
    return axiosRequest({
        url: '/api/report/hk/piggyhkdeal/query',
        method: 'post',
        timeout: 180000,
        data: params
    })
}

export const exportPiggyHkDeal = (params: queryPiggyHkDealParam): any => {
    return axiosRequest({
        url: '/api/report/hk/piggyhkdeal/export',
        method: 'post',
        timeout: 180000,
        data: params
    })
}

export const viewDetail = (params: viewDetailParam): any => {
    return axiosRequest({
        url: '/api/report/hk/piggyhkdeal/viewdetail',
        method: 'post',
        data: params
    })
}
