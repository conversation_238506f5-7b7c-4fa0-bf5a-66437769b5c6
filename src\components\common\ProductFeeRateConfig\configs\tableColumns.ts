/*
 * @Description: 产品费率配置通用表格列配置
 * @Author: hongdong.xie
 * @Date: 2025-06-06 09:42:20
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 09:42:20
 * @FilePath: /ds-report-web/src/components/common/ProductFeeRateConfig/configs/tableColumns.ts
 */

import type { TableColumnItem } from '@/type/tableColumn'
import {
    formatDateForTable,
    formatTableValue,
    formatAuditStatus,
    formatFofOnlyFlag,
    formatTieredRateType,
    formatRateEffectiveType,
    formatPerformanceAccrualType,
    formatPerformanceAccrualForm,
    formatFixedAccrualMonthType,
    formatFixedAccrualDateType,
    formatFixedAccrualCalcType,
    formatRedemptionHoldingDaysRule,
    formatDividendHoldingDaysRule,
    formatPerformanceFormula,
    formatShareLockType,
    formatFundClosedType,
    formatRedemptionHoldingCalcRule,
    formatRedemptionSpecialRule
} from '../utils'

/**
 * @description: 产品费率配置通用表格列定义
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @return {TableColumnItem[]} 表格列配置数组
 */
export const getProductFeeRateConfigTableColumns = (): TableColumnItem[] => {
    return [
        {
            key: 'filingCode',
            label: '备案代码',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'productCode',
            label: '产品代码',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'shareCode',
            label: '份额代码',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'productFullName',
            label: '产品全称',
            width: 200,
            formatter: formatTableValue
        },
        {
            key: 'fofOnlyFlag',
            label: '仅限FOF',
            width: 100,
            formatter: formatFofOnlyFlag
        },
        {
            key: 'payerFullName',
            label: '付款方全称',
            width: 200,
            formatter: formatTableValue
        },
        {
            key: 'productManager',
            label: '产品经理',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'signingDate',
            label: '签约日期',
            width: 120,
            formatter: formatDateForTable
        },
        {
            key: 'howbuySigningEntity',
            label: '好买签约主体',
            width: 150,
            formatter: formatTableValue
        },
        {
            key: 'counterpartContact',
            label: '对方联系人',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'durationDCoefficient',
            label: '存续D系数',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'durationDRemark',
            label: '存续D备注',
            width: 150,
            formatter: formatTableValue
        },
        {
            key: 'rateStartDate',
            label: '费率开始日期',
            width: 120,
            formatter: formatDateForTable
        },
        {
            key: 'rateEndDate',
            label: '费率结束日期',
            width: 120,
            formatter: formatDateForTable
        },
        {
            key: 'tieredRateType',
            label: '费率配置类型',
            width: 120,
            formatter: formatTieredRateType
        },
        {
            key: 'rateEffectiveType',
            label: '费率配置生效日',
            width: 140,
            formatter: formatRateEffectiveType
        },
        {
            key: 'configLowerLimit',
            label: '配置下限',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'configUpperLimit',
            label: '配置上限',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'rateEffectiveStartDate',
            label: '费率生效日期',
            width: 120,
            formatter: formatDateForTable
        },
        {
            key: 'rateEffectiveEndDate',
            label: '费率结束日期',
            width: 120,
            formatter: formatDateForTable
        },
        {
            key: 'subscriptionRate',
            label: '认申购费率',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'subscriptionRemark',
            label: '认申购费备注',
            width: 150,
            formatter: formatTableValue
        },
        {
            key: 'managementRate',
            label: '管理费率',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'managementFormula',
            label: '管理费公式',
            width: 150,
            formatter: formatTableValue
        },
        {
            key: 'managementRemark',
            label: '管理费备注',
            width: 150,
            formatter: formatTableValue
        },
        {
            key: 'performanceSharingRate1',
            label: '业绩报酬分成费率1',
            width: 160,
            formatter: formatTableValue
        },
        {
            key: 'performanceRate1',
            label: '业绩报酬费率1',
            width: 140,
            formatter: formatTableValue
        },
        {
            key: 'performanceBenchmark1',
            label: '业绩报酬计提基准1',
            width: 160,
            formatter: formatTableValue
        },
        {
            key: 'performanceSharingRate2',
            label: '业绩报酬分成费率2',
            width: 160,
            formatter: formatTableValue
        },
        {
            key: 'performanceRate2',
            label: '业绩报酬费率2',
            width: 140,
            formatter: formatTableValue
        },
        {
            key: 'performanceBenchmark2',
            label: '业绩报酬计提基准2',
            width: 160,
            formatter: formatTableValue
        },
        {
            key: 'performanceSharingRate3',
            label: '业绩报酬分成费率3',
            width: 160,
            formatter: formatTableValue
        },
        {
            key: 'performanceRate3',
            label: '业绩报酬费率3',
            width: 140,
            formatter: formatTableValue
        },
        {
            key: 'performanceBenchmark3',
            label: '业绩报酬计提基准3',
            width: 160,
            formatter: formatTableValue
        },
        {
            key: 'performanceAccrualType',
            label: '业绩报酬计提类型',
            width: 160,
            formatter: formatPerformanceAccrualType
        },
        {
            key: 'performanceAccrualForm',
            label: '业绩报酬计提形式',
            width: 160,
            formatter: formatPerformanceAccrualForm
        },
        {
            key: 'fixedAccrualMonthType',
            label: '固定计提日月份类型',
            width: 160,
            formatter: formatFixedAccrualMonthType
        },
        {
            key: 'fixedAccrualDateType',
            label: '固定计提日日期类型',
            width: 160,
            formatter: formatFixedAccrualDateType
        },
        {
            key: 'fixedAccrualCalcType',
            label: '固定计提日计算类型',
            width: 160,
            formatter: formatFixedAccrualCalcType
        },
        {
            key: 'redemptionHoldingDaysRule',
            label: '赎回业绩报酬持有天数规则',
            width: 180,
            formatter: formatRedemptionHoldingDaysRule
        },
        {
            key: 'dividendHoldingDaysRule',
            label: '分红业绩报酬持有天数规则',
            width: 180,
            formatter: formatDividendHoldingDaysRule
        },
        {
            key: 'performanceFormula',
            label: '业绩报酬公式',
            width: 140,
            formatter: formatPerformanceFormula
        },
        {
            key: 'shareLockType',
            label: '份额锁定期类型',
            width: 140,
            formatter: formatShareLockType
        },
        {
            key: 'shareLockDays',
            label: '份额锁定期天数',
            width: 140,
            formatter: formatTableValue
        },
        {
            key: 'fundClosedType',
            label: '基金封闭期类型',
            width: 140,
            formatter: formatFundClosedType
        },
        {
            key: 'fundClosedDays',
            label: '基金封闭期天数',
            width: 140,
            formatter: formatTableValue
        },
        {
            key: 'noAccrualNavBenchmark',
            label: '不计提净值基准',
            width: 140,
            formatter: formatTableValue
        },
        {
            key: 'performanceRemark',
            label: '业绩报酬备注',
            width: 150,
            formatter: formatTableValue
        },
        {
            key: 'redemptionRate1',
            label: '赎回费率1',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'redemptionHoldingDays1',
            label: '赎回费持有天数1',
            width: 140,
            formatter: formatTableValue
        },
        {
            key: 'redemptionRate2',
            label: '赎回费率2',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'redemptionHoldingDays2',
            label: '赎回费持有天数2',
            width: 140,
            formatter: formatTableValue
        },
        {
            key: 'redemptionRate3',
            label: '赎回费率3',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'redemptionHoldingDays3',
            label: '赎回费持有天数3',
            width: 140,
            formatter: formatTableValue
        },
        {
            key: 'redemptionRate4',
            label: '赎回费率4',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'redemptionHoldingDays4',
            label: '赎回费持有天数4',
            width: 140,
            formatter: formatTableValue
        },
        {
            key: 'redemptionFormula',
            label: '赎回费公式',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'redemptionHoldingCalcRule',
            label: '赎回费持有天数计算规则',
            width: 180,
            formatter: formatRedemptionHoldingCalcRule
        },
        {
            key: 'redemptionSpecialRule',
            label: '赎回费特例',
            width: 120,
            formatter: formatRedemptionSpecialRule
        },
        {
            key: 'redemptionRemark',
            label: '赎回费备注',
            width: 150,
            formatter: formatTableValue
        },
        {
            key: 'creator',
            label: '创建者',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'createTime',
            label: '创建时间',
            width: 160,
            formatter: formatTableValue
        },
        {
            key: 'modor',
            label: '修改者',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'updateTime',
            label: '修改时间',
            width: 160,
            formatter: formatTableValue
        },
        {
            key: 'auditor',
            label: '审核者',
            width: 120,
            formatter: formatTableValue
        },
        {
            key: 'auditTime',
            label: '审核时间',
            width: 160,
            formatter: formatTableValue
        },
        {
            key: 'auditStatus',
            label: '审核状态',
            width: 120,
            formatter: formatAuditStatus
        },
        {
            key: 'auditRemark',
            label: '审核备注',
            width: 200,
            formatter: formatTableValue
        },
        {
            key: 'recordDate',
            label: '录入日期',
            width: 120,
            formatter: formatDateForTable
        }
    ]
}

/**
 * @description: 根据字段数组过滤表格列
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {string[]} keyArr 要显示的字段数组
 * @param {TableColumnItem[]} tableColumnList 完整的表格列配置
 * @return {TableColumnItem[]} 过滤后的表格列配置
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]): TableColumnItem[] => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
        return false
    })
}
