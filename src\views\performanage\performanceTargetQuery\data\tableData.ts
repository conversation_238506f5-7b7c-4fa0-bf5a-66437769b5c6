/*
 * @Description: table表格展示
 * @Author: gang.zou
 * @Date: 2024-04-17 17:14:42
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, formatNumber } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const performanceTargetTableColumn: TableColumnItem[] = [
    {
        key: 'consRankName',
        label: '职级',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceDate',
        label: '考核节点',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceMonth',
        label: '考核节点司龄月份',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceD',
        label: '存续D目标',
        minWidth: 120,
        formatter: formatTableValue
    }
]
//>12M
export const performanceTargetMoreYearTableColumn: TableColumnItem[] = [
    {
        key: 'consRankName',
        label: '职级',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceDate',
        label: '当前考核节点-存续D目标',
        minWidth: 120,
        formatter: formatTableValue,
        child: [
            {
                key: 'performanceDate',
                label: '考核节点',
                minWidth: 120,
                formatter: formatTableValue
            },
            {
                key: 'currentFormal',
                label: '年化月日均存续D',
                minWidth: 120,
                formatter: formatTableValue
            }
        ]
    },
    {
        key: 'performanceDate',
        label: '下一考核节点-存续D目标',
        minWidth: 120,
        formatter: formatTableValue,
        child: [
            {
                key: 'nextPerformanceDate',
                label: '考核节点',
                minWidth: 120,
                formatter: formatTableValue
            },
            {
                key: 'nextCurrentFormal',
                label: '年化月日均存续D',
                minWidth: 120,
                formatter: formatTableValue
            }
        ]
    },
    {
        key: 'performanceDate',
        label: '12M以上',
        minWidth: 120,
        formatter: formatTableValue,
        child: [
            {
                key: 'qualifiedMoreTwelveMonth',
                label: '合格人力',
                minWidth: 120,
                formatter: formatTableValue
            },
            {
                key: 'qualifiedMoreSubTwelveMonth',
                label: '合格分公司',
                minWidth: 120,
                formatter: formatTableValue
            }
        ]
    }
]
//<=12M
export const performanceTargetLessYearTableColumn: TableColumnItem[] = [
    {
        key: 'consRankName',
        label: '职级',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'cxd',
        label: '存续D目标',
        minWidth: 120,
        formatter: formatTableValue,
        child: [
            {
                key: 'threeMonth',
                label: '试用3M',
                minWidth: 120,
                formatter: formatTableValue
            },
            {
                key: 'sixMonth',
                label: '试用6M',
                minWidth: 120,
                formatter: formatTableValue
            },
            {
                key: 'twelveMonth',
                label: '跟踪12M',
                minWidth: 120,
                formatter: formatTableValue
            }
        ]
    },
    {
        key: 'fgcrl',
        label: '非观察人力目标',
        minWidth: 120,
        formatter: formatTableValue,
        child: [
            {
                key: 'notTestThreeMonth',
                label: '试用3M',
                minWidth: 120,
                formatter: formatTableValue
            },
            {
                key: 'notTestSixMonth',
                label: '试用6M',
                minWidth: 120,
                formatter: formatTableValue
            }
        ]
    },
    {
        key: 'hgrl',
        label: '合格人力目标',
        minWidth: 120,
        formatter: formatTableValue,
        child: [
            {
                key: 'qualifiedTwelveMonth',
                label: '跟踪12M',
                minWidth: 120,
                formatter: formatTableValue
            }
        ]
    },
    {
        key: 'fgcfgs',
        label: '非观察分公司目标',
        minWidth: 120,
        formatter: formatTableValue,
        child: [
            {
                key: 'notTestSubThreeMonth',
                label: '试用3M',
                minWidth: 120,
                formatter: formatTableValue
            },
            {
                key: 'notTestSubSixMonth',
                label: '试用6M',
                minWidth: 120,
                formatter: formatTableValue
            }
        ]
    },
    {
        key: 'hgfgs',
        label: '合格分公司目标',
        minWidth: 120,
        formatter: formatTableValue,
        child: [
            {
                key: 'qualifiedSubTwelveMonth',
                label: '跟踪12M',
                minWidth: 120,
                formatter: formatTableValue
            }
        ]
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
