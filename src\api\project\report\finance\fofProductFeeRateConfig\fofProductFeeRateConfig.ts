/*
 * @Description: FOF产品费率配置API接口
 * @Author: hongdong.xie
 * @Date: 2025-06-03 17:19:56
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-04 10:28:31
 * @FilePath: /ds-report-web/src/api/project/report/finance/fofProductFeeRateConfig/fofProductFeeRateConfig.ts
 */

import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '../../../../mock.js'
import {
    FofProductFeeRateConfigQueryParam,
    FofProductFeeRateConfigAddParam,
    FofProductFeeRateConfigUpdateParam,
    FofProductFeeRateConfigDeleteParam,
    FofProductFeeRateConfigAuditParam,
    FofProductFeeRateConfigExportParam
} from './type/apiReqType'

/**
 * @description: FOF产品费率配置查询接口
 * @param {FofProductFeeRateConfigQueryParam} params 查询参数
 * @return {*}
 */
export const fofProductFeeRateConfigQuery = (params: FofProductFeeRateConfigQueryParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fof/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: FOF产品费率配置导出接口
 * @param {FofProductFeeRateConfigExportParam} params 导出参数
 * @return {*}
 */
export const fofProductFeeRateConfigExport = (params: FofProductFeeRateConfigExportParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fof/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: FOF产品费率配置新增接口
 * @param {FofProductFeeRateConfigAddParam} params 新增参数
 * @return {*}
 */
export const fofProductFeeRateConfigAdd = (params: FofProductFeeRateConfigAddParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fof/add',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: FOF产品费率配置修改接口
 * @param {FofProductFeeRateConfigUpdateParam} params 修改参数
 * @return {*}
 */
export const fofProductFeeRateConfigUpdate = (params: FofProductFeeRateConfigUpdateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fof/update',
            method: 'post',
            data: params,
            // Mock数据配置
            mockData: {
                code: '200',
                description: '修改成功',
                data: null
            }
        })
    )
}

/**
 * @description: FOF产品费率配置删除接口
 * @param {FofProductFeeRateConfigDeleteParam} params 删除参数
 * @return {*}
 */
export const fofProductFeeRateConfigDelete = (params: FofProductFeeRateConfigDeleteParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fof/delete',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: FOF产品费率配置审核接口
 * @param {FofProductFeeRateConfigAuditParam} params 审核参数
 * @return {*}
 */
export const fofProductFeeRateConfigAudit = (params: FofProductFeeRateConfigAuditParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fof/audit',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 获取操作按钮权限
 * @return {*}
 */
export const getFofProductFeeRateConfigAuth = () => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fof/getAuth',
            method: 'post'
        })
    )
}
