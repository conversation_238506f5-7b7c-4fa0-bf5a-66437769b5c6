import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '../../../../mock.js'
import {
    FinanceAuditAddOrUpdateParam,
    DeleteById,
    BatchDeleteId,
    BatchReviewId,
    FinanceAudit,
    FeeParam
} from './type/apiReqType.js'

/**
 * @description: 财务审批新增接口
 * @return {*}
 */
export const financeAuditInsert = (params: FinanceAuditAddOrUpdateParam) => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/finance/financeAudit/insert',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 财务审批修改接口
 * @return {*}
 */
export const financeAuditUpdate = (params: FinanceAuditAddOrUpdateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/financeAudit/update',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 财务审批删除接口
 * @return {*}
 */
export const financeAuditlDeleteById = (params: DeleteById) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/financeAudit/delete',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 财务审批删除接口
 * @return {*}
 */
export const financeAuditlBatchDeleteById = (params: BatchDeleteId) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/financeAudit/batchDelete',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 财务审批批量审核接口
 * @return {*}
 */
export const financeAuditBatchReview = (params: BatchReviewId) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/financeAudit/batchReview',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 财务配置审核接口
 * @return {*}
 */
export const financeAuditReview = (params: FinanceAudit) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/financeAudit/review',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核结算页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const FinanceAuditSettleFeeQuery = (params: FeeParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/financeAudit/financeAuditSettleFeeQuery',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核结算的导出接口
 * @return {*}
 */
export const FinanceAuditSettleFeeExport = (params: FeeParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/financeAudit/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核结算页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const financeAuditSettleFeeDetailQuery = (params: FeeParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/financeAudit/financeAuditSettleFeeDetailQuery',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核结算的导出接口
 * @return {*}
 */
export const FinanceAuditSettleFeeDetailExport = (params: FeeParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/financeAudit/exportDetail',
            method: 'post',
            data: params
        })
    )
}
