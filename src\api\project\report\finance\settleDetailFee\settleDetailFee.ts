import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { SettleDetailFeeParam } from './type/apiReqType.js'

/**
 * @description: 股权产品的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const SettleDetailFeeQuery = (params: SettleDetailFeeParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/settleDetailFee/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 股权产品的导出接口
 * @return {*}
 */
export const SettleDetailFeeExport = (params: SettleDetailFeeParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/settleDetailFee/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 详细页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const SettleDetailFeeDetailQuery = (params: SettleDetailFeeParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/settleDetailFee/queryDetail',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 明细页面的导出
 * @return {*}
 */
export const SettleDetailFeeDetailExport = (params: SettleDetailFeeParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/settleDetailFee/exportdetail',
            method: 'post',
            data: params
        })
    )
}
