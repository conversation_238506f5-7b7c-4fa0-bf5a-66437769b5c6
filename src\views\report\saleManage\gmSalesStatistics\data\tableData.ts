/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import {
    formatTableValue,
    numberWithThousandSeparatorTwoDecimalPlaces,
    numberWithThousandSeparatorNoPoint
} from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const gmSalesStatisticsTableColumn: TableColumnItem[] = [
    {
        key: 'productName',
        label: '产品名称',
        width: 140,
        formatter: formatTableValue
    },
    {
        key: 'totalAppCustNum',
        label: '申请客户数',
        width: 140,
        formatter: numberWithThousandSeparatorNoPoint
    },
    {
        key: 'totalAppAmt',
        label: '申请金额',
        width: 140,
        formatter: numberWithThousandSeparatorTwoDecimalPlaces
    },
    {
        key: 'totalAckAmt',
        label: '确认金额',
        width: 140,
        formatter: numberWithThousandSeparatorTwoDecimalPlaces
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
