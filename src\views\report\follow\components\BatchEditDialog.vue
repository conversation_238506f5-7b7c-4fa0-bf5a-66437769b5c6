<template>
    <crm-dialog
        v-model="dialogVisible"
        width="510px"
        :border="true"
        :title="props.transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div class="batch-edit-content">
            <el-form
                ref="formRef"
                :model="formData"
                :rules="rules"
                label-width="150px"
                class="batch-edit-form"
            >
                <el-form-item v-if="props.transData?.type === '1'" label="已选择数据：">
                    <span>{{ props.transData?.ids.length }}</span>
                </el-form-item>
                <!-- 开始时间 -->
                <el-form-item label="开始时间：" prop="startDt">
                    <el-date-picker
                        v-model="formData.startDt"
                        popper-class="date_range_picker_popper"
                        size="small"
                        show-format="YYYYMMDD"
                        value-format="YYYYMMDD"
                        placeholder="请选择"
                        style-type="fund"
                        :style="{ width: '160px' }"
                    />
                </el-form-item>

                <!-- 结束时间 -->
                <el-form-item label="结束时间：" prop="endDt">
                    <el-date-picker
                        v-model="formData.endDt"
                        popper-class="date_range_picker_popper"
                        size="small"
                        show-format="YYYYMMDD"
                        value-format="YYYYMMDD"
                        placeholder="请选择"
                        style-type="fund"
                        :style="{ width: '160px' }"
                    />
                </el-form-item>

                <!-- 客服回访-是否满意 -->
                <el-form-item
                    :label="dataList.feedbackIsSatisfied.label + '：'"
                    prop="feedbackIsSatisfied"
                >
                    <crm-select
                        v-model="formData.feedbackIsSatisfied"
                        placeholder="请选择"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="dataList.feedbackIsSatisfied.selectList"
                        :style="{ width: '160px' }"
                    />
                </el-form-item>

                <!-- 最终结果 -->
                <el-form-item :label="dataList.finalResult.label + '：'" prop="finalResult">
                    <crm-select
                        v-model="formData.finalResult"
                        placeholder="请选择"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="dataList.finalResult.selectList"
                        :style="{ width: '160px' }"
                    />
                </el-form-item>

                <!-- 备注 -->
                <el-form-item label="备注：" prop="remark">
                    <crm-input
                        v-model="formData.remark"
                        type="text"
                        placeholder="请输入备注"
                        maxlength="200"
                    />
                </el-form-item>
            </el-form>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <crm-button size="small" :radius="true" @click="dialogVisible = false"
                    >取消</crm-button
                >
                <crm-button type="primary" size="small" :radius="true" @click="handleSubmit"
                    >确定</crm-button
                >
            </span>
        </template>
    </crm-dialog>
</template>

<script setup lang="ts">
    import { ref } from 'vue'
    import type { FormInstance } from 'element-plus'
    import { dataList } from '../data/labelData'
    import { modifyFollowStatistic } from '@/api/project/report/follow/follow'
    import type { ModifyFollowStatisticRequest } from '@/api/project/report/follow/type/request'
    import { fetchRes } from '@/utils'

    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                ids: string[]
                title: string
                id: string
                type: string
            }
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    ids: [] as string[],
                    title: '',
                    id: '',
                    type: '1'
                }
            }
        }
    )

    const emit = defineEmits(['update:modelValue', 'refresh'])

    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    // 表单ref
    const formRef = ref<FormInstance>()

    // 表单数据
    const formData = ref({
        startDt: '',
        endDt: '',
        feedbackIsSatisfied: '',
        finalResult: '',
        remark: ''
    })

    // 表单校验规则
    const rules = {
        remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
    }

    // 提交表单
    const handleSubmit = async () => {
        if (!formRef.value) {
            return
        }

        await formRef.value.validate(async valid => {
            if (valid) {
                const params: ModifyFollowStatisticRequest = {
                    idList: props.transData?.ids,
                    id: props.transData?.id,
                    ...formData.value
                }

                fetchRes(modifyFollowStatistic(params), {
                    successCB: () => {
                        emit('refresh')
                        dialogVisible.value = false
                    },
                    successTxt: '修改成功',
                    failTxt: '修改失败，请重试！'
                })
            }
        })
    }
    // 重置表单数据
    const resetForm = () => {
        formData.value = {
            startDt: '',
            endDt: '',
            feedbackIsSatisfied: '',
            finalResult: '',
            remark: ''
        }
        // 如果需要重置表单的校验状态
        formRef.value?.resetFields()
    }

    // 监听弹窗状态
    watch(
        () => props.visibleCus,
        newVal => {
            if (!newVal) {
                resetForm()
            }
        }
    )
</script>

<style lang="less" scoped>
    .batch-edit-content {
        padding: 20px;
        padding-left: 0;

        .batch-edit-form {
            width: 100%;
        }
    }

    :deep(.el-form-item__label) {
        font-weight: normal;
    }
</style>
