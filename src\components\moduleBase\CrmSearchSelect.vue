<!--
* author: di.zong
* 搜索调研人  需要透传searchUser参数
* 搜索基金类的需要透传searchType
* 若初始有值得情况  则需要透传defaultList（2021-05-28改为透传用户id数组defaultFids）
* crm通用的远程搜索下拉选择组件
{
    '100': '公募基金',
    '200': '私募基金',
    '200_0': '私募基金披露',
    '200_1': '私募基金不披露',
    '300': '公募经理',
    '400': '私募经理',
    '500': '公募公司',
    '600': '私募公司',
    '700': '私募固收',
    '800': '新闻',
    '900': '研报',
    '1000': '私募股权'
}
 -->
<template>
    <el-select
        class="crm_search_select crm-search-select-pro"
        :collapse-tags="$attrs.multiple && !$attrs.notags"
        v-bind="$attrs"
        :filterable="!disableAll"
        :clearable="!disableAll"
        remote
        reserve-keyword
        :remote-method="remoteMethod"
        size="small"
        :popper-class="popperClass"
        @change="valueChange"
    >
        <el-option
            v-for="item in datas"
            :key="searchUser ? item.fid : item.code + '-' + item.type"
            :label="
                searchUser
                    ? allUser
                        ? item.cn
                        : item.cn + '(' + item.username + ')'
                    : item[labelFormat]
            "
            :value="searchUser ? item.fid : item.code"
            :disabled="subDisabled(item)"
        >
            <div v-if="searchUser" class="label search-result-item">
                <div
                    class="fund-name"
                    v-html="highlightSearchKey(item.cn + '(' + item.username + ')')"
                ></div>
                <div class="fund-code" v-html="formaterSource(item.source)"></div>
            </div>
            <div v-else-if="searchManager" class="search_result_item search_manager">
                <div class="left" v-html="highlightSearchKey(item.name)"></div>
                <div class="right" v-html="highlightSearchKey(item.jgjc)"></div>
            </div>
            <div v-else class="label result-item search-result-item twosearch-result-item">
                <div class="result-item-tpl result-item-tpl1 fund-name fund-name1">
                    <span v-html="highlightSearchKey(item.name)"></span>
                </div>
                <div
                    class="result-item-tpl fund-code"
                    v-html="highlightSearchKey(formatCodeValue(item))"
                ></div>
                <div
                    v-if="isMaterial"
                    class="result-item-tpl right"
                    v-html="highlightSearchKey(item.typeShowName)"
                ></div>
            </div>
        </el-option>
    </el-select>
</template>

<script>
    // import { searchByType, querySubUserInfo, warehouseInitialize } from '@rootCommon/api/common/crmComponents'
    const sourceType = {
        0: '域账号',
        1: '自定义'
    }
    // 支持查询机构空值
    const EMPTY_CODE = '99999'
    export default {
        name: 'CrmSearchSelect',
        props: {
            searchType: {
                type: String,
                default: '100,200,700,1000'
            },
            // 搜索产品或机构的时候传递的类型参数
            searchTypeParcrm: {
                type: Object,
                default: () => {
                    return {}
                }
            },
            searchUser: {
                type: Boolean,
                default: false
            },
            defaultList: {
                type: Array,
                default: () => {
                    return []
                }
            },
            all: {
                type: Boolean,
                default: true
            },
            // 部分情况搜索产品的时候，输入框填入的值是产品代码
            labelFormat: {
                type: String,
                default: 'name'
            },
            defaultFids: {
                type: Array,
                default: null
            },
            // 单选是传递的默认code，用于获取名称并显示到输入框
            defaultCode: {
                type: String,
                default: ''
            },
            // 研究员搜索，是否包含同级
            containSameLevel: {
                type: Boolean,
                default: true
            },
            param: {
                // 查询时任意配置的参数
                type: Object,
                default: () => {
                    return {}
                }
            },
            clearSelectOptions: {
                // 有的时候切换了类型  需要将下拉框里的选项清空掉
                type: Number,
                default: 0
            },
            isMaterial: {
                // 是否为资料
                type: Boolean,
                default: false
            },
            searchByTypeAndCode: {
                type: Function,
                default: () => {
                    return undefined
                }
            },
            searchByTypeFun: {
                type: Function,
                default: () => {
                    return undefined
                }
            },
            querySubUserInfoFun: {
                type: Function,
                default: () => {
                    return null
                }
            },
            warehouseInitializeFun: {
                type: Function,
                default: () => {
                    return undefined
                }
            },
            onlySub: {
                type: Boolean,
                default: false
            },
            allUser: {
                type: Boolean,
                default: false
            },
            // 默认不可编辑的选项
            disabledFids: {
                type: Array,
                default: null
            },
            // disabled的时候，可以展示被选择的数据
            disableAll: {
                type: Boolean,
                default: false
            },
            searchNoCancel: {
                type: Boolean,
                default: false
            },
            // 单选是传递的默认type，用于获取名称并显示到输入框
            defaultType: {
                type: String,
                default: ''
            },
            // 多选或者单选 item包含code和type 来确认某项
            defaultItems: {
                type: Array,
                default: () => {
                    return []
                }
            },
            selectCode: {
                type: String,
                default: ''
            }
        },
        emits: ['select'],
        data() {
            return {
                loading: false,
                datas: [],
                checkedArr: [],
                queryString: ''
            }
        },
        computed: {
            // 是否是搜索基金经理，该情况下搜索结果三列展示：经理姓名，机构名称，机构代码
            searchManager() {
                let isSearchManager = false
                try {
                    isSearchManager = this.searchType === this.searchTypeParcrm.manager
                } catch (e) {
                    isSearchManager = false
                }
                return isSearchManager
            },
            popperClass() {
                return `crm_search_select_popper${
                    this.searchManager
                        ? ' crm_search_manager'
                        : this.searchUser
                        ? ' crm-search-user'
                        : ' crm-search-other'
                }`
            }
        },
        watch: {
            defaultList: {
                handler(val) {
                    if (val?.length) {
                        this.datas = val
                        this.checkedArr = val
                    }
                },
                deep: true,
                immediate: true
            },
            defaultFids: {
                handler(val) {
                    if (Array.isArray(val) && val.length > 0 && !this.defaultList) {
                        if (this.onlySub && this.inited) {
                            return
                        }
                        this.inited = true
                        this.initializeUserName()
                    }
                },
                immediate: true
            },
            // 根据code获取名称并展示到输入框
            defaultCode(val) {
                if (val) {
                    this.datas = []
                    this.checkedArr = []
                    // 回显
                    this.remoteMethod(val)
                } else {
                    // 切换某些东西的时候(比如新增资料中的类型)，要清空下拉的框
                    this.datas = []
                    this.checkedArr = []
                }
            },
            clearSelectOptions(val) {
                if (val) {
                    this.datas = []
                }
            }
        },
        created() {
            if (this.defaultCode) {
                this.datas = []
                this.checkedArr = []
                this.remoteMethod(this.defaultCode, true)
            }
        },
        methods: {
            // 搜索结果列表右侧展示的字段值处理
            formatCodeValue(item) {
                let codeName = ''

                switch (item.type) {
                    default:
                    case '100':
                        // 公募基金：基金代码
                        codeName = 'code'
                        break
                    case '200':
                        // 私募基金：备案编号
                        codeName = 'babh'
                        break
                    case '300':
                        // 公募经理：所属机构简称
                        codeName = 'jgjc'
                        break
                    case '400':
                        // 私募经理：所属机构简称
                        codeName = 'jgjc'
                        break
                    case '500':
                        // 公募公司：空
                        codeName = ''
                        break
                    case '600':
                        // 私募公司：协会登记编号
                        codeName = 'babh'
                        break
                }

                return item?.[codeName] || ''
            },
            subDisabled(item) {
                if (this.disableAll) {
                    // 禁用选择，详情页时允许查看，不可选择
                    return true
                }
                if (this.disabledFids) {
                    return this.disabledFids.some(fid => {
                        return item.fid === fid
                    })
                }
                if (!this.onlySub || item.subUser) {
                    return false
                }

                return true
            },
            remoteMethod(queryString, flag = false) {
                this.loading = true
                this.queryString = queryString
                if (!queryString) {
                    return
                }
                if (this.searchUser) {
                    const params = {
                        keyword: queryString,
                        all: this.all,
                        ...(this.containSameLevel
                            ? { containSameLevel: this.containSameLevel }
                            : null)
                    }
                    if (this.querySubUserInfoFun && this.querySubUserInfoFun(params)) {
                        this.querySubUserInfoFun(params).then(res => {
                            const arr = []
                            const flag = {}
                            this.checkedArr
                                .forEach(item => {
                                    flag[item.fid] = true
                                    arr.push(item)
                                })(res?.body?.dataList || [])
                                .forEach(item => {
                                    if (!flag[item.fid]) {
                                        flag[item.fid] = true
                                        arr.push(item)
                                    }
                                })
                            this.datas = arr.map(item => {
                                item.fid = Number(item.fid)
                                return item
                            })
                        })
                    }
                } else {
                    if (this.defaultType) {
                        this.searchByTypeAndCode({
                            code: this.defaultCode,
                            type: this.defaultType
                        })
                        return
                    }
                    this.searchByTypeFun(
                        {
                            type: flag ? this.selectCode || this.searchType : this.searchType,
                            keyword: queryString,
                            all: this.all,
                            ...this.param
                        },
                        this.searchNoCancel
                    ).then(res => {
                        let dataList = res.body
                        let oldList = []
                        if (this.$attrs.multiple !== undefined) {
                            oldList = this.checkedArr
                            const listArr = oldList.map(item => item.code || item.fid)
                            dataList = dataList.filter(item => {
                                if (item.code && !listArr.includes(item.code)) {
                                    return true
                                } else if (item.fid && !listArr.includes(item.fid)) {
                                    return true
                                }
                                return false
                            })
                        }
                        if (queryString === EMPTY_CODE && dataList.length < 1) {
                            dataList.push({
                                batchNo: null,
                                cnNames: null,
                                code: EMPTY_CODE,
                                cpzt: null,
                                createdTime: '2021-01-08 16:48:27',
                                createdUser: 'auto',
                                createdUsername: 'auto',
                                fpzt: null,
                                fullName: '空',
                                id: null,
                                jgclbz: 0,
                                name: '空',
                                pinyin: 'kong',
                                qjbz: null,
                                researchers: null,
                                shortPinyin: 'k',
                                type: '',
                                typeName: '',
                                typeShowName: '',
                                updateTime: '2021-12-28 00:12:15',
                                updateUser: null,
                                updateUsername: 'auto',
                                warehouseType: 2
                            })
                        }
                        this.datas = [...oldList, ...dataList]
                    })
                }
            },
            filterKeyWord(val) {
                if (!val || val.length === 0) {
                    return ''
                }
                return val.replace(/[#$]/g, '')
            },
            // 替换搜索关键字
            highlightSearchKey(text) {
                const searchKey = this.queryString || ''
                if (!searchKey) {
                    return text
                }
                const replaceReg = new RegExp(searchKey, 'g') // 匹配关键字正则
                const replaceString = '<span class="highlight_search_key">' + searchKey + '</span>' // 高亮替换v-html值
                return text.replace(replaceReg, replaceString) // 开始替换
            },
            formaterSource(source) {
                return sourceType[source]
            },
            valueChange(val) {
                if (this.$attrs.multiple !== undefined) {
                    // 多选的操作
                    let arr = []
                    val.forEach(_item => {
                        const obj = this.datas.find(
                            item =>
                                String(item.fid) === String(_item) ||
                                String(item.code) === String(_item)
                        )
                        arr.push(obj)
                    })
                    if (arr.length < this.preDefaultList?.length || 0) {
                        arr = this.preDefaultList
                    }
                    this.checkedArr = arr
                    this.$emit('select', arr)
                } else {
                    // 单选的操作
                    const obj = this.datas.find(item => item.code === val || item.fid === val)
                    this.$emit('select', obj)
                }
            },
            // 初始话传入了用户id的话，要调接口获取用户列表展示到输入框
            initializeUserName() {
                this.warehouseInitializeFun(this.defaultFids)
                    .then(res => {
                        const userList = res?.body?.dataList || []
                        // 把数字fid转成字符串
                        // userList.map(item => (item.fid = this.$$.getString(item.fid)))
                        if (this.onlySub) {
                            userList.sort((pre, next) => {
                                if (!next.subUser && !pre.subUser) {
                                    return 0
                                }
                                if (!next.subUser) {
                                    return 1
                                }
                                return -1
                            })
                            this.preDefaultList = userList.filter(
                                v =>
                                    !v.subUser &&
                                    this.defaultFids.some(id => String(v.fid) === String(id))
                            )
                        }

                        this.datas = userList
                        this.checkedArr = userList
                        this.$emit('select', userList)
                    })
                    .catch(() => {
                        this.$message({
                            message: '初始化所属用户下拉框失败',
                            type: 'warning',
                            duration: 2000
                        })
                    })
            }
        }
    }
</script>
<style lang="less">
    .crm_search_select {
        width: 100%;
        --el-select-input-focus-border-color: @border_focus;

        .el-input__wrapper {
            padding: 1px 5px 1px 1px;
            border-radius: 2px;
        }

        .el-input--small .el-input__inner {
            height: 24px;
            line-height: 24px;
        }

        .el-input__inner {
            padding: 0 8px;
            font-size: 12px;
            border-radius: 2px;
            // padding-left: 9px;
        }

        .el-select__tags {
            height: 24px !important;
            overflow: hidden;

            .el-tag--info {
                color: @font_color_02;
            }

            .el-select-tags-wrapper {
                display: contents;

                > span {
                    margin-left: 4px;
                }
            }
        }

        .el-select__input.is-small {
            height: 24px;
        }

        .el-input.is-focus .el-input__inner {
            border-color: @border_focus;
        }

        .el-input__inner:focus {
            border-color: @border_focus;
        }
        // .el-input__suffix {
        //     // top: 4px;
        // }
        // .el-input--suffix .el-input__inner {
        //     padding-right: 10px;
        // }
        .el-input--small .el-input__icon {
            line-height: 24px !important;
        }
    }

    .el-select-dropdown__item.selected {
        color: @theme_main;
    }

    .crm_search_select_popper {
        /* min-width: 260px !important; */
        .el-scrollbar__wrap {
            overflow-x: auto;
        }

        .twosearch-result-item {
            display: flex;
            justify-content: space-between;

            .highlight_search_key {
                color: @theme_main;
            }
        }

        &.el-select-dropdown {
            margin-top: 4px;
        }

        .el-select-dropdown__list {
            .el-select-dropdown__item {
                padding: 0 16px;
                color: @font_color_05;
                white-space: inherit;

                .highlight_search_key {
                    color: @font_red_light;
                }

                .search_result_item {
                    display: flex;
                    flex-wrap: wrap;
                    width: 100%;
                    overflow: hidden;
                    font-size: 13px;
                    line-height: 1.2;

                    > div {
                        padding: 10px 0;
                    }

                    .fund-name {
                        flex: 5;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        word-break: break-all;
                        white-space: nowrap;
                    }

                    .fund-code {
                        flex: 1;
                        margin-left: auto;
                        text-align: right;
                    }

                    .fundType {
                        display: none;
                        width: 15%;
                        text-align: right;
                    }

                    .highlight_search_key {
                        color: @font_red_light;
                    }

                    &.search_manager {
                        .left {
                            width: 60px;
                            text-align: left;
                        }

                        .mid {
                            flex: 1;
                            text-align: center;
                        }

                        .right {
                            margin-left: auto;
                            text-align: right;
                        }
                    }
                }

                &.selected {
                    color: @theme_main;
                }

                &.is-disabled {
                    color: #c1c4cc;
                }

                &:not(.is-disabled):hover {
                    color: @theme_main;
                    background-color: rgba(200, 45, 48, 0.1);
                }
            }
        }

        .popper__arrow {
            display: none;
        }

        &.crm-search-other {
            .el-select-dropdown__list {
                .el-select-dropdown__item {
                    height: auto;
                    line-height: 1.2;
                }
            }
        }
    }
</style>
<style lang="less" scoped>
    .crm_search_select_popper {
        :deep(.el-select-dropdown__list) {
            .el-select-dropdown__item {
                &.is-disabled {
                    color: #c1c4cc;
                }

                &:not(.is-disabled):hover {
                    color: @theme_main;
                    background-color: rgba(200, 45, 48, 0.1);
                }
            }
        }
    }

    .result-item-tpl {
        display: flex;
        align-items: center;
        padding: 5px 0;
        padding-right: 40px;
    }

    .result-item-tpl:last-child {
        padding-right: 40px;
    }

    .result-item-tpl1 {
        min-width: 150px;
    }
</style>
