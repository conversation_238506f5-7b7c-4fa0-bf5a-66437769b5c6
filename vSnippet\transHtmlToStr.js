/*
 * @Description:
 * @Author: chaohui.wu
 * @Date: 2024-05-15 14:49:27
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-09-24 17:22:33
 * @FilePath: /crm-asset-web/vSnippet/transHtmlToStr.js
 *
 */
// import { snppetList } from './snppet.js'
const { snppetList } = require('./snppet.js')

// enum ScopeMap {
//     hb = 'vue,html',
//     ht = 'vue,html',
//     hu = 'javascript,typescript,vue',
//     hs = 'css,less,scss,vue',
//     he = 'javascript,typescript,vue'
// }

const scopeMap = {
    hb: 'vue,html',
    ht: 'vue,html,javascript,typescript',
    v3: 'javascript,typescript,vue',
    hu: 'javascript,typescript,vue',
    hs: 'css,less,scss,vue',
    he: 'javascript,typescript,vue'
}

/**
 * @description: 获取snippet并将其转换为对应格式
 * @param {SnppetItem} list
 * @return {*}
 */
const transSnppetListMethods = list => {
    const snppetList = list?.map(item => {
        const { scope, prefix, desTitle, des, bodyStr } = item || {}
        const bodyStrTpl = bodyStr.replace(/ {4}/g, '\t')
        return {
            key: `${prefix}.code-snippets`,
            content: JSON.stringify({
                [desTitle]: {
                    scope: scopeMap[scope],
                    prefix: prefix,
                    body: [bodyStrTpl],
                    description: des
                }
            })
        }
    })
    return snppetList
}

const transListData = transSnppetListMethods(snppetList)

/**
 * @description: 转换
 * @return {*}
 */
module.exports.transListData = transListData

// 将代码片段的列表转换为对应文件并输出到文件中
// console.log(str.replace(/ {4}/g, '\\t').replace(/\n/g, '\\n').replace(/"/g, '\\"'))
