/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const saveRecordTableColumn: TableColumnItem[] = [
    {
        key: 'optName',
        label: '操作',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'saveTime',
        label: '操作时间',
        width: 160,
        formatter: formatTableValue
    },
    {
        key: 'userId',
        label: '操作人',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'saveResult',
        label: '操作结果',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'orgShow',
        label: '备注',
        minWidth: 120,
        formatter: formatTableValue
    },
]

