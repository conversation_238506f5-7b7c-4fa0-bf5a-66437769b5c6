<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            :show-export-btn="true"
            @searchFn="handleLoading"
            @exportFn="exportHandle"
        >
            <template #searchArea>
                <!-- 产品代码输入框 -->
                <label-item :label="jjdm.label">
                    <crm-input
                        v-model="queryForm.jjdm"
                        :placeholder="jjdm.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <!-- 产品名称下拉框 -->
                <label-item :label="cpmc.label">
                    <crm-input
                        v-model="queryForm.cpmc"
                        :placeholder="cpmc.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <!-- 录入日期 -->
                <label-item :label="recordDate.label">
                    <date-range
                        v-model="queryForm.recordDate"
                        show-format="YYYY-MM-DD"
                        :placeholder="recordDate.placeholder"
                        style-type="fund"
                    />
                </label-item>

                <!-- 审核状态下拉框 -->
                <label-item :label="hszt.label">
                    <crm-select
                        v-model="queryForm.hszt"
                        :placeholder="hszt.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="hszt.selectList"
                        :style="{ width: '150px' }"
                        @change="handlehszt"
                    />
                </label-item>
            </template>
            <template #operationBtns>
                <crm-button
                    v-if="addShow"
                    size="small"
                    :icon="Plus"
                    type="primary"
                    :radius="true"
                    @click="handleAdd"
                    >新增</crm-button
                >
                <crm-button
                    v-if="editShow"
                    size="small"
                    :icon="Edit"
                    type="primary"
                    :radius="true"
                    @click="handleEdit"
                    >修改</crm-button
                >
                <crm-button
                    v-if="aduitShow"
                    size="small"
                    :icon="Check"
                    type="primary"
                    :radius="true"
                    @click="handleAudit"
                    >审核</crm-button
                >
                <crm-button
                    v-if="deleteShow"
                    size="small"
                    :icon="Minus"
                    type="primary"
                    :radius="true"
                    @click="handleBatchDelete"
                    >批量删除</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="true"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    @select="changeSelectTable"
                    @selectionChange="changeSelectTable"
                >
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>
        <product-index
            v-if="dialogVisible"
            v-model="dialogVisible"
            :fix-income-conf-product-add="true"
            @callback="handleClose()"
        >
        </product-index>
        <edit-product-index
            v-if="editDialogVisible"
            v-model="editDialogVisible"
            :fix-income-conf-product-edit="true"
            :fix-income-conf-product-data="editdata"
            @callback="handleClose()"
        >
        </edit-product-index>
        <aduit-product-index
            v-if="aduitDialogVisible"
            v-model="aduitDialogVisible"
            :fix-income-conf-product-aduit="true"
            :fix-income-conf-product-data="aduitdata"
            @callback="handleClose()"
        >
        </aduit-product-index>
    </div>
</template>

<script lang="ts" setup>
    import { Plus, Edit, Minus, Check } from '@element-plus/icons-vue'
    import { fetchRes, messageBox } from '@/utils'
    import { ElMessage } from 'element-plus'
    import { dataList } from './data/labelData'
    import ProductIndex from '@/views/report/finance/components/fixIncomeConfProductRateAdd.vue'
    import EditProductIndex from '@/views/report/finance/components/fixIncomeConfProductRateEdit.vue'
    import AduitProductIndex from '@/views/report/finance/components/fixIncomeConfProductRateAduit.vue'

    import { fixIncomeConfProductRateTableColumn, showTableColumn } from './data/tableData'
    import { ResVO } from '@/type'
    import {
        fixIncomeConfProductRateQuery,
        fixIncomeConfProductRateExport,
        fixIncomeConfProductRateDelete,
        getAuth
    } from '@/api/project/report/finance/fixIncomeConfProductRate/fixIncomeConfProductRate'

    const { recordDate, jjdm, cpmc, hszt } = dataList

    const listLoading = ref<boolean>(false)
    const dialogVisible = ref<boolean>(false)
    const editDialogVisible = ref<boolean>(false)
    const aduitDialogVisible = ref<boolean>(false)
    const addShow = ref<boolean>(false)
    const editShow = ref<boolean>(false)
    const aduitShow = ref<boolean>(false)
    const deleteShow = ref<boolean>(false)
    /**
     * @description: 关闭弹框刷新数据
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleClose = (): void => {
        queryList()
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        jjdm = ''
        cpmc = ''
        hszt = ''
        isReview = ''
        startDate = ''
        endDate = ''
        recordDate = {
            startDate: '',
            endDate: ''
        }
    }
    const queryForm = reactive(new QueryForm())
    // 筛选条件框的处理
    const handlecpmc = (val: string) => {
        queryForm.cpmc = val
    }
    const handlehszt = (val: string) => {
        queryForm.hszt = val
    }
    const handleisReview = (val: string) => {
        queryForm.isReview = val
    }

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            fixIncomeConfProductRateTableColumn.map(item => item.key),
            fixIncomeConfProductRateTableColumn
        )
    })
    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            fundCode: queryForm.jjdm,
            fundName: queryForm.cpmc,
            reviewState: queryForm.hszt,
            isReview: queryForm.isReview,
            startDate: queryForm.recordDate.startDate,
            endDate: queryForm.recordDate.endDate,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(fixIncomeConfProductRateQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                tableData.value = rows
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const pdfUrl = ref<string>('')
    const exportList = async () => {
        const params = {
            fundCode: queryForm.jjdm,
            fundName: queryForm.cpmc,
            reviewState: queryForm.hszt,
            isReview: queryForm.isReview,
            startDate: queryForm.recordDate.startDate,
            endDate: queryForm.recordDate.endDate
        }
        const res: any = await fixIncomeConfProductRateExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }
    // 控制新增和修改弹框页面显示
    const handleAdd = (): void => {
        console.log('add1111111')
        dialogVisible.value = true
    }
    // table选中数组
    const tableSelectList = ref<object[]>([])
    // table选中事件
    const changeSelectTable = (sel: ResVO[]): void => {
        // 默认选择按成立时间排序
        tableSelectList.value = unref(sel)
            .map((item: any) => {
                return item
            })
            .filter(item => item)
    }
    // 到编辑页面能看到的数据
    const editdata = ref({
        taskId: '',
        fundCode: '',
        fundName: '',
        valueStartDate: '',
        valueEndDate: '',
        countDay: '',
        yearDay: '',
        consultRate: '',
        manageRate: '',
        redemRate: '',
        redemDay: '',
        consultFormula: '',
        manageFormula: '',
        redemFormula: '',
        adjustAmount: '',
        selfCust: '',
        agarement: '',
        settleStartDate: '',
        settleEndDate: '',
        remark: '',
        performanceFormula: '',
        hswType: '',
        hbPerformanceRate1: '',
        hbPerformanceRate2: '',
        performanceRate1: '',
        performanceRate2: '',
        performancejtType: '',
        redemDate: '',
        settleDate: '',
        shareDate: '',
        closedPeriodDays: '',
        performanceBase1: '',
        performanceBase2: '',
        rateStartDate: '',
        rateEndDate: ''
    })
    const aduitdata = ref({
        taskId: ''
    })
    // 根据选中的Id进行修改操作
    const handleEdit = (): void => {
        const updateIds = tableSelectList.value
            .map((item: any) => {
                return item
            })
            .filter(item => item)
        if (updateIds.length > 1 || updateIds.length === 0) {
            ElMessage({
                message: '请选择一条数据进行修改',
                type: 'warning',
                duration: 2000
            })
            return
        }
        tableSelectList.value
            .map((item: any) => {
                editdata.value.taskId = item.taskId
                editdata.value.fundCode = item.fundCode
                editdata.value.fundName = item.fundName
                editdata.value.valueStartDate = item.valueStartDate
                editdata.value.valueEndDate = item.valueEndDate
                editdata.value.countDay = item.countDay
                editdata.value.yearDay = item.yearDay
                editdata.value.consultRate = item.consultRate
                editdata.value.manageRate = item.manageRate
                editdata.value.redemRate = item.redemRate
                editdata.value.redemDay = item.redemDay
                editdata.value.consultFormula = item.consultFormula
                editdata.value.manageFormula = item.manageFormula
                editdata.value.redemFormula = item.redemFormula
                editdata.value.adjustAmount = item.adjustAmount
                editdata.value.selfCust = item.selfCust
                editdata.value.agarement = item.agarement
                editdata.value.settleStartDate = item.settleStartDate
                editdata.value.settleEndDate = item.settleEndDate
                editdata.value.remark = item.remark
                editdata.value.performanceFormula = item.performanceFormula
                editdata.value.hswType = item.hswType
                editdata.value.hbPerformanceRate1 = item.hbPerformanceRate1
                editdata.value.hbPerformanceRate2 = item.hbPerformanceRate2
                editdata.value.performanceRate1 = item.performanceRate1
                editdata.value.performanceRate2 = item.performanceRate2
                editdata.value.performancejtType = item.performancejtType
                editdata.value.redemDate = item.redemDate
                editdata.value.settleDate = item.settleDate
                editdata.value.shareDate = item.shareDate
                editdata.value.closedPeriodDays = item.closedPeriodDays
                editdata.value.performanceBase1 = item.performanceBase1
                editdata.value.performanceBase2 = item.performanceBase2
                editdata.value.rateStartDate = item.rateStartDate
                editdata.value.rateEndDate = item.rateEndDate
            })
            .filter(item => item)
        editDialogVisible.value = true
    }

    // 控制新增和修改弹框页面显示
    const handleAudit = (): void => {
        const auditIds = tableSelectList.value
            .map((item: any) => {
                return item
            })
            .filter(item => item)
        if (auditIds.length > 1 || auditIds.length === 0) {
            ElMessage({
                message: '请选择一条数据进行审核',
                type: 'warning',
                duration: 2000
            })
            return
        }
        tableSelectList.value
            .map((item: any) => {
                aduitdata.value.taskId = item.taskId
            })
            .filter(item => item)
        aduitDialogVisible.value = true
    }

    // 批量删除操作
    const handleBatchDelete = (): void => {
        const taskIdList = tableSelectList.value.map((item: any) => {
            return item.taskId
        })
        if (taskIdList.length === 0) {
            ElMessage({
                message: '请至少选择一条数据进行删除',
                type: 'warning',
                duration: 2000
            })
            return
        }
        messageBox(
            {
                confirmBtn: '确定',
                content: `确认要删除这些记录吗?`
            },
            () => {
                confirmDelete()
            },
            () => false
        )
    }
    // 确认删除操作
    const confirmDelete = async () => {
        const taskIdList = tableSelectList.value.map((item: any) => {
            return item.taskId
        })
        const batchDeleteId = {
            taskIdList: taskIdList
        }
        fetchRes(fixIncomeConfProductRateDelete(batchDeleteId), {
            successCB: (res: any) => {
                ElMessage({
                    type: 'success',
                    message: '删除成功'
                })
                queryList()
            },
            errorCB: (res: any) => {
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => true,
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }
    /**
     * @description: 获取菜单权限
     * @return {*}
     */
    const getMenuAuth = async () => {
        const res: any = await getAuth()
        const operateList = res.data
        addShow.value = operateList?.some(
            (item: any) => item.operateName === '新增' && item.display === '1'
        )
        editShow.value = operateList?.some(
            (item: any) => item.operateName === '修改' && item.display === '1'
        )
        aduitShow.value = operateList?.some(
            (item: any) => item.operateName === '审核' && item.display === '1'
        )
        deleteShow.value = operateList?.some(
            (item: any) => item.operateName === '删除' && item.display === '1'
        )
    }
    /**
     * @description 提示
     */

    onBeforeMount(() => {
        getMenuAuth()
    })
</script>
<style lang="less" scoped></style>
