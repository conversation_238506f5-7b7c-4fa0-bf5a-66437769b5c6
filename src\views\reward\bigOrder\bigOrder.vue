<template>
    <div class="report-list-module">
        <table-wrap-cust
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
        >
            <template #searchArea>
                <table class="table-cust-select">
                    <tr>
                        <td>
                            <!-- 统计日期 -->
                            <LabelItemCust label="统计日期" class-name="text-left">
                                <el-date-picker
                                    v-model="queryForm.tradeYear"
                                    type="year"
                                    format="YYYY"
                                    value-format="YYYY"
                                    style="width: 150px; height: 24px"
                                    placeholder="请选择"
                                />
                                <crm-select
                                    v-model="queryForm.tradeQuarter"
                                    placeholder="请选择"
                                    filterable
                                    clearable
                                    label-format="label"
                                    value-format="key"
                                    :option-list="quarter.selectList"
                                    :style="{ width: '150px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 核算产品类型 -->
                            <LabelItemCust label="核算产品类型" class-name="text-left">
                                <crm-select
                                    v-model="queryForm.accountProductType"
                                    placeholder="请选择核算产品类型"
                                    filterable
                                    clearable
                                    label-format="label"
                                    value-format="key"
                                    :option-list="accountProductTypeSelectList"
                                    :style="{ width: '150px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 大单产品类型 -->
                            <LabelItemCust label="大单产品类型" class-name="text-left">
                                <crm-select
                                    v-model="queryForm.bigOrderProductType"
                                    placeholder="请选择大单产品类型"
                                    filterable
                                    clearable
                                    label-format="label"
                                    value-format="key"
                                    :option-list="bigOrderProductTypeSelectList"
                                    :style="{ width: '150px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 是否结算 -->
                            <LabelItemCust label="结算状态" class-name="text-left">
                                <crm-select
                                    v-model="queryForm.isSettlement"
                                    placeholder="请选择结算状态"
                                    filterable
                                    clearable
                                    label-format="label"
                                    value-format="key"
                                    :option-list="isSettlement.selectList"
                                    :style="{ width: '150px' }"
                                />
                            </LabelItemCust>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <!-- 预计结算日期 -->
                            <LabelItemCust label="预计结算日期" class-name="text-left">
                                <el-date-picker
                                    v-model="queryForm.settlementYear"
                                    type="year"
                                    format="YYYY"
                                    value-format="YYYY"
                                    style="width: 150px; height: 24px"
                                    placeholder="请选择"
                                />
                                <crm-select
                                    v-model="queryForm.settlementQuarter"
                                    placeholder="请选择"
                                    filterable
                                    clearable
                                    label-format="label"
                                    value-format="key"
                                    :option-list="quarter.selectList"
                                    :style="{ width: '150px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 产品名称 -->
                            <LabelItemCust label="产品名称" minwidth="180px" class-name="text-left">
                                <autocomplete
                                    v-model="queryForm.label"
                                    placeholder="请输入产品名称/代码"
                                    :search-func="autoProd"
                                    :style="{ width: '225px' }"
                                    @handleSet="handleProdCode"
                                />
                                <!-- <crm-input
                                    v-model="queryForm.productName"
                                    placeholder="请输入产品名称"
                                    :clearable="true"
                                    :style="{ width: '150px' }"
                                /> -->
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 客户姓名 -->
                            <LabelItemCust label="客户姓名" class-name="text-left">
                                <crm-input
                                    v-model="queryForm.custName"
                                    placeholder="请输入客户姓名"
                                    :clearable="true"
                                    :style="{ width: '150px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 预约ID -->
                            <LabelItemCust label="预约ID" class-name="text-left">
                                <crm-input
                                    v-model="queryForm.preId"
                                    placeholder="请输入预约ID"
                                    :clearable="true"
                                    :style="{ width: '150px' }"
                                />
                            </LabelItemCust>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <!-- 所属投顾 -->
                            <LabelItemCust label="所属投顾" class-name="text-left">
                                <ReleatedSelect
                                    ref="newlySelect"
                                    v-model="queryForm.orgvalue"
                                    :organization-list="organizationList"
                                    :cons-list-default="consultList"
                                    :default-org-code="orgCodeDefault"
                                    :default-cons-code="consCodeDefault"
                                    :cons-status="consStatus"
                                    :module="module"
                                    code-width="120px"
                                    :add-all="true"
                                ></ReleatedSelect>
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 投顾code -->
                            <LabelItemCust label="投顾code" class-name="text-left">
                                <crm-input
                                    v-model="queryForm.userId"
                                    placeholder="请输入投顾code"
                                    :clearable="true"
                                    :style="{ width: '150px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 投顾客户号 -->
                            <LabelItemCust label="投顾客户号" class-name="text-left">
                                <crm-input
                                    v-model="queryForm.consCustNo"
                                    placeholder="请输入投顾客户号"
                                    :clearable="true"
                                    :style="{ width: '150px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td></td>
                    </tr>
                </table>
            </template>
            <template #operationBtns>
                <ButtonCust size="small" :radius="true" plain @click.stop="queryList"
                    >查询</ButtonCust
                >
                <button-cust
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    plain
                    @click.stop="exportHandle"
                    >导出</button-cust
                >
                <button-cust
                    v-if="batchModifyShow"
                    size="small"
                    :radius="true"
                    plain
                    @click.stop="batchModifyHandle"
                    >批量修改</button-cust
                >
                <button-cust
                    v-if="batchSettlementShow"
                    size="small"
                    :radius="true"
                    plain
                    @click.stop="batchSettlementHandle"
                    >批量结算</button-cust
                >
            </template>
            <template #tableContentMiddle>
                <base-table-cust
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    :pops="{ selectFixed: 'left' }"
                    :row-class-name="handleRowClassName"
                    :cell-class-name="handleCellClassName"
                    @selectionChange="handleSelectionChange"
                >
                </base-table-cust>
            </template>
            <template #tableContentBottom>
                <pagination
                    :page="pageObj"
                    :total="pageObj.total"
                    :page-sizes="[20, 50, 100, 500, 1000]"
                    @change="handleCurrentChange"
                />
            </template>
        </table-wrap-cust>

        <batch-edit-performance-index
            v-if="batchModifyDialogVisible"
            v-model="batchModifyDialogVisible"
            :trans-data="batchModifyObj"
            :organization-list="organizationList"
            :consult-list="consultList"
            :org-code="orgCodeDefault"
            :cons-code="consCodeDefault"
            @callback="handleClose()"
        >
        </batch-edit-performance-index>
    </div>
</template>

<script lang="ts" setup>
    import { fetchRes } from '@/utils'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import BatchEditPerformanceIndex from '@/views/reward/bigOrder/components/bigOrderBatchEdit.vue'
    import BaseTableCust from '@/views/modBusiness/pageModule/components/BaseTableCust.vue'
    import LabelItemCust from '@/views/modBusiness/pageModule/components/LabelItemCust.vue'
    import TableWrapCust from '@/views/modBusiness/pageModule/components/TableWrapCust.vue'
    import ButtonCust from '@/views/modBusiness/pageModule/components/ButtonCust.vue'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import { ElMessage, ElLoading } from 'element-plus'
    import { getMenuPermission, autoProd, fetchConstant } from '@/api/project/common/common'
    import { REWARD_BIG_ORDER_OPER_PERMISSION } from '@/constant/rewardConst'
    import { bigOrderTableColumn, showTableColumn } from './data/tableData'
    import { dataList } from './data/labelData'
    import {
        queryBigOrderList,
        exportBigOrderList,
        bigOrderBatchSettlement
    } from '@/api/project/reward/bigOrder/bigOrder'
    import { ACCOUNT_PRODUCT_TYPE_NEW, BIG_ORDER_PRODUCT_TYPE } from '@/constant/hbConstant'

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const listLoading = ref<boolean>(false)

    const batchModifyDialogVisible = ref<boolean>(false)
    const batchModifyObj = ref<any>({})

    const exportShow = ref<boolean>(false)
    const batchModifyShow = ref<boolean>(false)
    const batchSettlementShow = ref<boolean>(false)
    const consStatus = ref<string>('0') //包含离职人员
    const module = ref<string>('071615')

    const accountProductTypeSelectList = ref<object[]>([])
    const bigOrderProductTypeSelectList = ref<object[]>([])
    // 表格数据
    const tableData = ref<object[]>([])
    //批量修改选中的id
    let checkPreIds = [] as string[]
    let checkSettlements = [] as string[]

    // 分页对象
    class PageObj {
        page = 1
        size = 100
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    // 从labelData中获取下拉选项
    const { isSettlement, quarter } = dataList

    // 表格列配置
    const tableColumn = computed(() => {
        return showTableColumn(
            bigOrderTableColumn.map(item => item.key),
            bigOrderTableColumn
        )
    })

    const handleRowClassName = (rowObj: any, rowIndex: number) => {
        const { row } = rowObj
        if (row && row.haveHumanModify === '1') {
            return 'highlight-bg'
        }
    }

    const handleCellClassName = (rowObj: any) => {
        const { row, column, rowIndex, columnIndex } = rowObj
        if (row && column) {
            if (
                column.property === 'bigOrderProductType' &&
                row.modBigOrderProductTypeFlag === '1'
            ) {
                return 'redColumn'
            }
            if (column.property === 'availVol' && row.modAvailVolFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'availCost' && row.modAvailCostFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'commissionRate' && row.modCommissionRateFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'achieveCoeff' && row.modAchieveCoeffFlag === '1') {
                return 'redColumn'
            }
            if (
                column.property === 'manageCoeffCommission' &&
                row.modManageCoeffCommissionFlag === '1'
            ) {
                return 'redColumn'
            }
        }
    }

    /**
     * @description: 关闭弹框刷新数据
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleClose = (): void => {
        queryList()
    }

    // 批量修改
    const batchModifyHandle = () => {
        console.log('批量修改')
        if (checkPreIds.length === 0) {
            ElMessage({
                type: 'info',
                message: '请先勾选需要批量修改的数据!'
            })
            return
        }

        // 校验选中的数据是否都未结算 筛选选择的数据
        const isAllSettled = checkSettlements.every(
            (item: any) => item === isSettlement.selectList[1].label
        )
        if (!isAllSettled) {
            ElMessage({
                type: 'warning',
                message: '勾选的记录中存在状态不是未结算的数据，请检查!'
            })
            return
        }
        batchModifyObj.value = {
            preIds: checkPreIds,
            type: 'edit',
            title: '批量修改'
        }
        batchModifyDialogVisible.value = true
    }

    // 批量结算
    const batchSettlementHandle = () => {
        console.log('批量结算')
        if (checkPreIds.length === 0) {
            ElMessage({
                type: 'info',
                message: '请先勾选需要批量结算的数据!'
            })
            return
        }
        debugger
        // 校验选中的数据是否都未结算 筛选选择的数据
        const isAllSettled = checkSettlements.every(
            (item: any) => item === isSettlement.selectList[1].label
        )
        if (!isAllSettled) {
            ElMessage({
                type: 'warning',
                message: '勾选的记录中存在状态不是未结算的数据，请检查!'
            })
            return
        }

        const params = {
            ids: checkPreIds
        }
        fetchRes(bigOrderBatchSettlement(params), {
            successCB: (resObj: any) => {
                console.log(resObj)
                queryList()
            },
            errorCB: () => {},
            catchCB: () => {},
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    const handleSelectionChange = (sel: any): void => {
        checkPreIds = sel.map((item: any) => item.preId)
        checkSettlements = sel.map((item: any) => item.settlementStatus)
        console.log(checkPreIds)
    }

    // 查询条件
    class QueryForm {
        userId = ''
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        custName = ''
        consCustNo = ''
        tradeYear = ''
        tradeQuarter = ''
        settlementYear = ''
        settlementQuarter = ''
        accountProductType = ''
        bigOrderProductType = ''
        productCode = ''
        productName = ''
        preId = ''
        isSettlement = ''
        label = ''
    }
    const queryForm = reactive(new QueryForm())

    const handleProdCode = (item: any) => {
        console.log(item)
        queryForm.productCode = item.productCode || ''
    }
    // 查询方法
    const queryList = async () => {
        listLoading.value = true
        const params = {
            userId: queryForm.userId,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            custName: queryForm.custName,
            consCustNo: queryForm.consCustNo,
            tradeYear: queryForm.tradeYear,
            tradeQuarter: queryForm.tradeQuarter,
            settlementYear: queryForm.settlementYear,
            settlementQuarter: queryForm.settlementQuarter,
            accountProductType: queryForm.accountProductType,
            bigOrderProductType: queryForm.bigOrderProductType,
            productCode: queryForm.productCode,
            productName: queryForm.productName,
            preId: queryForm.preId,
            isSettlement: queryForm.isSettlement,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }

        fetchRes(queryBigOrderList(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                tableData.value = rows
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    // 导出方法
    const exportHandle = async () => {
        const allLoading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)'
        })

        const params = {
            userId: queryForm.userId,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            custName: queryForm.custName,
            consCustNo: queryForm.consCustNo,
            tradeYear: queryForm.tradeYear,
            tradeQuarter: queryForm.tradeQuarter,
            settlementYear: queryForm.settlementYear,
            settlementQuarter: queryForm.settlementQuarter,
            accountProductType: queryForm.accountProductType,
            bigOrderProductType: queryForm.bigOrderProductType,
            productCode: queryForm.productCode,
            productName: queryForm.productName,
            preId: queryForm.preId,
            isSettlement: queryForm.isSettlement
        }

        const res: any = await exportBigOrderList(params)
        const { fileByte, name, errorMsg } = res.data
        if (errorMsg) {
            ElMessage({
                message: errorMsg,
                type: 'warning',
                duration: 2000
            })
            allLoading.close()
            return
        }
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
        allLoading.close()
    }

    const initData = async () => {
        const params = {
            menuCode: module.value
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode === REWARD_BIG_ORDER_OPER_PERMISSION.EXPORT &&
                            item.display === '1'
                        ) {
                            exportShow.value = true
                        }
                        if (
                            item.operateCode === REWARD_BIG_ORDER_OPER_PERMISSION.BATCH_MODIFY &&
                            item.display === '1'
                        ) {
                            batchModifyShow.value = true
                        }
                        if (
                            item.operateCode ===
                                REWARD_BIG_ORDER_OPER_PERMISSION.BATCH_SETTLEMENT &&
                            item.display === '1'
                        ) {
                            batchSettlementShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })

        fetchRes(
            fetchConstant({ typeCodeList: [ACCOUNT_PRODUCT_TYPE_NEW, BIG_ORDER_PRODUCT_TYPE] }),
            {
                successCB: (resObj: any) => {
                    const { constantTypeMap } = resObj
                    accountProductTypeSelectList.value = constantTypeMap[ACCOUNT_PRODUCT_TYPE_NEW]
                    bigOrderProductTypeSelectList.value = constantTypeMap[BIG_ORDER_PRODUCT_TYPE]
                },
                errorCB: () => {},
                catchCB: () => {},
                successTxt: '',
                failTxt: '请求失败请重试！',
                fetchKey: ''
            }
        )
    }

    // 监听默认值变化
    watch(
        [orgCodeDefault, consCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.orgvalue.orgCode = orgCodeDefault.value
                queryForm.orgvalue.consCode = consCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    onMounted(() => {
        fetchConsOrgList('', module.value)
        initData()
    })
</script>

<style lang="less" scoped>
    .table-cust-select {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        tr {
            background-color: #ffffff;

            td {
                border: 1px solid #dddddd;
            }
        }
    }

    :deep(.el-date-editor .el-input__inner::placeholder) {
        font-size: 12px; // 调整为需要的字号
    }
</style>
