<!--
 * @Description: 测试案例
 * @Author: chaohui.wu
 * @Date: 2024-09-27 10:48:16
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-14 09:50:08
 * @FilePath: /ds-report-web/src/views/modBusiness/ModuleKeyDia.vue
 *  待完善，目前还没搞定
-->
<!--
* 设置表格显示字段
* @props title: 弹框标题
* @props dialogVisible: 显示或隐藏
-->
<template>
    <div class="cust-module-box" :class="styleClass">
        <!-- 指标设置弹框 -->
        <crm-dialog v-model="dialogVisible" width="800px" :title="title" @close="handleClose">
            <!-- 是否支持模版设置模式 -->
            <div v-if="supportModel" class="model-select">
                <el-select
                    v-model="modelId"
                    size="small"
                    placeholder="请选择"
                    @change="changeModelId"
                >
                    <el-option
                        v-for="item in localAllModels"
                        :key="item.templateId"
                        :label="item.templateName"
                        :value="item.templateId"
                    >
                        <div class="flex">
                            <span class="flex-1">{{ item.templateName }}</span>
                            <el-icon
                                v-if="!item.isDefault"
                                style="margin: auto"
                                @click="checkOption('edit', item)"
                            >
                                <EditPen />
                            </el-icon>
                        </div>
                    </el-option>
                    <el-option value="新增模板">
                        <div class="flex" style="align-items: center">
                            <el-icon style="margin-right: 5px">
                                <CirclePlus />
                            </el-icon>
                            <span>新增模板</span>
                        </div>
                    </el-option>
                </el-select>
            </div>
            <!-- 表格字段选择 -->
            <div class="table-col-wrap" :class="{ 'table-col-wrap2': supportModel }">
                <div class="tableColumns left">
                    <div class="columnsHeader">
                        <div class="title">可选字段</div>
                        <div class="handleBtns">
                            <!-- <span @click="handleCheckAll">全部添加</span> -->
                        </div>
                    </div>
                    <div class="filterBar">
                        <el-input
                            v-model="filterText"
                            placeholder="输入关键字进行过滤"
                            :suffix-icon="Search"
                        />
                    </div>
                    <div class="set-tree tree-select-box">
                        <el-tree
                            ref="tree"
                            :data="tableColumns"
                            show-checkbox
                            check-on-click-node
                            :expand-on-click-node="false"
                            :default-expand-all="expandAll"
                            node-key="key"
                            :default-checked-keys="checkList"
                            :props="defaultProps"
                            :filter-node-method="filterNode"
                            @check="checkCb"
                        >
                            <template #default="{ node }">
                                <span v-html="highlightSearchKey(node.label)" />
                            </template>
                        </el-tree>
                    </div>
                    <!-- </div> -->
                </div>
                <div class="tableColumns right">
                    <div class="columnsHeader">
                        <div class="title">
                            已选字段 {{ rankListHasNoChildren.length }}/{{ maxNum }}
                            <span style="font-size: small">(可拖拽调整字段展示顺序)</span>
                        </div>
                        <div class="handleBtns">
                            <!-- <span @click="searchModelDetail">重置</span> -->
                            <span @click="resetChecked">清空</span>
                        </div>
                    </div>
                    <div class="set-tree selected-colunms">
                        <draggable
                            v-if="rankListHasNoChildren.length > 0"
                            v-model="rankListHasNoChildren"
                            filter=".disabled"
                            item-key="key"
                        >
                            <template #item="{ element }">
                                <div
                                    :class="
                                        element?.disabled
                                            ? 'selected-col-item disabled'
                                            : 'selected-col-item'
                                    "
                                >
                                    <span class="iconfont">
                                        <svg-icons name="drag"></svg-icons>
                                    </span>
                                    <span class="label-name">{{
                                        element[defaultProps.label]
                                    }}</span>
                                    <el-icon
                                        v-if="!element?.disabled"
                                        @click="() => deleteSelectedColumn(element)"
                                    >
                                        <Close />
                                    </el-icon>
                                </div>
                            </template>
                        </draggable>
                    </div>
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <crm-button size="small" :radius="true" plain @click="handleClose"
                        >取 消</crm-button
                    >
                    <crm-button size="small" :radius="true" :click-func="promiseResetTableColumns"
                        >应 用</crm-button
                    >
                </div>
            </template>
        </crm-dialog>

        <!-- 模版编辑弹框-->
        <crm-dialog
            v-model="modelVisible"
            width="800px"
            class="model-detail-dialog"
            :title="modelTitle"
            @close="cancelAdd"
        >
            <el-input
                v-model="activeModelItem.templateName"
                placeholder="请输入内容"
                maxlength="16"
                show-word-limit
            />
            <template #footer>
                <div class="dialog-footer">
                    <span
                        v-if="!!activeModelItem.templateId"
                        class="deleteModel"
                        @click="deleteModel"
                        >删除当前模板
                    </span>
                    <base-button black size="small" @click="cancelAdd">取 消</base-button>
                    <base-button type="primary" size="small" @click="submitModelEdit()"
                        >确 定</base-button
                    >
                </div>
            </template>
        </crm-dialog>
    </div>
</template>

<script setup lang="ts">
    import draggable from 'vuedraggable'
    import { EditPen, CirclePlus, Close, Search } from '@element-plus/icons-vue'
    import { useKeyDia } from './hooks/useKeyDia' // 获取关键字逻辑
    import { useTepDia } from './hooks/useTepDia' // 获取模版逻辑

    const props = withDefaults(
        defineProps<{
            modelValue: number | string | null
            modelType: string
            visible?: boolean
            supportModel?: boolean
            title?: string
            tableColumns?: any[]
            checkList?: any[]
            styleClass?: string
            expandAll?: boolean
            maxNum?: number
        }>(),
        {
            modelValue: '',
            modelType: '',
            visible: false,
            supportModel: true,
            title: '设置显示字段',
            tableColumns: () => {
                return []
            },
            checkList: () => [],
            styleClass: '',
            expandAll: true,
            maxNum: 40
        }
    )

    const emit = defineEmits(['update:modelValue', 'update:visible', 'change'])

    // const rankList = ref<Array<any>>([])
    const dialogVisible = ref(true)
    const defaultProps = ref({
        children: 'children',
        label: 'label'
    })
    const tree = ref<any>(null)

    // 关键指标设置
    const {
        filterText,
        flatTableColumns,
        rankListHasNoChildren,
        initData,
        handleClose,
        deleteSelectedColumn,
        checkCb,
        filterNode,
        resetChecked,
        handleCheckAll,
        highlightSearchKey
    } = useKeyDia({
        props,
        tree,
        emit
    })

    // template 模版设置
    const {
        localAllModels,
        modelId,
        activeModelItem,
        modelVisible,
        modelTitle,
        // queryModelList,
        // searchModelDetail,
        submitModelEdit,
        deleteModel,
        checkOption,
        changeModelId,
        promiseResetTableColumns,
        cancelAdd
    } = useTepDia({
        props,
        tree,
        flatTableColumns,
        rankListHasNoChildren,
        emit,
        handleClose,
        resetChecked
    })

    watch(
        () => props.visible,
        val => {
            if (val) {
                filterText.value = ''
                modelId.value = props.modelValue
                // searchModelDetail()
                // queryModelList()
            }
        }
    )

    watch(filterText, val => {
        if (tree.value) {
            tree.value?.filter(val)
        }
    })

    onMounted(() => {
        initData()
    })
</script>

<style lang="less" scoped>
    .cust-module-box {
        min-height: 100vh;

        .model-select {
            flex: 0 0 35px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e6e6e6;

            .el-input__wrapper {
                height: 26px;
            }
        }

        .table-col-wrap {
            box-sizing: border-box;
            display: flex;
            flex: 1;
            flex-direction: row;
            width: 100%;
            height: 410px;
            padding: 0 15px 0 20px;
            font-size: 12px;
            background-color: #ffffff;

            .tableColumns {
                display: flex;
                flex: 1;
                flex-direction: column;

                &.left {
                    padding-right: 10px;
                    border-right: 2px solid #e6e6e6;
                }

                &.right {
                    padding-left: 20px;
                }

                .columnsHeader {
                    display: flex;
                    flex: 0 0 39px;
                    padding-top: 14px;
                    line-height: 25px;

                    .title {
                        font-size: 15px;
                        color: #252b3d;
                    }

                    .handleBtns {
                        margin-left: auto;
                        font-size: 13px;
                        color: #5262eb;

                        span {
                            cursor: pointer;

                            &:not(:first-child) {
                                margin-left: 20px;
                            }
                        }
                    }
                }

                .filterBar {
                    display: flex;
                    flex: 0 0 32px;
                    align-items: center;
                    padding: 2px 0;
                    border-bottom: 1px solid #e6e6e6;

                    .el-input__wrapper {
                        box-shadow: none;
                    }

                    .el-input {
                        .el-input__inner {
                            height: 26px;
                            font-size: 13px;
                            line-height: 26px;
                            color: #252b3d;
                            border: none;
                            box-shadow: 0 0 0 0 #ffffff inset;
                        }
                    }

                    .el-icon-search {
                        margin: 0 14px 0 auto;
                        font-size: 14px;
                        color: #252b3d;
                        cursor: pointer;
                    }
                }
            }

            .set-tree {
                flex: 1;
                height: 100%;
                padding: 10px 0;
                overflow: hidden;

                > div {
                    height: 100%;
                    padding-right: 10px;
                    overflow-y: auto;

                    &::-webkit-scrollbar {
                        width: 4px;
                    }

                    &::-webkit-scrollbar-thumb {
                        background-color: #e5e5e9;
                        box-shadow: none;
                    }

                    &::-webkit-scrollbar-track {
                        background-color: #f2f2f2;
                        box-shadow: none;
                    }
                }

                :deep(.el-tree) {
                    flex-flow: column wrap;
                    width: 100%;
                    white-space: normal;

                    .el-tree-node {
                        min-width: 260px;

                        .el-tree-node__content {
                            font-size: 13px;
                            font-weight: bold;
                            color: #252b3d;

                            > span {
                                line-height: 26px;
                            }

                            .highlight_search_key {
                                color: #e57471;
                            }

                            // .el-checkbox__input {
                            //     &.is-disabled {
                            //         .el-checkbox__inner {
                            //             background-color: #e6e6e6;
                            //         }
                            //     }
                            // }
                        }
                    }
                }
            }

            .selected-colunms {
                .selected-col-item {
                    display: flex;
                    align-items: center;
                    padding: 3px 0;
                    cursor: move;

                    span {
                        vertical-align: middle;

                        &.label-name {
                            padding-right: 4px;
                            font-size: 13px;
                            color: #252b3d;
                        }

                        &.iconfont {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-right: 5px;
                            font-size: 15px;
                            color: #878ba5;
                        }
                    }

                    i {
                        margin-left: auto;
                        color: #878ba5;
                        cursor: pointer;
                    }
                }
            }
        }

        .table-col-wrap2 {
            height: 365px;
        }

        :deep(.el-input__wrapper) {
            box-shadow: none;

            &.is-focus {
                box-shadow: none;
            }
        }

        :deep(.el-dialog) {
            // overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
            height: 500px;
            padding: 0;

            .el-dialog__header {
                // flex: 0 0 40px;
                padding: 0 20px;
                margin: 0;
                line-height: 40px;

                // background-color: @bg_color_2;
                .el-dialog__title {
                    font-size: 14px;
                    font-weight: bold;
                    color: #252b3d;
                }
            }

            .el-dialog__headerbtn {
                top: 0;
                right: 2px;
                width: 40px;
                height: 40px;
            }

            .el-dialog__footer {
                // flex: 0 0 50px;
                // width: 100%;
                padding: 12px 15px;
                text-align: right;
                // background-color: @bg_color_2;
            }

            .el-dialog__body {
                display: flex;
                flex: 1;
                // height: 100%;
                flex-direction: column;
                width: 100%;
                padding: 0 15px;
                overflow: hidden;
                background-color: #eff1f5;
            }
        }

        &.bigWrap {
            :deep(.el-dialog) {
                .el-dialog__body {
                    .table-col-wrap {
                        .tableColumns {
                            .el-checkbox-group {
                                label {
                                    min-width: 20%;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .model-detail-dialog {
        .el-dialog {
            min-width: 500px;
            max-width: 500px;
            min-height: 200px;
        }

        .deleteModel {
            margin-right: 220px;
            line-height: 32px;
            color: #409eff;
            cursor: pointer;
        }
    }

    .tree-select-box {
        margin: 15px 30px 0 0;

        :deep(.el-select) {
            .el-input__wrapper {
                height: 26px;
            }
        }
    }
</style>
