/*
 * @Description:
 * @Author: chao<PERSON>.wu
 * @Date: 2023-07-25 17:08:03
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-08-08 10:48:25
 * @FilePath: /crm-asset-web/src/utils/loadDom.ts
 *
 */
import Loading from '@/components/moduleBase/Loading.vue'

const msg = reactive({
    visible: false,
    type: '2',
    showPercentage: false,
    loadingTxt: '加载中，请稍候...'
})

const $loading = createApp(Loading, msg).mount(document.createElement('div'))
const load = {
    show() {
        // 控制显示loading的方法
        msg.visible = true
        document.body.appendChild($loading.$el)
        $loading.$el.style.display = 'block'
    },

    hide() {
        // 控制loading隐藏的方法
        msg.visible = false
        $loading.$el.style.display = 'none'
    }
}
export { load }
