/*
 * @Description: 定义搜索的label列表
 * @Author: jian<PERSON><PERSON>.yang
 * @Date: 2024-04-01 10:40:30
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-04-01 10:40:30
 * @FilePath: /src/views/report/kpi/assetRepDownload/data/labelData.ts
 *
 */
export const dataList = {
    // 0-离职，1-在职
    workState: {
        label: '在职状态',
        placeholder: '请选择在职状态',
        selectList: [
            {
                key: '0',
                label: '离职'
            },
            {
                key: '1',
                label: '在职'
            }
        ]
    },
    // 是否参与考核
    isKpi: {
        label: '是否参与考核',
        placeholder: '请选择是否参与考核',
        selectList: [
            {
                key: '是',
                label: '是'
            },
            {
                key: '否',
                label: '否'
            }
        ]
    },
    userId: {
        label: '员工编码',
        placeholder: '请输入员工编码'
    },
    orgData: {
        label: '投顾管理层',
        placeholder: '请选择所在组织'
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
