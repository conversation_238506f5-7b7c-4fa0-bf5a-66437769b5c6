export {}
declare module './apiReqType' {
    type performanceAssetHandConfParam = {
        /**
         * 查询类型  0-手工存续D配置， 1-理财师手工存续 D，2-管理层手工存续D
         */
        flag?: string
        /**
         * 业务类型
         */
        busitype?: string
        /**
         * 层级
         */
        conslevel?: string
        /**
         * 部门
         */
        orgCode?: string
        /**
         * 投顾号
         */
        conscode?: string
        /**
         * 投顾客户号
         */
        conscustno?: string
        /**
         * 客户姓名
         */
        custname?: string
        /**
         * 录入开始时间
         */
        recordStartDate?: string
        /**
         * 录入结束时间
         */
        recordEndDate?: string
        /**
         * 审核状态
         */
        auditState?: string
        /**
         * 在职状态
         */
        workState?: string
        /**
         * 产品名称
         */
        fundname?: string
        /**
         * 第一来源
         */
        firstSource?: string
        /**
         * 查询年
         */
        querYear?: string
    }
    type performanceAssetHandConfAddOrUpdateParam = {
        busitype?: string
        conslevel?: string
        custInfo?: string
        consCode?: string
        conscustno?: string
        fundname?: string
        scale?: string
        stockfeed?: string
        firstSource?: string
        foldcoeff?: string
        managecoeff?: string
        otherrate?: string
        assetcalcstartdt?: string
        assetcalcenddt?: string
        remark?: string
    }

    // 默认信息参数表
    type defaultInfoParam = {
        /**
         * 层级
         */
        conslevel?: string
        /**
         * 投顾code
         */
        conscode?: string
        /**
         * 投顾客户号
         */
        conscustno?: string
    }

    // 审核参数
    type aduitParam = {
        /**
         * id
         */
        id?: string
        /**
         * 审核状态
         */
        auditState?: string
        /**
         * 审核意见
         */
        reviewAdvice?: string
    }
    // 删除参数
    type deleteParam = {
        /**
         * id
         */
        id?: string
        /**
         * 审核状态
         */
        delreason?: string
    }
    type CustVO = {
        /**
         * 投顾客户号
         */
        queryStr?: string
        /**
         * 页码
         */
        page?: number
        /**
         * 每页显示多少条
         */
        rows?: number
    }

    export {
        performanceAssetHandConfParam,
        performanceAssetHandConfAddOrUpdateParam,
        aduitParam,
        deleteParam,
        defaultInfoParam,
        CustVO
    }
}
