<template>
    <div
        style="
            padding-top: 20px;
            padding-bottom: 10px;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
        "
    >
        健康客户明细
    </div>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_title_wraper"
            :show-operation-left="true"
            :show-search-area="false"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #operationLeft>
                <Text type="info" size="small" style="margin-left: 10px; font-size: 14px"
                    >统计时间：
                    <span class="last-update-time">{{ sdateRef }}</span>
                </Text>
            </template>
            <template #operationBtns>
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="exportHandle"
                    >导出</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :no-index="true"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                >
                </base-table>
            </template>
        </table-wrapper>
    </div>
</template>

<script lang="ts" setup>
    import { Download } from '@element-plus/icons-vue'
    import { fetchRes } from '@/utils'
    import { healthDetailTableColumn, showTableColumn } from './data/detailTableData'
    import { getMenuPermission } from '@/api/project/common/common'
    import { HEALTH_CUSTOMER_OPER_PERMISSION } from '@/constant/reportConst'
    import { useRoute } from 'vue-router'

    import {
        // eslint-disable-next-line camelcase
        healthDetailQuery,
        healthDetailExport
    } from '@/api/project/report/health/healthDetail/healthDetail'

    const module = ref<string>('140901')
    const exportShow = ref<boolean>(false)

    const listLoading = ref<boolean>(false)

    const sdateRef = ref<string>('')

    /**
     * @description: 分页数据
     * @return {*}
     */
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 100
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    const queryParams = ref<any>({})

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            healthDetailTableColumn.map(item => item.key),
            healthDetailTableColumn
        )
    })

    //查詢
    const queryList = async () => {
        listLoading.value = true
        fetchRes(healthDetailQuery(queryParams.value), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows } = resObj
                tableData.value = rows
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    const initData = async () => {
        const params = {
            menuCode: module.value
        }
        listLoading.value = true
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode === HEALTH_CUSTOMER_OPER_PERMISSION.HEALTH_EXPORT &&
                            item.display === '1'
                        ) {
                            exportShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })

        const route = useRoute()

        const sdate = route.query.sdate
        if (typeof sdate === 'string') {
            // yyyymmdd => yyyy/mm/dd
            sdateRef.value = sdate.slice(0, 4) + '/' + sdate.slice(4, 6) + '/' + sdate.slice(6, 8)
        }
        queryParams.value = {
            sdate: route.query.sdate,
            consCode: route.query.consCode,
            userLevel: route.query.userLevel
        }

        queryList()
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }
    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const res: any = await healthDetailExport(queryParams.value)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }
    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        initData()
    })
</script>
<style lang="less" scoped></style>
