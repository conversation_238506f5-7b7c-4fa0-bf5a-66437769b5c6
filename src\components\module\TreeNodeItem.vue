<template>
    <div class="custom-tree-node tree-node-item" @contextmenu.prevent.stop="rightClick()">
        <!-- :style="`left: ${fixLeft}px;top: ${fixTop}px;`" -->
        <ul
            v-if="node.isCurrent && isShow"
            class="el-dropdown-menu el-popper el-dropdown-menu--small"
        >
            <li v-if="addFlag" class="el-dropdown-menu__item" @click="handleAddRole">
                {{ addBtnTxt }}
            </li>
            <li v-if="deleteFlag" class="el-dropdown-menu__item" @click="handleRemoveRole">删除</li>
            <li v-if="renameFlag" class="el-dropdown-menu__item" @click="handleEditRole">重命名</li>
            <slot name="otherOptions" />
        </ul>
        <div class="tree-node-label-text">
            <span v-html="highlightSearchKey(node.label)" />
        </div>
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'TreeNodeItem',
        props: {
            node: {
                type: Object,
                default: () => {
                    return {}
                }
            },
            addFlag: {
                type: Boolean,
                default: true
            },
            addBtnTxt: {
                type: String,
                default: '新增'
            },
            deleteFlag: {
                type: Boolean,
                default: true
            },
            renameFlag: {
                type: Boolean,
                default: true
            },
            handleAddRole: {
                type: Function,
                default: () => undefined
            },
            handleRemoveRole: {
                type: Function,
                default: () => undefined
            },
            handleEditRole: {
                type: Function,
                default: () => undefined
            },
            // 编辑权限，无权限的时候右键不显示操作项
            editPermission: {
                type: Boolean,
                default: true
            },
            // 搜索关键字
            keyword: {
                type: String,
                default: ''
            }
        },
        data() {
            return {
                isShow: false
                // fixLeft: 10,
                // fixTop: 10,
                // clientHeight: 0
            }
        },
        watch: {
            node: {
                // 父组件切换当前选择时   将isshow关闭  不然下次点回来  会直接开启
                handler: function (newVal) {
                    this.isShow = false
                },
                deep: true
            }
        },
        mounted() {
            // this.clientHeight = `${document.documentElement.clientHeight}`
            document.querySelector('.pageContainer').addEventListener('click', e => {
                this.isShow = false
            })
        },
        methods: {
            rightClick() {
                // this.fixLeft = e.clientX
                // this.fixTop =
                //     e.clientY > Number(this.clientHeight) - 130
                //         ? Number(this.clientHeight) - 130
                //         : e.clientY
                if (this.node.isCurrent && this.editPermission) {
                    this.isShow = true
                }
            },
            // 替换搜索关键字
            highlightSearchKey(text) {
                const keyword = this.keyword || ''
                if (!keyword) {
                    return text
                }
                const replaceReg = new RegExp(keyword, 'g') // 匹配关键字正则
                const replaceString = '<span class="red">' + keyword + '</span>' // 高亮替换v-html值
                return text.replace(replaceReg, replaceString) // 开始替换
            }
        }
    })
</script>
<style lang="less" scoped>
    .tree-node-item {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .el-dropdown-menu {
            // position: fixed !important;
            // top: 20px;
            // left: 20px;
            top: 27px;
            left: 10px;
            z-index: 1000;
            padding: 5px 0;
            border: 1px solid #ebeef5;
            box-shadow: 0 0 5px 1px #ebeef5;

            li {
                padding: 0 18px;
            }

            &::before {
                position: absolute;
                top: -3px;
                left: 50%;
                z-index: 100;
                display: block;
                width: 8px;
                height: 8px;
                content: '';
                background-color: @font_color_01;
                border: 1px solid #ebeef5;
                border-right: none;
                border-bottom: none;
                // box-shadow: -2px -2px 12px 0 rgba(0, 0, 0, 0.1);
                transform: rotate(45deg) translateX(-50%);
            }
        }

        .highlight_search_key {
            color: @font_red_light;
        }
    }
</style>
