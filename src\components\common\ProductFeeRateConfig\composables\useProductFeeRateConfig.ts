/*
 * @Description: 产品费率配置主要逻辑hooks
 * @Author: hongdong.xie
 * @Date: 2025-06-06 09:42:20
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 09:42:20
 * @FilePath: /ds-report-web/src/components/common/ProductFeeRateConfig/composables/useProductFeeRateConfig.ts
 */

import { ref, reactive, shallowRef } from 'vue'
import { ElMessage } from 'element-plus'
import { fetchRes, messageBox } from '@/utils'
import type { ProductFeeRateConfig, BaseQueryParam, BaseExportParam, BaseDeleteParam, BaseAuditParam, QueryForm, PageObj } from '../types'
import { formatDateToBackend, handleExportFile, handleApiError, handleApiCatch, validateSelectedCount } from '../utils'

/**
 * @description: 产品费率配置主要逻辑hooks
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {ProductFeeRateConfig} config 配置对象
 * @return {*}
 */
export const useProductFeeRateConfig = (config: ProductFeeRateConfig) => {
    // 响应式数据
    const listLoading = ref<boolean>(false)
    const addShow = ref<boolean>(false)
    const copyAddShow = ref<boolean>(false)
    const editShow = ref<boolean>(false)
    const auditShow = ref<boolean>(false)
    const deleteShow = ref<boolean>(false)

    // 弹框相关数据
    const addDialogVisible = ref<boolean>(false)
    const dialogType = ref<'add' | 'copyAdd' | 'edit' | 'audit'>('add')
    const editData = ref<any>(null)

    // 审核弹框相关数据
    const auditDialogVisible = ref<boolean>(false)
    const auditData = ref<any>(null)

    // 查询条件
    const queryForm = reactive<QueryForm>({
        productFullName: '',
        payerFullName: '',
        filingCode: '',
        auditStatus: '0',
        queryStartDate: '',
        queryEndDate: '',
        recordDate: {
            startDate: '',
            endDate: ''
        },
        page: 1,
        rows: 10,
        rateType: config.rateType
    })

    // 分页数据
    const pageObj = shallowRef<PageObj>({
        page: 1,
        size: 10,
        total: 0,
        perPage: 1
    })

    // 表格数据
    const tableData = ref<object[]>([])
    const tableSelectList = ref<any[]>([])

    // 搜索条件配置
    const searchConfig = {
        filingCode: {
            label: '备案代码',
            placeholder: '请输入备案代码(6位)'
        },
        productFullName: {
            label: '产品全称',
            placeholder: '请输入产品全称'
        },
        payerFullName: {
            label: '付款方全称',
            placeholder: '请输入付款方全称'
        },
        recordDate: {
            label: '录入日期',
            placeholder: ['开始日期', '结束日期']
        },
        auditStatus: {
            label: '审核状态',
            placeholder: '请选择审核状态',
            selectList: [
                { key: '0', label: '全部' },
                { key: '1', label: '待审核' },
                { key: '2', label: '审核通过' },
                { key: '3', label: '审核不通过' }
            ]
        }
    }

    /**
     * @description: 查询列表数据
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @return {Promise<void>}
     */
    const queryList = async (): Promise<void> => {
        console.log(`🔍 开始查询${config.pageConfig.title}数据...`)
        listLoading.value = true

        const params: BaseQueryParam = {
            productFullName: queryForm.productFullName,
            payerFullName: queryForm.payerFullName,
            filingCode: queryForm.filingCode,
            auditStatus: queryForm.auditStatus,
            queryStartDate: formatDateToBackend(queryForm.recordDate.startDate),
            queryEndDate: formatDateToBackend(queryForm.recordDate.endDate),
            page: pageObj.value.page,
            rows: pageObj.value.size,
            rateType: queryForm.rateType
        }

        console.log('📤 查询参数:', params)

        // 使用配置的API URL
        try {
            const { axiosRequest } = await import('@/utils/index')
            const { paramsMerge } = await import('@/api/mock.js')

            const queryFunction = (params: any) => {
                return axiosRequest(
                    paramsMerge({
                        url: config.apiConfig.queryUrl,
                        method: 'post',
                        data: params
                    })
                )
            }

            fetchRes(queryFunction(params), {
                successCB: (resObj: any) => {
                    console.log('✅ 查询成功，返回数据:', resObj)
                    listLoading.value = false
                    const { rows, total } = resObj
                    console.log('📊 数据行数:', rows?.length, '总记录数:', total)
                    tableData.value = rows
                    pageObj.value.total = Number(total)
                    pageObj.value.size = Number(pageObj.value.size)
                },
                errorCB: (error: any) => {
                    listLoading.value = false
                    handleApiError(error, '查询失败，请重试')
                },
                catchCB: (error: any) => {
                    listLoading.value = false
                    handleApiCatch(error)
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        } catch (error) {
            listLoading.value = false
            console.error('❌ API模块加载失败:', error)
            ElMessage({
                type: 'error',
                message: 'API模块加载失败'
            })
        }
    }

    /**
     * @description: 导出数据
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @return {Promise<void>}
     */
    const exportList = async (): Promise<void> => {
        console.log(`📤 开始导出${config.pageConfig.title}数据...`)

        const params: BaseExportParam = {
            productFullName: queryForm.productFullName,
            payerFullName: queryForm.payerFullName,
            filingCode: queryForm.filingCode,
            auditStatus: queryForm.auditStatus,
            queryStartDate: formatDateToBackend(queryForm.recordDate.startDate),
            queryEndDate: formatDateToBackend(queryForm.recordDate.endDate),
            rateType: queryForm.rateType
        }

        console.log('📋 导出参数:', params)

        try {
            const { axiosRequest } = await import('@/utils/index')
            const { paramsMerge } = await import('@/api/mock.js')

            const exportFunction = (params: any) => {
                return axiosRequest(
                    paramsMerge({
                        url: config.apiConfig.exportUrl,
                        method: 'post',
                        data: params
                    })
                )
            }

            const res: any = await exportFunction(params)
            handleExportFile(res, config.pageConfig.exportFileName)
        } catch (error) {
            console.error('❌ 导出失败:', error)
            ElMessage({
                type: 'error',
                message: '导出失败，请重试'
            })
        }
    }

    /**
     * @description: 删除数据
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {any} deleteItem 要删除的数据项
     * @return {Promise<void>}
     */
    const confirmDelete = async (deleteItem: any): Promise<void> => {
        const deleteParam: BaseDeleteParam = {
            id: deleteItem.id,
            rateType: config.rateType
        }

        console.log('🗑️ 删除参数:', deleteParam)

        try {
            const { axiosRequest } = await import('@/utils/index')
            const { paramsMerge } = await import('@/api/mock.js')

            const deleteFunction = (params: any) => {
                return axiosRequest(
                    paramsMerge({
                        url: config.apiConfig.deleteUrl,
                        method: 'post',
                        data: params
                    })
                )
            }

            fetchRes(deleteFunction(deleteParam), {
                successCB: (res: any) => {
                    ElMessage({
                        type: 'success',
                        message: '删除成功'
                    })
                    queryList()
                },
                errorCB: (error: any) => {
                    handleApiError(error, '删除失败，请重试')
                },
                catchCB: (error: any) => {
                    handleApiCatch(error)
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        } catch (error) {
            console.error('❌ API模块加载失败:', error)
            ElMessage({
                type: 'error',
                message: 'API模块加载失败'
            })
        }
    }

    /**
     * @description: 获取菜单权限
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @return {Promise<void>}
     */
    const getMenuAuth = async (): Promise<void> => {
        try {
            const { axiosRequest } = await import('@/utils/index')
            const { paramsMerge } = await import('@/api/mock.js')

            const authFunction = () => {
                return axiosRequest(
                    paramsMerge({
                        url: config.apiConfig.authUrl,
                        method: 'get'
                    })
                )
            }

            const res: any = await authFunction()
            const operateList = res.data
            addShow.value = operateList?.some(
                (item: any) => item.operateName === '新增' && item.display === '1'
            )
            copyAddShow.value = operateList?.some(
                (item: any) => item.operateName === '复制新增' && item.display === '1'
            )
            editShow.value = operateList?.some(
                (item: any) => item.operateName === '修改' && item.display === '1'
            )
            auditShow.value = operateList?.some(
                (item: any) => item.operateName === '审核' && item.display === '1'
            )
            deleteShow.value = operateList?.some(
                (item: any) => item.operateName === '删除' && item.display === '1'
            )
        } catch (error) {
            // 如果权限接口失败，默认显示所有按钮
            addShow.value = true
            copyAddShow.value = true
            editShow.value = true
            auditShow.value = true
            deleteShow.value = true
        }
    }

    // 事件处理函数
    const handleLoading = () => queryList()
    const exportHandle = () => exportList()

    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    const handleAuditStatus = (val: string) => {
        queryForm.auditStatus = val
    }

    const changeSelectTable = (sel: any[]): void => {
        tableSelectList.value = sel.filter(item => item)
    }

    // 操作按钮处理函数
    const handleAdd = (): void => {
        dialogType.value = 'add'
        editData.value = null
        addDialogVisible.value = true
    }

    const handleCopyAdd = (): void => {
        if (!validateSelectedCount(tableSelectList.value, 1, '复制新增')) {
            return
        }
        dialogType.value = 'copyAdd'
        editData.value = tableSelectList.value[0]
        addDialogVisible.value = true
    }

    const handleEdit = (): void => {
        if (!validateSelectedCount(tableSelectList.value, 1, '修改')) {
            return
        }

        messageBox(
            {
                title: '提示',
                confirmBtn: '确定',
                cancelBtn: '取消',
                content: `
                    <div style="text-align: left; line-height: 1.6; font-size: 14px;">
                        <div style="margin-bottom: 8px;">1-若录入费率有误，请直接修改原数据</div>
                        <div>2-若以某时点区分新老费率，需先修改老费率开始、结束时间，并新增一条新费率</div>
                    </div>
                `
            },
            () => {
                dialogType.value = 'edit'
                editData.value = tableSelectList.value[0]
                addDialogVisible.value = true
            },
            () => {
                console.log('用户取消修改操作')
            }
        )
    }

    const handleAudit = (): void => {
        if (!validateSelectedCount(tableSelectList.value, 1, '审核')) {
            return
        }
        auditData.value = tableSelectList.value[0]
        auditDialogVisible.value = true
    }

    const handleBatchDelete = (): void => {
        if (!validateSelectedCount(tableSelectList.value, 1, '删除')) {
            return
        }

        const deleteItem = tableSelectList.value[0]
        messageBox(
            {
                confirmBtn: '确定',
                cancelBtn: '取消',
                content: `确认要删除这条记录吗？<br/>删除后将不可恢复`
            },
            () => {
                confirmDelete(deleteItem)
            },
            () => false
        )
    }

    // 成功回调
    const handleAddSuccess = () => queryList()
    const handleAuditSuccess = () => queryList()

    return {
        // 响应式数据
        listLoading,
        addShow,
        copyAddShow,
        editShow,
        auditShow,
        deleteShow,
        addDialogVisible,
        dialogType,
        editData,
        auditDialogVisible,
        auditData,
        queryForm,
        pageObj,
        tableData,
        tableSelectList,
        searchConfig,

        // 方法
        queryList,
        exportList,
        confirmDelete,
        getMenuAuth,
        handleLoading,
        exportHandle,
        handleCurrentChange,
        handleAuditStatus,
        changeSelectTable,
        handleAdd,
        handleCopyAdd,
        handleEdit,
        handleAudit,
        handleBatchDelete,
        handleAddSuccess,
        handleAuditSuccess
    }
}
