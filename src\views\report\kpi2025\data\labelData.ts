/*
 * @Description: IPS面访记录查询条件定义
 * @Author: AI Assistant
 * @Date: 2024-12-19
 * @FilePath: /src/views/report/kpi2025/data/labelData.ts
 */
export const dataList = {
    orgCode: {
        label: '部门编码',
        placeholder: '请输入'
    },
    consCode: {
        label: '投顾编码',
        placeholder: '请输入'
    },
    completeDateStart: {
        label: '完成日期开始',
        placeholder: '请选择'
    },
    completeDateEnd: {
        label: '完成日期结束',
        placeholder: '请选择'
    },
    completeDate: {
        label: '完成日期',
        placeholder: ['开始日期', '结束日期']
    },
    isIpsVisitComplete: {
        label: '是否完成IPS面访',
        placeholder: '请选择',
        selectList: [
            {
                key: '是',
                label: '是'
            },
            {
                key: '否',
                label: '否'
            }
        ]
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
