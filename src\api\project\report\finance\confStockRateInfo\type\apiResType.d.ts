/*
 * @Description: reportEditTop 返回参数类型
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-03-25 23:23:39
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:39:29
 * @FilePath: /crm-template/src/api/project/reportList/type/apiResType.d.ts
 *
 */
export {}
declare module './apiResType' {
    interface ResVO {
        taskId: string
        fundCode: string
        fundName: string
        valueStartDate: string
        valueEndDate: string
        countDay: string
        yearDay: string
        consultRate: string
        manageRate: string
        redemRate: string
        redemDay: string
        consultFormula: string
        manageFormula: string
        redemFormula: string
        adjustAmount: string
        selfCust: string
        agarement: string
        settleStartDate: string
        settleEndDate: string
        remark: string
        performanceFormula: string
        hswType: string
        hbPerformanceRate1: string
        hbPerformanceRate2: string
        performanceRate1: string
        performanceRate2: string
        performancejtType: string
        redemDate: string
        settleDate: string
        shareDate: string
        closedPeriodDays: string
        performanceBase1: string
        performanceBase2: string
        rateStartDate: string
        rateEndDate: string
        reviewState: string
        reviewtime: string
        reviewer: string
        creator: string
        createtime: string
        updater: string
        updattime: string
    }
    export { ResVO }
}
