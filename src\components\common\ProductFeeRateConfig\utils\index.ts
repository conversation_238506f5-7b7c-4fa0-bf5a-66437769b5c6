/*
 * @Description: 产品费率配置通用工具函数
 * @Author: hongdong.xie
 * @Date: 2025-06-06 09:42:20
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 09:42:20
 * @FilePath: /ds-report-web/src/components/common/ProductFeeRateConfig/utils/index.ts
 */

import { ElMessage } from 'element-plus'

/**
 * @description: 将 YYYY-MM-DD 格式转换为 yyyyMMdd 格式
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {string} dateStr 日期字符串
 * @return {string} 转换后的日期字符串
 */
export const formatDateToBackend = (dateStr: string): string => {
    if (!dateStr) {
        return ''
    }
    return dateStr.replace(/-/g, '')
}

/**
 * @description: 将 yyyyMMdd 格式转换为 YYYY-MM-DD 格式
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {string} dateStr 日期字符串
 * @return {string} 转换后的日期字符串
 */
export const formatDateFromBackend = (dateStr: string): string => {
    if (!dateStr || dateStr.length !== 8) {
        return ''
    }
    return `${dateStr.substring(0, 4)}-${dateStr.substring(4, 6)}-${dateStr.substring(6, 8)}`
}

/**
 * @description: 表格日期格式化函数 - 将 yyyyMMdd 格式转换为 YYYY-MM-DD 格式
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的日期
 */
export const formatDateForTable = (row: any, column: any, cellValue: any): string => {
    if (!cellValue || cellValue.length !== 8) {
        return cellValue || '--'
    }
    return `${cellValue.substring(0, 4)}-${cellValue.substring(4, 6)}-${cellValue.substring(6, 8)}`
}

/**
 * @description: 表格值格式化函数
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的值
 */
export const formatTableValue = (row: any, column: any, cellValue: any): string => {
    // 修复：确保值为0的字段能正常显示，避免0值被显示为'--'
    // @author: hongdong.xie @date: 2025-06-06 14:24:52
    if (cellValue === 0) {
        return '0'
    }
    return cellValue || '--'
}

/**
 * @description: 审核状态格式化函数
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的审核状态
 */
export const formatAuditStatus = (row: any, column: any, cellValue: any): string => {
    const statusMap: { [key: string]: string } = {
        '1': '待审核',
        '2': '审核通过',
        '3': '审核不通过'
    }
    return statusMap[cellValue] || cellValue
}

/**
 * @description: 费率配置类型格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的费率配置类型
 */
export const formatTieredRateType = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '0': '不涉及',
        '1': '存量市值',
        '2': '存量份额',
        '3': '交易日',
        '4': '累计购买金额',
        '5': '累计购买净额'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 费率配置生效日格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的费率配置生效日
 */
export const formatRateEffectiveType = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '0': '不涉及',
        '1': '次日生效',
        '2': '次月生效',
        '3': '次季度生效',
        '4': '次年生效',
        '5': '触发即直接改所在结算周期的费率'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 业绩报酬计提类型格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的业绩报酬计提类型
 */
export const formatPerformanceAccrualType = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '1': '固定计提',
        '2': '浮动计提'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 业绩报酬计提形式格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的业绩报酬计提形式
 */
export const formatPerformanceAccrualForm = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '1': '按月计提',
        '2': '按季计提',
        '3': '按年计提'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 固定计提日月份类型格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的固定计提日月份类型
 */
export const formatFixedAccrualMonthType = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '1': '每月',
        '2': '指定月份'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 固定计提日日期类型格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的固定计提日日期类型
 */
export const formatFixedAccrualDateType = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '1': '月末',
        '2': '指定日期'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 固定计提日计算类型格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的固定计提日计算类型
 */
export const formatFixedAccrualCalcType = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '1': '自然日',
        '2': '工作日'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 赎回业绩报酬持有天数规则格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的赎回业绩报酬持有天数规则
 */
export const formatRedemptionHoldingDaysRule = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '0': '不涉及',
        '1': '自然日',
        '2': '工作日'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 分红业绩报酬持有天数规则格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的分红业绩报酬持有天数规则
 */
export const formatDividendHoldingDaysRule = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '0': '不涉及',
        '1': '自然日',
        '2': '工作日'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 业绩报酬公式格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的业绩报酬公式
 */
export const formatPerformanceFormula = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '0': '不涉及',
        '1': '公式1',
        '2': '公式2'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 份额锁定期类型格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的份额锁定期类型
 */
export const formatShareLockType = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '1': '无锁定期',
        '2': '有锁定期'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 基金封闭期类型格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的基金封闭期类型
 */
export const formatFundClosedType = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '1': '无封闭期',
        '2': '有封闭期'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 赎回费持有天数计算规则格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的赎回费持有天数计算规则
 */
export const formatRedemptionHoldingCalcRule = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '1': '自然日',
        '2': '工作日'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 赎回费特例格式化函数
 * @author: hongdong.xie
 * @date: 2025-01-16 14:30:00
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的赎回费特例
 */
export const formatRedemptionSpecialRule = (row: any, column: any, cellValue: any): string => {
    const typeMap: { [key: string]: string } = {
        '0': '无特例',
        '1': '有特例'
    }
    return typeMap[cellValue] || cellValue || '--'
}

/**
 * @description: 仅限FOF格式化函数
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {any} row 行数据
 * @param {any} column 列配置
 * @param {any} cellValue 单元格值
 * @return {string} 格式化后的值
 */
export const formatFofOnlyFlag = (row: any, column: any, cellValue: any): string => {
    return cellValue === '1' ? '是' : '否'
}

/**
 * @description: 导出文件处理函数
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {any} res API响应数据
 * @param {string} defaultFileName 默认文件名
 * @return {void}
 */
export const handleExportFile = (res: any, defaultFileName: string): void => {
    console.log('✅ 导出接口返回:', res)

    if (res && res.data) {
        const { fileByte, name } = res.data
        if (fileByte) {
            const bstr = atob(fileByte)
            let n = bstr.length
            const u8arr = new Uint8Array(n)
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n)
            }
            const blob = new Blob([u8arr], {
                // 下载的文件类型
                type: 'application/vnd.ms-excel;chartset=UTF-8'
            })
            const link = document.createElement('a')
            link.download = name || defaultFileName
            link.href = window.URL.createObjectURL(blob)
            link.click()

            // 清理URL对象
            setTimeout(() => {
                window.URL.revokeObjectURL(link.href)
            }, 100)

            ElMessage({
                type: 'success',
                message: '导出成功'
            })
        } else {
            ElMessage({
                type: 'warning',
                message: '导出文件为空'
            })
        }
    } else {
        ElMessage({
            type: 'error',
            message: '导出失败，请重试'
        })
    }
}

/**
 * @description: 统一错误处理函数
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {any} error 错误对象
 * @param {string} defaultMessage 默认错误信息
 * @return {void}
 */
export const handleApiError = (error: any, defaultMessage: string): void => {
    console.error('❌ API错误:', error)
    const errorMsg = error?.data?.description || error?.description || defaultMessage
    ElMessage({
        type: 'error',
        message: errorMsg
    })
}

/**
 * @description: 统一异常处理函数
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {any} error 异常对象
 * @return {void}
 */
export const handleApiCatch = (error: any): void => {
    console.error('💥 API异常:', error)
    ElMessage({
        type: 'error',
        message: '网络异常，请重试'
    })
}

/**
 * @description: 重置表单数据到初始状态
 * @author: hongdong.xie
 * @date: 2025-01-16 15:10:00
 * @param {any} formData 表单数据对象
 * @param {string} rateType 费率类型
 * @return {void}
 */
export const resetFormData = (formData: any, rateType: string): void => {
    console.log('🔄 重置表单数据到初始状态')

    // 重置所有字段到初始值，确保数值字段为数字类型
    Object.assign(formData, {
        id: undefined,
        filingCode: '',
        productCode: '',
        shareCode: '',
        productFullName: '',
        fofOnlyFlag: '0',
        payerFullName: '',
        productManager: '',
        signingDate: '',
        howbuySigningEntity: '',
        counterpartContact: '',
        durationDCoefficient: 0,
        durationDRemark: '',
        rateStartDate: '',
        rateEndDate: '2999-12-31',
        tieredRateType: '0',
        rateEffectiveType: '0',
        configLowerLimit: 0,
        configUpperLimit: 0,
        rateEffectiveStartDate: '',
        rateEffectiveEndDate: '',
        subscriptionRate: 0,
        subscriptionRemark: '',
        managementRate: 0,
        managementFormula: '',
        managementRemark: '',
        performanceSharingRate1: 0,
        performanceRate1: 0,
        performanceBenchmark1: '',
        performanceSharingRate2: 0,
        performanceRate2: 0,
        performanceBenchmark2: '',
        performanceSharingRate3: 0,
        performanceRate3: 0,
        performanceBenchmark3: '',
        performanceAccrualType: '',
        performanceAccrualForm: '',
        fixedAccrualMonthType: '',
        fixedAccrualDateType: '',
        fixedAccrualCalcType: '',
        redemptionHoldingDaysRule: '',
        dividendHoldingDaysRule: '',
        performanceFormula: '',
        shareLockType: '',
        shareLockDays: 0,
        fundClosedType: '',
        fundClosedDays: 0,
        noAccrualNavBenchmark: 0,
        performanceRemark: '',
        redemptionRate1: 0,
        redemptionHoldingDays1: 0,
        redemptionRate2: 0,
        redemptionHoldingDays2: 0,
        redemptionRate3: 0,
        redemptionHoldingDays3: 0,
        redemptionRate4: 0,
        redemptionHoldingDays4: 0,
        redemptionFormula: '',
        redemptionHoldingCalcRule: '',
        redemptionSpecialRule: '',
        redemptionRemark: '',
        rateType: rateType
    })

    console.log('🔄 重置后的表单数据:', formData)

    // 验证关键数值字段
    const criticalFields = ['configLowerLimit', 'configUpperLimit', 'subscriptionRate', 'managementRate']
    criticalFields.forEach(field => {
        console.log(`🔍 重置后关键字段 ${field}:`, formData[field], typeof formData[field])
    })
}

/**
 * @description: 复制表单数据（用于复制新增）
 * @author: hongdong.xie
 * @date: 2025-01-16 14:55:00
 * @param {any} sourceData 源数据
 * @param {any} targetData 目标数据对象
 * @return {void}
 */
export const copyFormData = (sourceData: any, targetData: any): void => {
    // 复制所有字段，但排除ID和审核相关字段
    const excludeFields = ['id', 'creator', 'createTime', 'modor', 'updateTime', 'auditor', 'auditTime', 'auditStatus', 'auditRemark']

    Object.keys(sourceData).forEach(key => {
        if (!excludeFields.includes(key)) {
            const sourceValue = sourceData[key]
            // 如果源数据为null或undefined，保持目标数据的默认值
            if (sourceValue !== null && sourceValue !== undefined) {
                targetData[key] = sourceValue
            }
            // 如果源数据为null或undefined，不覆盖目标数据的默认值
        }
    })
}

/**
 * @description: 验证选中数据数量
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {any[]} selectedItems 选中的数据数组
 * @param {number} expectedCount 期望的数量
 * @param {string} operation 操作名称
 * @return {boolean} 是否验证通过
 */
export const validateSelectedCount = (selectedItems: any[], expectedCount: number, operation: string): boolean => {
    if (selectedItems.length !== expectedCount) {
        const message = expectedCount === 1 
            ? `请选择一条数据进行${operation}`
            : `请选择${expectedCount}条数据进行${operation}`
        
        ElMessage({
            message,
            type: 'warning',
            duration: 2000
        })
        return false
    }
    return true
}
