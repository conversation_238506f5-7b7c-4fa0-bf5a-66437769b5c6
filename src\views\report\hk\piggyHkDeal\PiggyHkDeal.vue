<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 业务订单号 -->
                <label-item :label="dealNo.label">
                    <crm-input
                        v-model="queryForm.dealNo"
                        :placeholder="dealNo.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 香港客户号 -->
                <label-item :label="hkCustNo.label">
                    <crm-input
                        v-model="queryForm.hkCustNo"
                        :placeholder="hkCustNo.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 一账通号 -->
                <label-item :label="hboneNo.label">
                    <crm-input
                        v-model="queryForm.hboneNo"
                        :placeholder="hboneNo.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 投顾客户号 -->
                <label-item :label="conscustno.label">
                    <crm-input
                        v-model="queryForm.conscustno"
                        :placeholder="conscustno.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 审核状态 -->
                <label-item :label="checkState.label">
                    <crm-select
                        v-model="queryForm.checkState"
                        :placeholder="checkState.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="checkState.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 业务类型 -->
                <label-item :label="busiName.label">
                    <crm-select
                        v-model="queryForm.busiName"
                        :placeholder="busiName.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="busiName.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 签署/终止方式 -->
                <label-item :label="signCancelType.label">
                    <crm-select
                        v-model="queryForm.signCancelType"
                        :placeholder="signCancelType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        no-data-text="请先勾选业务类型！"
                        :option-list="signCancelTypeSelectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 签署/终止时间 -->
                <label-item :label="signCancelTime.label">
                    <date-range
                        v-model="queryForm.signCancelTime"
                        show-format="YYYY-MM-DD"
                        :placeholder="signCancelTime.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 签署/终止渠道 -->
                <label-item :label="channel.label">
                    <crm-select
                        v-model="queryForm.channel"
                        :placeholder="channel.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="channel.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 当前投顾 -->
                <label-item label="当前投顾">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <!-- 客户类型(当前） -->
                <label-item :label="invstType.label">
                    <crm-select
                        v-model="queryForm.invstType"
                        :placeholder="invstType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="invstType.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 投资者类型(当前） -->
                <label-item :label="investorQualification.label">
                    <crm-select
                        v-model="queryForm.investorQualification"
                        :placeholder="investorQualification.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="investorQualification.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
            </template>
            <template #operationLeft>
                <Text type="info" size="small" style="padding-left: 10px; font-size: 14px"
                    >数据更新时间：{{ lastUpdateTimeRef }}
                </Text>
            </template>

            <template #operationBtns>
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="exportHandle"
                    >导出</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="piggyHkDealTableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="true"
                    :no-index="false"
                    :no-select="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    :pops="{ selectFixed: 'left' }"
                    :row-class-name="handleRowClassName"
                    @sortChange="handleSortChange"
                >
                    <template #operation="{ scope }">
                        <el-button
                            v-if="viewDetailShow"
                            size="small"
                            :text="true"
                            link
                            @click="handleViewDetail(scope.row)"
                            >查看详情</el-button
                        >
                    </template>
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>

        <view-piggy-hk-deal
            v-if="viewDetailDialogVisible"
            v-model="viewDetailDialogVisible"
            :trans-data="viewObj"
            :organization-list="organizationList"
            :consult-list="consultList"
            :org-code="orgCodeDefault"
            :cons-code="consCodeDefault"
            @callback="handleClose()"
        >
        </view-piggy-hk-deal>
    </div>
</template>

<script lang="ts" setup>
    import { Download } from '@element-plus/icons-vue'
    import { fetchRes } from '@/utils'
    import { dataList } from './data/labelData'

    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import { piggyHkDealTableColumn } from './data/tableData'
    import ViewPiggyHkDeal from '@/views/report/hk/components/viewPiggyHkDeal.vue'
    import {
        queryPiggyHkDeal,
        exportPiggyHkDeal
    } from '@/api/project/report/hk/piggyHkDeal/piggyHkDeal'
    import { PIGGY_HK_DEAL_OPER_PERMISSION } from '@/constant/reportConst'
    import { getMenuPermission } from '@/api/project/common/common'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import { SortOrderCumstom } from '@/type/index'

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const {
        hkCustNo,
        hboneNo,
        conscustno,
        invstType,
        investorQualification,
        dealNo,
        busiName,
        checkState,
        signCancelTime,
        channel,
        signCancelType
    } = dataList

    const listLoading = ref<boolean>(false)
    const viewDetailDialogVisible = ref<boolean>(false)
    const consStatus = ref<string>('1') //不包含离职人员

    const exportShow = ref<boolean>(false)
    const viewDetailShow = ref<boolean>(false)

    const lastUpdateTimeRef = ref<string>('')

    const handleRowClassName = (rowObj: any, rowIndex: number) => {
        const { row } = rowObj
        if (row && row.ifExceptionData === '1') {
            return 'highlight-bg'
        }
    }

    const signCancelTypeSelectList = computed(() => {
        console.log('queryForm.busiName', queryForm.busiName)
        if (queryForm.busiName === '海外储蓄罐协议签署') {
            return signCancelType.selectList1
        }
        if (queryForm.busiName === '海外储蓄罐协议终止') {
            return signCancelType.selectList2
        }
        return []
    })

    const module = ref<string>('071702')
    /**
     * @description: 关闭弹框刷新数据
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleClose = (): void => {
        queryList()
    }

    // 排序值映射
    const transOrder = (key: string) => {
        switch (key) {
            case 'ascending':
                return 'ASC'
            case 'descending':
                return 'DESC'
            default:
                return 'DESC'
        }
    }
    interface SortParams {
        order: SortOrderCumstom
        prop: string
    }
    // 排序联动
    const handleSortChange = (val: SortParams) => {
        queryForm.order = val.order
        queryForm.sort = val.prop
        nextTick(() => {
            queryList()
        })
    }

    /**
     * @description: 编辑
     * @return {*}
     */
    const viewObj = ref({
        dealNo: '',
        type: '',
        title: ''
    })

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        dealNo = ''
        hkCustNo = ''
        hboneNo = ''
        conscustno = ''
        invstType = ''
        investorQualification = ''
        busiName = ''
        checkState = ''
        channel = ''
        signCancelType = ''
        signCancelTime = {
            startDate: '',
            endDate: ''
        }
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        sort = ''
        order = 'descending'
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            hkCustNo: queryForm.hkCustNo,
            hboneNo: queryForm.hboneNo,
            conscustno: queryForm.conscustno,
            dealNo: queryForm.dealNo,
            checkState: queryForm.checkState,
            busiName: queryForm.busiName,
            signCancelTimeStart: queryForm.signCancelTime.startDate,
            signCancelTimeEnd: queryForm.signCancelTime.endDate,
            signCancelType: queryForm.signCancelType,
            channel: queryForm.channel,
            invstType: queryForm.invstType,
            investorQualification: queryForm.investorQualification,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            page: pageObj.value.page,
            rows: pageObj.value.size,
            sort: queryForm.sort,
            order: transOrder(queryForm.order)
        }
        fetchRes(queryPiggyHkDeal(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total, lastUpdateTime } = resObj
                tableData.value = rows
                lastUpdateTimeRef.value = lastUpdateTime
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    const handleViewDetail = (row: any) => {
        const { dealNo } = row
        viewObj.value = {
            dealNo: dealNo,
            type: 'edit',
            title: '查看详情'
        }
        viewDetailDialogVisible.value = true
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const params = {
            hkCustNo: queryForm.hkCustNo,
            hboneNo: queryForm.hboneNo,
            conscustno: queryForm.conscustno,
            dealNo: queryForm.dealNo,
            checkState: queryForm.checkState,
            busiName: queryForm.busiName,
            signCancelTimeStart: queryForm.signCancelTime.startDate,
            signCancelTimeEnd: queryForm.signCancelTime.endDate,
            signCancelType: queryForm.signCancelType,
            channel: queryForm.channel,
            invstType: queryForm.invstType,
            investorQualification: queryForm.investorQualification,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            sort: queryForm.sort,
            order: transOrder(queryForm.order)
        }
        const res: any = await exportPiggyHkDeal(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }

    const initData = async () => {
        const params = {
            menuCode: module.value
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode === PIGGY_HK_DEAL_OPER_PERMISSION.EXPORT &&
                            item.display === '1'
                        ) {
                            exportShow.value = true
                        }
                        if (
                            item.operateCode === PIGGY_HK_DEAL_OPER_PERMISSION.VIEW_DETAIL &&
                            item.display === '1'
                        ) {
                            viewDetailShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [orgCodeDefault, consCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.orgvalue.orgCode = orgCodeDefault.value
                queryForm.orgvalue.consCode = consCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        console.log('onMounted')
        initData()
        fetchConsOrgList('', module.value, '1')
    })
</script>
<style lang="less" scoped></style>
