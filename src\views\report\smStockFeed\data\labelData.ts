/*
 * @Description: 定义搜索的label列表
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:36:23
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/labelData.ts
 *
 */
export const dataList = {
    accountProductType: {
        selectList: [
            {
                key: '1',
                label: '权益类'
            },
            {
                key: '2',
                label: '固收类'
            },
            {
                key: '3',
                label: '股权类'
            },
            {
                key: '4',
                label: '倒追类'
            },
            {
                key: '5',
                label: '信托类'
            },
            {
                key: '6',
                label: '债券A类'
            },
            {
                key: '7',
                label: '债券B类'
            },
            {
                key: '8',
                label: '小集合'
            },
            {
                key: '9',
                label: '国内创新'
            },
            {
                key: '10',
                label: '海外创新'
            },
            {
                key: '11',
                label: '债券C类'
            }
        ]
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
