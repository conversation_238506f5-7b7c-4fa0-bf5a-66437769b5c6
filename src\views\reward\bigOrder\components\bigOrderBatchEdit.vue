<!--
 * @Description: 修改弹框
 * @Author: jianjian.yang
 * @Date: 2024-12-03 20:03:35
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="500px"
        :border="true"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <!-- 编辑的弹框 -->
            <el-form ref="ruleFormRef" :model="formList" :rules="rules" status-icon>
                <el-row>
                    <el-col>
                        <el-form-item prop="bigOrderProductType" style="margin-top: 0">
                            <el-checkbox
                                v-model="bigOrderProductTypeVisible"
                                label="大单产品类型"
                                style="width: 140px; margin-right: 10px"
                                :true-value="'1'"
                                :false-value="'0'"
                            />
                            <crm-select
                                v-if="bigOrderProductTypeVisible"
                                v-model="formList.bigOrderProductType"
                                :placeholder="bigOrderProductType.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="bigOrderProductTypeList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col>
                        <el-form-item prop="availVol" style="margin-top: 15px; margin-bottom: 15px">
                            <el-checkbox
                                v-model="availVolVisible"
                                label="剩余份额"
                                style="width: 140px; margin-right: 10px"
                                :true-value="'1'"
                                :false-value="'0'"
                            />
                            <crm-input
                                v-if="availVolVisible"
                                v-model="formList.availVol"
                                :placeholder="'请输入剩余份额'"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col>
                        <el-form-item
                            prop="availCost"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <el-checkbox
                                v-model="availCostVisible"
                                label="剩余本金"
                                style="width: 140px; margin-right: 10px"
                                :true-value="'1'"
                                :false-value="'0'"
                            />
                            <crm-input
                                v-if="availCostVisible"
                                v-model="formList.availCost"
                                :placeholder="'请输入剩余本金'"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col>
                        <el-form-item
                            prop="commissionRate"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <el-checkbox
                                v-model="commissionRateVisible"
                                label="佣金率(%)"
                                style="width: 140px; margin-right: 10px"
                                :true-value="'1'"
                                :false-value="'0'"
                            />
                            <crm-input
                                v-if="commissionRateVisible"
                                v-model="formList.commissionRate"
                                :placeholder="'请输入佣金率'"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col>
                        <el-form-item
                            prop="achieveCoeff"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <el-checkbox
                                v-model="achieveCoeffVisible"
                                label="业绩系数"
                                style="width: 140px; margin-right: 10px"
                                :true-value="'1'"
                                :false-value="'0'"
                            />
                            <crm-input
                                v-if="achieveCoeffVisible"
                                v-model="formList.achieveCoeff"
                                :placeholder="'请输入业绩系数'"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col>
                        <el-form-item
                            prop="manageCoeffCommission"
                            style="margin-top: 15px; margin-bottom: 40px"
                        >
                            <el-checkbox
                                v-model="manageCoeffCommissionVisible"
                                label="管理奖金基数"
                                style="width: 140px; margin-right: 10px"
                                :true-value="'1'"
                                :false-value="'0'"
                            />
                            <crm-input
                                v-if="manageCoeffCommissionVisible"
                                v-model="formList.manageCoeffCommission"
                                :placeholder="'请输入管理奖金基数'"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 45%">
                            <el-button type="primary" @click="submitForm(ruleFormRef)">
                                确认
                            </el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 55%">
                            <el-button type="primary" @click="handleClose(ruleFormRef)"
                                >取消</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { fetchRes, messageBox } from '@/utils'
    import { dataList } from '../data/labelData'
    import { fetchConstant } from '@/api/project/common/common'
    import { bigOrderBatchUpdate } from '@/api/project/reward/bigOrder/bigOrder'
    import { BIG_ORDER_PRODUCT_TYPE } from '@/constant/hbConstant'
    const { bigOrderProductType } = dataList
    const loadingFlag = ref<boolean>(false)

    const bigOrderProductTypeVisible = ref<boolean>(false)
    const availVolVisible = ref<boolean>(false)
    const availCostVisible = ref<boolean>(false)
    const commissionRateVisible = ref<boolean>(false)
    const achieveCoeffVisible = ref<boolean>(false)
    const manageCoeffCommissionVisible = ref<boolean>(false)

    const bigOrderProductTypeList = ref<any>([])

    class FormList {
        bigOrderProductType = ''
        availVol = ''
        availCost = ''
        commissionRate = ''
        achieveCoeff = ''
        manageCoeffCommission = ''
    }

    const formList = reactive<any>(new FormList())

    const ruleFormRef = ref<FormInstance>()

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                ids: string[]
                preIds: string[]
                title: string
                consNames: string[]
            }
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    ids: [] as string[],
                    preIds: [] as string[],
                    title: '',
                    consNames: [] as string[]
                }
            }
        }
    )

    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()
    const rules = reactive<FormRules>({
        availVol: [
            {
                pattern: /^\d+(\.\d{1,2})?$/,
                message: '请输入正确的数字格式(最多两位小数)',
                trigger: 'blur'
            }
        ],
        availCost: [
            {
                pattern: /^\d+(\.\d{1,2})?$/,
                message: '请输入正确的数字格式(最多两位小数)',
                trigger: 'blur'
            }
        ],
        commissionRate: [
            {
                pattern: /^\d+(\.\d{1,2})?$/,
                message: '请输入正确的数字格式(最多两位小数)',
                trigger: 'blur'
            }
        ],
        achieveCoeff: [
            {
                pattern: /^\d+(\.\d{1,2})?$/,
                message: '请输入正确的数字格式(最多两位小数)',
                trigger: 'blur'
            }
        ],
        manageCoeffCommission: [
            {
                pattern: /^\d+(\.\d{1,2})?$/,
                message: '请输入正确的数字格式(最多两位小数)',
                trigger: 'blur'
            }
        ]
    })

    //提交方法
    const submitForm = async (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }

        await formEl.validate((valid, fields) => {
            if (valid) {
                console.log('submit!', fields)
                performSubmit()
            } else {
                console.log('error submit!', fields)
            }
        })
    }
    //重置方法
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        formEl.resetFields()
    }

    /**
     * 提交方法
     * @param params
     */
    const performSubmit = async () => {
        if (
            !bigOrderProductTypeVisible.value &&
            !availVolVisible.value &&
            !availCostVisible.value &&
            !commissionRateVisible.value &&
            !achieveCoeffVisible.value &&
            !manageCoeffCommissionVisible.value
        ) {
            ElMessage({
                message: '请勾选要修改的字段',
                type: 'warning',
                duration: 2000
            })
            return
        }

        if (bigOrderProductTypeVisible.value && !formList.bigOrderProductType) {
            ElMessage({
                message: '大单产品类型不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if (availVolVisible.value && !formList.availVol) {
            ElMessage({
                message: '剩余份额不能为空',
                type: 'warning',
                duration: 2000
            })
        }
        if (availCostVisible.value && !formList.availCost) {
            ElMessage({
                message: '剩余本金不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if (commissionRateVisible.value && !formList.commissionRate) {
            ElMessage({
                message: '佣金率不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if (achieveCoeffVisible.value && !formList.achieveCoeff) {
            ElMessage({
                message: '业绩系数不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if (manageCoeffCommissionVisible.value && !formList.manageCoeffCommission) {
            ElMessage({
                message: '管理奖金基数不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }

        const totalCount = props.transData.preIds.length
        // 修改的预约
        const modifyStr = props.transData.preIds
            .map((item: string) => {
                return `${item}`
            })
            .join(',')

        messageBox(
            {
                confirmBtn: '确定修改',
                cancelBtn: '取消',
                content: `本次修改涉及预约：<span style="color:red">${modifyStr}</span>，
                合计<span style="color:red">${totalCount}</span>条数据！`
            },
            () => {
                confirmUpdate()
            },
            () => false
        )
    }

    const confirmUpdate = async () => {
        const requestParams = {
            ids: props.transData.preIds,
            bigOrderProductType: bigOrderProductTypeVisible.value
                ? formList.bigOrderProductType
                : null,
            availVol: availVolVisible.value ? formList.availVol : null,
            availCost: availCostVisible.value ? formList.availCost : null,
            commissionRate: commissionRateVisible.value ? formList.commissionRate : null,
            achieveCoeff: achieveCoeffVisible.value ? formList.achieveCoeff : null,
            manageCoeffCommission: manageCoeffCommissionVisible.value
                ? formList.manageCoeffCommission
                : null
        }

        const res: any = await bigOrderBatchUpdate(requestParams)
        if (res.code === 'C030000') {
            ElMessage({
                type: 'success',
                message: res.description
            })
            loadingFlag.value = false
            dialogVisible.value = false
            return emit('callback')
        }
        if (res.code !== 'C030000') {
            ElMessage({
                type: 'error',
                message: res?.description || '请求失败'
            })
        }
    }

    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        dialogVisible.value = false
        // formEl.resetFields()
    }

    const initData = async () => {
        fetchRes(fetchConstant({ typeCodeList: [BIG_ORDER_PRODUCT_TYPE] }), {
            successCB: (resObj: any) => {
                loadingFlag.value = false
                const { constantTypeMap } = resObj
                bigOrderProductTypeList.value = constantTypeMap[BIG_ORDER_PRODUCT_TYPE]
            },
            errorCB: () => {
                loadingFlag.value = false
            },
            catchCB: () => {
                loadingFlag.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    onMounted(() => {
        // 初始化
        initData()
    })
</script>

<style lang="less" scoped>
    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
