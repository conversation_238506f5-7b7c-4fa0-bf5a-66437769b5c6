<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            :show-export-btn="true"
            @searchFn="handleLoading"
            @exportFn="exportHandle"
        >
            <template #searchArea>
                <label-item :label="radioNodeList.label">
                    <crm-radio
                        v-model="queryForm.constype"
                        :placeholder="radioNodeList.placeholder"
                        :option-list="radioNodeList.selectList"
                        @change="changeConsType"
                    ></crm-radio>
                </label-item>
                <label-item label="投顾">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <label-item :label="resType.label">
                    <crm-select
                        v-model="queryForm.resType"
                        :placeholder="resType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="resType.selectList"
                        :style="{ width: '130px' }"
                        @change="handleResType"
                    />
                </label-item>

                <label-item :label="assignTime.label">
                    <date-range
                        v-model="queryForm.assignTime"
                        show-format="YYYYMMDD"
                        :placeholder="assignTime.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <label-item :label="curAssignTime.label">
                    <date-range
                        v-model="queryForm.curAssignTime"
                        show-format="YYYYMMDD"
                        :placeholder="curAssignTime.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <label-item :label="isRepeat.label">
                    <crm-select
                        v-model="queryForm.isRepeat"
                        :placeholder="isRepeat.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="isRepeat.selectList"
                        :style="{ width: '130px' }"
                        @change="handleIsRepeat"
                    />
                </label-item>

                <label-item :label="custSource.label">
                    <crm-select2
                        v-model="queryForm.custSource"
                        :placeholder="custSource.placeholder"
                        filterable
                        clearable
                        multiple
                        label-format="label"
                        value-format="key"
                        :option-list="custSource.selectList"
                        :style="{ width: '180px' }"
                    />
                </label-item>
                <label-item :label="secondHand.label">
                    <crm-select
                        v-model="queryForm.secondHand"
                        :placeholder="secondHand.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="secondHand.selectList"
                        :style="{ width: '130px' }"
                        @change="handleSecondHand"
                    />
                </label-item>
                <label-item :label="isBindWeiXin.label">
                    <crm-select
                        v-model="queryForm.isBindWeiXin"
                        :placeholder="isBindWeiXin.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="isBindWeiXin.selectList"
                        :style="{ width: '130px' }"
                        @change="handleIsBindWeiXin"
                    />
                </label-item>
                <label-item :label="isDeal.label">
                    <crm-select
                        v-model="queryForm.isDeal"
                        :placeholder="isDeal.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="isDeal.selectList"
                        :style="{ width: '130px' }"
                        @change="handleIsDeal"
                    />
                </label-item>
                <label-item :label="conscustno.label">
                    <crm-input
                        v-model="queryForm.conscustno"
                        :placeholder="conscustno.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
            </template>
            <template #operationBtns>
                <span style="font-size: 12px; color: red"
                    >累计分配 {{ allSumCust }}个客户，其中
                    {{ allSumSucDealCust }} 个客户已成交，成交率 {{ dealRate }}</span
                >

                <crm-button
                    size="small"
                    :radius="true"
                    plain
                    :icon="RemoveFilled"
                    @click="clearHandle"
                    >清空</crm-button
                >
                <crm-button
                    size="small"
                    :radius="true"
                    :icon="InfoFilled"
                    plain
                    @click="handleExplain"
                    >说明</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                >
                </base-table>
            </template>
            <template #tableContentBottom>
                <Pagination2 :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>
        <ResoCustAssignExplain v-model="explainDialogVisiable"></ResoCustAssignExplain>
    </div>
</template>

<script lang="ts" setup>
    import { RemoveFilled, InfoFilled } from '@element-plus/icons-vue'
    import { downloadFile, fetchRes, message } from '@/utils'
    import { dataList } from './data/labelData'
    import { resoCustAssignTableColumn, showTableColumn } from './data/tableData'
    import { useStockListData } from '@/views/common/scripts/stockListData'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import Pagination2 from '@/views/report/custManage/components/Pagination.vue'
    import CrmSelect2 from '@/views/report/custManage/components/CrmSelect.vue'
    import ResoCustAssignExplain from '@/views/report/custManage/components/resoCustAssignExplain.vue'
    import {
        // eslint-disable-next-line camelcase
        resoCustAssignQuery,
        resoCustAssignExport
    } from '@/api/project/report/custManage/resoCustAssign/resoCustAssign'
    import { valueEquals } from 'element-plus'
    const {
        isRepeat,
        isDeal,
        secondHand,
        custSource,
        resType,
        isBindWeiXin,
        conscustno,
        assignTime,
        curAssignTime,
        radioNodeList
    } = dataList

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const listLoading = ref<boolean>(false)
    const consStatus = ref<string>('0')
    const module = ref<string>('070228')
    const allSumCust = ref<Number>(0)
    const allSumSucDealCust = ref<Number>(0)
    const dealRate = ref<string>('0.0%')
    /**
     * @description: 说明展示隐藏
     * @param explainDialogVisiable 展示隐藏
     * @method handleExplain 触发方法
     * @return {*}
     */
    const explainDialogVisiable = ref<boolean>(false)
    const handleExplain = () => {
        explainDialogVisiable.value = true
    }

    const handleResType = (val: string) => {
        queryForm.resType = val
    }
    // const handleCustSource = (val: Array) => {
    //     queryForm.custSource = val
    // }
    const handleIsRepeat = (val: string) => {
        queryForm.isRepeat = val
    }
    const handleIsDeal = (val: string) => {
        queryForm.isDeal = val
    }
    const handleSecondHand = (val: string) => {
        queryForm.secondHand = val
    }
    const handleIsBindWeiXin = (val: string) => {
        queryForm.isBindWeiXin = val
    }
    const changeConsType = (val: string) => {
        consStatus.value = val
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        constype = '0'
        isRepeat = ''
        isDeal = ''
        secondHand = ''
        custSource = []
        resType = ''
        isBindWeiXin = ''
        conscustno = ''
        assignTime = {
            startDate: '',
            endDate: ''
        }
        curAssignTime = {
            startDate: '',
            endDate: ''
        }
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: 分页数据
     * @return {*}
     */
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 100
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            resoCustAssignTableColumn.map(item => item.key),
            resoCustAssignTableColumn
        )
    })

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            isRepeat: queryForm.isRepeat,
            isDeal: queryForm.isDeal,
            isBindWeiXin: queryForm.isBindWeiXin,
            secondHand: queryForm.secondHand,
            custSource: queryForm.custSource.join(),
            resType: queryForm.resType,
            conscustno: queryForm.conscustno,
            startDate: queryForm.assignTime.startDate,
            endDate: queryForm.assignTime.endDate,
            curStartDate: queryForm.curAssignTime.startDate,
            curEndDate: queryForm.curAssignTime.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            conscode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            consType: consStatus.value,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(resoCustAssignQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                // debugger
                tableData.value = rows
                if (rows.length > 0) {
                    console.log(JSON.stringify(resObj[0]))
                    allSumCust.value = rows[0].sumCust
                    allSumSucDealCust.value = rows[0].sumSucDealCust
                    dealRate.value = rows[0].dealRate
                } else {
                    //初始化数据复原
                    allSumCust.value = 0
                    allSumSucDealCust.value = 0
                    dealRate.value = '0.0%'
                }
                // TODO list接口待添加分页数据
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    const clearHandle = () => {
        const today = new Date()
        const year = today.getFullYear()
        const date = new Date(year, 0, 1)
        const formattedDate = `${date.getFullYear()}${(date.getMonth() + 1)
            .toString()
            .padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}`
        queryForm.constype = '0'
        queryForm.isRepeat = ''
        queryForm.isDeal = ''
        queryForm.secondHand = ''
        queryForm.custSource = []
        queryForm.resType = ''
        queryForm.isBindWeiXin = ''
        queryForm.conscustno = ''
        queryForm.assignTime.startDate = formattedDate
        queryForm.assignTime.endDate = ''
        queryForm.curAssignTime.startDate = ''
        queryForm.curAssignTime.endDate = ''
        ;(queryForm.orgvalue.orgCode = ''), (queryForm.orgvalue.consCode = '')
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const pdfUrl = ref<string>('')
    const exportList = async () => {
        const params = {
            isRepeat: queryForm.isRepeat,
            isDeal: queryForm.isDeal,
            isBindWeiXin: queryForm.isBindWeiXin,
            secondHand: queryForm.secondHand,
            custSource: queryForm.custSource.join(),
            resType: queryForm.resType,
            conscustno: queryForm.conscustno,
            startDate: queryForm.assignTime.startDate,
            endDate: queryForm.assignTime.endDate,
            curStartDate: queryForm.curAssignTime.startDate,
            curEndDate: queryForm.curAssignTime.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            conscode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            consType: consStatus.value
        }
        const res: any = await resoCustAssignExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }
    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        fetchConsOrgList('', '0702281', '1')
        //初始化日期
        const today = new Date()
        const year = today.getFullYear()
        const date = new Date(year, 0, 1)
        const formattedDate = `${date.getFullYear()}${(date.getMonth() + 1)
            .toString()
            .padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}`
        queryForm.assignTime.startDate = formattedDate
    })
</script>
<style lang="less" scoped></style>
