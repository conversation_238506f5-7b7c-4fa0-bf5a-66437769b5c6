<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 管理人员 -->
                <label-item label="管理人员" class-name="text-left">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <!-- 考核周期 -->
                <label-item :label="periodExplain.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.periodExplain"
                        :placeholder="periodExplain.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="periodExplain.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 层级 -->
                <label-item :label="userLevel.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.userLevel"
                        :placeholder="userLevel.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="userLevel.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 预计考核结果 -->
                <label-item :label="exaimneResult.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.exaimneResult"
                        :placeholder="exaimneResult.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="exaimneResult.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 投顾code -->
                <label-item :label="userId.label" class-name="text-left">
                    <crm-input
                        v-model="queryForm.userId"
                        :placeholder="userId.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 考核节点 -->
                <label-item :label="exaimneNode.label" class-name="text-left">
                    <date-range
                        v-model="queryForm.exaimneNode"
                        show-format="YYYY-MM-DD"
                        :placeholder="exaimneNode.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 是否TP -->
                <label-item :label="istp.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.istp"
                        :placeholder="istp.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="istp.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 最终考核结果 -->
                <label-item :label="exaimneEndresult.label" class-name="text-left">
                    <crm-select
                        v-model="queryForm.exaimneEndresult"
                        :placeholder="exaimneEndresult.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="exaimneEndresult.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
            </template>
            <template #operationBtns>
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="exportHandle"
                    >导出
                </crm-button>
                <crm-button
                    v-if="batchModifyShow"
                    size="small"
                    :radius="true"
                    plain
                    @click="handleBatchEdit"
                    >批量修改
                </crm-button>
                <crm-button v-if="saveShow" size="small" :radius="true" plain @click="saveHandle"
                    >保存
                </crm-button>
                <crm-button
                    v-if="saveRecordShow"
                    size="small"
                    :radius="true"
                    plain
                    @click="handleSaveRecord"
                    >操作记录
                </crm-button>
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    :pops="{ selectFixed: 'left' }"
                    :row-class-name="handleRowClassName"
                    :cell-class-name="handleCellClassName"
                    @selectionChange="handleSelectionChange"
                >
                </base-table>
            </template>
        </table-wrapper>

        <batch-edit-performance-index
            v-if="batchEditDialogVisible"
            v-model="batchEditDialogVisible"
            :trans-data="batchObj"
            :organization-list="organizationList"
            :consult-list="consultList"
            :org-code="orgCodeDefault"
            :cons-code="consCodeDefault"
            @callback="handleClose()"
        >
        </batch-edit-performance-index>

        <save-record-performance-index
            v-if="saveRecordDialogVisible"
            v-model="saveRecordDialogVisible"
            :trans-data="recordObj"
            :organization-list="organizationList"
            :consult-list="consultList"
            :org-code="orgCodeDefault"
            :cons-code="consCodeDefault"
            @callback="handleClose()"
        >
        </save-record-performance-index>
    </div>
</template>

<script lang="ts" setup>
    import { Plus, Download, InfoFilled } from '@element-plus/icons-vue'
    import { fetchRes, messageBox } from '@/utils'
    import { dataList } from './data/labelData'
    import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'

    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import BatchEditPerformanceIndex from '@/views/performanage/performanceRegionTotalBusiness/components/performanceManageRegionTotalBatchEdit.vue'
    import SaveRecordPerformanceIndex from '@/views/performanage/performanceManageBusiness/components/performanceManageSubTotalSaveRecord.vue'
    import { performanceRegionTotalBusinessTableColumn, showTableColumn } from './data/tableData'
    import {
        performanceRegionTotalBusinessQuery,
        performanceRegionTotalBusinessExport,
        performanceRegionTotalBusinessSave,
        performanceRegionTotalBusinessCurrWorkDays
    } from '@/api/project/performanage/performanceRegionTotalBusiness/performanceRegionTotalBusiness'
    import { MANAGE_REGION_TOTAL_OPER_PERMISSION } from '@/constant/performanceConst'
    import { getMenuPermission } from '@/api/project/common/common'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import LabelItem from '@/components/moduleBase/LabelItem.vue'
    import { defineComponent } from 'vue'
    import {
        performanceManageBusinessQuery,
        performanceManageCalSaveManageDSumData
    } from '@/api/project/performanage/performanceManageBusiness/performanceManageBusiness'

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const { userId, periodExplain, exaimneResult, userLevel, exaimneNode, istp, exaimneEndresult } =
        dataList

    const listLoading = ref<boolean>(false)
    const batchEditDialogVisible = ref<boolean>(false)
    const saveRecordDialogVisible = ref<boolean>(false)
    const consStatus = ref<string>('0') //包含离职人员

    const exportShow = ref<boolean>(false)
    const saveShow = ref<boolean>(false)
    const batchModifyShow = ref<boolean>(false)
    const saveRecordShow = ref<boolean>(false)

    //批量修改选中的id
    let checkIds = [] as string[]
    let consNames = [] as string[]

    const handleRowClassName = (rowObj: any, rowIndex: number) => {
        const { row } = rowObj
        if (row && row.haveHumanModify === '1') {
            return 'highlight-bg'
        }
    }

    const handleCellClassName = (rowObj: any) => {
        const { row, column, rowIndex, columnIndex } = rowObj
        if (row && column) {
            if (column.property === 'newRank' && row.humanNewRankFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'exaimneEndResult' && row.humanEndResultFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'newEndRank' && row.humanNewEndRankFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'exaimneResult' && row.humanExaimneResultFlag === '1') {
                return 'redColumn'
            }
        }
    }

    const module = ref<string>('B140706')

    const lastYearMonthValue = ''
    const lastUpdateTimeRef = ref<string>('')
    /**
     * @description: 批量修改
     * @return {*}
     */
    const batchObj = ref({
        ids: [] as string[],
        type: '',
        title: '',
        consNames: [] as string[]
    })

    /**
     * @description: 操作记录
     * @return {*}
     */
    const recordObj = ref({
        pageType: ''
    })
    /**
     * @description: 关闭弹框刷新数据
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleClose = (): void => {
        queryList()
    }

    const handleSelectionChange = (sel: any): void => {
        checkIds = sel.map((item: any) => item.id)
        consNames = sel.map((item: any) => item.consName)
        console.log(checkIds)
    }

    const handleBatchEdit = (val: any): void => {
        if (checkIds.length === 0) {
            ElMessage({
                type: 'info',
                message: '请先勾选需要批量修改的数据!'
            })
            return
        }
        batchObj.value = {
            ids: checkIds,
            type: 'edit',
            title: '批量修改',
            consNames: consNames
        }
        batchEditDialogVisible.value = true
    }

    const handleSaveRecord = (): void => {
        recordObj.value = {
            pageType: '3'
        }
        saveRecordDialogVisible.value = true
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        userId = ''
        periodExplain = ''
        userLevel = ''
        istp = ''
        exaimneResult = ''
        exaimneEndresult = ''
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        exaimneNode = {
            startDate: '',
            endDate: ''
        }
    }

    const queryForm = reactive(new QueryForm())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])
    /**
     * 当前日期是本月第几个工作日
     */
    const currWorkDays = ref<number>(0)

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            performanceRegionTotalBusinessTableColumn.map(item => item.key),
            performanceRegionTotalBusinessTableColumn
        )
    })

    const saveList = async () => {
        if (currWorkDays.value < 6) {
            ElMessage({
                message: '上个月存续D未计算完成，不能保存数据',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if (
            !(
                (queryForm.orgvalue.orgCode === '1' && queryForm.orgvalue.consCode === '') ||
                (queryForm.orgvalue.orgCode === '10' && queryForm.orgvalue.consCode === '') ||
                (queryForm.orgvalue.orgCode === '1000005627' && queryForm.orgvalue.consCode === '')
            )
        ) {
            ElMessage({
                message: '请选择中心的全部数据后再保存数据',
                type: 'warning',
                duration: 2000
            })
            return
        }
        messageBox(
            {
                confirmBtn: '确定',
                cancelBtn: '取消',
                content: `确认保存上月结果数据？`
            },
            () => {
                confirmSave()
            },
            () => false
        )
    }

    const confirmSave = async () => {
        const allLoading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)'
        })

        const requestParams = {
            orgCode: queryForm.orgvalue.orgCode
        }
        fetchRes(performanceRegionTotalBusinessSave(requestParams), {
            successCB: () => {
                ElMessage({
                    message: '保存成功',
                    type: 'success',
                    duration: 2000
                })
                allLoading.close()
            },
            errorCB: () => {
                allLoading.close()
            },
            catchCB: () => {
                allLoading.close()
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            flag: '0',
            userId: queryForm.userId,
            periodExplain: queryForm.periodExplain,
            userLevel: queryForm.userLevel,
            istp: queryForm.istp,
            exaimneResult: queryForm.exaimneResult,
            exaimneEndresult: queryForm.exaimneEndresult,
            exaimneNodeStart: queryForm.exaimneNode.startDate,
            exaimneNodeEnd: queryForm.exaimneNode.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value
        }
        fetchRes(performanceRegionTotalBusinessQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { list, haveExceptionData, workDays } = resObj
                if (haveExceptionData) {
                    // 弹框显示
                    ElMessage({
                        message: '考核配置中存在异常数据，请先修正后再使用此菜单',
                        type: 'warning',
                        duration: 2000
                    })
                } else {
                    tableData.value = list
                    currWorkDays.value = workDays
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description:
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    const saveHandle = () => {
        saveList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const params = {
            flag: '0',
            userId: queryForm.userId,
            periodExplain: queryForm.periodExplain,
            userLevel: queryForm.userLevel,
            istp: queryForm.istp,
            exaimneResult: queryForm.exaimneResult,
            exaimneEndresult: queryForm.exaimneEndresult,
            exaimneNodeStart: queryForm.exaimneNode.startDate,
            exaimneNodeEnd: queryForm.exaimneNode.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value
        }
        const allLoading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)'
        })
        fetchRes(performanceRegionTotalBusinessExport(params), {
            successCB: (resObj: any) => {
                const { fileByte, name } = resObj
                const bstr = atob(fileByte)
                let n = bstr.length
                const u8arr = new Uint8Array(n)
                while (n--) {
                    u8arr[n] = bstr.charCodeAt(n)
                }
                const blob = new Blob([u8arr], {
                    // 下载的文件类型
                    type: 'application/vnd.ms-excel;chartset=UTF-8'
                })
                const link = document.createElement('a')
                link.download = name || '导出文件'
                link.href = window.URL.createObjectURL(blob)
                link.click()
                allLoading.close()
            },
            errorCB: () => {
                allLoading.close()
            },
            catchCB: () => {
                allLoading.close()
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    const initData = async () => {
        const params = {
            menuCode: module.value
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode === MANAGE_REGION_TOTAL_OPER_PERMISSION.EXPORT &&
                            item.display === '1'
                        ) {
                            exportShow.value = true
                        }
                        if (
                            item.operateCode === MANAGE_REGION_TOTAL_OPER_PERMISSION.BATCH_MODIFY &&
                            item.display === '1'
                        ) {
                            batchModifyShow.value = true
                        }
                        if (
                            item.operateCode === MANAGE_REGION_TOTAL_OPER_PERMISSION.SAVE_RECORD &&
                            item.display === '1'
                        ) {
                            saveRecordShow.value = true
                        }
                        if (
                            item.operateCode === MANAGE_REGION_TOTAL_OPER_PERMISSION.SAVE &&
                            item.display === '1'
                        ) {
                            saveShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    const confirmCurrWorkDays = async () => {
        fetchRes(performanceRegionTotalBusinessCurrWorkDays(), {
            successCB: (resObj: any) => {
                currWorkDays.value = resObj
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [orgCodeDefault, consCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.orgvalue.orgCode = orgCodeDefault.value
                queryForm.orgvalue.consCode = consCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        console.log('onMounted')
        initData()
        confirmCurrWorkDays()
        fetchConsOrgList('', module.value)
    })
</script>
<style lang="less" scoped></style>
