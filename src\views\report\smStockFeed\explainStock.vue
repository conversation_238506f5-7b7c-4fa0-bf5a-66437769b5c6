<!--
 * @Description: 风险测评问卷弹框
 * @Author: chaohui.wu
 * @Date: 2023-03-23 13:26:04
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-21 13:39:50
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/explainStock.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        title="统计说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <template #default>
            <span style="font-weight: bold"
                >①不挂钩客户折算系数/管理系数、划转存量规则/接管存量规则等</span
            ><br />
            <span style="font-weight: bold"
                >②每日的产品存续D系数可能有变化，月数据计算过程中以实际产品存续D系数进行核算，列表中的产品存续D系数为月末的系数，仅供参考</span
            ><br />
            <span style="font-weight: bold">③时点投顾：数据核算保存时的投顾</span><br />
            <span style="font-weight: bold">④资源类型：客户当前的资源类型</span>
        </template>

        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">确定</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { useVisible } from '@/views/common/scripts/useVisible'
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()

    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })
</script>

<style lang="less" scoped>
    .middle-content {
        padding: 10px 0;
    }
</style>
