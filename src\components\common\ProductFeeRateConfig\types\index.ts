/*
 * @Description: 产品费率配置通用类型定义
 * @Author: hongdong.xie
 * @Date: 2025-06-06 09:42:20
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 09:42:20
 * @FilePath: /ds-report-web/src/components/common/ProductFeeRateConfig/types/index.ts
 */

/**
 * 产品类型枚举
 */
export type ProductType = 'fof' | 'fixedIncome' | 'secondary' | 'overseasRmb'

/**
 * 费率类型枚举
 */
export type RateType = '1' | '2' | '3' | '4' // 1-固收 2-二级 3-FOF 4-海外人民币

/**
 * 弹框类型枚举
 */
export type DialogType = 'add' | 'copyAdd' | 'edit' | 'audit'

/**
 * API配置接口
 */
export interface ApiConfig {
    /** 查询接口URL */
    queryUrl: string
    /** 新增接口URL */
    addUrl: string
    /** 修改接口URL */
    updateUrl: string
    /** 删除接口URL */
    deleteUrl: string
    /** 审核接口URL */
    auditUrl: string
    /** 导出接口URL */
    exportUrl: string
    /** 权限接口URL */
    authUrl: string
}

/**
 * 页面配置接口
 */
export interface PageConfig {
    /** 页面标题 */
    title: string
    /** 弹框标题配置 */
    dialogTitle: {
        add: string
        edit: string
        copyAdd: string
        audit: string
    }
    /** 导出文件名 */
    exportFileName: string
}

/**
 * 产品费率配置总配置接口
 */
export interface ProductFeeRateConfig {
    /** 产品类型 */
    productType: ProductType
    /** 费率类型 */
    rateType: RateType
    /** API配置 */
    apiConfig: ApiConfig
    /** 页面配置 */
    pageConfig: PageConfig
}

/**
 * 查询参数基础接口
 */
export interface BaseQueryParam {
    /** 备案代码 */
    filingCode?: string
    /** 产品全称 */
    productFullName?: string
    /** 付款方全称 */
    payerFullName?: string
    /** 查询开始日期 格式：yyyyMMdd */
    queryStartDate?: string
    /** 查询结束日期 格式：yyyyMMdd */
    queryEndDate?: string
    /** 审核状态 0-全部 1-待审核 2-审核通过 3-审核不通过 */
    auditStatus: string
    /** 页码 默认1 */
    page: number
    /** 每页记录数 默认10 */
    rows: number
    /** 费率类型 1-固收产品 2-二级产品 3-FOF产品 4-海外人民币产品 */
    rateType: string
}

/**
 * 导出参数基础接口
 */
export interface BaseExportParam {
    /** 备案代码 */
    filingCode?: string
    /** 产品全称 */
    productFullName?: string
    /** 付款方全称 */
    payerFullName?: string
    /** 查询开始日期 格式：yyyyMMdd */
    queryStartDate?: string
    /** 查询结束日期 格式：yyyyMMdd */
    queryEndDate?: string
    /** 审核状态 0-全部 1-待审核 2-审核通过 3-审核不通过 */
    auditStatus: string
    /** 费率类型 1-固收产品 2-二级产品 3-FOF产品 4-海外人民币产品 */
    rateType: string
}

/**
 * 删除参数基础接口
 */
export interface BaseDeleteParam {
    /** 主键ID */
    id: number
    /** 费率类型，1-固收产品 2-二级产品 3-FOF产品 4-海外人民币产品 */
    rateType: string
}

/**
 * 审核参数基础接口
 */
export interface BaseAuditParam {
    /** 主键ID */
    id: number
    /** 审核状态，2-审核通过 3-审核不通过 */
    auditStatus: string
    /** 费率类型，1-固收产品 2-二级产品 3-FOF产品 4-海外人民币产品 */
    rateType: string
    /** 审核备注 */
    auditRemark?: string
}

/**
 * 表单数据基础接口
 */
export interface BaseFormData {
    /** 主键ID（修改时需要） */
    id?: number
    /** 备案代码 */
    filingCode: string
    /** 产品code（好买内部） */
    productCode: string
    /** 份额代码 */
    shareCode: string
    /** 产品全称 */
    productFullName: string
    /** 仅限FOF，0-否 1-是 */
    fofOnlyFlag: string
    /** 付款方全称 */
    payerFullName: string
    /** 产品经理 */
    productManager: string
    /** 签约日期 */
    signingDate?: string
    /** 好买签约主体 */
    howbuySigningEntity: string
    /** 对方联系人 */
    counterpartContact: string
    /** 存续D系数 */
    durationDCoefficient: number
    /** 存续D备注 */
    durationDRemark?: string
    /** 费率开始日期 */
    rateStartDate: string
    /** 费率结束日期 */
    rateEndDate: string
    /** 费率配置类型 */
    tieredRateType: string
    /** 费率配置生效日 */
    rateEffectiveType: string
    /** 配置下限 */
    configLowerLimit?: number
    /** 配置上限 */
    configUpperLimit?: number
    /** 费率生效日期 */
    rateEffectiveStartDate?: string
    /** 费率结束日期 */
    rateEffectiveEndDate?: string
    /** 认申购费率 */
    subscriptionRate: number
    /** 认申购费备注 */
    subscriptionRemark?: string
    /** 管理费率 */
    managementRate: number
    /** 管理费公式 */
    managementFormula?: string
    /** 管理费备注 */
    managementRemark?: string
    /** 业绩报酬分成费率1 */
    performanceSharingRate1: number
    /** 业绩报酬费率1 */
    performanceRate1?: number
    /** 业绩报酬计提基准1 */
    performanceBenchmark1?: string
    /** 业绩报酬分成费率2 */
    performanceSharingRate2?: number
    /** 业绩报酬费率2 */
    performanceRate2?: number
    /** 业绩报酬计提基准2 */
    performanceBenchmark2?: string
    /** 业绩报酬分成费率3 */
    performanceSharingRate3?: number
    /** 业绩报酬费率3 */
    performanceRate3?: number
    /** 业绩报酬计提基准3 */
    performanceBenchmark3?: string
    /** 业绩报酬计提类型 */
    performanceAccrualType?: string
    /** 业绩报酬计提形式 */
    performanceAccrualForm?: string
    /** 固定计提日月份类型 */
    fixedAccrualMonthType?: string
    /** 固定计提日日期类型 */
    fixedAccrualDateType?: string
    /** 固定计提日计算类型 */
    fixedAccrualCalcType?: string
    /** 赎回业绩报酬持有天数规则 */
    redemptionHoldingDaysRule?: string
    /** 分红业绩报酬持有天数规则 */
    dividendHoldingDaysRule?: string
    /** 业绩报酬公式 */
    performanceFormula?: string
    /** 份额锁定期类型 */
    shareLockType?: string
    /** 份额锁定期天数 */
    shareLockDays?: number
    /** 基金封闭期类型 */
    fundClosedType?: string
    /** 基金封闭期天数 */
    fundClosedDays?: number
    /** 不计提净值基准 */
    noAccrualNavBenchmark?: number
    /** 业绩报酬备注 */
    performanceRemark?: string
    /** 赎回费率1 */
    redemptionRate1: number
    /** 赎回费持有天数1 */
    redemptionHoldingDays1?: number
    /** 赎回费率2 */
    redemptionRate2?: number
    /** 赎回费持有天数2 */
    redemptionHoldingDays2?: number
    /** 赎回费率3 */
    redemptionRate3?: number
    /** 赎回费持有天数3 */
    redemptionHoldingDays3?: number
    /** 赎回费率4 */
    redemptionRate4?: number
    /** 赎回费持有天数4 */
    redemptionHoldingDays4?: number
    /** 赎回费公式 */
    redemptionFormula?: string
    /** 赎回费持有天数计算规则 */
    redemptionHoldingCalcRule?: string
    /** 赎回费特例 */
    redemptionSpecialRule?: string
    /** 赎回费备注 */
    redemptionRemark?: string
    /** 费率类型 */
    rateType: string
}

/**
 * 分页对象接口
 */
export interface PageObj {
    page: number
    size: number
    total: number
    perPage: number
}

/**
 * 查询表单接口
 */
export interface QueryForm {
    productFullName: string
    payerFullName: string
    filingCode: string
    auditStatus: string
    queryStartDate: string
    queryEndDate: string
    recordDate: {
        startDate: string
        endDate: string
    }
    page: number
    rows: number
    rateType: string
}
