/*
 * @Description: 定义搜索的label列表
 * @Author: chaohui.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:36:23
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/labelData.ts
 *
 */
export const dataList = {
    recordDate: {
        label: '录入日期',
        placeholder: ['开始日期', '结束日期']
    },
    jjdm: {
        label: '产品代码',
        placeholder: '输入产品代码'
    },
    cpmc: {
        label: '产品名称',
        placeholder: '输入产品名称'
    },
    hszt: {
        label: '审核状态',
        placeholder: '输入审核状态',
        selectList: [
            {
                key: '0',
                label: '待审核'
            },
            {
                key: '1',
                label: '审核通过'
            },
            {
                key: '2',
                label: '审核不通过'
            }
        ]
    },
    // isReview: {
    //     label: '是否审核过',
    //     placeholder: '输入审核状态',
    //     selectList: [
    //         {
    //             key: '0',
    //             label: '全部'
    //         },
    //         {
    //             key: '1',
    //             label: '是'
    //         },
    //         {
    //             key: '2',
    //             label: '否'
    //         }
    //     ]
    // },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
