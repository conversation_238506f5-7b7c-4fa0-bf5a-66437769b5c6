/*
 * @Description: 定义搜索的label列表
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:36:23
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/labelData.ts
 *
 */
export const dataList = {
    startDt: {
        label: '开始日期',
        placeholder: '输入开始日期'
    },
    endDt: {
        label: '结束日期',
        placeholder: '输入结束日期'
    },
    fundMan: {
        label: '基金管理人',
        placeholder: '输入基金管理人'
    },
    // 产品类型
    productType: {
        label: '产品类型',
        placeholder: '请选择产品类型',
        selectList: [
            {
                key: '1',
                label: '固收'
            },
            {
                key: '2',
                label: '二级'
            },
            {
                key: '3',
                label: '股权'
            }
        ]
    },
    // 费用类型
    feeType: {
        label: '费用类型',
        placeholder: '请选择费用类型',
        fixedList: [
            {
                key: '1',
                label: '咨询费-普通口径'
            },
            {
                key: '2',
                label: '管理费-存量口径'
            },
            {
                key: '3',
                label: '退出费-赎回口径'
            },
            {
                key: '10',
                label: '业绩报酬 (固收产品)'
            }
        ],
        secondList: [
            {
                key: '4',
                label: '赎回费(二级产品)'
            },
            {
                key: '5',
                label: '管理费(二级产品)'
            },
            {
                key: '6',
                label: '业绩报酬 (二级产品)'
            }
        ],
        stockList: [
            {
                key: '7',
                label: '认购费/设立服务费(股权产品)'
            },
            {
                key: '8',
                label: '管理费/咨询服务费 (股权产品)'
            },
            {
                key: '9',
                label: '业绩报酬 (股权产品)'
            }
        ]
    },
    orgCode: {
        label: '部门',
        placeholder: '部门'
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
