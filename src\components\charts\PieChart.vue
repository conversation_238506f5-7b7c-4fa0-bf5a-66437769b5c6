<!--
 * @Description: 
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-26 10:31:26
 * @FilePath: /crm-asset-web/src/components/charts/BarChart.vue
 *  
-->
<template>
    <div id="main"></div>
</template>
<script>
    import * as echarts from 'echarts'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: '<PERSON><PERSON><PERSON>',
        props: {
            // eslint-disable-next-line vue/require-default-prop
            vId: {
                type: String
            },
            title: {
                type: String,
                default: 'title'
            },
            // eslint-disable-next-line vue/require-default-prop
            seriesData: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            boardData: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            dataX: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            dataY: {
                type: Array
            }
        },
        data() {
            return {
                option: {
                    tooltip: {
                        trigger: 'item'
                    },
                    legend: {
                        top: '5%',
                        left: 'center'
                    },
                    series: [
                        {
                            name: 'Access From',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 10,
                                borderColor: '#fff',
                                borderWidth: 2
                            },
                            label: {
                                show: false,
                                position: 'center'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: 40,
                                    fontWeight: 'bold'
                                }
                            },
                            labelLine: {
                                show: false
                            },
                            data: [
                                { value: 1048, name: 'Search Engine' },
                                { value: 735, name: 'Direct' },
                                { value: 580, name: 'Email' },
                                { value: 484, name: 'Union Ads' },
                                { value: 300, name: 'Video Ads' }
                            ]
                        }
                    ]
                }
            }
        },
        mounted() {
            this.renderGaugeChart()
        },
        methods: {
            renderGaugeChart() {
                const gaugeChart = echarts.init(document.getElementById('main'))
                gaugeChart.setOption(this.option)
            }
        }
    })
</script>
<style lang="less" scoped>
    * {
        padding: 0;
        margin: 0;
    }

    #main {
        position: relative;
        height: 100vh;
        overflow: hidden;
    }
</style>
