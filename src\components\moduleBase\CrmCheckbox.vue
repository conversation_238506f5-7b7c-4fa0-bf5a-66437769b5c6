<!--
* author: di.zong
* 多选框复用组件
* params: optionList === 是多选的选项   实例为对象 {label: 1, value: 1, disabled： true}
* params: labelFormat === 对象中用于显示的字段  默认值为label
* params: valueFormat === vmode绑定的数组中是对象中的哪个字段   默认值为value
* params: width === 复选框宽度 string  默认100%
* check: 勾选或取消勾选选项时抛出的事件   参数分别是： 点击后的状态，选项实例，index序列
* 若禁用某选项  则在对象中加入disabled： true即可
* 通用复选框组件
 -->
<template>
    <div class="crm-checkbox">
        <el-checkbox
            v-if="needAll"
            v-model="checkAll"
            class="check-all"
            :indeterminate="isIndeterminate"
            @change="handleCheckAllChange"
        >
            不限
        </el-checkbox>
        <el-checkbox-group :style="`width: ${width}`" v-bind="$attrs">
            <el-checkbox
                v-for="(item, index) in optionList"
                :key="index"
                :label="item[valueFormat]"
                :disabled="item.disabled"
                :style="`width: ${itemWidth}`"
                @change="
                    status => {
                        optionChange(status, item, index)
                    }
                "
            >
                {{ item[labelFormat] }}
            </el-checkbox>
        </el-checkbox-group>
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmCheckbox',
        props: {
            optionList: {
                type: Array,
                default: () => {
                    return []
                }
            },
            isIndeterminate: {
                type: Boolean,
                default: false
            },
            labelFormat: {
                type: String,
                default: 'label'
            },
            valueFormat: {
                type: String,
                default: 'value'
            },
            width: {
                type: String,
                default: '100%'
            },
            itemWidth: {
                type: String,
                default: ''
            },
            needAll: {
                type: Boolean,
                default: false
            }
        },
        emits: ['checkAll', 'check'],
        data() {
            return {
                checkAll: true
                // isIndeterminate: false
            }
        },
        methods: {
            handleCheckAllChange(val) {
                // this.$emit(
                //     'checkAll',
                //     val
                //         ? this.optionList.map(item => {
                //               return item[this.valueFormat]
                //           })
                //         : []
                // )
                this.$emit('checkAll', (val = []))
            },
            optionChange(status, value, index) {
                this.$emit('check', status, value, index)
            }
        }
    })
</script>
<style lang="less">
    .crm-checkbox {
        .el-checkbox {
            float: left;
            height: 24px;
            margin-right: 5px;
            font-size: 12px;
            line-height: 24px;
            // margin-bottom: 10px; // 私募机会池筛选中的兼容修改
            &.check-all {
                .el-checkbox__label {
                    min-width: 48px;
                }
            }

            .el-checkbox__label {
                display: inline-block;
                min-width: 100px;
                height: 18px;
                padding-left: 5px;
                font-family: 'Microsoft YaHei', '微软雅黑';
                font-size: 12px;
                font-weight: 400;
                line-height: 17px;
                color: @font_color;
            }

            .el-checkbox__inner {
                border-color: #adadb9;
                border-radius: 0;
            }

            &:last-of-type {
                margin-right: 5px;
            }
        }

        .is-checked {
            .el-checkbox__label {
                font-size: 12px;
                color: @font_color;
            }

            .el-checkbox__inner {
                background-color: @theme_main;
                border-color: @theme_main;
            }
        }
    }
</style>
