import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { TradeGdHkParam } from './type/apiReqType.js'

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const tradeGdHkQuery = (params: TradeGdHkParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/saleManage/tradeGdHk/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const tradeGdHkExport = (params: TradeGdHkParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/saleManage/tradeGdHk/export',
            method: 'post',
            data: params
        })
    )
}
