<!--
 * @Description: svg通用组件
 * @Author: chaohui.wu
 * @Date: 2023-03-30 22:35:46
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-04-03 13:49:25
 * @FilePath: /crm-asset-web/src/components/moduleBase/SvgIcons.vue
 *  
-->

<template>
    <svg aria-hidden="true" class="svg-icon" :style="{ width: width, height: height }">
        <use :xlink:href="symbolId" :fill="color" />
    </svg>
</template>

<script setup lang="ts">
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            prefix?: string
            name: string
            color?: string
            width?: string
            height?: string
        }>(),
        {
            prefix: 'icon',
            name: '',
            color: '#fff',
            width: '1em',
            height: '1em'
        }
    )

    const symbolId = computed(() => `#${props.prefix}-${props.name}`)
</script>

<style lang="less" scoped>
    .svg-icon {
        overflow: hidden;
        fill: currentcolor;
    }
</style>
