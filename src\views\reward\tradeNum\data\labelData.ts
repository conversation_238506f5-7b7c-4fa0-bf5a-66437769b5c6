/*
 * @Description: 定义搜索的label列表
 * @Author: jianji<PERSON>.yang
 * @Date: 2024-04-01 10:40:30
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-04-01 10:40:30
 * @FilePath: /src/views/report/kpi/assetRepDownload/data/labelData.ts
 *
 */
export const dataList = {
    accountProductType: {
        label: '核算产品类型',
        placeholder: '请选择'
    },
    consCustNo: {
        label: '投顾客户号',
        placeholder: '请输入'
    },
    containRelationAccount: {
        label: '',
        placeholder: '请输入'
    },
    preId: {
        label: '预约ID',
        placeholder: '请输入'
    },
    workType: {
        label: '在职状态',
        placeholder: '请选择',
        selectList: [
            {
                key: '1',
                label: '在职'
            },
            {
                key: '2',
                label: '离职'
            }
        ]
    },
    userId: {
        label: '投顾code',
        placeholder: '请输入'
    },
    fundType: {
        placeholder: '请选择',
        selectList: [
            {
                key: '4',
                label: '私募'
            },
            {
                key: '5',
                label: '公募'
            }
        ]
    },
    markFlag: {
        placeholder: '请选择',
        selectList: [
            {
                key: '1',
                label: '是'
            },
            {
                key: '0',
                label: '否'
            }
        ]
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
