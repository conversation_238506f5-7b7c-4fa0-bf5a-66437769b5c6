<!--
 * @Description: 首页路由及layout
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-27 14:55:51
 * @FilePath: /crm-template/src/App.vue
 *  
-->
<script>
    import { defineComponent } from 'vue'
    import { ElConfigProvider } from 'element-plus'

    import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

    export default defineComponent({
        components: {
            ElConfigProvider
        },
        setup() {
            return {
                locale: zhCn
            }
        }
    })
</script>
<template>
    <el-config-provider :locale="locale">
        <router-view v-slot="{ Component }">
            <component :is="Component" />
        </router-view>
    </el-config-provider>
</template>

<style lang="less">
    @import './assets/css/elVariable.less';
    @import './assets/css/pageCommon.less';
</style>
