<!--
 * @Description: 产品费率配置通用列表组件
 * @Author: hongdong.xie
 * @Date: 2025-06-06 09:42:20
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 09:42:20
 * @FilePath: /ds-report-web/src/components/common/ProductFeeRateConfig/ProductFeeRateConfigList.vue
-->
<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            :show-export-btn="true"
            @searchFn="handleLoading"
            @exportFn="exportHandle"
        >
            <template #searchArea>
                <!-- 备案代码输入框 -->
                <label-item :label="searchConfig.filingCode.label">
                    <crm-input
                        v-model="queryForm.filingCode"
                        :placeholder="searchConfig.filingCode.placeholder"
                        :clearable="true"
                        :maxlength="6"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <!-- 产品全称输入框 -->
                <label-item :label="searchConfig.productFullName.label">
                    <crm-input
                        v-model="queryForm.productFullName"
                        :placeholder="searchConfig.productFullName.placeholder"
                        :clearable="true"
                        :style="{ width: '200px' }"
                    />
                </label-item>
                <!-- 付款方全称输入框 -->
                <label-item :label="searchConfig.payerFullName.label">
                    <crm-input
                        v-model="queryForm.payerFullName"
                        :placeholder="searchConfig.payerFullName.placeholder"
                        :clearable="true"
                        :style="{ width: '200px' }"
                    />
                </label-item>
                <!-- 录入日期 -->
                <label-item :label="searchConfig.recordDate.label">
                    <date-range
                        v-model="queryForm.recordDate"
                        show-format="YYYY-MM-DD"
                        :placeholder="searchConfig.recordDate.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 审核状态下拉框 -->
                <label-item :label="searchConfig.auditStatus.label">
                    <crm-select
                        v-model="queryForm.auditStatus"
                        :placeholder="searchConfig.auditStatus.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="searchConfig.auditStatus.selectList"
                        :style="{ width: '150px' }"
                        @change="handleAuditStatus"
                    />
                </label-item>
            </template>

            <template #operationBtns>
                <crm-button
                    v-if="addShow"
                    size="small"
                    :icon="Plus"
                    type="primary"
                    :radius="true"
                    @click="handleAdd"
                    >新增</crm-button
                >
                <crm-button
                    v-if="copyAddShow"
                    size="small"
                    :icon="CopyDocument"
                    type="primary"
                    :radius="true"
                    @click="handleCopyAdd"
                    >复制新增</crm-button
                >
                <crm-button
                    v-if="editShow"
                    size="small"
                    :icon="Edit"
                    type="primary"
                    :radius="true"
                    @click="handleEdit"
                    >修改</crm-button
                >
                <crm-button
                    v-if="auditShow"
                    size="small"
                    :icon="Check"
                    type="primary"
                    :radius="true"
                    @click="handleAudit"
                    >审核</crm-button
                >
                <crm-button
                    v-if="deleteShow"
                    size="small"
                    :icon="Minus"
                    type="primary"
                    :radius="true"
                    @click="handleBatchDelete"
                    >删除</crm-button
                >
            </template>

            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumns"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="true"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    @select="changeSelectTable"
                    @selectionChange="changeSelectTable"
                >
                </base-table>
            </template>

            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>

        <!-- 新增/编辑弹框 -->
        <ProductFeeRateConfigAdd
            v-model:visible="addDialogVisible"
            :config="config"
            :dialog-type="dialogType"
            :edit-data="editData"
            @success="handleAddSuccess"
        />

        <!-- 审核弹框 -->
        <ProductFeeRateConfigAudit
            v-model:visible="auditDialogVisible"
            :config="config"
            :audit-data="auditData"
            @success="handleAuditSuccess"
        />
    </div>
</template>

<script lang="ts" setup>
    import { computed, onBeforeMount, onMounted } from 'vue'
    import { Plus, Edit, Minus, Check, CopyDocument } from '@element-plus/icons-vue'
    import type { ProductFeeRateConfig } from './types'
    import { useProductFeeRateConfig } from './composables/useProductFeeRateConfig'
    import ProductFeeRateConfigAdd from './ProductFeeRateConfigAdd.vue'
    import ProductFeeRateConfigAudit from './ProductFeeRateConfigAudit.vue'

    // 组件props定义
    interface Props {
        /** 配置对象 */
        config: ProductFeeRateConfig
        /** 表格列配置 */
        tableColumns: any[]
    }

    const props = defineProps<Props>()

    // 使用通用逻辑hooks
    const {
        // 响应式数据
        listLoading,
        addShow,
        copyAddShow,
        editShow,
        auditShow,
        deleteShow,
        addDialogVisible,
        dialogType,
        editData,
        auditDialogVisible,
        auditData,
        queryForm,
        pageObj,
        tableData,
        tableSelectList,
        searchConfig,

        // 方法
        queryList,
        exportList,
        confirmDelete,
        getMenuAuth,
        handleLoading,
        exportHandle,
        handleCurrentChange,
        handleAuditStatus,
        changeSelectTable,
        handleAdd,
        handleCopyAdd,
        handleEdit,
        handleAudit,
        handleBatchDelete,
        handleAddSuccess,
        handleAuditSuccess
    } = useProductFeeRateConfig(props.config)

    // 生命周期
    onBeforeMount(() => {
        getMenuAuth()
    })

    onMounted(() => {
        // 页面加载时自动查询数据
        queryList()
    })
</script>

<style lang="less" scoped></style>
