import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { NetIncreaseOverseasParam } from './type/apiReqType.js'
import { time } from 'console'

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const NetIncreaseOverseasQuery = (params: NetIncreaseOverseasParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreaseoverseas2025/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const NetIncreaseOverseasExport = (params: NetIncreaseOverseasParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreaseoverseas2025/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const NetIncreaseOverseasDetailQuery = (params: NetIncreaseOverseasParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreaseoverseas2025/querydetail',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const NetIncreaseOverseasDetailExport = (params: NetIncreaseOverseasParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreaseoverseas2025/exportdetail',
            timeout: 60000,
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const NetIncreaseOverseasInit = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/netincreaseoverseas2025/initdata',
            method: 'post',
            data: null
        })
    )
}
