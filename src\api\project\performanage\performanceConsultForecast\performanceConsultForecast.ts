import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import {
    performanceConsultForecastParam,
    performanceConsultForecastBatchUpdateParam,
    performanceConsultForecastSaveParam,
    querySaveRecordParam,
    queryConsRankParam
} from './type/apiReqType.js'

/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const performanceConsultForecastQuery = (params: performanceConsultForecastParam): any => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/consult/forecast/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 导出接口
 * @return {*}
 */
export const performanceConsultForecastExport = (params: performanceConsultForecastParam) => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/consult/forecast/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 批量修改
 * @return {*}
 */
export const performanceConsultForecastBatchUpdate = (
    params: performanceConsultForecastBatchUpdateParam
) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/consult/forecast/batchupdate',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 批量修改初始化
 * @return {*}
 */
export const performanceConsultForecastBatchUpdateInitData = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/consult/forecast/batchupdateinitdata',
            method: 'post'
            //    data: params
        })
    )
}

/**
 * @description: 保存
 * @return {*}
 */
export const performanceConsultForecastSave = (params: performanceConsultForecastSaveParam) => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/consult/forecast/save',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 操作记录查询
 * @return {*}
 */
export const performanceConsultForecastQuerySaveRecord = (params: querySaveRecordParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/consult/forecast/querysaverecord',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 分总列表页初始化数据
 * @return {*}
 */
export const performanceConsultForecastListInitData = (): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/consult/forecast/listinitdata',
            method: 'post'
            //    data: params
        })
    )
}
/**
 * @description: 汇总并保存存续D 数据
 * @return {*}
 */
export const performanceCalSaveDSumData = (params: {}): any => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/consult/forecast/calsavedsumdata',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 职级查询
 * @return {*}
 */
export const queryConsRankList = (params: queryConsRankParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/consult/forecast/getconsrank',
            method: 'post',
            data: params
        })
    )
}
