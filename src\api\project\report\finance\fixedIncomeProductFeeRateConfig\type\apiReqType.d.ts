/*
 * @Description: 固收产品费率配置 API请求参数类型定义
 * @Author: hongdong.xie
 * @Date: 2025-06-05 18:53:18
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-05 18:53:18
 * @FilePath: /ds-report-web/src/api/project/report/finance/fixedIncomeProductFeeRateConfig/type/apiReqType.d.ts
 */

/**
 * 固收产品费率配置查询参数
 */
export interface FixedIncomeProductFeeRateConfigQueryParam {
    /** 备案代码 */
    filingCode?: string
    /** 产品全称 */
    productFullName?: string
    /** 付款方全称 */
    payerFullName?: string
    /** 查询开始日期 格式：yyyyMMdd */
    queryStartDate?: string
    /** 查询结束日期 格式：yyyyMMdd */
    queryEndDate?: string
    /** 审核状态 0-全部 1-待审核 2-审核通过 3-审核不通过 */
    auditStatus: string
    /** 页码 默认1 */
    page: number
    /** 每页记录数 默认10 */
    rows: number
    /** 费率类型 1-固收产品 2-二级产品 3-FOF产品 4-海外人民币产品 */
    rateType: string
}

/**
 * 固收产品费率配置新增参数
 */
export interface FixedIncomeProductFeeRateConfigAddParam {
    /** 备案代码 */
    filingCode: string
    /** 产品code（好买内部） */
    productCode: string
    /** 份额代码 */
    shareCode: string
    /** 产品全称 */
    productFullName: string
    /** 仅限FOF，0-否 1-是 */
    fofOnlyFlag: string
    /** 付款方全称 */
    payerFullName: string
    /** 产品经理 */
    productManager: string
    /** 签约日期 */
    signingDate?: string
    /** 好买签约主体 */
    howbuySigningEntity: string
    /** 对方联系人 */
    counterpartContact: string
    /** 存续D系数 */
    durationDCoefficient: number
    /** 存续D备注 */
    durationDRemark?: string
    /** 费率开始日期 */
    rateStartDate: string
    /** 费率结束日期 */
    rateEndDate: string
    /** 费率配置类型，0-不涉及 1-存量市值 2-存量份额 3-交易日 4-累计购买金额 5-累计购买净额 */
    tieredRateType: string
    /** 费率配置生效日，0-不涉及 1-次日生效 2-次月生效 3-次季度生效 4-次年生效 5-触发即直接改所在结算周期的费率 */
    rateEffectiveType: string
    /** 配置下限 */
    configLowerLimit?: number
    /** 配置上限 */
    configUpperLimit?: number
    /** 费率生效日期 */
    rateEffectiveStartDate?: string
    /** 费率结束日期 */
    rateEffectiveEndDate?: string
    /** 认申购费率 */
    subscriptionRate: number
    /** 认申购费备注 */
    subscriptionRemark?: string
    /** 管理费率 */
    managementRate: number
    /** 管理费公式 */
    managementFormula?: string
    /** 管理费备注 */
    managementRemark?: string
    /** 业绩报酬分成费率1 */
    performanceSharingRate1: number
    /** 业绩报酬费率1 */
    performanceRate1?: number
    /** 业绩报酬计提基准1 */
    performanceBenchmark1?: string
    /** 业绩报酬分成费率2 */
    performanceSharingRate2?: number
    /** 业绩报酬费率2 */
    performanceRate2?: number
    /** 业绩报酬计提基准2 */
    performanceBenchmark2?: string
    /** 业绩报酬分成费率3 */
    performanceSharingRate3?: number
    /** 业绩报酬费率3 */
    performanceRate3?: number
    /** 业绩报酬计提基准3 */
    performanceBenchmark3?: string
    /** 业绩报酬计提类型，0-不涉及 1-基金整体高水位净值法 2-单人单笔份额高水位法 */
    performanceAccrualType?: string
    /** 业绩报酬计提形式，1-净值调减 2-份额调减 3-无业绩报酬 4-金额扣减 5-混合扣减 */
    performanceAccrualForm?: string
    /** 固定计提日月份类型，0-不涉及 1-12对应1月-12月 20-每月等 */
    fixedAccrualMonthType?: string
    /** 固定计提日日期类型，0-不涉及 1-31对应1日-31日 41-月最后一个T日等 */
    fixedAccrualDateType?: string
    /** 固定计提日计算类型，1-往前找最近T日 2-往后找最近T日 */
    fixedAccrualCalcType?: string
    /** 赎回业绩报酬持有天数规则，0-不涉及 1-赎回开放日期 2-赎回确认日期 */
    redemptionHoldingDaysRule?: string
    /** 分红业绩报酬持有天数规则，0-不涉及 1-分红场外除息日期 2-分红交易确认日期 */
    dividendHoldingDaysRule?: string
    /** 业绩报酬公式，0-无 1-历史最高 2-上个成功计提日等 */
    performanceFormula?: string
    /** 份额锁定期类型，0-不涉及 1-锁定期计提 2-锁定期不计提 */
    shareLockType?: string
    /** 份额锁定期天数 */
    shareLockDays?: number
    /** 基金封闭期类型，0-不涉及 1-封闭期计提 2-封闭期不计提 */
    fundClosedType?: string
    /** 基金封闭期天数 */
    fundClosedDays?: number
    /** 不计提净值基准 */
    noAccrualNavBenchmark?: number
    /** 业绩报酬备注 */
    performanceRemark?: string
    /** 赎回费率1 */
    redemptionRate1: number
    /** 赎回费持有天数1 */
    redemptionHoldingDays1?: number
    /** 赎回费率2 */
    redemptionRate2?: number
    /** 赎回费持有天数2 */
    redemptionHoldingDays2?: number
    /** 赎回费率3 */
    redemptionRate3?: number
    /** 赎回费持有天数3 */
    redemptionHoldingDays3?: number
    /** 赎回费率4 */
    redemptionRate4?: number
    /** 赎回费持有天数4 */
    redemptionHoldingDays4?: number
    /** 赎回费公式 */
    redemptionFormula?: string
    /** 赎回费持有天数计算规则，0-不涉及 1-开放日期 2-确认日期 */
    redemptionHoldingCalcRule?: string
    /** 赎回费特例，0-不涉及 1-分红份额不计赎回费 */
    redemptionSpecialRule?: string
    /** 赎回费备注 */
    redemptionRemark?: string
    /** 费率类型，1-固收产品 2-二级产品 3-FOF产品 4-海外人民币产品 */
    rateType: string
}

/**
 * 固收产品费率配置修改参数
 */
export interface FixedIncomeProductFeeRateConfigUpdateParam extends FixedIncomeProductFeeRateConfigAddParam {
    /** 主键ID */
    id: number
}

/**
 * 固收产品费率配置删除参数
 */
export interface FixedIncomeProductFeeRateConfigDeleteParam {
    /** 主键ID */
    id: number
    /** 费率类型，1-固收产品 2-二级产品 3-FOF产品 4-海外人民币产品 */
    rateType: string
}

/**
 * 固收产品费率配置审核参数
 */
export interface FixedIncomeProductFeeRateConfigAuditParam {
    /** 主键ID */
    id: number
    /** 审核状态，2-审核通过 3-审核不通过 */
    auditStatus: string
    /** 费率类型，1-固收产品 2-二级产品 3-FOF产品 4-海外人民币产品 */
    rateType: string
    /** 审核备注 */
    auditRemark?: string
}

/**
 * 固收产品费率配置导出参数
 */
export interface FixedIncomeProductFeeRateConfigExportParam {
    /** 备案代码 */
    filingCode?: string
    /** 产品全称 */
    productFullName?: string
    /** 付款方全称 */
    payerFullName?: string
    /** 查询开始日期 格式：yyyyMMdd */
    queryStartDate?: string
    /** 查询结束日期 格式：yyyyMMdd */
    queryEndDate?: string
    /** 审核状态 0-全部 1-待审核 2-审核通过 3-审核不通过 */
    auditStatus: string
    /** 费率类型 1-固收产品 2-二级产品 3-FOF产品 4-海外人民币产品 */
    rateType: string
} 