import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { AssetGdHkParam } from './type/apiReqType.js'

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const assetGdHkQuery = (params: AssetGdHkParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/saleManage/assetGdHk/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const assetGdHkExport = (params: AssetGdHkParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/saleManage/assetGdHk/export',
            method: 'post',
            data: params
        })
    )
}
