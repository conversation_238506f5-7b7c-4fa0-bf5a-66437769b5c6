<!--
 * @Description: 修改弹框
 * @Author: jianjian.yang
 * @Date: 2024-07-26 10:07:35
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="600px"
        height="800px"
        :border="true"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <!-- 编辑的弹框 -->
            <el-form
                ref="ruleFormRef"
                :model="formList"
                label-width="100px"
                :rules="rules"
                status-icon
            >
                <el-row>
                    <el-col :span="20">
                        <el-form-item 
                            prop="orgName"
                            label-width="140px"
                            label="所属部门" 
                            style="margin-top: 15px; margin-bottom: 15px;">
                            <crm-input
                                v-model="formList.orgName"
                                :clearable="true"
                                disabled="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col >
                        <el-form-item
                            prop="consName"
                            label-width="140px"
                            label="管理人员" 
                            style="margin-top: 15px; margin-bottom: 15px;"
                           >
                            <crm-input
                                v-model="formList.consName"
                                :clearable="true"
                                disabled="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item 
                            prop="conslevel" 
                            label-width="140px"
                            label="层级" 
                            style="margin-top: 15px; margin-bottom: 15px;">
                            <crm-input
                                v-model="formList.userLevel"
                                :clearable="true"
                                disabled="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col>
                        <el-form-item 
                            prop="regDt"
                            label-width="140px"
                            label="入职日期"
                            style="margin-top: 15px; margin-bottom: 15px;">
                            <crm-input
                                v-model="formList.regDt"
                                :clearable="true"
                                disabled="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item 
                            prop="regularDt"
                            label-width="140px"
                            label="转正日期" 
                            style="margin-top: 15px; margin-bottom: 15px;">
                            <crm-input
                                v-model="formList.regularDt"
                                :clearable="true"
                                disabled="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="promoteDate"
                            label="管理日期"
                            label-width="140px"
                            style="margin-top: 15px; margin-bottom: 15px;"
                        >
                            <el-date-picker
                                v-model="formList.promoteDate"
                                class="popperClass"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                                @change="handleExaimneMonth"
                            >
                            </el-date-picker>
                        </el-form-item>
                     </el-col>
                </el-row>
                        
                <el-row>
                    <el-col>
                        <el-form-item 
                            prop="workState"
                            label-width="140px" 
                            label="在职状态" 
                            style="margin-top: 15px; margin-bottom: 15px;">
                            <crm-input
                                v-model="formList.workState"
                                :clearable="true"
                                disabled="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item 
                            prop="periodExplain" 
                            label-width="140px"
                            label="考核周期" 
                            style="margin-top: 15px; margin-bottom: 15px;">
                            <crm-select
                                v-model="formList.periodExplain"
                                :placeholder="periodExplain.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="periodExplain.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="startDt"
                            label-width="140px"
                            label="开始时间" style="margin-top: 15px; margin-bottom: 15px;"
                        >
                            <el-date-picker
                                v-model="formList.startDt"
                                class="popperClass"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                size="small"
                                :disabled="startDtDisabled"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item 
                            prop="exaimneNode" 
                            label-width="140px"
                            label="考核节点" 
                            style="margin-top: 15px; margin-bottom: 15px;">
                            <el-date-picker
                                v-model="formList.exaimneNode"
                                class="popperClass"
                                size="small"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                                @change="handleExaimneMonth"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item
                        prop="exaimneMonth" 
                            label-width="140px"
                            label="考核节点司龄月(管)" 
                            style="margin-top: 15px; margin-bottom: 15px;">
                            <crm-input
                                v-model="formList.exaimneMonth"
                                :clearable="true"
                                disabled="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="calcDepartment"
                            label="计入分公司" 
                            label-width="140px"
                            style="margin-top: 15px; margin-bottom: 15px;"
                        >
                        <crm-select
                                v-model="formList.calcDepartment"
                                :placeholder="calcDepartment.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="calcDepartment.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col>
                        <el-form-item
                            prop="calcNewDepartment"
                            label-width="140px"
                            label="计入净新增分公司" 
                            style="margin-top: 15px; margin-bottom: 15px;"
                        >
                        <crm-select
                                v-model="formList.calcNewDepartment"
                                :placeholder="calcNewDepartment.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="calcNewDepartment.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item
                            prop="remark"
                            label="备注"
                            style="margin-top: 25px; margin-bottom: 50px;"
                        >
                            <crm-input
                                v-model="formList.remark"
                                :clearable="true"
                                :style="{ width: '335px', height: '25px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 25%">
                            <el-button type="primary" @click="submitForm(ruleFormRef)">
                                确认
                            </el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 5%">
                            <el-button type="primary" @click="handleClose(ruleFormRef)"
                                >取消</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { fetchRes } from '@/utils/index'
    import { dataList } from '../data/labelData'
    import {
        performanceManageNodeConfUpdate,
        queryEdit
    } from '@/api/project/performanage/performanceManageNodeConf/performanceManageNodeConf'
    const { calcDepartment, calcNewDepartment, periodExplain } = dataList
    const loadingFlag = ref<boolean>(false)
    
    const startDtDisabled = ref<boolean>(false)

    class FormList {
        promoteDate = ''
        periodExplain = ''
        startDt = ''
        exaimneNode = ''
        calcDepartment = ''
        calcNewDepartment = ''
        userLevel = ''
        consName = ''
        orgName = ''
        regDt = ''
        regularDt = ''
        workState = ''
        exaimneMonth = ''
        remark = ''
        adjustManageServingMonth = ''
    }

    const formList = reactive<any>(new FormList())

    const ruleFormRef = ref<FormInstance>()

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                title: string
                type: string
                id: string
            }
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    title: '修改',
                    type: 'edit',
                    id: ''
                }
            },
        }
    )


    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()
    const rules = reactive<FormRules<FormList>>({
    })

    //提交方法
    const submitForm = async (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        await formEl.validate((valid, fields) => {
            if (valid) {
                console.log('submit!', fields)
                performanceManageNodeConfSubmit()
            } else {
                console.log('error submit!', fields)
            }
        })
    }
    //重置方法
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        formEl.resetFields()
    }

    /**
     * 产品费率新增方法
     * @param params
     */
    const performanceManageNodeConfSubmit = async () => {
        
        if((formList.periodExplain === '6' || formList.periodExplain === '7') 
            && !formList.startDt) {
                ElMessage({
                message: '观察期周期时开始时间必填',
                type: 'warning',
                duration: 2000
            })
            return
        }
        const requestParams = {
            id: props.transData.id,
            promoteDate: formList.promoteDate,
            periodExplain: formList.periodExplain,
            startDt: formList.startDt,
            exaimneNode: formList.exaimneNode,
            calcDepartment: formList.calcDepartment,
            calcNewDepartment: formList.calcNewDepartment,
            exaimneMonth: formList.exaimneMonth,
            remark: formList.remark
        }

        const res: any = await performanceManageNodeConfUpdate(requestParams)
        if (res.code === 'C030000') {
            ElMessage({
                type: 'success',
                message: res.description
            })
            loadingFlag.value = false
            dialogVisible.value = false
            return emit('callback')
        }
        if (res.code !== 'C030000') {
            ElMessage({
                type: 'error',
                message: res?.description || '请求失败'
            })
        }
    }

    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        dialogVisible.value = false
        // formEl.resetFields()
    }


    watch(() => formList.periodExplain, (newVal, oldVal) => {
        if(newVal === '6' || newVal === '7') {
            startDtDisabled.value = false
        }else{
            startDtDisabled.value = true
        }
    })
    

    const handleExaimneMonth = (value: string) => {
        changePerfomanceMonth(formList.promoteDate, formList.exaimneNode)
    }

    const changePerfomanceMonth = (promoteDate: string, exaimneNode: string) => {
        if(!promoteDate || !exaimneNode) {
            formList.exaimneMonth = null
            return 
        }
        const promoteYear = promoteDate.substring(0, 4)
        const promoteMonth = promoteDate.substring(4, 6)
        const exaimneNodeYear = exaimneNode.substring(0, 4)
        const exaimneNodeMonth = exaimneNode.substring(4, 6)
        let month = (Number(exaimneNodeYear) - Number(promoteYear)) * 12 + (Number(exaimneNodeMonth) - Number(promoteMonth))
        const day = promoteDate.substring(6, 8)
        if(promoteYear < '2023') {
            console.log('promoteYear', promoteYear)
            if(Number(day) <= 10) {
                month += 1
            }
        }else if(Number(day) <= 15) {
            month += 1
        }
        formList.exaimneMonth = month + Number(formList.adjustManageServingMonth)
    }
    /**
     * @description: 编辑初始化
     * @return {*}
     */
    const fetchList = () => {
        // 初始化
        fetchRes(queryEdit({ id: props.transData.id }), {
            successCB: (res: any) => {
                // 编辑初始化
                const {
                    promoteDate,
                    periodExplain,
                    startDt,
                    exaimneNode,
                    calcDepartment,
                    calcNewDepartment,
                    userLevel,
                    consName,
                    orgName,
                    regDt,
                    regularDt,
                    workState,
                    exaimneMonth,
                    remark,
                    adjustManageServingMonth
                } = res || {}
                formList.promoteDate = promoteDate
                formList.periodExplain = periodExplain
                formList.startDt = startDt
                formList.exaimneNode = exaimneNode
                formList.calcDepartment = calcDepartment
                formList.calcNewDepartment = calcNewDepartment
                formList.userLevel = userLevel
                formList.consName = consName
                formList.orgName = orgName
                formList.regDt = regDt
                formList.regularDt = regularDt
                formList.workState = workState
                formList.exaimneMonth = exaimneMonth
                formList.remark = remark
                formList.adjustManageServingMonth = adjustManageServingMonth
            },

            errorCB: () => {
                formList.value = new FormList()
            },
            catchCB: () => {
                formList.value = new FormList()
            },
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }
    onBeforeMount(() => {
        fetchList()
    })
</script>

<style lang="less" scoped>
    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
