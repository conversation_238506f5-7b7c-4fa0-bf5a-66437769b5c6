/*
 * @Description: 代码片段配置项
 * @Author: chaohui.wu
 * @Date: 2024-06-19 14:55:01
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-12 15:37:13
 * @FilePath: /ds-report-web/src/vSnippet/snppet.js
 *
 */

/* 命名规则：
 * vue3组基础模版 v3-ts-xxx
 * 基础组件：hb-xxx-xxx  hb[howbuy-baseModule]
 * 模版组件：ht-xxx-xxx ht[howbuy-template]
 * 方法类: hu-xxx-xxx hu[howbuy-utils]
 * 样式类: hs-xxx-xxx hs[howbuy-style]
 * 埋点类：he-xxx-xxx he[howbuy-event]
 */
// export interface SnppetItem {
//     scope: 'hb' | 'ht' | 'hu' | 'hs' | 'he' | string
//     prefix: string // 前缀,快捷方式
//     desTitle: string // 描述标题提示
//     des: string // 片段描述
//     bodyStr: string // 代码片段
// }

/**
 * @description: vue3 常用方法
 * @return {*}
 */
const v3SnppetList = [
    {
        scope: 'ht',
        prefix: 'v3-props',
        desTitle: 'props default',
        des: 'ts props default',
        bodyStr: `
            const props = withDefaults(
                defineProps<{
                    type: string
                }>(),
                {
                    type: 'view'
                }
            )
        `
    },
    {
        scope: 'ht',
        prefix: 'v3-watch',
        desTitle: 'ts watch',
        des: 'ts watch',
        bodyStr: `
            watch(
                () => {},
                (newVal, oldVal) => {
                    
                },
                {
                    deep: true,
                    immediate: true
                }
            )
        `
    },
    {
        scope: 'ht',
        prefix: 'v3-hooks',
        desTitle: '自定义hooks模版',
        des: '自定义hooks模版',
        bodyStr: `
            // utils函数
            import { $0 } from '@/utils/index'
            // 自定义hooks方法
            export function use$1({ props }: any) {
                // 导出方法
                return {
                    $2
                }
            }
        `
    },
    {
        scope: 'ht',
        prefix: 'v3-use-hooks',
        desTitle: '引用自定义hooks方法',
        des: '引用自定义hooks方法',
        bodyStr: `
            // hooks地址
            import { useDemoHooks } from './hooks/useDemoHooks'
            const {
                demoMetds
            } = useDemoHooks({ props })
        `
    }
]

/**
 * @description: 基础组件： hb-xxx-xxx  hb[howbuy-baseModule]
 * @return {*}
 */
const hbSnppetList = [
    {
        scope: 'hb',
        prefix: 'hb-table',
        desTitle: '基于el-table [module base-table]',
        des: 'module base-table table基础组件',
        bodyStr: `
            <BaseTable
                :columns="[]"
                :data="[]"
                style="width: 100%"
                :no-select="true"
                :stripe="true"
                height="100%"
                :border="true"
                :no-index="false"
                :show-operation="false"
                operation-width="200"
                @select="$0"
                @selectionChange="$1"
            >
            </BaseTable>
        `
    },
    {
        scope: 'hb',
        prefix: 'hb-radio',
        desTitle: '基于el-radio [module crm-radio]',
        des: 'module crm-radio radio基础组件',
        bodyStr: `
            <crm-radio
                v-model="$1"
                :disabled="false"
                width="180px"
                :option-list="[
                    { label: '否', value: '0' },
                    { label: '是', value: '1' }
                ]"
                @change="$2"
            />
        `
    },
    {
        scope: 'hb',
        prefix: 'hb-dialog',
        desTitle: '基于el-dialog [module crm-dialog]',
        des: 'module crm-dialog dialog基础组件',
        bodyStr: `
            <crm-dialog
                v-model="dialogVisible"
                width="770px"
                title="添加产品"
                :slot-list="['default', 'footer']"
                :before-close="handleClose" 
                :close-on-click-modal="false" 
                @keyup.enter.stop="confirmFn"> 
                <template #footer> 
                    <div>
                        <crm-button plain size="small" @click="handleClose">关 闭</crm-button> 
                        <crm-button size="small" @click="confirmFn">确 认</crm-button>
                    </div>
                </template>
            </crm-dialog>
        `
    },
    {
        scope: 'hb',
        prefix: 'hb-date-range',
        desTitle: '基于el-date-picker [moduleBase DateRange]',
        des: 'moduleBase DateRange 自定义样式选择日期区间',
        bodyStr: `
            <date-range v-model="$0"
                show-format="YYYY-MM-DD"
                placeholder="日期描述占位"
                style-type="fund"
            />
        `
    },
    {
        scope: 'hb',
        prefix: 'hb-date-picker',
        desTitle: '基于el-date-picker [moduleBase CrmDatePicker]',
        des: 'moduleBase crmDatePicker 基础组件案例',
        bodyStr: `
            <crm-date-picker
                v-model="$0"placeholder="请选择计算起始日"
                show-format="YYYY-MM-DD"
                value-format="YYYYMMDD"
                class-name="w180"
                :disabled="false"
                :limit-less-than-current="false"
                :disabled-date="{startDate: $1, endDate: $2}"
            />
        `
    },
    {
        scope: 'hb',
        prefix: 'hb-button',
        desTitle: '基于el-button [moduleBase BaseButton]',
        des: 'moduleBase BaseButton 基础组件案例',
        bodyStr: `
            <BaseButton
                plain
                :text="true"
                :underline="false"
                :link="false"
                :radius="true"
                :bold="false"
                round
                custom-class="btn-primary btn--white"
                @click=""
            >
            </BaseButton>
        `
    },
    {
        scope: 'hb',
        prefix: 'hb-checkbox',
        desTitle: '基于el-checkbox [moduleBase crmCheckbox]',
        des: 'moduleBase crmCheckbox 基础组件多选案例',
        bodyStr: `
            <LabelItem label="基础选项" col-type="w100">
                <CrmCheckbox 
                    v-model="$1"
                    :option-list="[]"
                    value-format="value"
                    @change="$2">
                </CrmCheckbox>
            </LabelItem>
        `
    },
    {
        scope: 'hb',
        prefix: 'hb-svg',
        desTitle: '基于vite-plugin-svg-icons',
        des: 'hb-svg svg组件',
        bodyStr: `
            <svg-icons name=" "></svg-icons>
        `
    },
    {
        scope: 'hb',
        prefix: 'hb-input',
        desTitle: '基于el-input',
        des: 'moduleBase el-input',
        bodyStr: `
            <crm-input
                v-model=""
                :disabled=""
                placeholder="请输入购买金额"
                :formatter=""
                :parser=""
                :clearable="true"
                class-name="w180"
                onkeypress=""
                @blur=""
            />
        `
    }
]

/**
 * @description: 方法类: hu-xxx-xxx hu[howbuy-utils]
 * @return {*}
 */
const huSnppetList = [
    {
        scope: 'hu',
        prefix: 'hu-fetchRes',
        desTitle: '基于utils [utils resBase]',
        des: 'utils [utils resBase]',
        bodyStr: `
            fetchRes(initData(params), {
                successCB: (res: any) => {$1},
                errorCB: () => {},
                catchCB: () => {$2},
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })`
    },
    {
        scope: 'hu',
        prefix: 'hu-vmodel',
        desTitle: '基于vueuse [utils useVModel]',
        des: 'vueuse [utils useVModel]',
        bodyStr: `
            import { useVModel } from '@vueuse/core'
            const props = withDefaults( 
                defineProps<{
                    modelValue?: boolean
                }>(),{
                    modelValue: false
                })
            const emit = defineEmits<{ (e: 'update:modelValue', modelValue: boolean): void }>()
            const dialogVisible = useVModel(props, 'modelValue', emit)`
    },
    {
        scope: 'hu',
        prefix: 'hu-message',
        desTitle: '基于el-message',
        des: 'el-message [utils message]',
        bodyStr: `
            message({
                type: '',
                message: ''
            })`
    },
]

/**
 * @description: 模版组件：ht-xxx-xxx ht[howbuy-template]
 * @return {*}
 */
const htSnppetList = [
    {
        scope: 'ht',
        prefix: 'ht-dialog-page',
        desTitle: '弹框页面自定义模版',
        des: '弹框页面自定义模版',
        bodyStr: `
            <template>
                <crm-dialog
                    v-model="dialogVisible"
                    width="770px"
                    title="提示"
                    :slot-list="['default', 'footer']"
                    boxpadding="30px 20px 20px"
                    :align-center="true"
                    :before-close="handleClose"
                    :close-on-click-modal="false"
                    @keyup.enter.stop="handleConfirm"
                >
                    <template #default>
                        <div class="content-box">
                            <p class="txt-default">
                                客户当前剩余可投资现金<b>{{ dataTpl?.remainInvestAbleAmt }}</b
                                >万元，增持金额已超出客户剩余可投资现金，确认调整？
                            </p>
                            <p class="txt-default txt-red">
                                *确认调整后，客户累计可投资金额，将上调为<b>{{ dataTpl?.investAbleAmt }}</b
                                >万元。
                            </p>
                        </div>
                    </template>
                    <template #footer>
                        <div>
                            <crm-button plain size="small" @click="handleClose">关 闭</crm-button>
                            <crm-button size="small" @click="handleConfirm">确 认</crm-button>
                        </div>
                    </template>
                </crm-dialog>
            </template>

            <script lang="ts" setup>
                import { fetchRes, NPNumRound } from '@/utils/index'
                import { useVModel } from '@vueuse/core'

                const props = withDefaults(
                    defineProps<{
                        modelValue?: boolean
                        transData?: any
                    }>(),
                    {
                        modelValue: false,
                        transData: () => {
                            return {
                                remainInvestAbleAmt: '0.00',
                                investAbleAmt: '0.00'
                            }
                        }
                    }
                )

                const emit = defineEmits<{
                    (e: 'update:modelValue', modelValue: boolean): void
                    (e: 'handleConfirm', widthoutCheck: string): any
                }>()

                const dialogVisible = useVModel(props, 'modelValue', emit)

                /**
                 * @description: 关闭
                 * @param {*} formEl
                 * @return {*}
                 */
                const handleClose = () => {
                    dialogVisible.value = false
                }

                /**
                 * @description: 确认
                 * @return {*}
                 */
                const handleConfirm = () => {
                    // 确认
                    dialogVisible.value = false
                    emit('handleConfirm', '1')
                }

                const dataTpl = computed(() => {
                    const { remainInvestAbleAmt, investAbleAmt } = props.transData || {}
                    return {
                        remainInvestAbleAmt: Number(remainInvestAbleAmt)
                            ? NPNumRound(Number(remainInvestAbleAmt), 2)
                            : '0.00',
                        investAbleAmt: Number(investAbleAmt) ? NPNumRound(Number(investAbleAmt), 2) : '0.00'
                    }
                })
            </script>

            <style lang="less" scoped>
                .txt-default {
                    .font-default();

                    margin-bottom: 8px;
                    font-size: 13px;
                    font-weight: normal;
                    line-height: 1.5;
                    color: #2a3050;

                    &.txt-red {
                        color: #c82d30;
                    }
                }
            </style>
        `
    }
]

/**
 * @description: 样式类: hs-xxx-xxx hs[howbuy-style]
 * @return {*}
 */
const hsSnppetList = []

/**
 * @description: 埋点类：he-xxx-xxx he[howbuy-event]
 * @return {*}
 */
const heSnppetList = []

module.exports.snppetList = [
    ...v3SnppetList,
    ...huSnppetList,
    ...hbSnppetList,
    ...htSnppetList,
    ...hsSnppetList,
    ...heSnppetList
]
