import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '../../../../mock.js'
import {
    TwoLevelConfProductRateParam,
    TwoLevelConfProductRateAddOrUpdateParam,
    BatchDeleteId,
    AduitProduct
} from './type/apiReqType.js'
/**
 * @description: 固收产品费率配置查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const fixIncomeConfProductRateQuery = (params: TwoLevelConfProductRateParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/fixIncomeConfProductRate/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 固收产品费率配置导出接口
 * @return {*}
 */
export const fixIncomeConfProductRateExport = (params: TwoLevelConfProductRateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/fixIncomeConfProductRate/export',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 固收产品费率配置新增接口
 * @return {*}
 */
export const fixIncomeConfProductRateInsert = (params: TwoLevelConfProductRateAddOrUpdateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/fixIncomeConfProductRate/insert',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 固收产品费率配置修改接口
 * @return {*}
 */
export const fixIncomeConfProductRateUpdate = (params: TwoLevelConfProductRateAddOrUpdateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/fixIncomeConfProductRate/update',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 固收产品费率配置删除接口
 * @return {*}
 */
export const fixIncomeConfProductRateDelete = (params: BatchDeleteId) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/fixIncomeConfProductRate/delete',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 固收产品费率配置复核接口
 * @return {*}
 */
export const fixIncomeConfProductRateReview = (params: AduitProduct) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/fixIncomeConfProductRate/review',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核-init
 * @param {object} params
 * @return {*}
 */
export const getAuth = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/fixIncomeConfProductRate/getAuth',
            method: 'post'
        })
    )
}
