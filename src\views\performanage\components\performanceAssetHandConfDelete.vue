<!--
 * @Description:删除弹框
 * @Author: chaohui.wu
 * @Date: 2023-03-24 21:07:35
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-27 19:14:16
 * @FilePath: /crm-template/src/views/components/dialogCommon/adjustAmt/adjustAmtIndex.vue
 * @ 当前未解耦，有时间解耦后可提取到dialogCommomn中
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="400px"
        height="500px"
        :border="true"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <el-form
                v-if="isAssetHandConfAdd"
                ref="ruleFormRef"
                :model="formList"
                :rules="rules"
                label-width="100px"
                status-icon
            >
                <el-row>
                    <el-col :span="24" style=" height: '100px';text-align: center">
                        <span>&nbsp; &nbsp;&nbsp;确认删除数据？</span>
                        <el-form-item
                            prop="delreason"
                            label="删除原因"
                            style=" height: '100px';margin: 10px 0 30px"
                        >
                            <crm-input
                                v-model="formList.delreason"
                                :clearable="true"
                                :style="{ width: '300px', height: '25px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="24">
                        
                    </el-col> -->
                </el-row>
            </el-form>
        </div>
        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="submitForm(ruleFormRef)"
                    >确认</crm-button
                >
                <crm-button size="small" :radius="true" @click="handleClose(ruleFormRef)"
                    >取 消</crm-button
                >
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { fetchRes } from '@/utils/index'
    import { dataList } from './data/labelData'
    import { performanceAssetHandConfDelete } from '@/api/project/performanage/performanceAssetHandConf/performanceAssetHandConf'
    const { busitype, firstSource, conslevel } = dataList
    const loadingFlag = ref<boolean>(false)

    class FormList {
        delreason = ''
    }

    const formList = reactive<any>(new FormList())

    const ruleFormRef = ref<FormInstance>()

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                title: string
                type: string
                id: string
            }
            isAssetHandConfAdd?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    title: '删除',
                    type: 'delete',
                    id: ''
                }
            },
            isAssetHandConfAdd: false
        }
    )

    // 弹窗标题配置
    const title = computed(() => {
        if (props.isAssetHandConfAdd) {
            return '删除'
        }
    })
    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })
    const custList = ref<object[]>()

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()
    const rules = reactive<FormRules<FormList>>({
        delreason: [{ required: true, message: '请输入删除原因', trigger: 'blur' }]
    })

    //提交方法
    const submitForm = async (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        await formEl.validate((valid, fields) => {
            if (valid) {
                performanceAssetHandConfSubmit()
            } else {
                console.log('error submit!', fields)
            }
        })
    }
    //重置方法
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        formEl.resetFields()
    }

    /**
     * 产品费率新增方法
     * @param params
     */
    function performanceAssetHandConfSubmit() {
        const requestParams = {
            id: props.transData.id,
            delreason: formList.delreason
        }
        fetchRes(performanceAssetHandConfDelete(requestParams), {
            successCB: (res: any) => {
                loadingFlag.value = false
                dialogVisible.value = false
                return emit('callback')
            },
            errorCB: (res: any) => {
                loadingFlag.value = false
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => {
                loadingFlag.value = false
            },
            successTxt: '操作成功',
            failTxt: '',
            fetchKey: ''
        })
    }
    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        dialogVisible.value = false
    }
</script>

<style lang="less" scoped>
    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
