<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 投顾选择 -->
                <label-item label="投顾">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <!-- 完成日期 -->
                <label-item :label="completeDate.label">
                    <date-range
                        v-model="queryForm.completeDate"
                        show-format="YYYY-MM-DD"
                        :placeholder="completeDate.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 是否完成IPS面访 -->
                <label-item :label="isIpsVisitComplete.label">
                    <crm-select
                        v-model="queryForm.isIpsVisitComplete"
                        :placeholder="isIpsVisitComplete.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="isIpsVisitComplete.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
            </template>

            <template #operationBtns>
                <Text type="info" size="small" style="padding-right: 10px; font-size: 14px; font-weight: bold;">
                    最后更新时间：
                    <span class="last-update-time">{{ lastUpdateTimeRef }}</span>
                    <span v-if="dataDeadlineRef">, 数据截止日期: {{ dataDeadlineRef }}</span>
                </Text>
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="exportHandle"
                    >导出</crm-button
                >
                <crm-button
                    size="small"
                    :radius="true"
                    :icon="InfoFilled"
                    plain
                    @click="handleExplain"
                    >说明</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="ipsVisitTableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-index="false"
                    :no-select="true"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    :pops="{ selectFixed: 'left' }"
                    @sortChange="handleSortChange"
                >
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination 
                    :page="pageObj" 
                    :total="pageObj.total" 
                    :page-sizes="[100, 200, 500, 1000, 2000]"
                    @change="handleCurrentChange" 
                />
            </template>
        </table-wrapper>

        <!-- 说明弹窗 -->
        <ExplainIpsVisit v-if="explainDialogVisible" v-model="explainDialogVisible"></ExplainIpsVisit>
    </div>
</template>

<script lang="ts" setup>
    import { ref, reactive, onMounted, nextTick, shallowRef, watch } from 'vue'
    import { storeToRefs } from 'pinia'
    import { Download, InfoFilled } from '@element-plus/icons-vue'
    import { fetchRes } from '@/utils'
    import { dataList } from './data/labelData'
    import { ipsVisitTableColumn } from './data/tableData'
    import {
        queryIpsVisitList,
        exportIpsVisitList,
        getIpsVisitInitData
    } from '@/api/project/report/kpi2025/ipsVisit/ipsVisit'
    import { SortOrderCumstom } from '@/type/index'
    import ExplainIpsVisit from '../components/ExplainIpsVisit.vue'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'

    const {
        completeDate,
        isIpsVisitComplete
    } = dataList

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const listLoading = ref<boolean>(false)
    const exportShow = ref<boolean>(true)
    const lastUpdateTimeRef = ref<string>('')
    const dataDeadlineRef = ref<string>('')
    const explainDialogVisible = ref<boolean>(false)
    const consStatus = ref<string>('1') //不包含离职人员
    const module = ref<string>('071702') // 模块编码，参考PiggyHkDeal

    // 排序值映射
    const transOrder = (key: string) => {
        switch (key) {
            case 'ascending':
                return 'ASC'
            case 'descending':
                return 'DESC'
            default:
                return 'DESC'
        }
    }

    interface SortParams {
        order: SortOrderCumstom
        prop: string
    }

    // 排序联动
    const handleSortChange = (val: SortParams) => {
        queryForm.order = val.order
        queryForm.sort = val.prop
        nextTick(() => {
            queryList()
        })
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        completeDateStart = ''
        completeDateEnd = ''
        isIpsVisitComplete = ''
        completeDate = {
            startDate: '',
            endDate: ''
        }
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        sort = ''
        order = 'descending'
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 100
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    //查询
    const queryList = async () => {
        listLoading.value = true
        const params = {
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            completeDateStart: queryForm.completeDate.startDate,
            completeDateEnd: queryForm.completeDate.endDate,
            isIpsVisitComplete: queryForm.isIpsVisitComplete,
            page: pageObj.value.page,
            rows: pageObj.value.size,
            sort: queryForm.sort,
            order: transOrder(queryForm.order)
        }
        fetchRes(queryIpsVisitList(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                tableData.value = rows
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    const handleExplain = () => {
        explainDialogVisible.value = true
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const params = {
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            completeDateStart: queryForm.completeDate.startDate,
            completeDateEnd: queryForm.completeDate.endDate,
            isIpsVisitComplete: queryForm.isIpsVisitComplete,
            page: pageObj.value.page,
            rows: pageObj.value.size,
            sort: queryForm.sort,
            order: transOrder(queryForm.order)
        }
        const res: any = await exportIpsVisitList(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || 'IPS面访记录导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }

    /**
     * @description: 初始化数据
     * @return {*}
     */
    const initData = async () => {
        fetchRes(getIpsVisitInitData(), {
            successCB: (resObj: any) => {
                const { lastUpdateTime, dataDeadline } = resObj
                lastUpdateTimeRef.value = lastUpdateTime
                dataDeadlineRef.value = dataDeadline
            },
            errorCB: () => {
                console.error('初始化数据失败')
            },
            catchCB: () => {
                console.error('初始化数据异常')
            },
            successTxt: '',
            failTxt: '初始化失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [orgCodeDefault, consCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.orgvalue.orgCode = orgCodeDefault.value
                queryForm.orgvalue.consCode = consCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    /**
     * @description 页面挂载的时候就获取初始化数据
     */
    onMounted(() => {
        console.log('IpsVisit onMounted')
        initData()
        fetchConsOrgList('', module.value, '1')
    })
</script>
<style lang="less" scoped></style>
