<!--
 * @Description: 基础卡片页面
 * @Author: chaohui.wu
 * @Date: 2023-04-04 13:19:49
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-18 10:48:30
 * @FilePath: /crm-asset-web/src/components/moduleBase/BaseCard.vue
 *  
-->

<template>
    <section v-if="moduleType === 'base'">
        <el-card class="box-card-base" :class="baseModuleObj.className" v-bind="$attrs">
            <slot></slot>
        </el-card>
    </section>
    <section v-if="moduleType === 'coupon'" class="coupon-box" :class="baseModuleObj.className">
        <el-card class="box-card-coupon">
            <slot></slot>
        </el-card>
        <el-card v-if="showButtom" class="box-card-coupon coupon-bottom">
            <slot name="couponBottom"></slot>
        </el-card>
    </section>
</template>

<script setup lang="ts">
    const props = withDefaults(
        defineProps<{
            moduleType?: string
            showButtom?: boolean
            className?: string
        }>(),
        {
            moduleType: 'base',
            showButtom: false,
            className: ''
        }
    )

    const baseModuleObj = computed(() => {
        return {
            className: props.className
        }
    })
</script>
<style lang="less" scoped>
    :deep(.el-card) {
        &.is-always-shadow {
            // box-shadow: 0px 0px 6px rgba(221, 221, 221, 0.12);
            box-shadow: 0 0 12px rgba(0, 0, 0, 0.06);
        }

        &.box-card-base {
            --el-card-border-radius: 8px;
            --el-card-padding: 15px 20px 30px 20px;

            &.module1 {
                --el-card-padding: 12px 20px;
            }

            &.module2 {
                --el-card-padding: 22px 20px 26px 20px;
            }

            &.module3 {
                --el-card-padding: 10px 20px 20px 20px;
                --el-card-margin: 0 0 15px 0;
            }

            &.module4 {
                --el-card-padding: 10px 20px 14px 20px;
            }
        }

        &.box-card-coupon {
            --el-card-padding: 12px 20px 10px 20px;
            --el-card-border-radius: 8px;

            border: none;
            box-shadow: none;

            &.coupon-bottom {
                margin-top: 1px;
            }
        }

        .el-button {
            &--small {
                --el-button-size: 28px;
            }
        }
    }

    .coupon-box {
        margin-bottom: 10px;
    }
</style>
