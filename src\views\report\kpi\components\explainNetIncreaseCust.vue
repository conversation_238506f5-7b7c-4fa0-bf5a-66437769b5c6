<!--
 * @Description: 统计说明
 * @Author: gang.zou
 * @Date: 2024-04-12 19:46:04
 * @LastEditors: gang.zou
 * @LastEditTime: 2024-04-12 19:46:04
 * @FilePath: /src/views/report/kpi/components/explainNetIncreaseCust.vue
 *  
-->
<template>
    <crm-dialog
        width="824px"
        title="统计说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <template #default>
            <div style="height: 300px">
                <b>统计2024年发生交易、香港开户的客户是否满足2024年KPI要求。</b><br />
                <span
                    >①购买金额≥40万笔数：股权、权益或债券B购买金额≥40万以上(股权、权益或债券B：单笔触及过即可)，具体以2024年KPI文件为准</span
                ><br />
                <span
                    >②小集合存续≥100万：小集合购买折后存续D100万（存量*存续D系数，不需折算天数）且统计时点需存续，具体以2024年KPI文件为准</span
                ><br />
                <span>③HK开户：满足a或b条件，具体以2024年KPI文件为准</span><br />
                <span>a、新开好买香港账户且2024年完成1笔不低于1万美金的交易（入金不含）</span><br />
                <span>b、新开好买香港账户且2024年完成香港专业投资者认证</span>
            </div>
        </template>

        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">确定</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { useVisible } from '@/views/common/scripts/useVisible'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()
    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })
</script>

<style lang="less" scoped>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 8px;
        text-align: left;
        border: 1px solid black;
    }

    th {
        background-color: #f2f2f2;
    }

    .middle-content {
        padding: 10px 0;
    }
</style>
