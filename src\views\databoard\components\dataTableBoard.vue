<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="board-s">
        <!-- header -->
        <div class="header-s">
            <span style="margin: 5px 0 0 5px">{{ title }}</span>
            <span style="float: right">
                <el-button
                    v-show="beginIndex > 0"
                    type="text"
                    icon="el-icon-d-arrow-left"
                    size="medium"
                    @click="scrollToLeft"
                ></el-button>
                <el-button
                    v-show="endIndex != length"
                    type="text"
                    icon="el-icon-d-arrow-right"
                    size="medium"
                    @click="scrollToRight"
                ></el-button>
            </span>
        </div>
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'ScrollBoard',
        props: {
            title: {
                type: String,
                default: 'title'
            },
            // eslint-disable-next-line vue/require-default-prop
            boardData: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            grid11Data: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            grid12Data: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            grid13Data: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            grid14Data: {
                type: Array
            }
        },

        data() {
            return {
                beginIndex: 0,
                endIndex: 0,
                length: 0,
                scrollBoardData: []
            }
        },
        watch: {
            boardData: {
                handler(val, oldVal) {
                    this.initScroll()
                },
                deep: true //true 深度监听
            }
        },
        mounted() {
            this.initScroll()
        },
        methods: {
            initScroll() {
                this.beginIndex = 0
                this.endIndex = 4
                this.length = this.boardData.length
                this.scrollBoardData = this.boardData.slice(this.beginIndex, this.endIndex)
            },
            top3(i) {
                return 'top3' + i
            },
            scrollToRight() {
                if (this.endIndex < this.length) {
                    this.beginIndex = this.beginIndex + 1
                    this.endIndex = this.endIndex + 1
                    this.scrollBoardData = this.boardData.slice(this.beginIndex, this.endIndex)
                }
            },
            scrollToLeft() {
                if (this.beginIndex > 0) {
                    this.beginIndex = this.beginIndex - 1
                    this.endIndex = this.endIndex - 1
                    this.scrollBoardData = this.boardData.slice(this.beginIndex, this.endIndex)
                }
            },
            sendMsgToParent(item) {
                // eslint-disable-next-line vue/require-explicit-emits
                this.$emit('ievent', item)
                console.log('88888888888888')
                console.log(item)
            }
        }
    })
</script>

<style scoped>
    .board-s {
        height: 95%;
        margin: 5px;
        border: 1px solid cornflowerblue;
    }

    .box-card {
        display: block;
        height: 70%;
        margin: 2px;
        font-size: 14px;
        text-align: center;
        background: #bce8f1;
    }

    .header-s {
        height: 20%;
        padding: 2px;
        margin: 2px;
        font-size: 16px;
        font-weight: bold;
        background-color: #d9edf7;
    }

    .ahover:hover {
        color: red;
    }
</style>
