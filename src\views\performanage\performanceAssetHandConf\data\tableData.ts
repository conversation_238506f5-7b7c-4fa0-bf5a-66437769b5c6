/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const performanceAssetHandConfTableColumn: TableColumnItem[] = [
    {
        key: 'auditState',
        label: '审核状态',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'createdate',
        label: '录入时间',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'busitype',
        label: '业务类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'conslevel',
        label: '层级',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consname',
        label: '管理层/投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'workState',
        label: '在职状态',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u1name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'conscustno',
        label: '投顾客户号',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custname',
        label: '客户名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundname',
        label: '产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'scale',
        label: '规模(万)',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'stockfeed',
        label: '产品存续D系数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'firstSource',
        label: '第一来源',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'foldcoeff',
        label: '客户折算系数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'managecoeff',
        label: '管理系数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'otherrateStr',
        label: '调整比例',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetcalcstartdt',
        label: '存D计算起始月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetcalcenddt',
        label: '存D计算结束月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'remark',
        label: '备注',
        width: 120,
        formatter: formatTableValue
    }
]
export const performanceAssetHandConfLCSTableColumn: TableColumnItem[] = [
    {
        key: 'auditState',
        label: '审核状态',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'busitype',
        label: '业务类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'conslevel',
        label: '层级',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consname',
        label: '投顾姓名',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'workState',
        label: '在职状态',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u1name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'conscustno',
        label: '投顾客户号',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custname',
        label: '客户名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundname',
        label: '产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'scale',
        label: '规模(万)',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'stockfeed',
        label: '产品存续D系数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'firstSource',
        label: '第一来源',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'foldcoeff',
        label: '客户折算系数',
        width: 120,
        formatter: formatTableValue
    },

    {
        key: 'otherrateStr',
        label: '调整比例',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt1',
        label: '01月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt2',
        label: '02月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt3',
        label: '03月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt4',
        label: '04月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt5',
        label: '05月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt6',
        label: '06月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt7',
        label: '07月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt8',
        label: '08月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt9',
        label: '09月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt10',
        label: '10月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt11',
        label: '11月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt12',
        label: '12月',
        width: 120,
        formatter: formatTableValue
    }
]
export const performanceAssetHandConfGLCTableColumn: TableColumnItem[] = [
    {
        key: 'auditState',
        label: '审核状态',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'busitype',
        label: '业务类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'conslevel',
        label: '层级',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consname',
        label: '管理层姓名',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'workState',
        label: '在职状态',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u1name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'conscustno',
        label: '投顾客户号',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custname',
        label: '客户名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundname',
        label: '产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'scale',
        label: '规模(万)',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'stockfeed',
        label: '产品存续D系数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'firstSource',
        label: '第一来源',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'managecoeff',
        label: '管理系数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'otherrateStr',
        label: '调整比例',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt1',
        label: '01月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt2',
        label: '02月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt3',
        label: '03月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt4',
        label: '04月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt5',
        label: '05月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt6',
        label: '06月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt7',
        label: '07月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt8',
        label: '08月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt9',
        label: '09月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt10',
        label: '10月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt11',
        label: '11月',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDt12',
        label: '12月',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
