/*
 * @Description: 固定路由配置
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-14 10:53:07
 * @FilePath: /ds-report-web/src/routers/views/index.ts
 *
 */
import { AddRouteRecordRaw } from '../index'
import Layout from '@/views/main.vue'

export default [
    {
        path: '/',
        component: Layout,
        name: 'main',
        meta: {
            title: '容器'
        },
        redirect: { path: 'productFeeRateConfigTest' },
        children: [
            {
                path: '/productFeeRateConfigTest',
                name: 'productFeeRateConfigTest',
                meta: {
                    title: '产品费率配置测试页面'
                },
                component: () => import('@/views/test/productFeeRateConfigTest.vue')
            },
            {
                path: '/equityFOFMasterFun',
                name: 'equityFOFMasterFun',
                meta: {
                    title: '权益类FOF母基金交易记录'
                },
                component: () =>
                    import('@/views/report/finance/equityFOFMasterFun/equityFOFMasterFun.vue')
            },
            {
                path: '/stockFOFMasterFun',
                name: 'stockFOFMasterFun',
                meta: {
                    title: '股权类FOF母基金交易记录'
                },
                component: () => import('@/views/report/finance/stockMasterFun/stockMasterFun.vue')
            },

            {
                path: '/twoLevelConfProductRate',
                name: 'twoLevelConfProductRate',
                meta: {
                    title: '二级产品费率配置'
                },
                component: () =>
                    import(
                        '@/views/report/finance/twoLevelConfProductRate/twoLevelConfProductRate.vue'
                    )
            },
            {
                path: '/confStockRateInfo',
                name: 'confStockRateInfo',
                meta: {
                    title: '股权产品费率配置'
                },
                component: () =>
                    import('@/views/report/finance/confStockRateInfo/confStockRateInfo.vue')
            },
            {
                path: '/fixIncomeConfProductRate',
                name: 'fixIncomeConfProductRate',
                meta: {
                    title: '固收产品费率配置'
                },
                component: () =>
                    import(
                        '@/views/report/finance/fixIncomeConfProductRate/fixIncomeConfProductRate.vue'
                    )
            },
            {
                path: '/fofProductFeeRateConfig',
                name: 'fofProductFeeRateConfig',
                meta: {
                    title: 'FOF产品费率配置'
                },
                component: () =>
                    import(
                        '@/views/report/finance/fofProductFeeRateConfig/fofProductFeeRateConfig.vue'
                    )
            },
            {
                path: '/secondaryProductFeeRateConfig',
                name: 'secondaryProductFeeRateConfig',
                meta: {
                    title: '二级产品费率配置'
                },
                component: () =>
                    import(
                        '@/views/report/finance/secondaryProductFeeRateConfig/secondaryProductFeeRateConfig.vue'
                    )
            },
            {
                path: '/overseasRmbProductFeeRateConfig',
                name: 'overseasRmbProductFeeRateConfig',
                meta: {
                    title: '海外人民币产品费率配置'
                },
                component: () =>
                    import(
                        '@/views/report/finance/overseasRmbProductFeeRateConfig/overseasRmbProductFeeRateConfig.vue'
                    )
            },
            {
                path: '/fixedIncomeProductFeeRateConfig',
                name: 'fixedIncomeProductFeeRateConfig',
                meta: {
                    title: '固收产品费率配置'
                },
                component: () =>
                    import(
                        '@/views/report/finance/fixedIncomeProductFeeRateConfig/fixedIncomeProductFeeRateConfig.vue'
                    )
            },
            {
                path: '/performanceAssetHandConf',
                name: 'performanceAssetHandConf',
                meta: {
                    title: '手工存续D配置'
                },
                component: () =>
                    import(
                        '@/views/performanage/performanceAssetHandConf/performanceAssetHandConf.vue'
                    )
            },
            {
                path: '/performanceAssetHandConflcs',
                name: 'performanceAssetHandConflcs',
                meta: {
                    title: '手工存续D理财师'
                },
                component: () =>
                    import(
                        '@/views/performanage/performanceAssetHandConf/performanceAssetHandConfLcs.vue'
                    )
            },
            {
                path: '/performanceAssetHandConfglc',
                name: 'performanceAssetHandConfglc',
                meta: {
                    title: '手工存续D管理层'
                },
                component: () =>
                    import(
                        '@/views/performanage/performanceAssetHandConf/performanceAssetHandConfglc.vue'
                    )
            },
            {
                path: '/resoCustAssign',
                name: 'resoCustAssign',
                meta: {
                    title: '公司资源分配客户明细'
                },
                component: () =>
                    import('@/views/report/custManage/resoCustAssign/resoCustAssign.vue')
            },
            {
                path: '/resoCustAssignConvRate',
                name: 'resoCustAssignConvRate',
                meta: {
                    title: '公司分配资源客户成交率'
                },
                component: () =>
                    import(
                        '@/views/report/custManage/resoCustAssignConvRate/resoCustAssignConvRate.vue'
                    )
            },
            {
                path: '/tradeGdHk',
                name: 'tradeGdHk',
                meta: {
                    title: '香港产品月度交易'
                },
                component: () => import('@/views/report/saleManage/tradeGdHk/tradeGdHk.vue')
            },
            {
                path: '/assetGdHk',
                name: 'assetGdHk',
                meta: {
                    title: '香港产品月度存量'
                },
                component: () => import('@/views/report/saleManage/assetGdHk/assetGdHk.vue')
            },
            {
                path: '/resoCustAssignConvRate',
                name: 'resoCustAssignConvRate',
                meta: {
                    title: '公司分配资源客户成交率'
                },
                component: () =>
                    import(
                        '@/views/report/custManage/resoCustAssignConvRate/resoCustAssignConvRate.vue'
                    )
            },
            {
                path: '/databoard',
                name: 'databoard',
                meta: {
                    title: '数据看板'
                },
                component: () => import('@/views/databoard/databoard.vue')
            },
            {
                path: '/assetrepdownload',
                name: 'assetRepDownload',
                meta: {
                    title: '资配覆盖率'
                },
                component: () => import('@/views/report/kpi/assetRepDownload/assetRepDownload.vue')
            },
            {
                path: '/netincreaseoverseas',
                name: 'netIncreaseOverseas',
                meta: {
                    title: '净申购（海外+非A）'
                },
                component: () =>
                    import('@/views/report/kpi/netIncreaseOverseas/netIncreaseOverseas.vue')
            },
            {
                path: '/netincreaseoverseasdetail',
                name: 'netIncreaseOverseasDetail',
                meta: {
                    title: '净申购（海外+非A）明细'
                },
                component: () =>
                    import('@/views/report/kpi/netIncreaseOverseas/netIncreaseOverseasDetail.vue')
            },
            {
                path: '/netincreasecust',
                name: 'netIncreaseCust',
                meta: {
                    title: '新增客户表'
                },
                component: () => import('@/views/report/kpi/netIncreaseCust/netIncreaseCust.vue')
            },
            {
                path: '/bxrenewal',
                name: 'bxRenewal',
                meta: {
                    title: '创新继续率'
                },
                component: () => import('@/views/report/kpi/bxRenewal/bxRenewal.vue')
            },
            {
                path: '/kpiconsulantsum',
                name: 'kpiConsulantSum',
                meta: {
                    title: '投顾KPI汇总表'
                },
                component: () => import('@/views/report/kpi/kpiConsulantSum/kpiConsulantSum.vue')
            },
            {
                path: '/fixIncomeFee',
                name: 'fixIncomeFee',
                meta: {
                    title: '固收产品后端费用'
                },
                component: () => import('@/views/report/finance/fixIncomeFee/fixIncomeFee.vue')
            },
            {
                path: '/fixIncomeFeeDetail',
                name: 'fixIncomeFeeDetail',
                meta: {
                    title: '固收产品后端费用明细'
                },
                component: () =>
                    import('@/views/report/finance/fixIncomeFee/fixIncomeFeeDetail.vue')
            },
            {
                path: '/secondProductFee',
                name: 'secondProductFee',
                meta: {
                    title: '二级产品后端费用'
                },
                component: () =>
                    import('@/views/report/finance/secondProductFee/secondProductFee.vue')
            },
            {
                path: '/secondProductFeeDetail',
                name: 'secondProductFeeDetail',
                meta: {
                    title: '二级产品后端费用明细'
                },
                component: () =>
                    import('@/views/report/finance/secondProductFee/secondProductFeeDetail.vue')
            },
            {
                path: '/stockProductFee',
                name: 'stockProductFee',
                meta: {
                    title: '股权产品后端费用'
                },
                component: () =>
                    import('@/views/report/finance/stockProductFee/stockProductFee.vue')
            },
            {
                path: '/stockProductFeeDetail',
                name: 'stockProductFeeDetail',
                meta: {
                    title: '股权产品后端费用明细'
                },
                component: () =>
                    import('@/views/report/finance/stockProductFee/stockProductFeeDetail.vue')
            },
            {
                path: '/settleSumFee',
                name: 'settleSumFee',
                meta: {
                    title: '部门结算报表'
                },
                component: () => import('@/views/report/finance/settleSumFee/settleSumFee.vue')
            },
            {
                path: '/settleDetailFee',
                name: 'settleDetailFee',
                meta: {
                    title: '结算明细汇总报表'
                },
                component: () =>
                    import('@/views/report/finance/settleDetailFee/settleDetailFee.vue')
            },
            {
                path: '/financeAuditSettleFee',
                name: 'financeAuditSettleFee',
                meta: {
                    title: '结算审批页'
                },
                component: () =>
                    import('@/views/report/finance/financeAuditSettleFee/financeAuditSettleFee.vue')
            },
            {
                path: '/financeAuditSettleFeeDetail',
                name: 'financeAuditSettleFeeDetail',
                meta: {
                    title: '结算审批明细页'
                },
                component: () =>
                    import(
                        '@/views/report/finance/financeAuditSettleFee/financeAuditSettleFeeDetail.vue'
                    )
            },
            {
                path: '/auditFeeCount',
                name: 'auditFeeCount',
                meta: {
                    title: '审批情况统计'
                },
                component: () => import('@/views/report/finance/auditFeeCount/auditFeeCount.vue')
            },
            {
                path: '/performanceTargetQuery',
                name: 'performanceTargetQuery',
                meta: {
                    title: '考核目标查询'
                },
                component: () =>
                    import('@/views/performanage/performanceTargetQuery/performanceTargetQuery.vue')
            },
            {
                path: '/smStockFeed',
                name: 'smStockFeed',
                meta: {
                    title: '私募_存量*产品存续D'
                },
                component: () => import('@/views/report/smStockFeed/smStockFeed.vue')
            },
            {
                path: '/gmStockFeed',
                name: 'gmStockFeed',
                meta: {
                    title: '公募_存量*产品存续D'
                },
                component: () => import('@/views/report/gmStockFeed/gmStockFeed.vue')
            },
            {
                path: '/gmSalesStiatistics',
                name: 'gmSalesStiatistics',
                meta: {
                    title: '公募销量统计'
                },
                component: () =>
                    import('@/views/report/saleManage/gmSalesStatistics/gmSalesStatistics.vue')
            },
            {
                path: '/gmSalesStatisticsDetail',
                name: 'gmSalesStatisticsDetail',
                meta: {
                    title: '公募销量统计明细'
                },
                component: () =>
                    import(
                        '@/views/report/saleManage/gmSalesStatistics/gmSalesStatisticsDetail.vue'
                    )
            },
            {
                path: '/performance/managenodeconf',
                name: 'performancemanagenodeconf',
                meta: {
                    title: '配置考核节点（管）'
                },
                component: () =>
                    import(
                        '@/views/performanage/performanceManageNodeConf/performanceManageNodeConf.vue'
                    )
            },
            {
                path: '/performance/managebusiness',
                name: 'performancemanagebusiness',
                meta: {
                    title: '分总预计考核结果-核算'
                },
                component: () =>
                    import(
                        '@/views/performanage/performanceManageBusiness/performanceManageBusiness.vue'
                    )
            },
            {
                path: '/performance/managebusinessfinal',
                name: 'performancemanagebusinessfinal',
                meta: {
                    title: '分总预计考核结果'
                },
                component: () =>
                    import(
                        '@/views/performanage/performanceManageBusinessFinal/performanceManageBusinessFinal.vue'
                    )
            },
            {
                path: '/performance/regiontotalbusiness',
                name: 'performanceregiontotalbusiness',
                meta: {
                    title: '区总区副预计考核结果-核算'
                },
                component: () =>
                    import(
                        '@/views/performanage/performanceRegionTotalBusiness/performanceRegionTotalBusiness.vue'
                    )
            },
            {
                path: '/performance/regiontotalbusinessfinal',
                name: 'performanceregiontotalbusinessfinal',
                meta: {
                    title: '区总区副预计考核结果'
                },
                component: () =>
                    import(
                        '@/views/performanage/performanceRegionTotalBusinessFinal/performanceRegionTotalBusinessFinal.vue'
                    )
            },
            {
                path: '/performance/consultforecast',
                name: 'performanageConsultForecast',
                meta: {
                    title: '理财师预计考核核算'
                },
                component: () =>
                    import(
                        '@/views/performanage/performanceConsultForecast/performanceConsultForecast.vue'
                    )
            },
            {
                path: '/performance/consultfinal',
                name: 'performanageConsultFinal',
                meta: {
                    title: '理财师预计考核核算结果'
                },
                component: () =>
                    import(
                        '@/views/performanage/performanceConsultFinal/performanceConsultFinal.vue'
                    )
            },
            {
                path: '/productCoefficientHis',
                name: 'productCoefficientHis',
                meta: {
                    title: '产品系数留档'
                },
                component: () =>
                    import('@/views/performanage/productCoefficientHis/productCoefficientHis.vue')
            },
            {
                path: '/report/hk/custHkInfo',
                name: 'custHkInfo',
                meta: {
                    title: '香港客户信息'
                },
                component: () => import('@/views/report/hk/custHkInfo/CustHkInfo.vue')
            },
            {
                path: '/report/hk/piggyHkDeal',
                name: 'piggyHkDeal',
                meta: {
                    title: '海外储蓄罐签约/终止明细表'
                },
                component: () => import('@/views/report/hk/piggyHkDeal/PiggyHkDeal.vue')
            },
            {
                path: '/keyTest',
                name: 'keyTest',
                meta: {
                    title: '测试弹框'
                },
                component: () => import('@/views/modBusiness/keyDia/keyTest.vue')
            },
            {
                path: '/pageTest',
                name: 'pageTest',
                meta: {
                    title: '测试弹框'
                },
                component: () => import('@/views/modBusiness/pageModule/pageDemo.vue')
            },
            {
                path: '/reward/consCombinationConfig',
                name: 'consCombinationConfig',
                meta: {
                    title: '组合配置'
                },
                component: () =>
                    import('@/views/reward/consCombinationConfig/consCombinationConfig.vue')
            },
            {
                path: '/reward/tradeNum',
                name: 'tradeNum',
                meta: {
                    title: '成交次数查询'
                },
                component: () => import('@/views/reward/tradeNum/tradeNum.vue')
            },
            {
                path: '/reward/bigOrder',
                name: 'bigOrder',
                meta: {
                    title: '大单查询'
                },
                component: () => import('@/views/reward/bigOrder/bigOrder.vue')
            },
            {
                path: '/report/health/overview',
                name: 'healthOverview',
                meta: {
                    title: '健康度完成率'
                },
                component: () => import('@/views/report/health/customerHealth/customerHealth.vue')
            },
            {
                path: '/report/custDetail',
                name: 'custDetail',
                meta: {
                    title: '24年底存量客户明细'
                },
                component: () => import('@/views/report/health/customerHealth/custDetail.vue')
            },
            {
                path: '/report/healthDetail',
                name: 'healthDetail',
                meta: {
                    title: '健康度明细'
                },
                component: () => import('@/views/report/health/customerHealth/healthDetail.vue')
            },
            {
                path: '/netincreaseoverseas2025',
                name: 'netIncreaseOverseas2025',
                meta: {
                    title: '净申购（海外+非A）'
                },
                component: () =>
                    import('@/views/report/kpi2025/netIncreaseOverseas/netIncreaseOverseas.vue')
            },
            {
                path: '/netincreaseoverseasdetail2025',
                name: 'netIncreaseOverseasDetail2025',
                meta: {
                    title: '净申购（海外+非A）明细'
                },
                component: () =>
                    import(
                        '@/views/report/kpi2025/netIncreaseOverseas/netIncreaseOverseasDetail.vue'
                    )
            },
            {
                path: '/netincreasecust2025',
                name: 'netincreasecust2025',
                meta: {
                    title: '新增客户表'
                },
                component: () =>
                    import('@/views/report/kpi2025/netIncreaseCust/netIncreaseCust.vue')
            },
            {
                path: '/bxRenewal2025',
                name: 'bxRenewal2025',
                meta: {
                    title: '创新继续率'
                },
                component: () => import('@/views/report/kpi2025/bxRenewal/bxRenewal.vue')
            },
            {
                path: '/kpiConsulantSum2025',
                name: 'kpiConsulantSum2025',
                meta: {
                    title: 'KPI汇总表'
                },
                component: () =>
                    import('@/views/report/kpi2025/kpiConsulantSum/kpiConsulantSum.vue')
            }
        ]
    }
] as AddRouteRecordRaw[]
