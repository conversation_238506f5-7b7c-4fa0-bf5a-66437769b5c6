/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils'

/**
 * @description: table表格数据
 * @return {*}
 */
export const auditTableColumn: TableColumnItem[] = [
    {
        key: 'paramType',
        label: '参数类型',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'operateType',
        label: '操作类型',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'status',
        label: '处理状态',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'checker',
        label: '审核人',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'checkTime',
        label: '审核日期时间',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'creator',
        label: '申请人',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'modifier',
        label: '修改人',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'createTime',
        label: '申请日期时间',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'updateTime',
        label: '修改日期时间',
        minWidth: 100,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}

/**
 * 参数类型枚举
 */
export const paramsTypeMap: { [key: string]: string } = {
    '10': '产品合同配置',
    '11': '产品默认文档配置',
    '12': '产品费率配置',
    '13': '产品金额赎回配置',
    '14': '产品金额赎回白名单配置'
}

/**
 * 操作类型枚举
 */
export const operTypeMap: { [key: string]: string } = {
    '1': '新增',
    '2': '修改',
    '3': '删除'
}

/**
 * 状态枚举
 */
export const statusListMap: { [key: string]: string } = {
    '0': '待申请',
    '1': '待审核',
    '2': '审核通过',
    '3': '已退回',
    '4': '作废'
}
