import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { tradeNumParam } from './type/apiReqType.js'

/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const TradeNumQuery = (params: tradeNumParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/reward/tradenum/queryTradeNum',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 导出接口
 * @return {*}
 */
export const TradeNumExport = (params: tradeNumParam) => {
    return axiosRequest(
        paramsMerge({
            timeout: 60000,
            url: '/api/reward/tradenum/exportTradeNum',
            method: 'post',
            data: params
        })
    )
}
