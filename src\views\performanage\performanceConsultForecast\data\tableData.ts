/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, getString } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const performanceConsultForecastTableColumn: TableColumnItem[] = [
    {
        key: 'u1Name',
        label: '中心',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 80,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '分公司',
        width: 110,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '投顾',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'consCode',
        label: '投顾code',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'regDt',
        label: '入职日期',
        width: 80,
        formatter: formatTableValue
    },
    {
        key: 'regularDt',
        label: '转正日期',
        width: 80,
        formatter: formatTableValue
    },
    {
        key: 'workState',
        label: '在职状态',
        width: 70,
        formatter: formatTableValue
    },
    {
        key: 'periodExplain',
        label: '考核周期',
        width: 80,
        formatter: formatTableValue
    },
    {
        key: 'userLevel',
        label: '层级',
        width: 70,
        formatter: formatTableValue
    },
    {
        key: 'curMonthLevel',
        label: '当前职级',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'startDt',
        label: '开始时间',
        width: 80,
        formatter: formatTableValue
    },
    {
        key: 'exaimneNode',
        label: '考核节点',
        width: 80,
        formatter: formatTableValue
    },
    {
        key: 'exaimneMonth',
        label: '考核节点司龄月',
        width: 110,
        formatter: formatTableValue
    },
    {
        key: 'targetAsset',
        label: '目标存量',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'resultAsset',
        label: '存量结果',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'gapAsset',
        label: '目标差距',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'monthAddAsset',
        label: '月均净新增存续D',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'exaimneResult',
        label: '预计考核结果',
        width: 95,
        formatter: formatTableValue
    },
    {
        key: 'newRank',
        label: '预计新职级',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'newNextTargetAsset',
        label: '上一档差距',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'istp',
        label: '是否TP',
        width: 70,
        formatter: formatTableValue
    },
    {
        key: 'exaimneEndresult',
        label: '最终考核结果',
        width: 95,
        formatter: formatTableValue
    },
    {
        key: 'newEndrank',
        label: '最终新职级',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'exaimneRemark',
        label: '备注',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'qualifiedStaff',
        label: '非观察人力-当前',
        width: 110,
        formatter: formatTableValue
    },
    {
        key: 'competentStaff',
        label: '合格人力-当前',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'qualifiedStaffNew',
        label: '非观察人力-预计',
        width: 110,
        formatter: formatTableValue
    },
    {
        key: 'competentStaffNew',
        label: '合格人力-预计',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'u1Staff',
        label: '中心(人力)',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'u2Staff',
        label: '区域(人力)',
        width: 80,
        formatter: formatTableValue
    },
    {
        key: 'u3Staff',
        label: '分公司(人力)',
        width: 110,
        formatter: formatTableValue
    },
    {
        key: 'humanNewRankFlag',
        label: '预计新职级人工修改标识',
        visible: true,
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'humanEndresultFlag',
        label: '最终考核结果人工修改标识',
        visible: true,
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'humanNewEndrankFlag',
        label: '最终新职级人工修改标识',
        visible: true,
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'humanQualifiedStaffFlag',
        label: '非观察人力人工修改标识',
        visible: true,
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'humanCompetentStaffFlag',
        label: '合格人力人工修改标识',
        visible: true,
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'humanQualifiedStaffNewFlag',
        label: '非观察人力预计人工修改标识',
        visible: true,
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'humanCompetentStaffNewFlag',
        label: '合格人力预计人工修改标识',
        visible: true,
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.visible) {
            return false
        }
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
