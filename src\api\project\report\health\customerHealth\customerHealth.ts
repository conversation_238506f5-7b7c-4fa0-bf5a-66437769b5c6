import { axiosRequest } from '@/utils/index'
import { QueryCustomerHealthParams } from './type/apiReqType'

// 查询健康客户统计列表
export const queryCustomerHealthList = (data: QueryCustomerHealthParams) => {
    return axiosRequest({
        url: '/api/report/health/consultant/query',
        method: 'post',
        data
    })
}

// 导出健康客户统计列表
export const exportCustomerHealthList = (data: QueryCustomerHealthParams) => {
    return axiosRequest({
        url: '/api/report/health/consultant/export',
        method: 'post',
        data
    })
}

export const healthInitData = () => {
    return axiosRequest({
        url: '/api/report/health/consultant/initdata',
        method: 'post'
    })
}
