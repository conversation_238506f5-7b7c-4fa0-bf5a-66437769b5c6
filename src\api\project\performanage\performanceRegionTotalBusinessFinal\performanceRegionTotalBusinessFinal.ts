import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { performanceRegionTotalBusinessFinalParam } from './type/apiReqType.js'

/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const performanceRegionTotalBusinessFinalQuery = (
    params: performanceRegionTotalBusinessFinalParam
): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/regiontotalFinal/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 导出接口
 * @return {*}
 */
export const performanceRegionTotalBusinessFinalExport = (
    params: performanceRegionTotalBusinessFinalParam
) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/regiontotalFinal/export',
            method: 'post',
            data: params
        })
    )
}
