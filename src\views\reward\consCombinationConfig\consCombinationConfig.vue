<template>
    <div class="report-list-module">
        <table-wrap-cust
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <table class="table-cust-select">
                    <tbody>
                        <tr>
                            <td>
                                <!-- 组合名称 -->
                                <label-item-cust
                                    :label="combinationName.label"
                                    class-name="text-left"
                                    labelwidth="140px"
                                >
                                    <crm-input
                                        v-model="queryForm.combinationName"
                                        :placeholder="combinationName.placeholder"
                                        :clearable="true"
                                        :style="{ width: '180px' }"
                                    />
                                </label-item-cust>
                            </td>
                            <td>
                                <label-item-cust
                                    :label="dateInterval.label"
                                    class-name="text-left"
                                    labelwidth="160px"
                                >
                                    <date-range
                                        v-model="queryForm.dateInterval"
                                        show-format="YYYY-MM-DD"
                                        :placeholder="dateInterval.placeholder"
                                        style-type="fund"
                                    />
                                </label-item-cust>
                            </td>
                            <td>
                                <!-- 海外占比≥80% -->
                                <label-item-cust :label="isOverEighty.label" labelwidth="160px">
                                    <crm-select
                                        v-model="queryForm.isOverEighty"
                                        :placeholder="isOverEighty.placeholder"
                                        filterable
                                        clearable
                                        label-format="label"
                                        value-format="key"
                                        :option-list="isOverEighty.selectList"
                                        :style="{ width: '160px' }"
                                    />
                                </label-item-cust>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </template>
            <template #operationBtns>
                <button-cust size="small" :radius="true" plain @click.stop="queryList"
                    >查询</button-cust
                >
                <button-cust
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    plain
                    @click="exportHandle"
                    >导出</button-cust
                >
            </template>
            <template #tableContentMiddle>
                <base-table-cust
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                >
                </base-table-cust>
            </template>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrap-cust>
    </div>
</template>

<script lang="ts" setup>
    import { fetchRes } from '@/utils'
    import { dataList } from './data/labelData'
    import { consCombinationConfigTableColumn } from './data/tableData'

    import {
        // eslint-disable-next-line camelcase
        ConsCombinationConfigQuery,
        ConsCombinationConfigExport
    } from '@/api/project/reward/consCombinationConfig/consCombinationConfig'

    import { getMenuPermission } from '@/api/project/common/common'
    import { REWARD_COMB_CONFIG_OPER_PERMISSION } from '@/constant/rewardConst'
    import BaseTableCust from '@/views/modBusiness/pageModule/components/BaseTableCust.vue'
    import LabelItemCust from '@/views/modBusiness/pageModule/components/LabelItemCust.vue'
    import TableWrapCust from '@/views/modBusiness/pageModule/components/TableWrapCust.vue'
    import ButtonCust from '@/views/modBusiness/pageModule/components/ButtonCust.vue'
    import { defineComponent } from 'vue'

    const { dateInterval, isOverEighty, combinationName } = dataList
    const module = ref<string>('071613')
    const exportShow = ref<boolean>(false)

    const listLoading = ref<boolean>(false)

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        combinationName = ''
        dateInterval = {
            startDate: '',
            endDate: ''
        }
        isOverEighty = ''
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: 说明展示隐藏
     * @param explainDialogVisiable 展示隐藏
     * @method handleExplain 触发方法
     * @return {*}
     */
    const explainDialogVisiable = ref<boolean>(false)
    const handleExplain = () => {
        explainDialogVisiable.value = true
    }

    /**
     * @description: 分页数据
     * @return {*}
     */
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 100
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return consCombinationConfigTableColumn
    })

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            combinationName: queryForm.combinationName,
            startDt: queryForm.dateInterval.startDate,
            endDt: queryForm.dateInterval.endDate,
            hwzbOverEighty: queryForm.isOverEighty,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(ConsCombinationConfigQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                tableData.value = rows
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const params = {
            combinationName: queryForm.combinationName,
            startDt: queryForm.dateInterval.startDate,
            endDt: queryForm.dateInterval.endDate,
            hwzbOverEighty: queryForm.isOverEighty
        }
        const res: any = await ConsCombinationConfigExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }

    const initData = async () => {
        const params = {
            menuCode: module.value
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode === REWARD_COMB_CONFIG_OPER_PERMISSION.EXPORT &&
                            item.display === '1'
                        ) {
                            exportShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }
    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        initData()
    })
</script>
<style lang="less" scoped>
    .table-cust-select {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        tr {
            background-color: #ffffff;

            td {
                border: 1px solid #dddddd;
            }
        }
    }
</style>
