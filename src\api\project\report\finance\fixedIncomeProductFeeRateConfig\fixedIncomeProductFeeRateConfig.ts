/*
 * @Description: 固收产品费率配置 API接口
 * @Author: hongdong.xie
 * @Date: 2025-06-05 18:53:18
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-05 18:53:18
 * @FilePath: /ds-report-web/src/api/project/report/finance/fixedIncomeProductFeeRateConfig/fixedIncomeProductFeeRateConfig.ts
 */

import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '../../../../mock.js'
import {
    FixedIncomeProductFeeRateConfigQueryParam,
    FixedIncomeProductFeeRateConfigAddParam,
    FixedIncomeProductFeeRateConfigUpdateParam,
    FixedIncomeProductFeeRateConfigDeleteParam,
    FixedIncomeProductFeeRateConfigAuditParam,
    FixedIncomeProductFeeRateConfigExportParam
} from './type/apiReqType.js'

/**
 * @description: 固收产品费率配置查询接口
 * @param {FixedIncomeProductFeeRateConfigQueryParam} params 查询参数
 * @return {*}
 */
export const fixedIncomeProductFeeRateConfigQuery = (params: FixedIncomeProductFeeRateConfigQueryParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fixedincome/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 固收产品费率配置导出接口
 * @param {FixedIncomeProductFeeRateConfigExportParam} params 导出参数
 * @return {*}
 */
export const fixedIncomeProductFeeRateConfigExport = (params: FixedIncomeProductFeeRateConfigExportParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fixedincome/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 固收产品费率配置新增接口
 * @param {FixedIncomeProductFeeRateConfigAddParam} params 新增参数
 * @return {*}
 */
export const fixedIncomeProductFeeRateConfigAdd = (params: FixedIncomeProductFeeRateConfigAddParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fixedincome/add',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 固收产品费率配置修改接口
 * @param {FixedIncomeProductFeeRateConfigUpdateParam} params 修改参数
 * @return {*}
 */
export const fixedIncomeProductFeeRateConfigUpdate = (params: FixedIncomeProductFeeRateConfigUpdateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fixedincome/update',
            method: 'post',
            data: params,
            // Mock数据配置
            mockData: {
                code: '200',
                description: '修改成功',
                data: null
            }
        })
    )
}

/**
 * @description: 固收产品费率配置删除接口
 * @param {FixedIncomeProductFeeRateConfigDeleteParam} params 删除参数
 * @return {*}
 */
export const fixedIncomeProductFeeRateConfigDelete = (params: FixedIncomeProductFeeRateConfigDeleteParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fixedincome/delete',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 固收产品费率配置审核接口
 * @param {FixedIncomeProductFeeRateConfigAuditParam} params 审核参数
 * @return {*}
 */
export const fixedIncomeProductFeeRateConfigAudit = (params: FixedIncomeProductFeeRateConfigAuditParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fixedincome/audit',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 获取固收产品费率配置操作按钮权限
 * @return {*}
 */
export const getFixedIncomeProductFeeRateConfigAuth = () => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/fixedincome/getAuth',
            method: 'post'
        })
    )
} 