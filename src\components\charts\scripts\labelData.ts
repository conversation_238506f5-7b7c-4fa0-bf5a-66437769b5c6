/*
 * @Description: 组件常用变量抽取
 * @Author: chaohui.wu
 * @Date: 2023-03-30 11:08:32
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-26 15:49:58
 * @FilePath: /crm-asset-web/src/views/assetReport/reportEdit/tabsPage/custPosition/data/labelData.ts
 *
 */

export const dataList = {}

/**
 * 前端枚举
 */
export const baseEnuber: any = {
    link: new Map([
        [
            'chromeLink',
            `https://dl.google.com/tag/s/appguid%3D%7B8A69D345-D564-463C-AFF1-A69D9E530F96%7D%26iid%3D%7B77CCCA8B-F59C-E570-E701-E2575690F29C%7D%26lang%3Dzh-CN%26browser%3D3%26usagestats%3D0%26appname%3DGoogle%2520Chrome%26needsadmin%3Dprefers%26ap%3Dx64-stable-statsdef_1%26installdataindex%3Dempty/chrome/install/ChromeStandaloneSetup64.exe`
        ]
    ]),
    riskMap: new Map([
        ['1', '低'],
        ['2', '中'],
        ['3', '高']
    ]),
    humanMap: new Map([
        ['1', '人生奋斗期'],
        ['2', '财富创造期'],
        ['3', '财富沉淀期'],
        ['4', '人生享受期']
    ]),
    healthMap: new Map([
        ['1', '小康'],
        ['2', '小康'],
        ['3', '殷实'],
        ['4', '富裕'],
        ['5', '豪门']
    ]),
    exprenceMap: new Map([
        ['1', '缺乏'],
        ['2', '有限'],
        ['3', '一般'],
        ['4', '较丰富'],
        ['5', '丰富']
    ])
}

/**
 * @description: 获取列表定义的枚举
 * @param {string} key
 * @return {*}
 */
export const getEnumber = (value: string, key: string, ValArr = baseEnuber) => {
    return ValArr[value].get(key) || ''
}
