<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
    <!-- class="crm-base-table" -->
    <div>
        <el-table
            ref="multipleTable"
            v-loading="isLoading"
            class="crm-base-table"
            :class="className"
            :resizable="true"
            stripe
            :data="data"
            :height="height"
            :empty-text="isLoading ? ' ' : ' '"
            :span-method="objectSpanMethod"
            :row-class-name="handleRowClassName"
            :cell-style="styleHandle"
            :header-cell-style="handleTheadStyle"
            v-bind="$attrs"
            @select="handleSelect"
            @selection-change="handleSelectionChange"
            @sort-change="handleSortColumns"
        >
            <el-table-column
                v-if="noSelect"
                :key="`column-select`"
                type="selection"
                width="55"
                :align="`center`"
                :fixed="pops && pops.selectFixed"
            />

            <el-table-column
                v-if="noIndex"
                :key="`column-index`"
                type="index"
                label="序号"
                width="50"
                :fixed="pops && pops.selectFixed"
            />
            <el-table-column
                v-for="(store, index) in columns"
                v-show="getTableColumnState(columns, store.key)"
                :key="`columns${index}`"
                :label="store.label"
                :prop="store.key"
                :align="store.align || 'center'"
                :min-width="store.minWidth || ''"
                :width="store.width || ''"
                :formatter="store.formatter"
                :show-overflow-tooltip="store.showOverflowTooltip !== false"
                :sortable="store.sortable"
                :sort-orders="store.sortOrders"
                :fixed="fixedHandle(store.key)"
                :filters="store.filters"
            >
                <!-- table头部自定义 -->
                <template v-if="store.slotHeader" #header>
                    <slot :name="store.slotHeaderName"></slot>
                </template>

                <!-- 内容自定义 -->
                <template v-if="store.type === 'slot'" #default="scope">
                    <slot
                        :name="store.slotName"
                        :scope="{ ...scope, firstFundManager: store.firstFundManager }"
                    ></slot>
                </template>
            </el-table-column>

            <el-table-column
                v-if="showOperation"
                :key="`column-operation`"
                label="操作"
                :min-width="operationWidth"
                :align="`center`"
                :fixed="operationFixed ? 'right' : false"
            >
                <template #default="scope">
                    <slot name="operation" :scope="scope" />
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'BaseTable',
        props: {
            className: {
                type: [String],
                default: ''
            },
            columns: {
                type: Array,
                default: () => {
                    return []
                }
            },
            data: {
                type: Array,
                default: () => {
                    return []
                }
            },
            /**
             * table表中各列的单独配置项
             * pops.selectFixed ===> 若table中有需要固定在左侧的列  该值需要传left 将多选框和序号固定
             */
            pops: {
                type: Object,
                default: () => {
                    return {}
                }
            },
            isLoading: {
                type: Boolean,
                default: false
            },
            objectSpanMethod: {
                type: Function,
                default: () => {
                    return undefined
                }
            },
            showOperation: {
                type: Boolean, // 是否需要操作列
                default: true
            },
            operationFixed: {
                type: Boolean, // 是否需要操作列
                default: true
            },
            noSelect: {
                type: Boolean,
                default: true
            },
            noIndex: {
                type: Boolean,
                default: true
            },
            height: {
                type: String,
                default: '100%'
            },
            operationWidth: {
                type: String,
                default: '130'
            },
            mutilSort: {
                type: Boolean,
                default: false // 是否需要多列排序，箭头高亮
            },
            // 图标
            columnIcon: {
                type: Boolean,
                default: false
            },
            // 图标宽度
            columnIconWidth: {
                type: Number,
                default: 50
            },
            isSingleSelect: {
                type: Boolean,
                default: false
            }
        },
        emits: ['selection-change', 'sort-change', 'select', 'current-change', 'clearSelect'],
        data() {
            return {
                activeThead: {} // 多列排序，箭头高亮，保存所选择的表头
            }
        },
        methods: {
            getTableColumnState(columns, key) {
                return columns.some(item => item.key === key)
            },
            // 定位
            fixedHandle(key, val) {
                if (this.pops && key) {
                    const pop = this.pops[key]
                    if (pop) {
                        if (pop.fixed) {
                            return pop.fixed
                        }
                    }
                }
                return false
            },
            // 涨红跌绿 需要在pops中透传column的key 值为对象 如{redGreen: true}
            styleHandle(item) {
                // 根据报警级别显示颜色
                if (
                    this.pops &&
                    this.pops[item.column.property] &&
                    this.pops[item.column.property].redGreen
                ) {
                    if (
                        item.row[item.column.property] &&
                        String(item.row[item.column.property]).replace(/%/g, '') > 0
                    ) {
                        return { color: '#c82d30' }
                    } else if (
                        item.row[item.column.property] &&
                        String(item.row[item.column.property]).replace(/%/g, '') < 0
                    ) {
                        return { color: '#018800' }
                    }
                }
                if (
                    this.pops &&
                    this.pops[item.column.property] &&
                    this.pops[item.column.property].borderRightNone
                ) {
                    return { borderRight: 'none' }
                }
            },
            // 列表选中事件
            handleSelectionChange(val) {
                this.$emit('selection-change', val)
                // 事件将table表格的实例对象推出
                if (this.isSingleSelect) {
                    this.$emit('clearSelect', val, this.$refs.multipleTable)
                }
            },
            handleSelect(sel, row) {
                this.$emit('select', sel, row)
            },
            handleClickTable(row) {
                this.$emit('current-change', row)
            },
            handleSortColumns(column) {
                this.$emit('sort-change', column)
                // 多列排序，箭头高亮
                if (!this.mutilSort) {
                    return
                }
                const { prop, order } = column
                this.activeThead[prop] = order || ''
            },
            // 多列排序，箭头高亮
            handleTheadStyle({ row, column, rowIndex, columnIndex }) {
                if (!this.mutilSort) {
                    return
                }
                if (this.activeThead[column.property]) {
                    column.order = this.activeThead[column.property]
                }
            },
            handleRowClassName(rowObj, rowIndex) {
                const { row } = rowObj
                if (row && row.lineStatus === '1') {
                    return 'off-line-bg'
                }
            }
        }
    })
</script>
<style lang="less">
    .crm-base-table {
        thead {
            th {
                position: relative;
                color: @font_color_01;
                content: '\e6ff';
                border-right: 1px solid @bg_main_02 !important;
            }

            &:hover {
                th {
                    border-right: 1px solid #f8f8f8 !important;
                }

                th.el-table-column--selection,
                th:last-child {
                    border-right: 1px solid @bg_main_02 !important;
                }
            }
        }
        // 公共表格样式
        &.el-table {
            font-size: 12px;
            color: @font_color_05;

            .el-table__header {
                tr {
                    th {
                        height: 38px;
                        padding: 0;
                        font-weight: bold;
                        line-height: 38px;
                        color: @bg_main_03;
                        background-color: @border_color_01;
                        // border: none;

                        .cell {
                            padding: 0 10px;
                            font-family: 'Microsoft YaHei', '微软雅黑';
                            // height: 18px;
                            font-size: 12px;
                            font-weight: 400;
                            line-height: 17px;
                            color: @font_color;
                            .text-overflow();

                            .caret-wrapper {
                                display: inline-block;
                                width: 16px;
                                height: 17px;

                                .sort-caret {
                                    &.ascending {
                                        top: -3px;
                                    }

                                    &.descending {
                                        bottom: -1px;
                                    }
                                }
                            }
                        }

                        &.is-sortable.is-right {
                            .cell {
                                display: flex;
                                align-items: center;
                                justify-content: flex-end;

                                .caret-wrapper {
                                    .sort-caret {
                                        &.ascending {
                                            top: -2px;
                                        }

                                        &.descending {
                                            bottom: -2px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .el-table__body {
                td {
                    height: 37px;
                    padding: 0;
                    color: @font_color_02;
                    border: none;

                    .cell {
                        .el-button {
                            &.is-round {
                                height: 20px;
                                padding: 2px 8px 2px 6px;
                                color: @font_link;
                                border: 1px solid @font_link;
                                border-radius: 8px;
                            }

                            &.is-link {
                                color: @font_link;
                                // text-decoration: underline;
                            }

                            &.el-button--danger {
                                color: @theme_main;
                            }
                        }

                        .el-input__icon {
                            line-height: inherit;
                        }
                    }

                    &.el-table-column--selection {
                        .cell {
                            padding-right: 10px;
                        }
                    }
                }

                tr.el-table__row {
                    &.off-line-bg {
                        background-color: #f5f6fa;

                        .el-table__cell {
                            background-color: #f5f6fa;
                        }
                    }

                    &.highlight-bg {
                        .el-table__cell{
                            font-weight:bold;
                            color: #ffffff;
                            background-color: #6293BB;

                            .el-button{
                                font-weight:bold;
                            }

                            &.redColumn {
                                color: #c01b26;
                            }
                        }
                    }

                    &.el-table__row--striped.highlight-bg  {
                        .el-table__cell{
                            background-color: #6293BB;
                        }
                    }

                    &.hover-row.highlight-bg{
                        td {
                            background-color: #6293BB;
                        }
                    }
                }

            }

            &.el-table--striped .el-table__body tr.el-table__row--striped td {
                background-color: #f5f6fa;
            }

            .el-table__fixed-right {
                &::before {
                    display: none;
                }
            }
            // 表格hover背景色
            .el-table--striped .el-table__body tr.el-table__row--striped.current-row td,
            .el-table__body tr.current-row > td,
            .el-table__body tr.hover-row.current-row > td,
            .el-table__body tr.hover-row.el-table__row--striped.current-row > td,
            .el-table__body tr.hover-row.el-table__row--striped > td,
            .el-table__body tr.hover-row > td {
                background-color: #fafafa;
            }

            .el-checkbox {
                .el-checkbox__input {
                    .el-checkbox__inner {
                        width: 12px;
                        height: 12px;
                        background-color: #fbfcfe;
                        border: 1px solid #e7e7ea;

                        &:hover {
                            border-color: #c01b26;
                        }

                        &::after {
                            top: 0;
                            left: 3px;
                            width: 3px;
                            height: 6px;
                        }
                    }

                    &.is-indeterminate .el-checkbox__inner {
                        background-color: #c01b26;
                        border-color: #c01b26;
                    }

                    &.is-checked {
                        .el-checkbox__inner {
                            background-color: #c01b26;
                            border-color: #c01b26;
                        }
                    }
                }
            }
        }
    }

    .el-table--border {
        .el-table__inner-wrapper::after {
            top: auto;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            content: '';
        }
    }
    // 模块无数据样式
    .noData,
    .no_data {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-size: 12px;
        color: #909399;
    }

    // 表格无数据样式
    .el-table__empty-text {
        .no_data();
    }
</style>
