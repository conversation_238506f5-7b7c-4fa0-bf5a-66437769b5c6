/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, formatNumber } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const assetRepDownloadTableColumn: TableColumnItem[] = [
    {
        key: 'u1Name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consCode',
        label: '员工编码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'isKpi',
        label: '是否参与考核',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'kpiBuyAmt',
        label: 'KPI申购量',
        width: 120,
        formatter: ({ kpiBuyAmt }: any) => {
            return formatNumber({ num: kpiBuyAmt })
        }
    },
    {
        key: 'kpiRedeemAmt',
        label: 'KPI赎回量',
        width: 120,
        formatter: ({ kpiRedeemAmt }: any) => {
            return formatNumber({ num: kpiRedeemAmt })
        }
    },
    {
        key: 'kpiNetIncreaseAmt',
        label: 'KPI净申购量',
        width: 120,
        formatter: ({ kpiNetIncreaseAmt }: any) => {
            return formatNumber({ num: kpiNetIncreaseAmt })
        }
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
