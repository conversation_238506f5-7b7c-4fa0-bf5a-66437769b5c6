<!--
* 通用table列表页面容器
* author: rog<PERSON><PERSON>
* @slot searchArea, 搜索条件区域
* @slot operationBtns, 操作按钮栏
* @slot tableContentMiddle, 表格列表区域
* @slot tableContentBottom, table列表底部区域, 分页器等
-->
<template>
    <div class="crm-form-wrapper">
        <slot />
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmFormWrapper',
        props: {
            // labelWidth: {
            //     type: String,
            //     default: ''
            // }
        },
        data() {
            return {}
        }
    })
</script>

<style lang="less" scoped></style>
