<!-- eslint-disable vue/require-default-prop -->
<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="board-s">
        <!-- header -->
        <div class="header-s">
            <span style="margin: 5px 0 0 5px">{{ title }}</span>
        </div>

        <!-- 滚动看板 -->
        <el-row span="24" class="demo">
            <el-col v-for="(item, index) in scrollBoardData" :key="index" :span="6">
                <el-card class="box-card">
                    <div slot="header">
                        <span style="font-size: 14px; font-weight: bold">
                            {{ item.value }}
                        </span>
                        <span v-if="item.note">{{ '/' + item.note }}</span>
                    </div>
                    <div>
                        <span
                            class="ahover"
                            style="
                                font-weight: bold;
                                text-decoration: underline;
                                white-space: nowrap;
                            "
                            @click="sendMsgToParent(item.id)"
                        >
                            {{ item.name }}
                        </span>
                    </div>
                    <div v-if="item.id == 21">
                        <span style="font-size: 16px; font-weight: bold">
                            {{ item.amt + '万' }}
                        </span>
                    </div>
                    <div v-if="item.id == 22">
                        <span style="font-size: 16px; font-weight: bold">
                            {{ item.amt + '万' }}
                        </span>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'ScrollBoard',
        props: {
            title: {
                type: String,
                default: 'title'
            },
            // eslint-disable-next-line vue/require-default-prop
            boardData: {
                type: Array
            }
        },

        data() {
            return {
                beginIndex: 0,
                endIndex: 0,
                length: 0,
                scrollBoardData: []
            }
        },
        watch: {
            boardData: {
                handler(val, oldVal) {
                    this.initScroll()
                },
                deep: true //true 深度监听
            }
        },
        mounted() {
            this.initScroll()
        },
        methods: {
            initScroll() {
                this.beginIndex = 0
                this.endIndex = 4
                this.length = this.boardData.length
                this.scrollBoardData = this.boardData.slice(this.beginIndex, this.endIndex)
            },

            sendMsgToParent(item) {
                // eslint-disable-next-line vue/require-explicit-emits
                this.$emit('ievent', item)
                console.log(item)
            }
        }
    })
</script>

<style scoped>
    .demo {
        :deep(.el-card__body) {
            padding: 5px;
        }
    }

    .board-s {
        height: 95%;
        margin: 5px;
        border: 1px solid cornflowerblue;
    }

    .box-card {
        display: block;
        height: 90%;
        margin: 2px;
        font-size: 14px;
        text-align: center;
        background: #bce8f1;
    }

    .header-s {
        height: 20%;
        padding: 2px;
        margin: 2px;
        font-size: 16px;
        font-weight: bold;
        background-color: #d9edf7;
    }

    .ahover:hover {
        color: red;
    }
</style>
