<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            :show-export-btn="false"
            @searchFn="handleLoading"
            @exportFn="exportHandle"
        >
            <template #searchArea>
                <!-- 年份选择器 -->
                <label-item :id="'selectedYears'" label="年份">
                    <el-select
                        v-model="selectedYears"
                        multiple
                        placeholder="请选择年份"
                        style="width: 100%"
                        @change="handleYearChange"
                    >
                        <el-option
                            v-for="yearOption in possibleYears"
                            :key="yearOption.value"
                            :label="yearOption.label"
                            :value="yearOption.value"
                        ></el-option>
                    </el-select>
                </label-item>
                <label-item :label="fundMan.label">
                    <crm-input
                        v-model="queryForm.fundMan"
                        :placeholder="fundMan.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <label-item :label="fundCode.label">
                    <crm-input
                        v-model="queryForm.fundCode"
                        :placeholder="fundCode.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
                <label-item :label="productType.label">
                    <crm-select
                        v-model="queryForm.productType"
                        :placeholder="productType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="productType.selectList"
                        :style="{ width: '120px' }"
                        @change="handleProductType"
                    />
                </label-item>
                <label-item :label="feeType.label">
                    <crm-select
                        v-model="queryForm.feeType"
                        :placeholder="feeType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="feeTypeList"
                        :style="{ width: '120px' }"
                        @change="handleFeeType"
                    />
                </label-item>
                <label-item :label="fundName.label">
                    <crm-input
                        v-model="queryForm.fundName"
                        :placeholder="fundName.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
                <label-item :label="subFundName.label">
                    <crm-input
                        v-model="queryForm.subFundName"
                        :placeholder="subFundName.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    :cell-style="getAmountStyle"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                >
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>
    </div>
</template>

<script lang="ts" setup>
    import { ref, computed } from 'vue'
    import { auditFeeCountTableColumn, showTableColumn } from './data/tableData'
    import { dataList } from './data/labelData'
    const { fundMan, fundCode, fundName, subFundName, productType, feeType } = dataList

    import {
        AuditFeeCountQuery,
        AuditFeeCountExport
    } from '@/api/project/report/finance/auditFeeCount/auditFeeCount'
    import { fetchRes } from '@/utils'
    import { getMenuPermission } from '@/api/project/common/common'
    import { ElMessage } from 'element-plus'

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        productType = '1'
        fundCode = ''
        fundMan = ''
        feeType = '1'
        fundName = ''
        subFundName = ''
    }
    const queryForm = reactive(new QueryForm())
    const feeTypeList = ref<object[]>(feeType.fixedList)

    /**
     * @description: 上一次点了查询的条件列表
     * @return {*}
     */
    const queryFormAction = new QueryForm()

    const module = ref<string>('B071217')
    const listLoading = ref<boolean>(false)

    // 用户选择的年份
    const selectedYears = ref([])
    // 可用的年份列表
    //const possibleYears = ref([2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025,2026, 2027, 2028])

    const currentYear = ref(new Date().getFullYear() + 1)
    // 填充年份选项的计算属性
    const possibleYears = computed(() => {
        const options = []
        for (let year = currentYear.value; year >= 2018; year--) {
            options.push({ label: year.toString(), value: year })
        }
        return options
    })

    // 动态生成表头的方法
    const generateTableColumns = () => {
        // 创建一个映射，以年份为键，月份列数组为值
        const columnsMap = new Map()

        // 遍历每个选择的年份
        selectedYears.value.forEach(year => {
            // 确保映射中为该年份创建了数组
            if (!columnsMap.has(year)) {
                columnsMap.set(year, [])
            }

            // 为当前年份生成月份列，并添加到对应年份的数组中
            for (let i = 1; i <= 12; i++) {
                // 注意这里格式化月份为两位数的字符串
                const monthStr = i < 10 ? `0${i}` : `${i}`
                const monthColumn = {
                    key: `${year}${monthStr}`,
                    label: `${year}年${i}月`,
                    width: 100 // 可以根据需要调整列宽
                }
                columnsMap.get(year).push(monthColumn)
            }
        })

        // 将所有年份的月份列数组扁平化为一个数组
        // 由于 Map 值的迭代顺序是按照插入顺序的，所以这将保持年份和月份的顺序
        return Array.from(columnsMap.values()).flat()
    }

    const tableData = ref<object[]>([])
    // table表格column数据展示
    const tableColumn = computed(() => {
        // 现有表头
        const baseColumns = showTableColumn(
            auditFeeCountTableColumn.map((item: any) => item.key),
            auditFeeCountTableColumn
        )

        // 动态生成的年份月份表头
        const monthlyColumns = generateTableColumns()

        // 合并基础表头和动态月份表头
        return [...baseColumns, ...monthlyColumns]
    })

    // 处理年份变化的方法
    const handleYearChange = () => {
        // 由于 tableColumn 是计算属性，不需要手动重新计算
        // 它的值将根据 selectedYears 的变化自动更新
    }

    const handleProductType = (val: string) => {
        queryForm.productType = val
        if (val === '1') {
            feeTypeList.value = feeType.fixedList
            queryForm.feeType = '1'
        }
        if (val === '2') {
            feeTypeList.value = feeType.secondList
            queryForm.feeType = '4'
        }
        if (val === '3') {
            feeTypeList.value = feeType.stockList
            queryForm.feeType = '7'
        }
    }
    const handleFeeType = (val: string) => {
        queryForm.feeType = val
    }

    const getAmountStyle = (row: any, column: any) => {
        const auditStateKey = row.column.property // 假设 column.property 是类似于 "202401" 的月份键
        console.log(auditStateKey)
        // 从 row 中获取对应的 auditState 值
        const auditState = row.row ? row.row[auditStateKey + 'AuditState'] : null
        console.log(auditState)
        if (row.column.label.includes('月')) {
            const color = auditState !== '已审核' ? 'red' : 'black'
            return { color } // 返回一个对象，包含动态生成的样式
        }
    }

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    // 假设这是你接收的数据结构
    interface RowData {
        fundCode: string
        fundName: string
        subFundName: string
        manager: string
        feeType: string
        productType: string
        startMonth: string
        endMonth: string
        fee: number
        auditState: string
    }

    interface DataItemWithFeesByMonth {
        feesByMonth: {
            [key: string]: number
        }
    }

    //查詢
    const queryList = async () => {
        if (selectedYears.value.length === 0) {
            ElMessage.error('请选择年份')
            return
        }
        listLoading.value = true
        const params = {
            fundCode: queryForm.fundCode,
            fundMan: queryForm.fundMan,
            productType: queryForm.productType,
            feeType: queryForm.feeType,
            fundName: queryForm.fundName,
            subFundName: queryForm.subFundName,
            selectedYears: selectedYears.value,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(AuditFeeCountQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                // 创建一个映射，以 fundCode 为键，以对象为值
                const fundCodeDataMap = new Map()
                // 清空旧数据
                tableData.value = []
                //rows.forEach((row: RowData) => {
                tableData.value = rows.map((row: RowData) => {
                    // 构建 fundCode 的映射键
                    const fundCodeKey = row.fundCode
                    // 检查当前 fundCode 是否已经存在于映射中
                    // if (!fundCodeDataMap.has(fundCodeKey)) {
                    // 如果不存在，初始化一个新的对象，并添加到映射中
                    const newDataItem: DataItemWithFeesByMonth = {
                        ...row,
                        feesByMonth: {}
                    }
                    // 初始化所有选中年份的月份费用为 0
                    selectedYears.value.forEach(selectedYear => {
                        for (let month = 1; month <= 12; month++) {
                            const monthKey = `${selectedYear}${month.toString().padStart(2, '0')}`
                            newDataItem.feesByMonth[monthKey] = 0
                        }
                    })
                    fundCodeDataMap.set(fundCodeKey, newDataItem)
                })
                //} else {
                tableData.value = rows.map((row: RowData) => {
                    const fundCodeKey = row.fundCode
                    let currentYear = parseInt(row.startMonth.substring(0, 4)) // 提取年份
                    let currentMonth = parseInt(row.startMonth.substring(4)) // 提取月份
                    // 转换endMonth为整数，用于比较
                    const endMonth = parseInt(row.endMonth)
                    // 如果已经存在，获取现有的对象
                    const existingDataItem = fundCodeDataMap.get(fundCodeKey)
                    // 填充年份和月份数据
                    while (currentYear * 100 + currentMonth <= endMonth) {
                        const monthKey = `${currentYear}${currentMonth.toString().padStart(2, '0')}`
                        if (existingDataItem.feesByMonth[monthKey] !== undefined) {
                            existingDataItem.feesByMonth[monthKey] += row.fee
                        } else {
                            existingDataItem.feesByMonth[monthKey] = row.fee
                        }

                        // 设置 auditState 状态
                        existingDataItem[monthKey + 'AuditState'] = row.auditState
                        // 月份递增逻辑
                        currentMonth++
                        if (currentMonth > 12) {
                            currentMonth = 1 // 重置月份为1
                            currentYear++ // 年份递增
                        }
                    }
                })

                // 将映射中的所有数据项转换为数组，并赋值给 tableData
                tableData.value = Array.from(fundCodeDataMap.values(), dataItem => {
                    // 这里返回一个新的对象数组，包含固定列和动态年月费列
                    return {
                        ...dataItem,
                        // 动态添加年月费列
                        ...dataItem.feesByMonth
                    }
                })
                // 更新动态列
                //tableColumn.value = generateTableColumns()
                console.log(tableColumn)
                console.log(tableData)
                // TODO list接口待添加分页数据
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    interface DataItem {
        fundCode: string
        fundName: string
        subFundName: string
        manager: string
        productType: string
        feeType: string
        startMonth: string // 例如 '202301' 格式
        endMonth: string // 例如 '202312' 格式
        fee: number // 假设每个记录只有一个费用值
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const pdfUrl = ref<string>('')
    const exportList = async () => {
        const params = {
            fundCode: queryForm.fundCode,
            fundMan: queryForm.fundMan,
            productType: queryForm.productType,
            feeType: queryForm.feeType,
            fundName: queryForm.fundName,
            subFundName: queryForm.subFundName
        }
        const res: any = await AuditFeeCountExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }
</script>
