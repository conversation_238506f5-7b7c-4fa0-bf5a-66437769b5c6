import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { AssetRepDownloadParam } from './type/apiReqType.js'

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const AssetRepDownloadQuery = (params: AssetRepDownloadParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/assetrepdownload/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const AssetRepDownloadExport = (params: AssetRepDownloadParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/assetrepdownload/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const AssetRepDownloadInit = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/assetrepdownload/initdata',
            method: 'post',
            data: null
        })
    )
}
