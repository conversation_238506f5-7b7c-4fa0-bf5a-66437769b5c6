/*
 * @Description: 定义搜索的label列表
 * @Author: chaohui.wu
 * @Date: 2024-05-13 13:40:30
 * @LastEditors: gang.zou
 * @LastEditTime: 2024-05-13 17:36:23
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/labelData.ts
 *
 */
export const dataList = {
    startDt: {
        label: '开始日期',
        placeholder: '输入开始日期'
    },
    endDt: {
        label: '结束日期',
        placeholder: '输入结束日期'
    },
    fundCode: {
        label: '基金代码',
        placeholder: '输入基金代码'
    },
    fundMan: {
        label: '基金管理人',
        placeholder: '输入基金管理人'
    },
    // 产品类型
    productType: {
        label: '产品类型',
        placeholder: '请选择产品类型',
        selectList: [
            {
                key: '1',
                label: '固收'
            },
            {
                key: '2',
                label: '二级'
            },
            {
                key: '3',
                label: '股权'
            }
        ]
    },
    // 费用类型
    feeType: {
        label: '费用类型',
        placeholder: '请选择费用类型',
        selectList: [
            {
                key: '1',
                label: '咨询费-普通口径'
            },
            {
                key: '2',
                label: '管理费-存量口径'
            },
            {
                key: '3',
                label: '退出费-赎回口径'
            },
            {
                key: '10',
                label: '业绩报酬 (固收产品)'
            }
        ]
    },
    fundName: {
        label: '产品名称',
        placeholder: '输入产品名称'
    },
    custName: {
        label: '客户姓名',
        placeholder: '输入客户姓名'
    },
    conscustNo: {
        label: '投顾客户号',
        placeholder: '投顾客户号'
    },
    preAmount: {
        label: '去税计提金额',
        placeholder: '去税计提金额'
    },
    preAmountTax: {
        label: '计提金额',
        placeholder: '计提金额'
    },
    realAmount: {
        label: '去税实际结算金额',
        placeholder: '去税实际结算金额'
    },
    realAmountTax: {
        label: '实际结算金额',
        placeholder: '实际结算金额'
    },
    deviation: {
        label: '误差',
        placeholder: '误差'
    },
    preStartdt: {
        label: '结算开始时间',
        placeholder: '结算开始时间'
    },
    preEnddt: {
        label: '结算结束时间',
        placeholder: '结算结束时间'
    },
    settlePeriod: {
        label: '结算周期',
        placeholder: '结算周期'
    },
    remark: {
        label: '备注',
        placeholder: '备注'
    },
    settleYear: {
        label: '周期年份',
        placeholder: '周期年份'
    },
    settlePeriodDetail: {
        label: '具体周期',
        placeholder: '具体周期'
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
