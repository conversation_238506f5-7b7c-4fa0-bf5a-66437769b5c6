/*
 * @Description: 产品费率配置表单逻辑hooks
 * @Author: hongdong.xie
 * @Date: 2025-06-06 09:42:20
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 09:42:20
 * @FilePath: /ds-report-web/src/components/common/ProductFeeRateConfig/composables/useProductFeeRateForm.ts
 */

import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { DialogType, BaseFormData } from '../types'
import { resetFormData, copyFormData, formatDateFromBackend } from '../utils'
import { useProductFeeRateValidation } from './useProductFeeRateValidation'

/**
 * @description: 产品费率配置表单逻辑hooks
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {string} rateType 费率类型
 * @return {*}
 */
export const useProductFeeRateForm = (rateType: string) => {
    const { formRules, validateForm } = useProductFeeRateValidation()

    // 响应式数据
    const activeTab = ref('basic')
    const submitLoading = ref(false)
    const basicFormRef = ref()
    const tieredFormRef = ref()
    const tradingManagementFormRef = ref()
    const performanceFormRef = ref()

    // 表单数据
    const formData = reactive<BaseFormData>({
        id: undefined,
        filingCode: '',
        productCode: '',
        shareCode: '',
        productFullName: '',
        fofOnlyFlag: '0',
        payerFullName: '',
        productManager: '',
        signingDate: '',
        howbuySigningEntity: '',
        counterpartContact: '',
        durationDCoefficient: 0,
        durationDRemark: '',
        rateStartDate: '',
        rateEndDate: '2999-12-31',
        tieredRateType: '0',
        rateEffectiveType: '0',
        configLowerLimit: 0,
        configUpperLimit: 0,
        rateEffectiveStartDate: '',
        rateEffectiveEndDate: '',
        subscriptionRate: 0,
        subscriptionRemark: '',
        managementRate: 0,
        managementFormula: '',
        managementRemark: '',
        performanceSharingRate1: 0,
        performanceRate1: 0,
        performanceBenchmark1: '',
        performanceSharingRate2: 0,
        performanceRate2: 0,
        performanceBenchmark2: '',
        performanceSharingRate3: 0,
        performanceRate3: 0,
        performanceBenchmark3: '',
        performanceAccrualType: '',
        performanceAccrualForm: '',
        fixedAccrualMonthType: '',
        fixedAccrualDateType: '',
        fixedAccrualCalcType: '',
        redemptionHoldingDaysRule: '',
        dividendHoldingDaysRule: '',
        performanceFormula: '',
        shareLockType: '',
        shareLockDays: 0,
        fundClosedType: '',
        fundClosedDays: 0,
        noAccrualNavBenchmark: 0,
        performanceRemark: '',
        redemptionRate1: 0,
        redemptionHoldingDays1: 0,
        redemptionRate2: 0,
        redemptionHoldingDays2: 0,
        redemptionRate3: 0,
        redemptionHoldingDays3: 0,
        redemptionRate4: 0,
        redemptionHoldingDays4: 0,
        redemptionFormula: '',
        redemptionHoldingCalcRule: '',
        redemptionSpecialRule: '',
        redemptionRemark: '',
        rateType: rateType
    })

    // 表单选项数据
    const selectOptions = reactive({
        fofOnlyFlag: [
            { key: '0', label: '否' },
            { key: '1', label: '是' }
        ],
        tieredRateType: [
            { key: '0', label: '不涉及' },
            { key: '1', label: '存量市值' },
            { key: '2', label: '存量份额' },
            { key: '3', label: '交易日' },
            { key: '4', label: '累计购买金额' },
            { key: '5', label: '累计购买净额' }
        ],
        rateEffectiveType: [
            { key: '0', label: '不涉及' },
            { key: '1', label: '次日生效' },
            { key: '2', label: '次月生效' },
            { key: '3', label: '次季度生效' },
            { key: '4', label: '次年生效' },
            { key: '5', label: '触发即直接改所在结算周期的费率' }
        ],
        redemptionHoldingCalcRule: [
            { key: '1', label: '自然日' },
            { key: '2', label: '工作日' }
        ],
        performanceAccrualType: [
            { key: '1', label: '固定计提' },
            { key: '2', label: '浮动计提' }
        ],
        performanceAccrualForm: [
            { key: '1', label: '按月计提' },
            { key: '2', label: '按季计提' },
            { key: '3', label: '按年计提' }
        ],
        fixedAccrualMonthType: [
            { key: '1', label: '每月' },
            { key: '2', label: '指定月份' }
        ],
        fixedAccrualDateType: [
            { key: '1', label: '月末' },
            { key: '2', label: '指定日期' }
        ],
        fixedAccrualCalcType: [
            { key: '1', label: '自然日' },
            { key: '2', label: '工作日' }
        ],
        shareLockType: [
            { key: '1', label: '无锁定期' },
            { key: '2', label: '有锁定期' }
        ],
        fundClosedType: [
            { key: '1', label: '无封闭期' },
            { key: '2', label: '有封闭期' }
        ],
        redemptionSpecialRule: [
            { key: '0', label: '无特例' },
            { key: '1', label: '有特例' }
        ],
        redemptionHoldingDaysRule: [
            { key: '0', label: '不涉及' },
            { key: '1', label: '自然日' },
            { key: '2', label: '工作日' }
        ],
        dividendHoldingDaysRule: [
            { key: '0', label: '不涉及' },
            { key: '1', label: '自然日' },
            { key: '2', label: '工作日' }
        ],
        performanceFormula: [
            { key: '0', label: '不涉及' },
            { key: '1', label: '公式1' },
            { key: '2', label: '公式2' }
        ]
    })

    // 表单标签页配置
    const formTabs = [
        { label: '基础属性', name: 'basic', slotName: 'basic' },
        { label: '阶梯费率', name: 'tiered', slotName: 'tiered' },
        { label: '交易管理费率', name: 'tradingManagement', slotName: 'tradingManagement' },
        { label: '业绩报酬费率', name: 'performance', slotName: 'performance' }
    ]

    // 计算属性：是否显示配置上下限
    const showConfigLimits = computed(() => {
        return ['1', '2', '4', '5'].includes(formData.tieredRateType)
    })

    // 计算属性：是否显示费率生效日期
    const showRateEffectiveDates = computed(() => {
        return formData.tieredRateType === '3'
    })

    /**
     * @description: 重置表单数据
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @return {void}
     */
    const resetForm = (): void => {
        resetFormData(formData, rateType)
        activeTab.value = 'basic'
        
        // 重置表单验证状态
        nextTick(() => {
            basicFormRef.value?.clearValidate()
            tieredFormRef.value?.clearValidate()
            tradingManagementFormRef.value?.clearValidate()
            performanceFormRef.value?.clearValidate()
        })
    }

    /**
     * @description: 填充表单数据（编辑/复制新增时使用）
     * @author: hongdong.xie
     * @date: 2025-01-16 14:50:00
     * @param {any} data 数据源
     * @param {DialogType} dialogType 弹框类型
     * @return {void}
     */
    const fillFormData = (data: any, dialogType: DialogType): void => {
        if (!data) {
            return
        }

        console.log('📝 填充表单数据:', { data, dialogType })

        if (dialogType === 'copyAdd') {
            // 复制新增：复制数据但不包含ID
            copyFormData(data, formData)
        } else {
            // 编辑：直接赋值所有数据，但保持默认值
            Object.keys(formData).forEach(key => {
                if (Object.prototype.hasOwnProperty.call(data, key)) {
                    const fieldKey = key as keyof BaseFormData
                    const dataValue = data[key]

                    // 处理日期字段的格式转换
                    if (['signingDate', 'rateStartDate', 'rateEndDate', 'rateEffectiveStartDate', 'rateEffectiveEndDate'].includes(key)) {
                        (formData as any)[fieldKey] = formatDateFromBackend(dataValue)
                    } else {
                        // 如果后端数据为null或undefined，保持表单的默认值
                        if (dataValue !== null && dataValue !== undefined) {
                            (formData as any)[fieldKey] = dataValue
                        }
                        // 如果后端数据为null或undefined，不覆盖表单的默认值
                    }
                }
            })
        }

        console.log('📝 填充后的表单数据:', formData)
    }

    /**
     * @description: 费率配置类型变化处理
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {string} value 新值
     * @return {void}
     */
    const handleTieredRateTypeChange = (value: string): void => {
        console.log('🔄 费率配置类型变化:', value)
        
        // 根据类型重置相关字段
        if (!['1', '2', '4', '5'].includes(value)) {
            formData.configLowerLimit = 0
            formData.configUpperLimit = 0
        }
        
        if (value !== '3') {
            formData.rateEffectiveStartDate = ''
            formData.rateEffectiveEndDate = ''
        }
    }

    /**
     * @description: 赎回费率1变化处理
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {number | undefined} value 新值
     * @return {void}
     */
    const handleRedemptionRate1Change = (value: number | undefined): void => {
        console.log('🔄 赎回费率1变化:', value)

        // 如果赎回费率1为0或undefined，清空其他赎回费率相关字段
        if (value === 0 || value === undefined) {
            formData.redemptionHoldingDays1 = 0
            formData.redemptionRate2 = 0
            formData.redemptionHoldingDays2 = 0
            formData.redemptionRate3 = 0
            formData.redemptionHoldingDays3 = 0
            formData.redemptionRate4 = 0
            formData.redemptionHoldingDays4 = 0
            formData.redemptionFormula = ''
            formData.redemptionHoldingCalcRule = ''
            formData.redemptionSpecialRule = ''
            formData.redemptionRemark = ''
        }
    }

    /**
     * @description: 业绩报酬分成费率1变化处理
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {number | undefined} value 新值
     * @return {void}
     */
    const handlePerformanceRate1Change = (value: number | undefined): void => {
        console.log('🔄 业绩报酬分成费率1变化:', value)

        // 如果业绩报酬分成费率1为0或undefined，清空其他业绩报酬相关字段
        if (value === 0 || value === undefined) {
            formData.performanceRate1 = 0
            formData.performanceBenchmark1 = ''
            formData.performanceSharingRate2 = 0
            formData.performanceRate2 = 0
            formData.performanceBenchmark2 = ''
            formData.performanceSharingRate3 = 0
            formData.performanceRate3 = 0
            formData.performanceBenchmark3 = ''
            formData.performanceAccrualType = ''
            formData.performanceAccrualForm = ''
            formData.fixedAccrualMonthType = ''
            formData.fixedAccrualDateType = ''
            formData.fixedAccrualCalcType = ''
            formData.redemptionHoldingDaysRule = ''
            formData.dividendHoldingDaysRule = ''
            formData.performanceFormula = ''
            formData.shareLockType = ''
            formData.shareLockDays = 0
            formData.fundClosedType = ''
            formData.fundClosedDays = 0
            formData.noAccrualNavBenchmark = 0
            formData.performanceRemark = ''
        }
    }

    /**
     * @description: 表单验证
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @return {Promise<boolean>} 验证结果
     */
    const validateFormData = async (): Promise<boolean> => {
        try {
            // 验证所有表单
            const validationPromises = [
                basicFormRef.value?.validate(),
                tieredFormRef.value?.validate(),
                tradingManagementFormRef.value?.validate(),
                performanceFormRef.value?.validate()
            ].filter(Boolean)

            await Promise.all(validationPromises)

            // 执行自定义验证
            const customValidation = validateForm(formData)
            if (!customValidation.valid) {
                ElMessage({
                    type: 'error',
                    message: customValidation.message || '表单验证失败'
                })
                return false
            }

            return true
        } catch (error) {
            console.error('表单验证失败:', error)
            
            // 找到第一个验证失败的标签页并切换过去
            const tabOrder = ['basic', 'tiered', 'tradingManagement', 'performance']
            for (const tab of tabOrder) {
                const formRef = tab === 'basic' ? basicFormRef.value :
                              tab === 'tiered' ? tieredFormRef.value :
                              tab === 'tradingManagement' ? tradingManagementFormRef.value :
                              performanceFormRef.value

                if (formRef) {
                    try {
                        await formRef.validate()
                    } catch {
                        activeTab.value = tab
                        
                        // 滚动到第一个错误字段
                        nextTick(() => {
                            const errorField = formRef.$el.querySelector('.is-error')
                            if (errorField) {
                                errorField.scrollIntoView({ behavior: 'smooth', block: 'center' })
                                const input = errorField.querySelector('input, textarea, .el-select')
                                if (input) {
                                    input.focus()
                                }
                            }
                        })
                        break
                    }
                }
            }

            return false
        }
    }

    return {
        // 响应式数据
        activeTab,
        submitLoading,
        basicFormRef,
        tieredFormRef,
        tradingManagementFormRef,
        performanceFormRef,
        formData,
        selectOptions,
        formTabs,
        formRules,

        // 计算属性
        showConfigLimits,
        showRateEffectiveDates,

        // 方法
        resetForm,
        fillFormData,
        handleTieredRateTypeChange,
        handleRedemptionRate1Change,
        handlePerformanceRate1Change,
        validateFormData
    }
}
