/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'

/**
 * @description: table表格数据
 * @return {*}
 */
export const custHkInfoAllColumn: TableColumnItem[] = [
    {
        label: '客户姓名(香港)',
        key: 'hkCustName',
        width: 100
    },
    {
        label: '香港客户号',
        key: 'hkCustNo',
        width: 90
    },
    {
        label: '一账通号',
        key: 'hboneNo',
        width: 100
    },
    {
        label: '投顾客户号',
        key: 'conscustno',
        width: 100
    },
    {
        label: '客户类型',
        key: 'invstType',
        width: 90
    },
    {
        label: '投资者类型（香港）',
        key: 'investorQualification',
        width: 100
    },
    {
        label: '香港客户状态',
        key: 'custStat',
        width: 100
    },
    {
        label: '风险等级',
        key: 'riskToleranceLevel',
        sortable: 'custom',
        width: 100
    },
    {
        label: '是否有衍生工具知识',
        key: 'derivativeKnowledge',
        width: 90
    },
    {
        label: '注册日期',
        key: 'regTime',
        sortable: 'custom',
        width: 100
    },
    {
        label: '香港开户日期',
        key: 'openDate',
        sortable: 'custom',
        width: 100
    },
    {
        label: '开户渠道',
        key: 'openChannel',
        width: 100
    },
    {
        label: '入金渠道',
        key: 'depositFundChannel',
        width: 100
    },
    {
        label: '当前投顾',
        key: 'consname',
        width: 100
    },
    {
        label: '当前投顾代码',
        key: 'conscode',
        width: 100
    },
    {
        label: '海外储蓄罐签约状态',
        key: 'signState',
        width: 100
    },
    {
        label: '海外储蓄罐签约日期',
        key: 'agreementSignDt',
        sortable: 'custom',
        width: 100
    },
    {
        label: '海外储蓄罐签署方式',
        key: 'agreementSignType',
        width: 100
    },
    {
        label: '投资者资质认证日期',
        key: 'investorQualificationDate',
        sortable: 'custom',
        width: 100
    },
    {
        label: '资产证明到期日期',
        key: 'assetCertExpiredDate',
        sortable: 'custom',
        width: 100
    },
    {
        label: '线上开户资料提交时间',
        key: 'submitTime',
        sortable: 'custom',
        width: 100
    },
    {
        label: '线上开户资料审核通过时间',
        key: 'passDt',
        sortable: 'custom',
        width: 100
    },
    {
        label: '开户申请成功日期',
        key: 'accountTime',
        sortable: 'custom',
        width: 100
    },
    {
        label: '开户入金审核提交时间',
        key: 'depositSubmitTime',
        sortable: 'custom',
        width: 100
    },
    {
        label: '开户入金审核通过时间',
        key: 'depositPassDt',
        sortable: 'custom',
        width: 100
    },
    {
        label: '风险测评日期',
        key: 'kycDt',
        sortable: 'custom',
        width: 100
    },
    {
        label: '风险测评到期日期',
        key: 'riskToleranceTerm',
        sortable: 'custom',
        width: 100
    },
    {
        label: '一级组织架构(当前投顾)',
        key: 'u1Name',
        width: 100
    },
    {
        label: '二级组织架构(当前投顾)',
        key: 'u2Name',
        width: 100
    },
    {
        label: '三级组织架构(当前投顾)',
        key: 'u3Name',
        width: 100
    },
    {
        label: '资产证明日期',
        key: 'assetCertDate',
        sortable: 'custom',
        width: 100
    },
    {
        label: '线上开户资料审核状态',
        key: 'txChkFlag',
        width: 100
    },
    {
        label: '销户日期',
        key: 'closeDate',
        sortable: 'custom',
        width: 100
    },
    {
        label: '休眠日期',
        key: 'dormantDate',
        sortable: 'custom',
        width: 100
    },
    {
        label: '首次打款不低1万美元日期',
        key: 'firstAckDt',
        sortable: 'custom',
        width: 100
    },
    {
        label: 'KPI达标日期',
        key: 'kpi2024Dt',
        sortable: 'custom',
        width: 100
    },
    {
        label: '投顾(KPI达标日)',
        key: 'consnameKpi',
        width: 100
    },
    {
        label: '一级组织架构(KPI)',
        key: 'u1NameKpi',
        width: 100
    },
    {
        label: '二级组织架构(KPI)',
        key: 'u2NameKpi',
        width: 100
    },
    {
        label: '三级组织架构(KPI)',
        key: 'u3NameKpi',
        width: 100
    },
    {
        label: '海外储蓄罐签署渠道',
        key: 'signChannel',
        width: 100
    },
    {
        label: '海外储蓄罐签约有效期',
        key: 'agreementSignExpiredDt',
        sortable: 'custom',
        width: 100
    },
    {
        label: '海外储蓄罐协议终止方式',
        key: 'agreementCancelType',
        width: 100
    },
    {
        label: '海外储蓄罐终止日期',
        key: 'agreementCancelDt',
        sortable: 'custom',
        width: 100
    },
    {
        label: '海外储蓄罐终止渠道',
        key: 'cancelChannel',
        width: 100
    }
]
