/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const resoCustAssignConRateTableColumn: TableColumnItem[] = [
    {
        key: 'origU1Name',
        label: '中心',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'origU2Name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'origU3Name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'origConsname',
        label: '投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'sumCust',
        label: '分配客户数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'sumSucDealCust',
        label: '成交客户数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'dealRate',
        label: '成交率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'aveTradePreiod',
        label: '平均成交周期（天）',
        width: 140,
        formatter: formatTableValue
    },
    {
        key: 'sumFristTradeAmount',
        label: '首次购买金额RMB(累计)',
        width: 160,
        formatter: formatTableValue
    },
    {
        key: 'aveFristTradeAmount',
        label: '首次购买金额RMB(人均)',
        width: 160,
        formatter: formatTableValue
    },
    {
        key: 'sumIsBindWeixin',
        label: '添加企微客户数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'isBindWeixinAve',
        label: '企微添加率',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
