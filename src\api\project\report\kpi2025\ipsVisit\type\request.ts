export interface QueryIpsVisitRequest {
    orgCode?: string // 部门编码
    consCode?: string // 投顾编码
    completeDateStart?: string // 完成日期开始
    completeDateEnd?: string // 完成日期结束
    isIpsVisitComplete?: string // 是否完成IPS面访
    page?: number // 页码
    rows?: number // 数据行数
}

export type ExportIpsVisitRequest = Omit<QueryIpsVisitRequest, 'page' | 'rows'>

export interface IpsVisitInitRequest {
    // 初始化接口暂无参数
}
