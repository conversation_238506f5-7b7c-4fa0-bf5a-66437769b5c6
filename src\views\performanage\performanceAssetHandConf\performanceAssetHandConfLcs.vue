<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 查询年份 -->
                <label-item :label="queryYear.label">
                    <el-date-picker
                        v-model="queryForm.queryYear"
                        type="year"
                        size="small"
                        :style="{ width: '100px' }"
                        placeholder="选择年"
                    >
                    </el-date-picker>
                </label-item>
                <!-- 业务类型下拉框 -->
                <label-item :label="busitype.label">
                    <crm-select
                        v-model="queryForm.busitype"
                        :placeholder="busitype.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="busitype.selectList"
                        :style="{ width: '100px' }"
                        @change="handlebusitype"
                    />
                </label-item>

                <!-- 审核状态 -->
                <label-item :label="auditState.label">
                    <crm-select
                        v-model="queryForm.auditState"
                        :placeholder="auditState.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="auditState.selectList"
                        :style="{ width: '100px' }"
                        @change="handleauditState"
                    />
                </label-item>
                <!-- 在职状态 -->
                <label-item :label="workState.label">
                    <crm-select
                        v-model="queryForm.workState"
                        :placeholder="workState.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="workState.selectList"
                        :style="{ width: '100px' }"
                        @change="handleworkState"
                    />
                </label-item>

                <!-- 投顾客户号 -->
                <label-item :label="conscustno.label">
                    <crm-input
                        v-model="queryForm.conscustno"
                        :placeholder="conscustno.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
                <!-- 客户名称 -->
                <label-item :label="custname.label">
                    <crm-input
                        v-model="queryForm.custname"
                        :placeholder="custname.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
                <!-- 产品名称 -->
                <label-item :label="fundname.label">
                    <crm-input
                        v-model="queryForm.fundname"
                        :placeholder="fundname.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>

                <!-- 投顾、管理层 -->
                <label-item label="管理层/投顾">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
            </template>
            <template #operationBtns>
                <span style="font-size: 12px; color: red"
                    >注： 仅「审核状态」=审核通过 的数据参与存续D计算</span
                >
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="exportHandle"
                    >导出</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    @select="changeSelectTable"
                    @selectionChange="changeSelectTable"
                >
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>
        <product-index
            v-if="dialogVisible"
            v-model="dialogVisible"
            :trans-data="stockObj"
            :is-asset-hand-conf-add="true"
            :former-organization-list="organizationList"
            :former-consult-list="consultList"
            :former-org-code="orgCodeDefault"
            :former-cons-code="consCodeDefault"
        >
        </product-index>
        <edit-product-index
            v-if="editDialogVisible"
            v-model="editDialogVisible"
            :trans-data="stockObj"
            :is-asset-hand-conf-add="true"
            :former-organization-list="organizationList"
            :former-consult-list="consultList"
            :former-org-code="orgCodeDefault"
            :former-cons-code="consCodeDefault"
        >
        </edit-product-index>
        <aduit-product-index
            v-if="aduitDialogVisible"
            v-model="aduitDialogVisible"
            :trans-data="stockObj"
            :is-asset-hand-conf-add="true"
            :former-organization-list="organizationList"
            :former-consult-list="consultList"
            :former-org-code="orgCodeDefault"
            :former-cons-code="consCodeDefault"
        >
        </aduit-product-index>
        <delete-product-index
            v-if="deleteStockVisiable"
            v-model="deleteStockVisiable"
            :trans-data="stockObj"
            :is-asset-hand-conf-add="true"
        >
        </delete-product-index>
        <log-product-index
            v-if="logStockVisiable"
            v-model="logStockVisiable"
            :trans-data="stockObj"
            :is-asset-hand-conf-add="true"
        >
        </log-product-index>
        <ExplainStock v-model="explainDialogVisiable"></ExplainStock>
    </div>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import { Plus, Download, InfoFilled } from '@element-plus/icons-vue'
    import { fetchRes } from '@/utils'
    import { dataList } from './data/labelData'
    import ProductIndex from '@/views/performanage/components/performanceAssetHandConfAdd.vue'
    import EditProductIndex from '@/views/performanage/components/performanceAssetHandConfEdit.vue'
    import AduitProductIndex from '@/views/performanage/components/performanceAssetHandConfAduit.vue'
    import LogProductIndex from '@/views/performanage/components/performanceAssetHandConfDealLog.vue'

    import ExplainStock from '@/views/performanage/components/explainStock.vue'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import { performanceAssetHandConfLCSTableColumn, showTableColumn } from './data/tableData'
    import { ResVO } from '@/type'
    import {
        performanceAssetHandConfLcsQuery,
        performanceAssetHandConfLcsExport,
        getAuth
    } from '@/api/project/performanage/performanceAssetHandConf/performanceAssetHandConfLcs'
    import { useStockListData } from '@/views/common/scripts/stockListData'
    import { usePermission } from '@/views/common/scripts/permission'
    const permissionStore = usePermission()
    const { getMenuRoles, isPremission } = permissionStore

    const route = useRoute()
    const { menuCode }: any = route.query || {}

    // 投顾管理层
    const stockListStore = useStockListData()
    const { getPageInit } = stockListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(stockListStore)

    const { conscustno, custname, fundname, busitype, workState, auditState, queryYear } = dataList

    const listLoading = ref<boolean>(false)
    const dialogVisible = ref<boolean>(false)
    const editDialogVisible = ref<boolean>(false)
    const aduitDialogVisible = ref<boolean>(false)
    const deleteStockVisiable = ref<boolean>(false)
    const logStockVisiable = ref<boolean>(false)
    const exportShow = ref<boolean>(false)
    const consStatus = ref<string>('1') //不包含离职人员
    const module = ref<string>('B140120')
    /**
     * @description: 获取菜单权限
     * @return {*}
     */
    const getMenuAuth = async () => {
        const res: any = await getAuth()
        const operateList = res.data
        exportShow.value = operateList?.some(
            (item: any) => item.operateName === '导出' && item.display === '1'
        )
    }
    /**
     * @description: 操作日志
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handledeaLLog = (val: any): void => {
        const { id } = val || {}
        stockObj.value = {
            id: id,
            title: '操作日志',
            type: 'log'
        }
        logStockVisiable.value = true
    }
    /**
     * @description: 删除
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleDelete = (val: any): void => {
        const { id } = val || {}
        stockObj.value = {
            id: id,
            title: '删除',
            type: 'delete'
        }
        deleteStockVisiable.value = true
    }
    const handleauditState = (val: string) => {
        queryForm.auditState = val
    }
    /**
     * @description: 修改
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleEdit = (val: any): void => {
        const { id } = val || {}
        stockObj.value = {
            title: '修改',
            id: id,
            type: 'edit'
        }
        editDialogVisible.value = true
    }
    /**
     * @description: 审核
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleAudit = (val: any): void => {
        const { id } = val || {}
        stockObj.value = {
            title: '审核',
            id: id,
            type: 'audt'
        }
        aduitDialogVisible.value = true
    }
    /**
     * @description: 编辑
     * @return {*}
     */
    const stockObj = ref({
        title: '',
        id: '',
        type: ''
    })

    /**
     * @description: 操作展示
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const operationShow = (val: any, type: string): boolean => {
        const { operateVoList } = val || { operateVoList: [] }
        return true
    }

    /**
     * @description: 说明展示隐藏
     * @param explainDialogVisiable 展示隐藏
     * @method handleExplain 触发方法
     * @return {*}
     */
    const explainDialogVisiable = ref<boolean>(false)
    const handleExplain = () => {
        explainDialogVisiable.value = true
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        auditState = ''
        queryYear = new Date()
        conscustno = ''
        custname = ''
        fundname = ''
        busitype = ''
        workState = ''
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
    }
    const queryForm = reactive(new QueryForm())
    // 筛选条件框的处理
    const handlebusitype = (val: string) => {
        queryForm.busitype = val
    }
    const handleworkState = (val: string) => {
        queryForm.workState = val
    }
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    class OptionProps {
        value = 'id'
        label = 'text'
        children = 'children'
    }

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            performanceAssetHandConfLCSTableColumn.map(item => item.key),
            performanceAssetHandConfLCSTableColumn
        )
    })
    //查詢
    const queryList = async () => {
        if (queryForm.queryYear === null) {
            ElMessage({
                message: '请先选择查询年份',
                type: 'warning',
                duration: 2000
            })
            return
        }
        listLoading.value = true
        const params = {
            flag: '1',
            auditState: queryForm.auditState,
            conscustno: queryForm.conscustno,
            custname: queryForm.custname,
            fundname: queryForm.fundname,
            busitype: queryForm.busitype,
            workState: queryForm.workState,
            queryYear: queryForm.queryYear,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            conscode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(performanceAssetHandConfLcsQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                tableData.value = rows
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const pdfUrl = ref<string>('')
    const exportList = async () => {
        const params = {
            flag: '1',
            auditState: queryForm.auditState,
            conscustno: queryForm.conscustno,
            custname: queryForm.custname,
            fundname: queryForm.fundname,
            busitype: queryForm.busitype,
            workState: queryForm.workState,
            queryYear: queryForm.queryYear,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            conscode: queryForm.orgvalue.consCode ?? consCodeDefault.value
        }
        const res: any = await performanceAssetHandConfLcsExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }
    // 控制新增和修改弹框页面显示
    const handleAdd = (): void => {
        stockObj.value = {
            title: '新增',
            id: '',
            type: 'add'
        }
        dialogVisible.value = true
    }
    // table选中数组
    const tableSelectList = ref<object[]>([])
    // table选中事件
    const changeSelectTable = (sel: ResVO[]): void => {
        // 默认选择按成立时间排序
        tableSelectList.value = unref(sel)
            .map((item: any) => {
                return item
            })
            .filter(item => item)
    }

    const aduitdata = ref({
        taskId: ''
    })

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [orgCodeDefault, consCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.orgvalue.orgCode = orgCodeDefault.value
                queryForm.orgvalue.consCode = consCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    /**
     * @description 提示
     */
    onMounted(() => {
        getPageInit(module.value)
    })
    onBeforeMount(() => {
        getMenuAuth()
    })
</script>
<style lang="less" scoped></style>
