/*
 * @Description: 定义搜索的label列表
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:36:23
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/labelData.ts
 *
 */
export const dataList = {
    conscustno: {
        label: '投顾客户号',
        placeholder: '输入投顾客户号'
    },
    custname: {
        label: '客户姓名',
        placeholder: '输入客户姓名'
    },
    fundname: {
        label: '产品名称',
        placeholder: '输入产品代码'
    },
    // 0-家办、1-综拓、2-创新、3-份额转让、4-其他
    busitype: {
        label: '业务类型',
        placeholder: '请选择业务类型',
        selectList: [
            {
                key: '0',
                label: '家办'
            },
            {
                key: '1',
                label: '综拓'
            },
            {
                key: '2',
                label: '创新'
            },
            {
                key: '3',
                label: '份额转让'
            },
            {
                key: '4',
                label: '其他'
            }
        ]
    },
    // 0-离职，1-在职
    workState: {
        label: '在职状态',
        placeholder: '请选择在职状态',
        selectList: [
            {
                key: '0',
                label: '离职'
            },
            {
                key: '1',
                label: '在职'
            }
        ]
    },
    // 0-待审核，1-审核通过,2-审核不通过
    auditState: {
        label: '审核状态',
        placeholder: '请选择审核状态',
        selectList: [
            {
                key: '0',
                label: '待审核'
            },
            {
                key: '1',
                label: '审核通过'
            },
            {
                key: '2',
                label: '审核未通过'
            }
        ]
    },
    // 1-公司资源，2-投顾资源
    firstSource: {
        label: '第一资源',
        placeholder: '请选择第一资源',
        selectList: [
            {
                key: '1',
                label: '公司资源'
            },
            {
                key: '2',
                label: '投顾资源'
            }
        ]
    },
    // 0-理财师，1-分总、2-区域执行副总、3-区域总
    conslevel: {
        label: '层级',
        placeholder: '请选择层级',
        selectList: [
            {
                key: '0',
                label: '投资顾问'
            },
            {
                key: '1',
                label: '分总'
            },
            {
                key: '2',
                label: '区域执行副总'
            },
            {
                key: '3',
                label: '区域总'
            }
        ]
    },
    recordDate: {
        label: '录入日期',
        placeholder: ['开始日期', '结束日期']
    },
    queryYear: {
        label: '查询年份',
        placeholder: ['年份']
    },
    orgData: {
        label: '投顾管理层',
        placeholder: '请选择所在组织'
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
