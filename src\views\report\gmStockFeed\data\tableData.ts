/*
 * @Description: table表格展示
 * @Author: gang.zou
 * @Date: 2024-04-17 17:14:42
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, formatNumber } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const gmStockFeedTableColumn: TableColumnItem[] = [
    {
        key: 'curCenterName',
        label: '中心（当前）',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'curUporgname',
        label: '区域（当前）',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'curOrgname',
        label: '分公司（当前）',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'curConsName',
        label: '投顾（当前）',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'curConsCode',
        label: '投顾code（当前）',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '投顾（时点）',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'consCode',
        label: '投顾code（时点）',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'custName',
        label: '客户姓名',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'conscustNo',
        label: '投顾客户号',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'avgAsset',
        label: '存量（月日均）',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'avgAssetD',
        label: '存量（月日均）*产品存续D',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'monthAsset',
        label: '存量（月末）',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'monthAssetD',
        label: '存量（月末）*产品存续D',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'manageCoefficient',
        label: '管理系数',
        minWidth: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
