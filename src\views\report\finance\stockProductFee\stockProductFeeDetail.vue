<template>
    <div
        style="
            padding-top: 20px;
            padding-bottom: 10px;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
        "
    >
        股权产品后端费用明细
    </div>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_title_wraper"
            :show-operation-left="true"
            :show-search-area="false"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #operationBtns>
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="exportHandle"
                    >导出</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                >
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination
                    :page="pageObj"
                    :page-sizes="[20, 50, 100]"
                    :total="pageObj.total"
                    @change="handleCurrentChange"
                />
            </template>
        </table-wrapper>
    </div>
</template>

<script lang="ts" setup>
    import { Download } from '@element-plus/icons-vue'
    import { fetchRes } from '@/utils'
    import { stockProductFeeDetailTableColumn, showTableColumn } from './data/detailTableData'
    import { useRoute } from 'vue-router'
    import {
        StockProductFeeDetailQuery,
        StockProductFeeDetailExport
    } from '@/api/project/report/finance/stockProductFee/stockProductFee'

    const module = ref<string>('B071215')
    const exportShow = ref<boolean>(true)

    const listLoading = ref<boolean>(false)

    /**
     * @description: 分页数据
     * @return {*}
     */
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 100
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    const queryParams = ref<any>({})

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            stockProductFeeDetailTableColumn.map(item => item.key),
            stockProductFeeDetailTableColumn
        )
    })

    //查詢
    const queryList = async () => {
        listLoading.value = true
        queryParams.value.page = pageObj.value.page
        queryParams.value.rows = pageObj.value.size
        fetchRes(StockProductFeeDetailQuery(queryParams.value), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                tableData.value = rows
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    const initData = async () => {
        const route = useRoute()

        queryParams.value = {
            startDt: route.query.startDt,
            endDt: route.query.endDt,
            fundCode: route.query.fundCode,
            fundMan: route.query.fundMan,
            feeType: route.query.feeType,
            fundName: route.query.fundName,
            custName: route.query.custName,
            conscustNo: route.query.conscustNo,
            auditState: route.query.auditState
        }

        queryList()
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }
    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const res: any = await StockProductFeeDetailExport(queryParams.value)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }
    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        initData()
    })
</script>
<style lang="less" scoped></style>
