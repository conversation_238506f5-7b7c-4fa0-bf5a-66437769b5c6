/*
 * @Description: 弹框类hooks
 * @Author: chaohui.wu
 * @Date: 2023-09-18 17:12:49
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-22 18:03:40
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/scripts/hooks/useDiaVisible.ts
 *
 */

import { message, messageBox, zeroTrans } from '@/utils'
import type { FormInstance, FormRules } from 'element-plus'

export function useDiaVisible({ props, emit, formList }: any) {
    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    /**
     * @description: 获取接口请求传参
     * @param {*} flag
     * @return {*}
     */
    const getParams = (flag?: string | undefined) => {
        const {
            id,
            audtAdvice,
            constObj,
            formerConstObj,
            calDt,
            formerCalRate,
            newlyCalRate,
            lockDurationAmt,
            ...otherParams
        } = formList.value || {}
        const { orgCode, consCode } = constObj || {}
        const { orgCode: formerOrgCode, consCode: formerConsCode } = formerConstObj || {}
        if (moduleTyping.value.isEdit) {
            return {
                ...otherParams,
                id,
                newlyConsCode: consCode,
                newlyOrgCode: orgCode,
                formerConsCode,
                formerOrgCode,
                formerCalRate: zeroTrans({ val: formerCalRate }),
                newlyCalRate: zeroTrans({ val: newlyCalRate }),
                lockDurationAmt: zeroTrans({ val: Number(lockDurationAmt) * 10000 })
            }
        } else if (moduleTyping.value.isAdd) {
            return {
                ...otherParams,
                newlyConsCode: consCode,
                newlyOrgCode: orgCode,
                formerConsCode,
                formerOrgCode,
                formerCalRate: zeroTrans({ val: formerCalRate }),
                newlyCalRate: zeroTrans({ val: newlyCalRate }),
                lockDurationAmt: zeroTrans({ val: Number(lockDurationAmt) * 10000 })
            }
        } else if (moduleTyping.value.isCheck) {
            return {
                id,
                advice: audtAdvice,
                auditPass: flag || ''
            }
        }
        return {}
    }

    /**
     * @description: 当前模块的状态,是否是编辑,预览,审核
     * @param {*} computed
     * @return {*}
     */
    const moduleTyping = computed(() => {
        const curType = {
            isAdd: false,
            isEdit: false,
            isView: false,
            isCheck: false
        }
        const { type } = props.transData || {}
        switch (type) {
            case 'add':
                Object.assign(curType, { isAdd: true })
                break
            case 'edit':
                Object.assign(curType, { isEdit: true })
                break
            case 'view':
                Object.assign(curType, { isView: true })
                break
            case 'audt':
                Object.assign(curType, { isCheck: true })
                break
            default:
                Object.assign(curType, {})
                break
        }
        return curType
    })

    /**
     * @description: 校验逻辑
     * @return {*}
     */
    const verifyMethods = ({
        flag,
        defaultFileDes = '请求失败请重试!',
        defaultSuccessDes = '请求失败请重试!',
        description,
        formEl
    }: {
        flag: string
        defaultFileDes?: string
        defaultSuccessDes?: string
        description: string
        formEl: FormInstance | undefined
    }) => {
        switch (flag) {
            case '0000':
                emit('callBack')
                dialogVisible.value = false
                // 重置
                formEl?.resetFields()
                message({
                    type: 'success',
                    message: description || defaultSuccessDes
                })
                break
            case '0001':
                break
            default:
                message({
                    type: 'error',
                    message: description || defaultFileDes
                })
                break
        }
    }

    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = ({ msgInfo = '', callBack }: { msgInfo?: string; callBack: Function }) => {
        // message({
        //     type: 'info',
        //     message: msgInfo ?? '关闭'
        // })
        dialogVisible.value = false
        callBack && callBack()
    }
    return { dialogVisible, moduleTyping, getParams, verifyMethods, handleClose }
}
