/*
 * @Description: 模版组件
 * @Author: chaohui.wu
 * @Date: 2024-09-27 11:17:43
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-12 15:32:26
 * @FilePath: /ds-report-web/src/views/modBusiness/api/index.ts
 *  
 */
import { axiosRequest } from '@/utils/index'
export function getAllTemplate(param:any) {
    return axiosRequest({
        url: '/userShowColumn/getAllTemplate',
        method: 'get',
        params: param
        // apiConfig: { headers: { "Content-Type": "multipart/form-data" } }
    })
}

export function addTemplate(param:any) {
    return axiosRequest({
        url: '/userShowColumn/addTemplate',
        method: 'post',
        data: param
        // apiConfig: { headers: { "Content-Type": "multipart/form-data" } }
    })
}

export function modifyTemplate(param:any) {
    return axiosRequest({
        url: '/userShowColumn/modifyTemplate',
        method: 'post',
        data: param
        // apiConfig: { headers: { "Content-Type": "multipart/form-data" } }
    })
}

export function deleteTemplate(param:any) {
    return axiosRequest({
        url: '/userShowColumn/delete',
        method: 'delete',
        data: param
        // apiConfig: { headers: { "Content-Type": "multipart/form-data" } }
    })
}

export function setColumnTemplate(param:any) {
    return axiosRequest({
        url: '/userShowColumn/setColumn',
        method: 'put',
        data: param
        // apiConfig: { headers: { "Content-Type": "multipart/form-data" } }
    })
}

export function queryModelDetail(param: Record<string, any>) {
    return axiosRequest({
        url: '/userShowColumn/get',
        method: 'get',
        params: param
        // apiConfig: { headers: { "Content-Type": "multipart/form-data" } }
    }) as Promise<any>
}
