<!--
 * @Description: 说明弹框
 * @Author: danpeng.gao
 * @Date: 2023-03-23 13:26:04
 * @LastEditors: danpeng.gao
 * @LastEditTime: 2023-09-21 13:39:50
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="800px"
        title="统计说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <template #default>
            <b>此报表用于统计公司分配的leads客户、公募20万客户，分配后的成交情况、企微添加情况。</b
            ><br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span
                >(不含“公司分配资源客户明细”表中字段“是否重复=是”的数据)</span
            >
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span>①分配客户数：统计分配到的客户数（不含重复客户）</span>
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span
                >②成交客户数：购买私募产品的客户数，如成交投顾不是分配投顾/当前投顾，正常计入成交客户数（按交易确认计入）</span
            >
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span>③成交率 ：成交客户数/分配客户数</span>
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span
                >④平均成交周期 （天）：成交客户的成交周期（天）合计天数/成交客户数</span
            >
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span
                >⑤首次购买金额RMB（累计）：成交客户首次购买私募产品的金额合计，如是外币转换为人民币</span
            >
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span
                >⑥首次购买金额RMB（人均）：首次购买金额（累计）/成交客户数</span
            >
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span>⑦添加企微客户数：添加企业微信的客户数</span>
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;<span>⑧企微添加率：添加企微客户数/分配客户数”</span>
            <br />
        </template>

        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">关闭</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { useVisible } from '@/views/common/scripts/useVisible'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()

    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })
</script>

<style lang="less" scoped>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 8px;
        text-align: left;
        border: 1px solid black;
    }

    th {
        background-color: #f2f2f2;
    }

    .middle-content {
        padding: 10px 0;
    }
</style>
