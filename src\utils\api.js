import axios from 'axios'

const base = ''

export const responseCode = {
    SUCCESS: '0000', // 成功
    CRMSUCCESS: 'C010000', // 成功
    RELOGIN: '0004', // 重新登录
    UNLOGIN: 'C010003', // 未登录
    SYS_ERROR: '9999', // 系统异常
    CRMSYS_ERROR: 'C019999', // 系统异常
    CRMSYS_NOACCESS: 'C010007', // 系统异常
    SYS_FILED: '0001', // 系统异常
    VERIFY_ERROR: '1000', // 校验错误
    NOT_FOUND: '4000', // 不存在
    RUN_ERROR: '5000' // 执行错误
}
export const postRequest = (url, params) => {
    return axios({
        method: 'post',
        url: `${base}${url}`,
        data: params
    })
}
export const getRequest = (url, params) => {
    return axios({
        method: 'get',
        url: `${base}${url}`,
        data: params
    })
}
