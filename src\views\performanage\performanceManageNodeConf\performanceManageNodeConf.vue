<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 管理人员 -->
                <label-item label="管理人员">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <!-- 考核周期 -->
                <label-item :label="periodExplain.label">
                    <crm-select
                        v-model="queryForm.periodExplain"
                        :placeholder="periodExplain.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="periodExplain.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 层级下拉框 -->
                <label-item :label="conslevel.label">
                    <crm-select
                        v-model="queryForm.conslevel"
                        :placeholder="conslevel.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="conslevel.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 职级下拉框 -->
                <label-item :label="curMonthLevel.label">
                    <crm-select
                        v-model="queryForm.curMonthLevel"
                        :placeholder="curMonthLevel.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="curMonthLevelDataList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 投顾code -->
                <label-item :label="userId.label">
                    <crm-input
                        v-model="queryForm.userId"
                        :placeholder="userId.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 考核节点 -->
                <label-item :label="exaimneNode.label">
                    <date-range
                        v-model="queryForm.exaimneNode"
                        show-format="YYYY-MM-DD"
                        :placeholder="exaimneNode.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 计入分公司 -->
                <label-item :label="calcDepartment.label" >
                    <crm-select
                        v-model="queryForm.calcDepartment"
                        :placeholder="calcDepartment.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="calcDepartment.selectList"
                        :style="{ width: '160px' }"
                    />
                </label-item>
                <!-- 计入净新增分公司 -->
                <label-item :label="calcNewDepartment.label">
                    <crm-select
                        v-model="queryForm.calcNewDepartment"
                        :placeholder="calcNewDepartment.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="calcNewDepartment.selectList"
                        :style="{ width: '170px' }"
                    />
                </label-item>
                <!-- 是否考核 -->
                <label-item :label="status.label">
                    <crm-select
                        v-model="queryForm.status"
                        :placeholder="status.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="status.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
            </template>
            <template #operationLeft>
                <Text 
                    type="info"
                    size="small" 
                    style="font-size: 14px;color: red;padding-left: 10px;"
                    >1、当考核周期为观察期时，开始时间必填；2、开始时间为观察期数据开始计算的时间（以月为单位）
                </Text>
            </template>
            
            <template #operationBtns>
                
                <Text 
                    v-if="exceptionTipShow"
                    type="info"
                    size="small" 
                    style="font-size: 14px;color: blue;"
                    >存在配置异常数据！
                </Text>
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="exportHandle"
                    >导出</crm-button
                >
                <crm-button
                    v-if="batchModifyShow"
                    size="small"
                    :radius="true"
                    :icon="InfoFilled"
                    plain
                    @click="handleBatchEdit"
                    >批量修改</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="true"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    :pops="{selectFixed:'left'}"
                    :row-class-name="handleRowClassName"
                    @selectionChange="handleSelectionChange"
                >
                    <template #operation="{ scope }">
                        <el-button
                            v-if="modifyShow"
                            size="small"
                            :text="true"
                            link
                            @click="handleEdit(scope.row)"
                            >修改</el-button
                        >
                        <el-button
                            v-if="adjustStatusShow"
                            size="small"
                            :text="true"
                            link
                            @click="handleAdjustStatus(scope.row)"
                            >调整是否考核</el-button
                        >
                    </template>
                </base-table>
            </template>
        </table-wrapper>
        
        <edit-performance-index
            v-if="editDialogVisible"
            v-model="editDialogVisible"
            :trans-data="stockObj"
            :organization-list="organizationList"
            :consult-list="consultList"
            :org-code="orgCodeDefault"
            :cons-code="consCodeDefault"
            @callback="handleClose()"
        >
        </edit-performance-index>
        <batch-edit-performance-index
            v-if="batchEditDialogVisible"
            v-model="batchEditDialogVisible"
            :trans-data="batchObj"
            :organization-list="organizationList"
            :consult-list="consultList"
            :org-code="orgCodeDefault"
            :cons-code="consCodeDefault"
            @callback="handleClose()"
        >
        </batch-edit-performance-index>
    </div>
</template>

<script lang="ts" setup>
    import { Plus, Download, InfoFilled } from '@element-plus/icons-vue'
    import { fetchRes,messageBox } from '@/utils'
    import { dataList } from './data/labelData'
    import { ElMessage } from 'element-plus'

    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import EditPerformanceIndex from '@/views/performanage/performanceManageNodeConf/components/performanceManageNodeConfEdit.vue'
    import BatchEditPerformanceIndex from '@/views/performanage/performanceManageNodeConf/components/performanceManageNodeConfBatchEdit.vue'
    import { performanceManageNodeConfTableColumn, showTableColumn } from './data/tableData'
    import {
        performanceManageNodeConfQuery,
        performanceManageNodeConfExport,
        performanceManageNodeConfInit,
        performanceManageNodeConfAdjustStatus
    } from '@/api/project/performanage/performanceManageNodeConf/performanceManageNodeConf'
    import { MANAGE_NODE_CONF_OPER_PERMISSION } from '@/constant/performanceConst'
    import { getMenuPermission } from '@/api/project/common/common'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const {
        userId,
        status,
        periodExplain,
        conslevel,
        calcDepartment,
        calcNewDepartment,
        exaimneNode,
        curMonthLevel
    } = dataList


    const handleRowClick = (row: any) => {
        console.log(row)
    }
    const listLoading = ref<boolean>(false)
    const dialogVisible = ref<boolean>(false)
    const editDialogVisible = ref<boolean>(false)
    const batchEditDialogVisible = ref<boolean>(false)
    const consStatus = ref<string>('0') //包含离职人员

    const exportShow = ref<boolean>(false)
    const batchModifyShow = ref<boolean>(true)
    const modifyShow = ref<boolean>(true)
    const adjustStatusShow = ref<boolean>(false)

    const exceptionTipShow = ref<boolean>(false)

    const curMonthLevelDataList = ref([])
    //批量修改选中的id
    let checkIds = [] as string[] 

    const handleRowClassName = (rowObj:any, rowIndex:number) => {
        const { row } = rowObj
        if(row && row.ifExceptionData === '1') {
            return 'highlight-bg'
        }
    }

    const module = ref<string>('B140703')
    /**
     * @description: 关闭弹框刷新数据
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleClose = (): void => {
        queryList()
    }

    /**
     * @description: 删除
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleAdjustStatus = (val: any): void => {
        const { id } = val || {}
        
        messageBox(
            {
                confirmBtn: '确定',
                content: `确认要调整这条记录吗?`
            },
            () => {
                confirmAdjust(id)
            },
            () => false
        )
    }

    const confirmAdjust = async (id:any) => {
        const params = {
            id: id
        }
        listLoading.value = true
        fetchRes(performanceManageNodeConfAdjustStatus(params), {
            successCB: (resObj: any) => {
                queryList()
                ElMessage({
                    type: 'success',
                    message: '成功'
                })
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }
    

    /**
     * @description: 编辑
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleEdit = (val: any): void => {
        const { id } = val || {}
        stockObj.value = {
            id: id,
            type: 'edit',
            title: '修改'
        }
        editDialogVisible.value = true
    }

    const handleBatchEdit = (val: any): void => {
        if(checkIds.length === 0) {
            ElMessage({
                type: 'info',
                message: '请先勾选需要批量修改的数据!'
            })
            return
        }
        batchObj.value = {
            ids: checkIds,
            type: 'edit',
            title: '批量修改'
        }
        batchEditDialogVisible.value = true
    }

    const handleSelectionChange = (sel: any): void => {
        checkIds = sel.map((item: any) => item.id)
        console.log(checkIds)
    }

    /**
     * @description: 编辑
     * @return {*}
     */
    const stockObj = ref({
        id: '',
        type: '',
        title: ''
    })
    
    /**
     * @description: 批量修改
     * @return {*}
     */
     const batchObj = ref({
        ids: [] as string[],
        type: '',
        title: ''
    })

    /**
     * @description: 操作展示
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const operationShow = (val: any, type: string): boolean => {
        const { operateVoList } = val || { operateVoList: [] }
        return operateVoList?.some((item: any) => item.operateCode === type)
        // return true
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        userId = ''
        status = ''
        periodExplain = ''
        conslevel = ''
        calcDepartment = ''
        calcNewDepartment = ''
        curMonthLevel = ''
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        exaimneNode = {
            startDate: '',
            endDate: ''
        }
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])


    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            performanceManageNodeConfTableColumn.map(item => item.key),
            performanceManageNodeConfTableColumn
        )
    })
    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            flag: '0',
            userId: queryForm.userId,
            calcNewDepartment: queryForm.calcNewDepartment,
            status: queryForm.status,
            periodExplain: queryForm.periodExplain,
            calcDepartment: queryForm.calcDepartment,
            userLevel: queryForm.conslevel,
            curMonthLevel:queryForm.curMonthLevel,
            exaimneNodeStart: queryForm.exaimneNode.startDate,
            exaimneNodeEnd: queryForm.exaimneNode.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value
        }
        fetchRes(performanceManageNodeConfQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { list,haveExceptionData } = resObj
                tableData.value = list
                if (haveExceptionData) {
                    exceptionTipShow.value = true
                } else {
                    exceptionTipShow.value = false
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }


    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const params = {
            flag: '0',
            userId: queryForm.userId,
            calcNewDepartment: queryForm.calcNewDepartment,
            status: queryForm.status,
            periodExplain: queryForm.periodExplain,
            calcDepartment: queryForm.calcDepartment,
            userLevel: queryForm.conslevel,
            curMonthLevel:queryForm.curMonthLevel,
            exaimneNodeStart: queryForm.exaimneNode.startDate,
            exaimneNodeEnd: queryForm.exaimneNode.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value
        }
        const res: any = await performanceManageNodeConfExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }


    const initData = async () => {
        
        const params = {
            menuCode: module.value
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if(rows.length > 0) {
                    rows.forEach((item: any) => {
                        if(item.operateCode === MANAGE_NODE_CONF_OPER_PERMISSION.EXPORT && item.display === '1') {
                            exportShow.value = true
                        }
                        if(item.operateCode === MANAGE_NODE_CONF_OPER_PERMISSION.BATCH_MODIFY && item.display === '1') {
                            batchModifyShow.value = true
                        }
                        if(item.operateCode === MANAGE_NODE_CONF_OPER_PERMISSION.MODIFY && item.display === '1') {
                            modifyShow.value = true
                        }
                        if(item.operateCode === MANAGE_NODE_CONF_OPER_PERMISSION.ADJUST_STATUS && item.display === '1') {
                            adjustStatusShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })

        fetchRes(performanceManageNodeConfInit(), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { curMonthLevelList } = resObj
                curMonthLevelDataList.value = curMonthLevelList
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [orgCodeDefault, consCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.orgvalue.orgCode = orgCodeDefault.value
                queryForm.orgvalue.consCode = consCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        console.log('onMounted')
        initData()
        fetchConsOrgList('',module.value)
    })
</script>
<style lang="less" scoped>
    
</style>
