<!--
 * @Description: tabs样式
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-19 11:11:14
 * @FilePath: /crm-asset-web/src/components/moduleBase/CrmDialogTabs.vue
 * @Link: https://element-plus.gitee.io/en-US/component/tabs.html#basic-usage
 * @eg: className: crm-tabs-card1 | wrapper_tabs_panel
-->

<template>
    <el-tabs v-bind="$attrs" :class="className" :style="`backgroundColor: ${backgroundColor};`">
        <slot />
    </el-tabs>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmDialogTabs',
        props: {
            backgroundColor: {
                type: String,
                default: '#ffffff'
            },
            className: {
                type: [String],
                default: 'crm-tabs-card1'
            },
            noBorder: {
                type: [<PERSON>olean],
                default: true
            }
        }
    })
</script>

<style lang="less">
    .crm-tabs-card1,
    .wrapper_tabs_panel {
        position: relative;
        min-width: 900px;

        .el-dialog__body {
            padding: 10px 20px 46px;
            // padding: 10px 0 0 0;
        }

        .el-tabs {
            --el-tabs-header-height: 27px;
        }

        .el-tabs--card > .el-tabs__header {
            margin: 0;

            .el-tabs__nav {
                border: none;
            }

            .el-tabs__item {
                box-sizing: border-box;
                height: 27px;
                padding: 5px 20px;
                margin-right: 10px;
                font-family: 'Microsoft YaHei', '微软雅黑';
                font-size: 13px;
                font-weight: normal;
                line-height: 18px;
                color: @font_color;
                list-style: none;
                border: 1px solid #b5b4ba;
                // border: 1px solid #979797;
                border-radius: 4px 4px 0 0;
                transition: padding var(--el-transition-duration)
                    var(--el-transition-function-ease-in-out-bezier);

                &.is-active {
                    font-family: 'Microsoft YaHei', '微软雅黑';
                    font-size: 13px;
                    font-weight: 400;
                    color: @font_color_01;
                    background-color: @theme_main;
                    transition: padding var(--el-transition-duration)
                        var(--el-transition-function-ease-in-out-bezier);
                }

                &:first-child {
                    border: 1px solid #b5b4ba;
                }

                &.is-active {
                    border: 1px solid @theme_main;
                }
            }
        }

        .el-tabs__content {
            position: static;
        }
    }

    .wrapper_tabs_panel {
        .el-tabs--card > .el-tabs__header {
            border: none;

            .el-tabs__item {
                font-weight: normal;
                line-height: 17px;

                &.is-active {
                    font-size: 13px;
                    font-weight: normal;
                }
            }
        }
    }
</style>
