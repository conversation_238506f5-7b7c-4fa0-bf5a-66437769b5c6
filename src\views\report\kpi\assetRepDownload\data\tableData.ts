/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const assetRepDownloadTableColumn: TableColumnItem[] = [
    {
        key: 'startDt',
        label: '开始时间',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'endDt',
        label: '结束时间',
        minWidth: 100,
        formatter: formatTableValue
    },
    {
        key: 'u1Name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consCode',
        label: '员工编码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetTotalCust',
        label: '存量客户_总客户数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetDownloadCust',
        label: '完成报告制作的存量客户数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetCovRate',
        label: '存量客户-覆盖率',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
