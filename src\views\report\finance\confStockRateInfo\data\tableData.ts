/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const confStockRateInfoTableColumn: TableColumnItem[] = [
    {
        key: 'fundCode',
        label: '产品代码',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundName',
        label: '产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'bottomFundName',
        label: '底层产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'productRange',
        label: '产品范围',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'term',
        label: '期限',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'contractDate',
        label: '合同日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundType',
        label: '股权产品类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'collectSales',
        label: '收款销量(万元)',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'sales',
        label: '销量(万元)',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'subRate',
        label: '认购/销售服务费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'subLowerLimit',
        label: '分级认购金额下限',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'subUpLimit',
        label: '分级认购金额上限',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'subIncome',
        label: '认购收入',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'subPayDate',
        label: '认购支付时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fixedRate',
        label: '固定费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fixedPayFrequency',
        label: '固定支付频率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'periodRate1',
        label: '投资期费率1',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'investmentPeriod1',
        label: '投资期期限1',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'periodRate2',
        label: '投资期费率2',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'investmentPeriod2',
        label: '投资期期限2',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'quitRate',
        label: '退出期费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'quitPeriod',
        label: '退出期期限',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'quitRemark',
        label: '退出期备注',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'manageFormula',
        label: '管理费公式',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fixedManage',
        label: '固定管理费',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fixedPayDate',
        label: '固定管理费支付时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performance',
        label: '业绩提成',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceFrequency',
        label: '业绩提成支付频率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceSupple',
        label: '业绩提成补充',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performancePayDate',
        label: '业绩提成支付时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'investmentAfter',
        label: '投后',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'agarement',
        label: '协议主体',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'reviewState',
        label: '审核状态',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'reviewtime',
        label: '审核时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'reviewer',
        label: '审核人',
        width: 120,
        formatter: formatTableValue
    },

    {
        key: 'creator',
        label: '创建者',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'createtime',
        label: '创建时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'updater',
        label: '更新者',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'updattime',
        label: '更新时间',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
