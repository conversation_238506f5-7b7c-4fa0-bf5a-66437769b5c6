/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, formatNumber } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const assetRepDownloadTableColumn: TableColumnItem[] = [
    {
        key: 'u1Name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consCode',
        label: '员工编码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'isKpi',
        label: '是否参与考核',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custCnt',
        label: '2023年底存量客户数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'conscustNo',
        label: '投顾客户号',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custName',
        label: '客户姓名',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custFlag',
        label: '客户状态',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'dealCnt',
        label: '购买金额≥40万笔数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'xjhFlag',
        label: '小集合存续≥100万',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'hkFlag',
        label: 'HK开户',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
