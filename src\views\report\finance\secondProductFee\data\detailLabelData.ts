/*
 * @Description: 定义搜索的label列表
 * @Author: jianji<PERSON>.yang
 * @Date: 2024-04-01 10:40:30
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-04-01 10:40:30
 * @FilePath: /src/views/report/kpi/assetRepDownload/data/labelData.ts
 *
 */
export const dataList = {
    startDt: {
        label: '开始日期',
        placeholder: '输入开始日期'
    },
    endDt: {
        label: '结束日期',
        placeholder: '输入结束日期'
    },
    fundCode: {
        label: '基金代码',
        placeholder: '输入基金代码'
    },
    fundMan: {
        label: '基金管理人',
        placeholder: '输入基金管理人'
    },
    // 费用类型
    feeType: {
        label: '费用类型',
        placeholder: '请选择费用类型',
        selectList: [
            {
                key: '1',
                label: '赎回费'
            },
            {
                key: '2',
                label: '管理费'
            },
            {
                key: '3',
                label: '业绩报酬'
            }
        ]
    },
    fundName: {
        label: '产品名称',
        placeholder: '输入产品名称'
    },
    custName: {
        label: '客户姓名',
        placeholder: '输入客户姓名'
    },
    conscustNo: {
        label: '投顾客户号',
        placeholder: '投顾客户号'
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
