/*
 * @Author: xing.zhou
 * @Date: 2021-12-21 17:26:05
 * @LastEditTime: 2023-04-02 07:38:18
 * @LastEditors: chaohui.wu
 * @Description: 消息中心 -- api
 */
import { axiosRequest } from '@/utils/index'

// 消息列表
export function getMsgList(params) {
    return axiosRequest({
        url: '/news/web/info/page',
        method: 'get',
        params
    })
}

// 心跳
export function getMsgPant() {
    return axiosRequest({
        url: '/news/web/pant',
        method: 'get'
    })
}

// 消息数量
export function getMsgCount() {
    return axiosRequest({
        url: '/news/web/count',
        method: 'get'
    })
}

// 读消息
export function readMsg(params) {
    return axiosRequest({
        url: `/news/web/read`,
        method: 'post',
        data: params
    })
}

// 批量读消息
export function readAllMsg(params) {
    return axiosRequest({
        url: `/news/web/list/read`,
        method: 'post',
        data: params
    })
}
