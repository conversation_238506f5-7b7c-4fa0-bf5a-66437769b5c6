<!--
 * @Description: 修改弹框
 * @Author: jianjian.yang
 * @Date: 2024-08-29 20:03:35
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="500px"
        height="900px"
        :border="true"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <!-- 编辑的弹框 -->
            <el-form ref="ruleFormRef" :model="formList" :rules="rules" status-icon>
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="exaimneResult"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <el-checkbox
                                v-model="exaimneResultVisible"
                                label="预计考核结果"
                                style="width: 140px; margin-right: 10px"
                                :true-value="true"
                                :false-value="false"
                            />
                            <crm-select
                                v-if="exaimneResultVisible"
                                v-model="formList.exaimneResult"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :placeholder="exaimneResult.placeholder"
                                :option-list="exaimneResult.selectList"
                                :style="{ width: '150px' }"
                            >
                            </crm-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col>
                        <el-form-item prop="newRank" style="margin-top: 15px; margin-bottom: 15px">
                            <el-checkbox
                                v-model="newRankVisible"
                                label="预计新职级"
                                style="width: 140px; margin-right: 10px"
                                :true-value="true"
                                :false-value="false"
                            />
                            <crm-select
                                v-if="newRankVisible"
                                v-model="formList.newRank"
                                :placeholder="newRank.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="positionLevelConstList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="exaimneEndResult"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <el-checkbox
                                v-model="exaimneEndResultVisible"
                                label="最终考核结果"
                                style="width: 140px; margin-right: 10px"
                                :true-value="true"
                                :false-value="false"
                            />
                            <crm-select
                                v-if="exaimneEndResultVisible"
                                v-model="formList.exaimneEndResult"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :placeholder="exaimneResult.placeholder"
                                :option-list="exaimneResult.selectList"
                                :style="{ width: '150px' }"
                            >
                            </crm-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col>
                        <el-form-item
                            prop="newEndRank"
                            style="margin-top: 15px; margin-bottom: 15px"
                        >
                            <el-checkbox
                                v-model="newEndRankVisible"
                                label="最终新职级"
                                style="width: 140px; margin-right: 10px"
                                :true-value="true"
                                :false-value="false"
                            />
                            <crm-select
                                v-if="newEndRankVisible"
                                v-model="formList.newEndRank"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="containLowPositionLevelConstList"
                                :style="{ width: '150px' }"
                            >
                            </crm-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="24">
                        <el-form-item prop="remark" style="margin-top: 15px; margin-bottom: 40px">
                            <el-checkbox
                                v-model="remarkVisible"
                                label="备注"
                                style="width: 140px; margin-right: 10px"
                                :true-value="true"
                                :false-value="false"
                            />
                            <crm-input
                                v-if="remarkVisible"
                                v-model="formList.remark"
                                :clearable="true"
                                :style="{ width: '250px', height: '25px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 45%">
                            <el-button type="primary" @click="submitForm(ruleFormRef)">
                                确认
                            </el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 55%">
                            <el-button type="primary" @click="handleClose(ruleFormRef)"
                                >取消</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { fetchRes, messageBox } from '@/utils'
    import { dataList } from '../data/labelData'
    import {
        performanceManageBatchUpdate,
        performanceManageBatchUpdateInitData
    } from '@/api/project/performanage/performanceRegionTotalBusiness/performanceRegionTotalBusiness'
    const { exaimneResult, newRank } = dataList
    const loadingFlag = ref<boolean>(false)

    const exaimneResultVisible = ref<boolean>(false)
    const newRankVisible = ref<boolean>(false)
    const exaimneEndResultVisible = ref<boolean>(false)
    const newEndRankVisible = ref<boolean>(false)
    const remarkVisible = ref<boolean>(true)

    const positionLevelConstList = ref<any>([])
    const containLowPositionLevelConstList = ref<any>([])

    class FormList {
        exaimneResult = ''
        newRank = ''
        exaimneEndResult = ''
        newEndRank = ''
        remark = ''
    }

    const formList = reactive<any>(new FormList())

    const ruleFormRef = ref<FormInstance>()

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                ids: string[]
                title: string
                consNames: string[]
            }
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    ids: [] as string[],
                    title: '',
                    consNames: [] as string[]
                }
            }
        }
    )

    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()
    const rules = reactive<FormRules<FormList>>({})

    //提交方法
    const submitForm = async (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }

        await formEl.validate((valid, fields) => {
            if (valid) {
                console.log('submit!', fields)
                performanceManageRegionTotalSubmit()
            } else {
                console.log('error submit!', fields)
            }
        })
    }
    //重置方法
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        formEl.resetFields()
    }

    /**
     * 提交方法
     * @param params
     */
    const performanceManageRegionTotalSubmit = async () => {
        if (
            !exaimneResultVisible.value &&
            !newRankVisible.value &&
            !exaimneEndResultVisible.value &&
            !newEndRankVisible.value &&
            !remarkVisible.value
        ) {
            ElMessage({
                message: '请勾选要修改的字段',
                type: 'warning',
                duration: 2000
            })
            return
        }

        if (exaimneResultVisible.value && !formList.exaimneResult) {
            ElMessage({
                message: '预计考核结果字段不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if (newRankVisible.value && !formList.newRank) {
            ElMessage({
                message: '预计新职级字段不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if (exaimneEndResultVisible.value && !formList.exaimneEndResult) {
            ElMessage({
                message: '最终考核结果字段不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if (newEndRankVisible.value && !formList.newEndRank) {
            ElMessage({
                message: '最终新职级字段不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }

        if (!formList.remark) {
            ElMessage({
                message: '备注字段不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }

        messageBox(
            {
                confirmBtn: '确定修改',
                cancelBtn: '取消',
                content: `本次修改涉及人员：<span style="color:red">${props.transData.consNames.join(
                    '、'
                )}</span>`
            },
            () => {
                confirmUpdate()
            },
            () => false
        )
    }

    const confirmUpdate = async () => {
        const requestParams = {
            ids: props.transData.ids,
            exaimneResult: exaimneResultVisible.value ? formList.exaimneResult : null,
            newRank: newRankVisible.value ? formList.newRank : null,
            exaimneEndResult: exaimneEndResultVisible.value ? formList.exaimneEndResult : null,
            newEndRank: newEndRankVisible.value ? formList.newEndRank : null,
            remark: remarkVisible.value ? formList.remark : null
        }

        const res: any = await performanceManageBatchUpdate(requestParams)
        if (res.code === 'C030000') {
            ElMessage({
                type: 'success',
                message: res.description
            })
            loadingFlag.value = false
            dialogVisible.value = false
            return emit('callback')
        }
        if (res.code !== 'C030000') {
            ElMessage({
                type: 'error',
                message: res?.description || '请求失败'
            })
        }
    }

    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        dialogVisible.value = false
        // formEl.resetFields()
    }

    const initData = async () => {
        fetchRes(performanceManageBatchUpdateInitData(), {
            successCB: (resObj: any) => {
                loadingFlag.value = false
                const { positionLevelList,containLowPositionLevelList } = resObj
                positionLevelConstList.value = positionLevelList
                containLowPositionLevelConstList.value = containLowPositionLevelList
            },
            errorCB: () => {
                loadingFlag.value = false
            },
            catchCB: () => {
                loadingFlag.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    onMounted(() => {
        // 初始化
        initData()
    })
</script>

<style lang="less" scoped>
    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
