<template>
    <el-tabs
        v-bind="$attrs"
        :addable="minor"
        :class="['form_tabs', { form_tabs_minor: minor }]"
        :style="`backgroundColor: ${backgroundColor};`"
    >
        <el-tab-pane
            v-for="(item, index) in tabsList"
            :key="`table-panel${index}`"
            :label="item.label"
            :name="item.name"
        >
            <slot :name="item.slotName"></slot>
        </el-tab-pane>
        <slot></slot>
    </el-tabs>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'FormTabs',
        props: {
            backgroundColor: {
                type: String,
                default: '#fff'
            },
            minor: {
                type: Boolean,
                default: false
            },
            tabsList: {
                type: [Array],
                default: () => {
                    return []
                }
            }
        }
    })
</script>
<style lang="less">
    .form_tabs {
        .el-tabs__nav-wrap::after {
            height: 1px;
            background-color: transparent;
        }

        .el-tabs__header {
            margin: 0;
        }

        & > .el-tabs__header > .el-tabs__nav-wrap > .el-tabs__nav-scroll {
            > .el-tabs__nav {
                margin: 0 30px;
            }
        }

        .el-tabs__item {
            padding: 0 30px;
            font-family: 'Microsoft YaHei-Bold', 'Microsoft YaHei';
            font-size: 16px;
            font-weight: bold;

            &:hover {
                color: @theme_main_hover;
            }
        }

        .el-tabs__item {
            &.is-active {
                color: @theme_main;
            }

            &.is-focus {
                color: @font_color_02;
            }
        }

        .el-tabs__active-bar {
            bottom: 6px;
            height: 4px;
            background-color: transparent;
            background-image: linear-gradient(to right, #ca3739, transparent);
            border-radius: 2px;
        }

        & > .el-tabs__content {
            padding: 0;
        }

        &.form_tabs_minor {
            & > .el-tabs__header {
                margin-bottom: 0;

                & > .el-tabs__nav-wrap {
                    & > .el-tabs__nav-scroll {
                        & > .el-tabs__nav {
                            margin: 0 !important;

                            .el-tabs__active-bar {
                                display: none;
                            }

                            .el-tabs__item {
                                height: 26px;
                                padding: 0 10px !important;
                                margin: 0 10px 0 0;
                                font-size: 13px;
                                font-weight: normal;
                                line-height: 24px;
                                color: @font_color;
                                border: 1px solid @border_color;
                                border-top-left-radius: 3px;
                                border-top-right-radius: 3px;
                                box-shadow: none;

                                &.is-disabled {
                                    padding: 0 !important;
                                }

                                &.is-active {
                                    position: relative;
                                    color: #ffffff;
                                    background-color: @theme_main;
                                    border-color: @theme_main;
                                    box-shadow: none;

                                    &::after {
                                        position: absolute;
                                        bottom: -1px;
                                        left: 50%;
                                        margin-left: -10px;
                                        content: '';
                                        border-right: 5px solid transparent;
                                        border-bottom: 5px solid #ffffff;
                                        border-left: 5px solid transparent;
                                    }
                                }

                                &is-focus {
                                    box-shadow: none;
                                }
                            }
                        }
                    }
                }

                .el-tabs__item:focus.is-active.is-focus:not(:active) {
                    box-shadow: none;
                }
            }

            & > .el-tabs__content {
                padding: 10px;
                border: 1px solid @border_color;
                border-top: none;
            }
        }

        .el-tabs__item:focus.is-active.is-focus:not(:active) {
            box-shadow: none;
        }
    }
</style>
