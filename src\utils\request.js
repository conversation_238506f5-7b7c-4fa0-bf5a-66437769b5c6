import axios from 'axios'
import { ElMessage } from 'element-plus'
import { setToken } from '@/utils/auth.js'
import {
    getLocalItem,
    setLocalItem,
    logoutWithoutRequest,
    removeToken,
    showLoading,
    hideLoading
} from '@/utils/index.js'
import { responseCode } from '@/constant/index'

const pending = [] // 上次请求的参数，用于跟当前请求参数作比较来阻断重复请求
const CancelToken = axios.CancelToken // axios提供的用于取消请求的api
const removePending = config => {
    for (const i in pending) {
        if (pending[i].requestConfig === config) {
            // 在当前请求在数组中存在时执行取消函数
            pending[i].cancelFn() // 执行取消操作
            pending.splice(i, 1) // 根据具体情况决定是否在这里就把pending去掉
        }
    }
}
// tab切换数据同步
export const removePendingAll = () => {
    pending.forEach((item, index) => {
        item.cancelFn()
    })
    pending.splice(0, pending.length)
}

/**
 * 处理接口返回码
 */
const handleResponseCode = (res, callbacks) => {
    if (!res) {
        return
    }
    const { successCB, errorCB } = callbacks
    const { code } = res
    console.log('success')
    if (
        code === responseCode.SUCCESS ||
        code === responseCode.CRMSUCCESS ||
        code === responseCode.CRM_SUCCESS
    ) {
        successCB && successCB(res)
    } else if (code === responseCode.UNLOGIN) {
        // 接口未登录
        successCB && successCB(res)
        const { origin, port, pathname } = window.location
        if (process.env.NODE_ENV === 'production') {
            removeToken()
            window.localStorage.removeItem('USER_INFO')
            window.localStorage.removeItem('USER_MENU')
            window.localStorage.removeItem('hb_crm_token')
            window.location.href = `${origin}/crm-sys/login`
            // window.open(`${origin}/crm-sys/login`)
        }
        // logoutWithoutRequest() // 不走接口、退出登录
    } else if (code === responseCode.RELOGIN || code === responseCode.CRMSYS_NOACCESS) {
        // logoutWithoutRequest() // 不走接口、退出登录
        errorCB && errorCB(res)
    } else {
        errorCB && errorCB(res)
    }
}

/**
 * @description: 老接口兼容
 * @param {*} res
 * @param {*} callbacks
 * @return {*}
 */
const handleResponseCodeOld = (res, callbacks) => {
    if (!res) {
        return
    }
    const { successCB, errorCB } = callbacks
    const code = responseCode.SUCCESS
    if (code === responseCode.SUCCESS || code === responseCode.CRMSUCCESS) {
        successCB && successCB(res)
    } else if (code === responseCode.RELOGIN || code === responseCode.CRMSYS_NOACCESS) {
        // logoutWithoutRequest() // 不走接口、退出登录
    } else {
        errorCB && errorCB(res)
    }
}

const requestFailed = (errorMsg, apiUrl) => {
    ElMessage({
        message: errorMsg || 'Error',
        type: 'error',
        duration: 3000
    })
}

/**
 * axios接口请求封装
 * @params ...apiConfig axios请求层的参数，包含: url, params/data，method等
 * @params callbacks: 回调{ errorCB }
 * @params 请求相关的其它参数 { timeout: 超时时间, baseURL: 地址前缀，默认process.env.VUE_APP_BASE_API }
 */
export const axiosRequest = ({
    callbacks = {},
    isOld = false,
    baseURL = window._msApiPrefix,
    timeout = 20000,
    loadingParams = '',
    ...apiConfig
}) =>
    new Promise((resolve, reject) => {
        const { errorCB } = callbacks || {}
        const service = axios.create({
            baseURL,
            timeout,
            withCredentials: true // 允许携带cookie
        })

        // 设置请求头参数
        service.interceptors.request.use(config => {
            if (loadingParams) {
                showLoading({ ...loadingParams })
            }
            const requestConfig = JSON.stringify(apiConfig)

            // 在一个axios发送前执行一下判定操作，在removePending中执行取消操作
            removePending(requestConfig)

            // 本次axios请求的配置添加cancelToken
            config.cancelToken = new CancelToken(function executor(c) {
                pending.push({
                    requestConfig,
                    cancelFn: c
                })
            })

            // 表示在配置中的设置头消息的字段Authorization为从本地获取的token值
            const token = getLocalItem('hb_crm_token')
            if (token) {
                config.headers.Authorization = token
            }
            return config
        })

        service.interceptors.response.use(
            response => {
                setTimeout(() => {
                    if (loadingParams) {
                        hideLoading({ ...loadingParams })
                    }
                }, 200)
                // 在一个axios响应后再执行一下取消操作，把已经完成的请求从pending中移除
                removePending(JSON.stringify(apiConfig))

                // 【投研系统】每次请求需要更新token
                const newToken = response?.headers?.token
                if (newToken) {
                    setToken(newToken)
                    setLocalItem('hb_crm_token', newToken)
                }

                const res = response.data
                const successCB = callbacks?.successCB || null
                if (!isOld) {
                    handleResponseCode(res, {
                        successCB: res => {
                            successCB && successCB(res)
                            resolve(res)
                        },
                        errorCB: err => {
                            if (errorCB) {
                                errorCB(err)
                            } else {
                                reject(err)
                                requestFailed(err.desc, apiConfig.url)
                            }
                        }
                    })
                } else {
                    handleResponseCodeOld(res, {
                        successCB: res => {
                            successCB && successCB(res)
                            resolve(res)
                        },
                        errorCB: err => {
                            if (errorCB) {
                                errorCB(err)
                            } else {
                                reject(err)
                                requestFailed(err.desc, apiConfig.url)
                            }
                        }
                    })
                }
            },
            error => {
                setTimeout(() => {
                    if (loadingParams) {
                        hideLoading({ ...loadingParams })
                    }
                }, 200)
                // 如果是被拦截掉的重复请求，不执行错误回调
                if (errorCB) {
                    errorCB(error)
                } else {
                    try {
                        // requestFailed(error.message || '', apiConfig.url)
                        reject(error)
                    } catch (e) {
                        console.log(e)
                    }
                }
            }
        )

        return service({
            method: 'post',
            ...apiConfig
        })
    })

/**
 * axios jsonp接口封装 替代$.ajax
 * @params url, data 链式调用
 */
export const axiosJsonp = ({ url, data }) => {
    axios.jsonp = () => {
        if (!url) {
            return
        }
        return new Promise((resolve, reject) => {
            window.jsonCallBack = result => {
                resolve(result)
            }
            const JSONP = document.createElement('script')
            JSONP.type = 'text/javascript'
            const urlObj = new URLSearchParams(`?callback=jsonCallBack`)
            if (data) {
                for (const key in data) {
                    if (data[key]) {
                        urlObj.set(key, data[key])
                    }
                }
            }
            JSONP.src = `${url}?${urlObj.toString()}`
            document.getElementsByTagName('head')[0].appendChild(JSONP)
            setTimeout(() => {
                document.getElementsByTagName('head')[0].removeChild(JSONP)
            }, 500)
        })
    }
    return axios
}
