<!--
 * @Author: chaohui.wu
 * @Date: 2023-1-30
 * @LastEditTime: 2023-1-30
 * @LastEditors: chaohui.wu
-->
<template>
    <el-dropdown size="small" popper-class="operation-btn-popper" @command="handleCommand">
        <crm-button class="crm_download_btn" plain size="small" :icon="Download"
            >{{ btnText }}<el-icon><arrow-down /></el-icon>
        </crm-button>
        <template #dropdown>
            <el-dropdown-menu>
                <!-- <el-dropdown-item v-if="template">
                    <crm-upload-excel-template />
                </el-dropdown-item> -->
                <el-dropdown-item
                    v-for="(item, index) in dropDataTrans"
                    :key="`drop-item-${index}`"
                    :command="index"
                    :disabled="item.isDisabled || false"
                    >{{ item.value }}
                </el-dropdown-item>
            </el-dropdown-menu>
        </template>
    </el-dropdown>
</template>

<script>
    import { Download, ArrowDown } from '@element-plus/icons-vue'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmButtonDrop',
        components: { ArrowDown },
        props: {
            dropData: {
                type: Array,
                default: () => {
                    return [
                        {
                            isShow: false,
                            isDisabled: false,
                            callBackFunc: 'downTemplate',
                            value: '导出模板'
                        },
                        {
                            isShow: true,
                            isDisabled: false,
                            callBackFunc: 'downCurPage',
                            value: '当前页'
                        },
                        {
                            isShow: true,
                            isDisabled: false,
                            callBackFunc: 'downFiveHundred',
                            value: '前500条'
                        },
                        {
                            isShow: true,
                            isDisabled: false,
                            callBackFunc: 'downSelectRow',
                            value: '导出选中行'
                        }
                    ]
                }
            },
            btnText: {
                type: String,
                default: '导出'
            }
        },
        setup() {
            return { Download }
        },
        computed: {
            dropDataTrans() {
                return this.dropData.filter(item => item.isShow)
            }
        },
        methods: {
            handleCommand(command) {
                const { callBackFunc } = this.dropDataTrans[command]
                if (callBackFunc) {
                    return this.$emit(callBackFunc)
                }
                throw new Error('dropData数组中请输入事件的回调函数callBackFunc')
            }
        }
    })
</script>
<style lang="less" scoped>
    .crm_download_btn {
        .el-icon {
            margin: 0 0 0 4px;
        }
    }

    .el-dropdown-menu__item {
        &:not(.is-disabled):focus {
            color: @theme_main;
            background-color: var(--el-fill-color-light);
        }
    }
</style>

<style lang="less">
    .el-dropdown-menu__item {
        &:not(.is-disabled):focus {
            color: @theme_main;
            background-color: var(--el-fill-color-light);
        }
    }
</style>
