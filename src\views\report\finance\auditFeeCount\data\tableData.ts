/*
 * @Description: table表格展示
 * @Author: gang.zou
 * @Date: 2024-05-09 17:14:42
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const auditFeeCountTableColumn: TableColumnItem[] = [
    {
        key: 'fundCode',
        label: '产品代码',
        width: 200,
        formatter: formatTableValue
    },
    {
        key: 'fundName',
        label: '产品名称',
        width: 200,
        formatter: formatTableValue
    },
    {
        key: 'manager',
        label: '管理人',
        width: 200,
        formatter: formatTableValue
    },
    {
        key: 'feeType',
        label: '费用类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'productType',
        label: '产品类型',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
