/*
 * @Description: table表格展示
 * @Author: gang.zou
 * @Date: 2024-05-09 17:14:42
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatNumber, formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const settleDetailFeeTableColumn: TableColumnItem[] = [
    {
        key: 'hboneNo',
        label: '一账通号',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'conscustNo',
        label: '投顾客户号',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundCode',
        label: '产品代码',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundName',
        label: '产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'manager',
        label: '管理人',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custName',
        label: '客户姓名',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consname',
        label: '所属投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'conscustNo',
        label: '投顾客户号',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'feeRate',
        label: '年化费率',
        width: 120,
        formatter: ({ balanceVol }: any) => {
            return formatNumber({ num: balanceVol })
        }
    },
    {
        key: 'feeType',
        label: '费用类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'preDt',
        label: '计提日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'balanceVol',
        label: '持仓份额',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'marketCap',
        label: '持仓市值',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'preNav',
        label: '计提单位净值',
        width: 120,
        formatter: ({ preNav }: any) => {
            return formatNumber({ num: preNav })
        }
    },
    {
        key: 'feeTax',
        label: '计提金额',
        width: 120,
        formatter: ({ feeTax }: any) => {
            return formatNumber({ num: feeTax })
        }
    },
    {
        key: 'fee',
        label: '去税计提金额',
        width: 120,
        formatter: ({ fee }: any) => {
            return formatNumber({ num: fee })
        }
    },
    {
        key: 'settleFeeTax',
        label: '实际结算金额',
        width: 120,
        formatter: ({ settleFeeTax }: any) => {
            return formatNumber({ num: settleFeeTax })
        }
    },
    {
        key: 'settleFee',
        label: '去税实际结算金额',
        width: 120,
        formatter: ({ settleFee }: any) => {
            return formatNumber({ num: settleFee })
        }
    },
    {
        key: 'u1Name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '部门',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ageSubject',
        label: '协议主体',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
