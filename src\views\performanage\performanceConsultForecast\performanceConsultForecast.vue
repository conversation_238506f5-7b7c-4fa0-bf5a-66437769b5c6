<template>
    <div class="report-list-module">
        <table-wrap-cust
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
        >
            <template #searchArea>
                <table class="table-cust-select">
                    <tr>
                        <td>
                            <!-- 投顾 -->
                            <LabelItemCust label="投顾" class-name="text-left">
                                <ReleatedSelect
                                    ref="newlySelect"
                                    v-model="queryForm.orgvalue"
                                    :organization-list="organizationList"
                                    :cons-list-default="consultList"
                                    :default-org-code="orgCodeDefault"
                                    :default-cons-code="consCodeDefault"
                                    :cons-status="consStatus"
                                    :module="module"
                                    code-width="120px"
                                    :add-all="true"
                                ></ReleatedSelect>
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 考核周期 -->
                            <LabelItemCust :label="periodExplain.label" class-name="text-left">
                                <crm-select
                                    v-model="queryForm.periodExplain"
                                    :placeholder="periodExplain.placeholder"
                                    filterable
                                    clearable
                                    label-format="label"
                                    value-format="key"
                                    :option-list="periodExplain.selectList"
                                    :style="{ width: '110px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 层级 -->
                            <LabelItemCust :label="userLevel.label" class-name="text-left">
                                <crm-select
                                    v-model="queryForm.userLevel"
                                    :placeholder="userLevel.placeholder"
                                    filterable
                                    clearable
                                    label-format="label"
                                    value-format="key"
                                    :option-list="userLevel.selectList"
                                    :style="{ width: '110px' }"
                                    @change="userLevelChange"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 职级 -->
                            <LabelItemCust :label="curMonthLevel.label" class-name="text-left">
                                <crm-select
                                    v-model="queryForm.curMonthLevel"
                                    :placeholder="curMonthLevel.placeholder"
                                    filterable
                                    clearable
                                    label-format="label"
                                    value-format="key"
                                    :option-list="curMonthLevelSelectList"
                                    :style="{ width: '110px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 是否TP -->
                            <LabelItemCust
                                :label="istp.label"
                                class-name="text-left"
                                minwidth="90px"
                            >
                                <crm-select
                                    v-model="queryForm.istp"
                                    :placeholder="istp.placeholder"
                                    filterable
                                    clearable
                                    label-format="label"
                                    value-format="key"
                                    :option-list="istp.selectList"
                                    :style="{ width: '110px' }"
                                />
                            </LabelItemCust>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <!-- 考核节点 -->
                            <LabelItemCust :label="exaimneNode.label" class-name="text-left">
                                <date-range
                                    v-model="queryForm.exaimneNode"
                                    show-format="YYYY-MM-DD"
                                    :placeholder="exaimneNode.placeholder"
                                    style-type="fund"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 预计考核结果 -->
                            <LabelItemCust :label="exaimneResult.label" class-name="text-left">
                                <crm-select
                                    v-model="queryForm.exaimneResult"
                                    :placeholder="exaimneResult.placeholder"
                                    filterable
                                    clearable
                                    label-format="label"
                                    value-format="key"
                                    :option-list="exaimneResult.selectList"
                                    :style="{ width: '110px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 最终考核结果 -->
                            <LabelItemCust :label="exaimneEndresult.label" class-name="text-left">
                                <crm-select
                                    v-model="queryForm.exaimneEndresult"
                                    :placeholder="exaimneEndresult.placeholder"
                                    filterable
                                    clearable
                                    label-format="label"
                                    value-format="key"
                                    :option-list="exaimneEndresult.selectList"
                                    :style="{ width: '110px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 投顾code -->
                            <LabelItemCust :label="userId.label" class-name="text-left">
                                <crm-input
                                    v-model="queryForm.userId"
                                    :placeholder="userId.placeholder"
                                    :clearable="true"
                                    :style="{ width: '110px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td></td>
                    </tr>
                </table>
            </template>
            <template #operationBtns>
                <ButtonCust size="small" :radius="true" plain @click.stop="queryList"
                    >查询</ButtonCust
                >
                <button-cust
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    plain
                    @click.stop="exportHandle"
                    >导出</button-cust
                >
                <button-cust
                    v-if="batchModifyShow"
                    size="small"
                    :radius="true"
                    plain
                    @click="handleBatchEdit"
                    >批量修改</button-cust
                >
                <button-cust
                    v-if="saveShow"
                    size="small"
                    :radius="true"
                    plain
                    @click.stop="saveHandle"
                    >保存</button-cust
                >

                <button-cust
                    v-if="calSaveDSumDataShow"
                    size="small"
                    :radius="true"
                    plain
                    @click.stop="calSaveDSumData"
                    >汇总当年存续D: <span>{{ lastUpdateTimeRef }}</span></button-cust
                >
                <button-cust
                    v-if="saveRecordShow"
                    size="small"
                    :radius="true"
                    plain
                    @click="handleSaveRecord"
                    >操作记录</button-cust
                >
            </template>
            <template #tableContentMiddle>
                <base-table-cust
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    :pops="{ selectFixed: 'left' }"
                    :row-class-name="handleRowClassName"
                    :cell-class-name="handleCellClassName"
                    @selectionChange="handleSelectionChange"
                >
                </base-table-cust>
            </template>
        </table-wrap-cust>

        <batch-edit-performance-index
            v-if="batchEditDialogVisible"
            v-model="batchEditDialogVisible"
            :trans-data="batchObj"
            :organization-list="organizationList"
            :consult-list="consultList"
            :org-code="orgCodeDefault"
            :cons-code="consCodeDefault"
            @callback="handleClose()"
        >
        </batch-edit-performance-index>

        <save-record-performance-index
            v-if="saveRecordDialogVisible"
            v-model="saveRecordDialogVisible"
            :trans-data="recordObj"
            :organization-list="organizationList"
            :consult-list="consultList"
            :org-code="orgCodeDefault"
            :cons-code="consCodeDefault"
            @callback="handleClose()"
        >
        </save-record-performance-index>
    </div>
</template>

<script lang="ts" setup>
    import { fetchRes, messageBox } from '@/utils'
    import { dataList } from './data/labelData'
    import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'

    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import BatchEditPerformanceIndex from '@/views/performanage/performanceConsultForecast/components/performanceConsultForecastBatchEdit.vue'
    import SaveRecordPerformanceIndex from '@/views/performanage/performanceManageBusiness/components/performanceManageSubTotalSaveRecord.vue'
    import { performanceConsultForecastTableColumn, showTableColumn } from './data/tableData'
    import {
        performanceConsultForecastQuery,
        performanceConsultForecastExport,
        performanceConsultForecastSave,
        performanceCalSaveDSumData,
        performanceConsultForecastListInitData,
        queryConsRankList
    } from '@/api/project/performanage/performanceConsultForecast/performanceConsultForecast'
    import { performanceManageBusinessCurrWorkDays } from '@/api/project/performanage/performanceManageBusiness/performanceManageBusiness'
    import { MANAGE_CONSULT_FORECAST_OPER_PERMISSION } from '@/constant/performanceConst'
    import { getMenuPermission } from '@/api/project/common/common'
    import BaseTableCust from '@/views/modBusiness/pageModule/components/BaseTableCust.vue'
    import LabelItemCust from '@/views/modBusiness/pageModule/components/LabelItemCust.vue'
    import TableWrapCust from '@/views/modBusiness/pageModule/components/TableWrapCust.vue'
    import ButtonCust from '@/views/modBusiness/pageModule/components/ButtonCust.vue'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import { defineComponent } from 'vue'

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const {
        userId,
        periodExplain,
        exaimneResult,
        userLevel,
        curMonthLevel,
        exaimneNode,
        istp,
        exaimneEndresult
    } = dataList

    const listLoading = ref<boolean>(false)
    const batchEditDialogVisible = ref<boolean>(false)
    const saveRecordDialogVisible = ref<boolean>(false)
    const consStatus = ref<string>('0') //包含离职人员

    const exportShow = ref<boolean>(false)
    const saveShow = ref<boolean>(false)
    const batchModifyShow = ref<boolean>(false)
    const saveRecordShow = ref<boolean>(false)
    const calSaveDSumDataShow = ref<boolean>(false)

    const curMonthLevelSelectList = ref<any[]>([])

    //批量修改选中的id
    let checkIds = [] as string[]
    let consNames = [] as string[]

    const handleRowClassName = (rowObj: any, rowIndex: number) => {
        const { row } = rowObj
        if (row && row.haveHumanModify === '1') {
            return 'highlight-bg'
        }
    }

    const handleCellClassName = (rowObj: any) => {
        const { row, column, rowIndex, columnIndex } = rowObj
        if (row && column) {
            if (column.property === 'exaimneResult' && row.humanResultFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'newRank' && row.humanNewRankFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'exaimneEndresult' && row.humanEndresultFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'newEndrank' && row.humanNewEndrankFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'qualifiedStaff' && row.humanQualifiedStaffFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'competentStaff' && row.humanCompetentStaffFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'qualifiedStaffNew' && row.humanQualifiedStaffNewFlag === '1') {
                return 'redColumn'
            }
            if (column.property === 'competentStaffNew' && row.humanCompetentStaffNewFlag === '1') {
                return 'redColumn'
            }
        }
    }

    const module = ref<string>('B140117')

    let lastYearMonthValue = ''
    const lastUpdateTimeRef = ref<string>('')
    /**
     * @description: 批量修改
     * @return {*}
     */
    const batchObj = ref({
        ids: [] as string[],
        type: '',
        title: '',
        consNames: [] as string[]
    })
    /**
     * @description: 操作记录
     * @return {*}
     */
    const recordObj = ref({
        pageType: ''
    })

    const userLevelChange = (val: any): void => {
        if (queryForm.userLevel) {
            fetchRes(queryConsRankList({ userLevel: queryForm.userLevel }), {
                successCB: (resObj: any) => {
                    const { positionLevelList } = resObj
                    curMonthLevelSelectList.value = positionLevelList
                },
                successTxt: '',
                failTxt: '请求失败请重试！',
                fetchKey: ''
            })
        }
    }

    /**
     * @description: 关闭弹框刷新数据
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleClose = (): void => {
        queryList()
    }

    const handleSelectionChange = (sel: any): void => {
        checkIds = sel.map((item: any) => item.id)
        consNames = sel.map((item: any) => item.consName)
        console.log(checkIds)
    }

    const handleBatchEdit = (val: any): void => {
        if (checkIds.length === 0) {
            ElMessage({
                type: 'info',
                message: '请先勾选需要批量修改的数据!'
            })
            return
        }
        batchObj.value = {
            ids: checkIds,
            type: 'edit',
            title: '批量修改',
            consNames: consNames
        }
        batchEditDialogVisible.value = true
    }

    const handleSaveRecord = (): void => {
        recordObj.value = {
            pageType: '1'
        }
        saveRecordDialogVisible.value = true
    }

    const calSaveDSumData = (): void => {
        messageBox(
            {
                confirmBtn: '确定',
                cancelBtn: '取消',
                content: `汇总并保存投顾个人存续D数据的截止时间<br>截止时间：<span style="color:red">${lastYearMonthValue}</span>`
            },
            () => {
                confirmSaveDSumData()
            },
            () => false
        )
    }

    const confirmSaveDSumData = async () => {
        const allLoading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)'
        })
        fetchRes(performanceCalSaveDSumData({}), {
            successCB: (resObj: any) => {
                listLoading.value = false
                ElMessageBox.alert('保存成功!', '', { center: true })
                allLoading.close()
            },
            errorCB: () => {
                listLoading.value = false
                ElMessageBox.alert('<span style="color:red">保存失败!</span>', '', {
                    dangerouslyUseHTMLString: true,
                    center: true
                })
                allLoading.close()
            },
            catchCB: () => {
                listLoading.value = false
                ElMessageBox.alert('<span style="color:red">保存失败!</span>', '', {
                    dangerouslyUseHTMLString: true,
                    center: true
                })
                allLoading.close()
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        userId = ''
        periodExplain = ''
        userLevel = ''
        curMonthLevel = ''
        istp = ''
        exaimneResult = ''
        exaimneEndresult = ''
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        exaimneNode = {
            startDate: '',
            endDate: ''
        }
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])
    /**
     * 当前日期是本月第几个工作日
     */
    const currWorkDays = ref<number>(0)

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            performanceConsultForecastTableColumn.map(item => item.key),
            performanceConsultForecastTableColumn
        )
    })

    const saveList = async () => {
        if (currWorkDays.value < 6) {
            ElMessage({
                message: '上个月存续D未计算完成，不能保存数据',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if (
            !(
                (queryForm.orgvalue.orgCode === '1' && queryForm.orgvalue.consCode === '') ||
                (queryForm.orgvalue.orgCode === '10' && queryForm.orgvalue.consCode === '') ||
                (queryForm.orgvalue.orgCode === '1000005627' && queryForm.orgvalue.consCode === '')
            )
        ) {
            ElMessage({
                message: '请选择中心的全部数据后再保存数据',
                type: 'warning',
                duration: 2000
            })
            return
        }
        messageBox(
            {
                confirmBtn: '确定',
                cancelBtn: '取消',
                content: `确认保存上月结果数据？`
            },
            () => {
                confirmSave()
            },
            () => false
        )
    }

    const confirmSave = async () => {
        const allLoading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)'
        })

        const requestParams = {
            orgCode: queryForm.orgvalue.orgCode
        }

        fetchRes(performanceConsultForecastSave(requestParams), {
            successCB: (resObj: any) => {
                allLoading.close()
                ElMessage({
                    type: 'success',
                    message: '保存成功'
                })
            },
            errorCB: () => {
                allLoading.close()
                ElMessage({
                    type: 'error',
                    message: '请求失败'
                })
            },
            catchCB: () => {
                allLoading.close()
                ElMessage({
                    type: 'error',
                    message: '请求失败'
                })
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            flag: '0',
            userId: queryForm.userId,
            periodExplain: queryForm.periodExplain,
            userLevel: queryForm.userLevel,
            curMonthLevel: queryForm.curMonthLevel,
            istp: queryForm.istp,
            exaimneResult: queryForm.exaimneResult,
            exaimneEndresult: queryForm.exaimneEndresult,
            exaimneNodeStart: queryForm.exaimneNode.startDate,
            exaimneNodeEnd: queryForm.exaimneNode.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value
        }
        fetchRes(performanceConsultForecastQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { list, haveExceptionData, workDays } = resObj
                if (haveExceptionData) {
                    // 弹框显示
                    ElMessage({
                        message: '考核配置中存在异常数据，请先修正后再使用此菜单',
                        type: 'warning',
                        duration: 2000
                    })
                } else {
                    tableData.value = list
                    currWorkDays.value = workDays
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    const exportHandle = () => {
        exportList()
    }

    const saveHandle = () => {
        saveList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const params = {
            flag: '0',
            userId: queryForm.userId,
            periodExplain: queryForm.periodExplain,
            userLevel: queryForm.userLevel,
            curMonthLevel: queryForm.curMonthLevel,
            istp: queryForm.istp,
            exaimneResult: queryForm.exaimneResult,
            exaimneEndresult: queryForm.exaimneEndresult,
            exaimneNodeStart: queryForm.exaimneNode.startDate,
            exaimneNodeEnd: queryForm.exaimneNode.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value
        }
        const allLoading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)'
        })
        const res: any = await performanceConsultForecastExport(params)
        const { fileByte, name, errorMsg } = res.data
        if (errorMsg) {
            ElMessage({
                message: errorMsg,
                type: 'warning',
                duration: 2000
            })
            allLoading.close()
            return
        }
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
        allLoading.close()
    }

    const initData = async () => {
        const params = {
            menuCode: module.value
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode === MANAGE_CONSULT_FORECAST_OPER_PERMISSION.EXPORT &&
                            item.display === '1'
                        ) {
                            exportShow.value = true
                        }
                        if (
                            item.operateCode ===
                                MANAGE_CONSULT_FORECAST_OPER_PERMISSION.BATCH_MODIFY &&
                            item.display === '1'
                        ) {
                            batchModifyShow.value = true
                        }
                        if (
                            item.operateCode ===
                                MANAGE_CONSULT_FORECAST_OPER_PERMISSION.CAL_D_SUM &&
                            item.display === '1'
                        ) {
                            calSaveDSumDataShow.value = true
                        }
                        if (
                            item.operateCode ===
                                MANAGE_CONSULT_FORECAST_OPER_PERMISSION.SAVE_RECORD &&
                            item.display === '1'
                        ) {
                            saveRecordShow.value = true
                        }
                        if (
                            item.operateCode === MANAGE_CONSULT_FORECAST_OPER_PERMISSION.SAVE &&
                            item.display === '1'
                        ) {
                            saveShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })

        fetchRes(performanceConsultForecastListInitData(), {
            successCB: (resObj: any) => {
                const { lastYearMonth, lastUpdateTime, positionLevelList } = resObj
                lastYearMonthValue = lastYearMonth
                lastUpdateTimeRef.value = lastUpdateTime
                curMonthLevelSelectList.value = positionLevelList
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    const confirmCurrWorkDays = async () => {
        fetchRes(performanceManageBusinessCurrWorkDays(), {
            successCB: (resObj: any) => {
                currWorkDays.value = resObj
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [orgCodeDefault, consCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.orgvalue.orgCode = orgCodeDefault.value
                queryForm.orgvalue.consCode = consCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        console.log('onMounted')
        initData()
        confirmCurrWorkDays()
        fetchConsOrgList('', module.value)
    })
</script>
<style lang="less" scoped>
    .table-cust-select {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        tr {
            background-color: #ffffff;

            td {
                border: 1px solid #dddddd;
            }
        }
    }
</style>
