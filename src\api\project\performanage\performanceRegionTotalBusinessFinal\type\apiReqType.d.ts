export {}
declare module './apiReqType' {
    type performanceRegionTotalBusinessFinalParam = {
        /**
         * 投顾code
         */
        userId?: string
        /**
         * 部门
         */
        orgCode?: string
        /**
         * 投顾号
         */
        consCode?: string
        /**
         * 考核节点开始时间
         */
        exaimneNodeStart?: string
        /**
         * 考核节点结束时间
         */
        exaimneNodeEnd?: string
        /**
         * 周期 1-试用3M 2-试用6M 3-跟踪-12M 4-正式-年中 5-正式年末 6-观察期-12M以上 7-观察期-12M以内 8-理1B
         */
        periodExplain?: string
        /**
         * 预计考核结果
         */
        exaimneResult?: string
        /**
         * 最终考核结果
         */
        exaimneEndResult?: string
        /**
         * 层级
         */
        userLevel?: string
        /**
         * 计算月份
         */
        calcMonth?: string
        /**
         * 是否TP
         */
        isTp?: string
    }

    export { performanceRegionTotalBusinessFinalParam }
}
