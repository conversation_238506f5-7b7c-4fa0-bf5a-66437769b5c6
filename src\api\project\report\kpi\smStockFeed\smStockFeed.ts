import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { smStockFeedQueryParam } from './type/apiReqType.js'

/**
 * @description:查询接口
 * @return {*}
 */
export const smStockFeedQuery = (params: smStockFeedQueryParam): any => {
    return axiosRequest(
        paramsMerge({
            timeout:40000,
            url: '/api/report/jobassessment/smstockfeed/smstockfeedquery',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 导出接口
 * @return {*}
 */
export const smStockFeedExport = (params: smStockFeedQueryParam) => {
    return axiosRequest(
        paramsMerge({
            timeout:60000,
            url: '/api/report/jobassessment/smstockfeed/smstockfeedexport',
            method: 'post',
            data: params
        })
    )
}
