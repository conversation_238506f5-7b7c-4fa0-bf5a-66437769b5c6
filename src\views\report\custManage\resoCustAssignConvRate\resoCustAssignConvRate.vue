<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            :show-export-btn="true"
            @searchFn="handleLoading"
            @exportFn="exportHandle"
        >
            <template #searchArea>
                <label-item :label="radioNodeList.label">
                    <crm-radio
                        v-model="queryForm.constype"
                        :placeholder="radioNodeList.placeholder"
                        :option-list="radioNodeList.selectList"
                        @change="changeConsType"
                    ></crm-radio>
                </label-item>
                <label-item label="投顾">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <label-item :label="resType.label">
                    <crm-select
                        v-model="queryForm.resType"
                        :placeholder="resType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="resType.selectList"
                        :style="{ width: '130px' }"
                        @change="handleResType"
                    />
                </label-item>
                <label-item :label="assignTime.label">
                    <date-range
                        v-model="queryForm.assignTime"
                        show-format="YYYY-MM-DD"
                        :placeholder="assignTime.placeholder"
                        style-type="fund"
                    />
                </label-item>

                <label-item :label="custSource.label">
                    <crm-select2
                        v-model="queryForm.custSource"
                        :placeholder="custSource.placeholder"
                        filterable
                        clearable
                        multiple
                        label-format="label"
                        value-format="key"
                        :option-list="custSource.selectList"
                        :style="{ width: '180px' }"
                    />
                </label-item>

                <label-item :label="isWork.label">
                    <crm-select
                        v-model="queryForm.isWork"
                        :placeholder="isWork.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="isWork.selectList"
                        :style="{ width: '130px' }"
                        @change="handleIsRepeat"
                    />
                </label-item>
                <label-item :label="secondHand.label">
                    <crm-select
                        v-model="queryForm.secondHand"
                        :placeholder="secondHand.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="secondHand.selectList"
                        :style="{ width: '130px' }"
                        @change="handleSecondHand"
                    />
                </label-item>
                <label-item :label="isDeal.label">
                    <crm-select
                        v-model="queryForm.isDeal"
                        :placeholder="isDeal.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="isDeal.selectList"
                        :style="{ width: '130px' }"
                        @change="handleIsDeal"
                    />
                </label-item>
            </template>

            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                >
                </base-table>
            </template>
            <template #operationBtns>
                <crm-button
                    size="small"
                    :radius="true"
                    plain
                    :icon="RemoveFilled"
                    @click="clearHandle"
                    >清空</crm-button
                >
                <crm-button
                    size="small"
                    :radius="true"
                    :icon="InfoFilled"
                    plain
                    @click="handleExplain"
                    >说明</crm-button
                >
            </template>
            <!-- <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template> -->
        </table-wrapper>
        <ResoCustAssignConvRateExplain
            v-model="explainDialogVisiable"
        ></ResoCustAssignConvRateExplain>
    </div>
</template>

<script lang="ts" setup>
    import { RemoveFilled, InfoFilled } from '@element-plus/icons-vue'
    import { fetchRes } from '@/utils'
    import { dataList } from './data/labelData'
    import { resoCustAssignConRateTableColumn, showTableColumn } from './data/tableData'
    import { useStockListData } from '@/views/common/scripts/stockListData'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import CrmSelect2 from '@/views/report/custManage/components/CrmSelect.vue'
    import ResoCustAssignConvRateExplain from '@/views/report/custManage/components/resoCustAssignConvRateExplain.vue'
    import {
        // eslint-disable-next-line camelcase
        resoCustAssignConvRateQuery,
        resoCustAssignConvRateExport
    } from '@/api/project/report/custManage/resoCustAssignConvRate/resoCustAssignConvRate'
    const { isWork, isDeal, secondHand, custSource, resType, assignTime, radioNodeList } = dataList
    const stockListStore = useStockListData()
    const { getPageInit } = stockListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(stockListStore)
    const listLoading = ref<boolean>(false)
    const consStatus = ref<string>('0')
    const module = ref<string>('070229')
    const handleResType = (val: string) => {
        queryForm.resType = val
    }
    const handleIsRepeat = (val: string) => {
        queryForm.isWork = val
    }
    const handleIsDeal = (val: string) => {
        queryForm.isDeal = val
    }
    const handleSecondHand = (val: string) => {
        queryForm.secondHand = val
    }
    const changeConsType = (val: string) => {
        consStatus.value = val
    }
    /**
     * @description: 说明展示隐藏
     * @param explainDialogVisiable 展示隐藏
     * @method handleExplain 触发方法
     * @return {*}
     */
    const explainDialogVisiable = ref<boolean>(false)
    const handleExplain = () => {
        explainDialogVisiable.value = true
    }
    const clearHandle = () => {
        const today = new Date()
        const year = today.getFullYear()
        const date = new Date(year, 0, 1)
        const formattedDate = `${date.getFullYear()}${(date.getMonth() + 1)
            .toString()
            .padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}`
        queryForm.constype = '0'
        queryForm.isDeal = ''
        queryForm.isWork = ''
        queryForm.secondHand = ''
        queryForm.custSource = []
        queryForm.resType = ''
        queryForm.isBindWeiXin = ''
        queryForm.assignTime.startDate = formattedDate
        queryForm.assignTime.endDate = ''
        ;(queryForm.orgvalue.orgCode = ''), (queryForm.orgvalue.consCode = '')
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        constype = '0'
        isWork = ''
        isDeal = ''
        secondHand = ''
        custSource = []
        resType = ''
        isBindWeiXin = ''
        conscustno = ''
        assignTime = {
            startDate: '',
            endDate: ''
        }
        curAssignTime = {
            curStartDate: '',
            curEndDate: ''
        }
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
    }
    const queryForm = reactive(new QueryForm())

    // /**
    //  * @description: 分页数据
    //  * @return {*}
    //  */
    // class PageObj {
    //     page = 1
    //     size = 20
    //     total = 0
    //     perPage = 1
    // }
    // const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            resoCustAssignConRateTableColumn.map(item => item.key),
            resoCustAssignConRateTableColumn
        )
    })

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            isWork: queryForm.isWork,
            isDeal: queryForm.isDeal,
            secondHand: queryForm.secondHand,
            custSource: queryForm.custSource.join(),
            resType: queryForm.resType,
            startDate: queryForm.assignTime.startDate,
            endDate: queryForm.assignTime.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            conscode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            sumBy: queryForm.constype
        }
        fetchRes(resoCustAssignConvRateQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                tableData.value = resObj
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    // /**
    //  * @description: 分页联动
    //  * @param {*} current
    //  * @param {*} perPage
    //  * @return {*}
    //  */
    // const handleCurrentChange = (current: number, perPage: number) => {
    //     pageObj.value = { ...pageObj.value, page: current, size: perPage }
    //     queryList()
    // }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const pdfUrl = ref<string>('')
    const exportList = async () => {
        const params = {
            isWork: queryForm.isWork,
            secondHand: queryForm.secondHand,
            custSource: queryForm.custSource.join(),
            resType: queryForm.resType,
            startDate: queryForm.assignTime.startDate,
            endDate: queryForm.assignTime.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            conscode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            sumBy: queryForm.constype
        }
        const res: any = await resoCustAssignConvRateExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }
    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        getPageInit(module.value)
        const today = new Date()
        const year = today.getFullYear()
        const date = new Date(year, 0, 1)
        const formattedDate = `${date.getFullYear()}${(date.getMonth() + 1)
            .toString()
            .padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}`
        queryForm.assignTime.startDate = formattedDate
    })
</script>
<style lang="less" scoped></style>
