/*
 * @Description: echarts 导入
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-05-24 17:40:39
 * @FilePath: /crm-asset-web/src/components/charts/scripts/echartsConfig.ts
 * @param 'echarts/core' 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
 * @param 'echarts/charts' 引入对应的charts
 * @param 'echarts/components' 引入对应的charts components 组件 组件后缀都为 Component
 * @param 'echarts/renderers' 渲染器 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
 * @param 'echarts.use' 注册echarts组件
 */

import * as echarts from 'echarts/core'
import { LineChart, BarChart, ScatterChart, RadarChart, PieChart, GaugeChart } from 'echarts/charts'

import {
    TooltipComponent,
    GridComponent,
    LegendComponent,
    DataZoomComponent,
    MarkPointComponent,
    PolarComponent,
    ToolboxComponent
} from 'echarts/components'

import { CanvasRenderer, SVGRenderer } from 'echarts/renderers'
import { GraphicComponent } from 'echarts/components'

echarts.use([
    LineChart,
    BarChart,
    ScatterChart,
    RadarChart,
    PieChart,
    GaugeChart,
    PolarComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent,
    DataZoomComponent,
    CanvasRenderer,
    SVGRenderer,
    GraphicComponent,
    MarkPointComponent,
    ToolboxComponent
])

const customChart = echarts

export default customChart
