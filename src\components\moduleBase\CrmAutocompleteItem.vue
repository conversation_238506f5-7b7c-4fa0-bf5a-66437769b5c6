<!--
 * @Description: 自动补全输入框
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-03-16 14:55:36
 * @FilePath: /crm-asset-web/src/components/moduleBase/CrmAutocompleteItem.vue
 * https://element-plus.gitee.io/zh-CN/component/autocomplete.html
-->
<template>
    <div class="crm_input_item">
        <div class="label">{{ label ? `${label}：` : '' }}</div>
        <div class="value">
            <crm-autocomplete v-bind="$attrs" />
        </div>
        <!-- <slot name="suffix" /> -->
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmAutocompleteItem',
        props: {
            label: {
                type: String,
                default: ''
            }
        }
    })
</script>
<style lang="less" scoped>
    .crm_input_item {
        display: flex;
        align-items: flex-start;
        margin: 15px 30px 0 0;

        .label {
            min-width: 72px;
            font-size: 12px;
            line-height: 26px;
            color: @font_color;
            text-align: right;
            white-space: nowrap;
        }

        .value {
            flex: 1;
            font-size: 12px;
        }
    }
</style>
