import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { BxRenewalParam } from './type/apiReqType.js'

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const BxRenewalQuery = (params: BxRenewalParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/bxrenewal2025/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const BxRenewalExport = (params: BxRenewalParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/bxrenewal2025/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 初始化的查询接口
 * @return {*}
 */
export const BxRenewalInit = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/kpi/bxrenewal2025/initdata',
            method: 'post',
            data: null
        })
    )
}
