import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { productCoefficientHisParam } from './type/apiReqType.js'

/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const productCoefficientHisQuery = (params: productCoefficientHisParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/prpproductcoefficient/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 导出接口
 * @return {*}
 */
export const productCoefficientHisExport = (params: productCoefficientHisParam) => {
    return axiosRequest(
        paramsMerge({
            timeout: 60000,
            url: '/api/report/performance/prpproductcoefficient/export',
            method: 'post',
            data: params
        })
    )
}
