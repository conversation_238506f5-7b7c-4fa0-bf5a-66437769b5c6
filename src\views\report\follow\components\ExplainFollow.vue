<template>
    <crm-dialog
        v-model="dialogVisible"
        width="598px"
        title="统计说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <div class="explain-content">
            <b>此报表用于统计：</b><br />
            1、Leads(一手+二手）客户、离职继承客户 的标准动作执行情况<br />
            2、存量：好买基金私募+好臻+好买香港<br />
            3、成交人民币：好买基金私募+好臻<br />
            4、成交美元：好买香港+海外创新<br /><br />

            其他统计规则，以相关文件为准
        </div>
        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">确定</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script setup lang="ts">
    import { useVisible } from '@/views/common/scripts/useVisible'
    import { watch } from 'vue'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            modelValue: boolean
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()

    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })

    // 监听 dialogVisible 变化，同步到父组件
    watch(dialogVisible, val => {
        emit('update:modelValue', val)
    })
</script>

<style lang="less" scoped>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 8px;
        text-align: left;
        border: 1px solid black;
    }

    th {
        background-color: #f2f2f2;
    }

    .middle-content {
        padding: 10px 0;
    }
</style>
