import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { StockMasterFunOrderParam } from './type/apiReqType.js'

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const stockMasterFunOrder_Json = (params: StockMasterFunOrderParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/stockFOFMasterFun/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const stockMasterFunOrderExport = (params: StockMasterFunOrderParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/stockFOFMasterFun/export',
            method: 'post',
            data: params
        })
    )
}
