import { axiosRequest } from '@/utils/index'
import {
    queryCustHkInfoParam,
    viewDetailParam,
    saveUserShowColumnRequest,
    viewRealTextRequest
} from './type/apiReqType.js'

export const queryCustHkInfo = (params: queryCustHkInfoParam): any => {
    return axiosRequest({
        url: '/api/report/hk/custhkinfo/query',
        method: 'post',
        timeout: 180000,
        data: params
    })
}

export const exportCustHkInfo = (params: queryCustHkInfoParam): any => {
    return axiosRequest({
        url: '/api/report/hk/custhkinfo/export',
        method: 'post',
        timeout: 180000,
        data: params
    })
}

export const viewDetail = (params: viewDetailParam): any => {
    return axiosRequest({
        url: '/api/report/hk/custhkinfo/viewdetail',
        method: 'post',
        data: params
    })
}

export const getInitData = (): any => {
    return axiosRequest({
        url: '/api/report/hk/custhkinfo/initdata',
        method: 'post'
    })
}

export const saveUserShowColumn = (params: saveUserShowColumnRequest): any => {
    return axiosRequest({
        url: '/api/report/hk/custhkinfo/saveusershowcolumn',
        method: 'post',
        data: params
    })
}

export const viewRealText = (params: viewRealTextRequest): any => {
    return axiosRequest({
        url: '/api/report/hk/custhkinfo/viewrealtext',
        method: 'post',
        data: params
    })
}
