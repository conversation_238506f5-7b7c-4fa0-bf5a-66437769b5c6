import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { StockProductFeeParam } from './type/apiReqType.js'

/**
 * @description: 股权产品的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const StockProductFeeQuery = (params: StockProductFeeParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/stockProductFee/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 股权产品的导出接口
 * @return {*}
 */
export const StockProductFeeExport = (params: StockProductFeeParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/stockProductFee/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 详细页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const StockProductFeeDetailQuery = (params: StockProductFeeParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/stockProductFee/queryDetail',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 明细页面的导出
 * @return {*}
 */
export const StockProductFeeDetailExport = (params: StockProductFeeParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/stockProductFee/exportDetail',
            method: 'post',
            data: params
        })
    )
}
