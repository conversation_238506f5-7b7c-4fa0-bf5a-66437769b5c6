<!--
 * @Description: 统计说明
 * @Author: jianji<PERSON>.yang
 * @Date: 2024-04-01 19:46:04
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-04-01 19:46:04
 * @FilePath: /src/views/report/kpi/components/explainAssetRep.vue
 *  
-->
<template>
    <crm-dialog
        width="824px"
        title="统计说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <template #default>
            <div style="height: 300px;">
            <b>此报表用于统计投顾名下在2024年发生的海外产品（私募+公募+创新）、非A产品（私募指定产品+公募指定产品）的净申购数据。</b><br />
            <span>①KPI相关数据字段：根据2024年KPI考核规则，最终计入KPI的数据</span><br />
            <span>②相关数据统计规则：参考2024年KPI文件</span>
        </div>
        </template>

        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">确定</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
   import { useVisible } from '@/views/common/scripts/useVisible'
    
    /**
     * @description: 组件传参
     * @return {*}
     */
     const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )
    
    /**
     * @description: $emit 平替
     * @return {*}
     */
     const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()
    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })
</script>

<style lang="less" scoped>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 8px;
        text-align: left;
        border: 1px solid black;
    }

    th {
        background-color: #f2f2f2;
    }

    .middle-content {
        padding: 10px 0;
    }
</style>
