import { chartColors } from './chartColors'
import { dateFormat, getString, convertDateToQuarter } from './methods'
import { dateTrans, addUnit } from '@/utils/index'

// 柱状图和折线图配置
export const LineAndBarChartConfig = {
    color: chartColors.lineBarColors,
    grid: {
        top: 10,
        right: 80,
        bottom: 70,
        left: 80
    },
    legend: {
        bottom: 0,
        icon: 'rect',
        itemWidth: 10,
        // itemHeight: 3,
        textStyle: {
            color: '#434549',
            fontSize: 12
        },
        formatter(item) {
            return item + '      '
        }
    },
    tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
            // 指示线
            type: 'shadow',
            lineStyle: {
                color: '#f14a51',
                type: 'solid'
            }
        },
        textStyle: {
            fontSize: 10,
            color: '#262626'
        },
        formatter(items) {
            // 避免悬浮在graphic文字上的时候报错
            if (items?.componentType === 'graphic') {
                return
            }

            // 类目条目
            const tmpItem = item =>
                `<div class="ehart_tooltip_item" style="opacity: ${
                    getString(item.value) ? 1 : 0.2
                };">
                    <span >
                        <i class="${item.seriesType}" style="background-color: ${item.color};"></i>
                        ${item.seriesName}：
                    </span>
                    <span>${item.value || '-'}%</span>
                </div>`

            // 季度维度的条目
            let relVal = `${dateFormat(items[0]?.axisValue)}`
            relVal += '<div class="ehart_tooltip_items">'
            items.slice(0, 3).forEach(item => {
                relVal += tmpItem(item)
            })

            relVal += '<br>'

            // 单日维度的条目
            relVal += `${items[3] ? dateFormat(items[3]?.axisValue) : ''}`
            items.slice(3).forEach(item => {
                relVal += tmpItem(item)
            })

            return relVal + '</div>'
        }
    }
}
// 普通型柱状图和折线图配置
export const TLineAndBarChartConfig = {
    color: chartColors.lineBarColors,
    grid: {
        top: 10,
        right: 80,
        bottom: 70,
        left: 80
    },
    legend: {
        bottom: 0,
        icon: 'rect',
        itemWidth: 10,
        // itemHeight: 3,
        textStyle: {
            color: '#434549',
            fontSize: 12
        },
        formatter(item) {
            return item + '      '
        }
    },
    tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
            // 指示线
            type: 'shadow',
            lineStyle: {
                color: '#f14a51',
                type: 'solid'
            }
        },
        textStyle: {
            fontSize: 10,
            color: '#262626'
        },
        formatter(items) {
            // 避免悬浮在graphic文字上的时候报错
            if (items?.componentType === 'graphic') {
                return
            }

            // 类目条目
            const tmpItem = item =>
                `<div class="ehart_tooltip_item" style="opacity: ${
                    getString(item.value) ? 1 : 0.2
                };">
                    <span >
                        <i class="${item.seriesType}" style="background-color: ${item.color};"></i>
                        ${item.seriesName}：
                    </span>
                    <span>${item.value || '-'}</span>
                </div>`

            // 季度维度的条目
            let relVal = `${dateFormat(items[0]?.axisValue)}`
            relVal += '<div class="ehart_tooltip_items">'
            items.forEach(item => {
                relVal += tmpItem(item)
            })

            // relVal += '<br>'

            // // 单日维度的条目
            // relVal += `${items[3] ? dateFormat(items[3]?.axisValue) : ''}`
            // items.slice(3).forEach(item => {
            //     relVal += tmpItem(item)
            // })

            return relVal + '</div>'
        }
    }
}
// 折线图公共配置
export const lineChartConfig = {
    color: chartColors.line.colors,
    grid: {
        top: 20,
        right: 30,
        bottom: '10%',
        left: 60
    },
    legend: {
        icon: 'circle',
        itemWidth: 10,
        itemHeight: 3,
        padding: [0, 0, 0, 0],
        textStyle: {
            color: '#434549',
            fontSize: 12
        }
    },
    tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
            // 指示线
            lineStyle: {
                color: '#f14a51',
                type: 'dashed'
            }
        },
        textStyle: {
            fontSize: 14,
            color: '#262626'
        },
        formatter(params) {
            const relVal = dateFormat(params[0].name)
            return `${relVal}
                <ul class="line-chart-tooltips">
                    ${params
                        .map(item => {
                            return `<li class="tooltips-item">
                            <span class="tips-box">
                                <i class="tips-icon icon-line" style="background-color: ${
                                    item.color
                                }"></i>
                                ${item.seriesName} ：
                            </span>
                            <span>${item.value === '0' ? '--' : addUnit({ val: item.value })}</span>
                        </li>`
                        })
                        .join('')}
                </ul>`
        }
    },
    // X轴
    xAxis: {
        type: 'category',
        boundaryGap: false, // 坐标轴两边留白['20%', '20%']，false为不留白
        axisLine: {
            lineStyle: {
                color: '#e0e0e3',
                width: 1
            }
        },
        axisTick: {
            show: false
        },
        axisLabel: {
            color: '#606266',
            showMinLabel: true,
            showMaxLabel: true
        }
    },
    // Y轴
    yAxis: {
        axisLine: {
            show: true,
            lineStyle: {
                color: '#e0e0e3',
                width: 1
            }
        },
        splitLine: {
            lineStyle: {
                color: '#e0e0e3',
                width: 1,
                type: 'dotted'
            }
        },
        axisLabel: {
            color: '#606266',
            fontSize: 12
        }
    }
}
// 双Y轴柱状图公共配置
export const doubleBarChartConfig = {
    color: chartColors.barColors,
    grid: {
        top: 20,
        right: 60,
        bottom: 60,
        left: 60
    },
    legend: {
        bottom: 0,
        icon: 'rect',
        itemWidth: 10,
        itemHeight: 10,
        padding: [0, 0, 0, 0],
        textStyle: {
            color: '#434549',
            fontSize: 12
        },
        formatter(item) {
            return item + '      '
        }
    },
    tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
            // 指示线
            type: 'shadow',
            lineStyle: {
                color: '#f14a51',
                type: 'solid'
            }
        },
        textStyle: {
            fontSize: 10,
            color: '#262626'
        },
        formatter(items) {
            // 避免悬浮在graphic文字上的时候报错
            if (items?.componentType === 'graphic') {
                return
            }
            let relVal = dateFormat(items[0]?.axisValue)
            relVal += '<div class="ehart_tooltip_items">'
            items.forEach(item => {
                if (item.seriesIndex === 0) {
                    relVal += `<div class="ehart_tooltip_item" style="opacity: ${
                        item.value && !!Number(item.value) ? '1' : 0.2
                    };"><span ><i style="background-color: ${item.color}"></i>${
                        item.seriesName
                    }：</span><span>${item.value || '-'}</span></div>`
                } else if (item.seriesIndex === 1) {
                    relVal += `<div class="ehart_tooltip_item" style="opacity: ${
                        item.value && !!Number(item.value) ? '1' : 0.2
                    };"><span ><i style="background-color: ${item.color}"></i>${
                        item.seriesName
                    }：</span><span>${item.value || '-'}%</span></div>`
                }
            })
            return relVal + '</div>'
        }
    },
    // X轴
    xAxis: {
        type: 'category',
        axisLine: {
            lineStyle: {
                color: '#e0e0e3',
                width: 1
            }
        },
        axisTick: {
            show: false
        },
        axisLabel: {
            color: '#606266'
        }
    }
}

// 柱状堆叠图公共配置
export const StackBarChartConfig = {
    grid: {
        top: 10,
        right: 60,
        bottom: 60,
        left: 60
    },
    legend: {
        bottom: 0,
        icon: 'rect',
        itemWidth: 10,
        itemHeight: 10,
        padding: [0, 0, 0, 0],
        textStyle: {
            color: '#434549',
            fontSize: 12
        },
        formatter(item) {
            return item + '      '
        }
    },
    tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
            // 指示线
            type: 'shadow',
            lineStyle: {
                color: '#f14a51',
                type: 'solid'
            }
        },
        textStyle: {
            fontSize: 10,
            color: '#262626'
        },
        formatter(items) {
            // 避免悬浮在graphic文字上的时候报错
            if (items?.componentType === 'graphic') {
                return
            }
            let relVal = `${dateFormat(items[0]?.axisValue)}`
            relVal += '<ul class="lineChartToolTipItems">'
            items.forEach(item => {
                relVal += `<li style="opacity: ${
                    item.value && !!Number(item.value) ? '1' : 0.2
                };"><span ><i style="background-color: ${item.color}"></i>${
                    item.seriesName
                }：</span><span>${item.value || '-'}%</span></li>`
            })
            return relVal + '</ul>'
        }
    },
    xAxis: {
        type: 'category',
        axisLine: {
            lineStyle: {
                color: '#e0e0e3',
                width: 1
            }
        },
        axisTick: {
            show: false
        },
        axisLabel: {
            color: '#606266'
        }
    },
    yAxis: {
        type: 'value',
        nameTextStyle: {
            color: '#606266',
            padding: [0, 0, 0, 100]
        },
        axisLine: {
            show: true,
            lineStyle: {
                color: '#e0e0e3',
                width: 1
            }
        },
        axisLabel: {
            color: '#606266',
            fontSize: 12,
            formatter: '{value}%'
        },
        splitLine: {
            lineStyle: {
                color: '#e0e0e3',
                width: 1,
                type: 'dotted'
            }
        }
    }
}

// 堆叠面积图公共配置
export const timeOrderChartConfig = {
    grid: {
        top: 40,
        right: 60,
        bottom: 10,
        left: 60
    },
    legend: {
        top: 10,
        icon: 'rect',
        itemWidth: 10,
        itemHeight: 10,
        padding: [0, 0, 0, 0],
        textStyle: {
            color: '#434549',
            fontSize: 12
        },
        formatter(item) {
            return item + '      '
        }
    },
    tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
            // 指示线
            type: 'line',
            lineStyle: {
                color: '#fff',
                type: 'solid',
                opacity: 0.2
            }
        },
        textStyle: {
            fontSize: 10,
            color: '#262626'
        }
        // formatter (items) {
        //     let relVal = `${items[0].axisValue} (R²=)`
        //     relVal += '<ul class="lineChartToolTipItems">'
        //     items.forEach(item => {
        //         relVal += `<li style="opacity: ${item.value && !!Number(item.value) ? '1' : 0.2};"><span><i style="background-color: ${item.color};"></i>${item.seriesName}：</span><span>${(item.value || '-')}%</span></li>`
        //         // relVal += `<li style="opacity: ${item.value && !!Number(item.value) ? '1' : 0.2};"><span><i style="background-color: ${item.color};"></i>${item.seriesName}：</span><span>${(item.value || '-')}%</span></li>`
        //     })
        //     return relVal + '</ul>'
        // }
    },
    // X轴
    xAxis: {
        type: 'category',
        axisLine: {
            lineStyle: {
                color: '#e0e0e3',
                width: 1
            }
        },
        axisTick: {
            show: false
        },
        axisLabel: {
            show: false,
            color: '#606266'
        }
    },
    // Y轴
    yAxis: [
        {
            type: 'value',
            name: '风险暴露',
            nameTextStyle: {
                color: '#606266',
                padding: [0, 0, 0, 100]
            },
            position: 'left',
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#e0e0e3',
                    width: 1
                }
            },
            axisLabel: {
                color: '#606266',
                fontSize: 12,
                formatter: '{value}'
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: '#e0e0e3',
                    width: 1,
                    type: 'dotted'
                }
            }
        },
        {
            type: 'value',
            name: '风险收益',
            nameTextStyle: {
                color: '#606266',
                padding: [0, 100, 0, 0]
            },
            position: 'right',
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#e0e0e3',
                    width: 1
                }
            },
            axisLabel: {
                color: '#606266',
                fontSize: 12,
                formatter: '{value}%'
            },
            splitLine: {
                lineStyle: {
                    color: '#e0e0e3',
                    width: 1,
                    type: 'dotted'
                }
            }
        }
    ]
}

// R²柱状图公共配置
export const rsquareChartConfig = {
    grid: {
        show: true,
        top: 0,
        right: 60,
        bottom: 20,
        left: 60,
        borderColor: '#e0e0e3',
        borderWidth: 1
    },
    xAxis: {
        type: 'category',
        // boundaryGap: false,
        axisLine: {
            show: false
        },
        axisTick: {
            show: false
        },
        axisLabel: {
            color: '#606266'
        }
    },
    yAxis: {
        type: 'value',
        axisLine: {
            show: false
        },
        axisTick: {
            show: false
        },
        axisLabel: {
            show: false
        },
        splitLine: {
            show: false
        }
    }
}

// 折线图缩放功能配置
export const lineChartDataZoomConfig = {
    show: true,
    type: 'slider',
    realtime: true,
    start: 0, // 区间开始点，最小0
    end: 100, // 区间结束点，最大100
    xAxisIndex: [0, 1],
    bottom: 0,
    left: 70,
    right: 70,
    borderColor: '#e3e4e9',
    borderRadius: 0,
    smooth: true,
    fillerColor: 'rgba(28, 31, 55, .15)',
    moveHandleSize: 0,
    filterMode: 'empty',
    dataBackground: {
        lineStyle: {
            color: '#d8dae7',
            shadowColor: '#b4b6c3'
        },
        areaStyle: {
            color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                    {
                        offset: 0,
                        color: '#fff' // 0% 处的颜色
                    },
                    {
                        offset: 1,
                        color: '#d8dae7' // 100% 处的颜色
                    }
                ],
                global: false // 缺省为 false
            },
            opacity: 1
        }
    },
    selectedDataBackground: {
        lineStyle: {
            color: '#d8dae7',
            shadowColor: '#b4b6c3'
        },
        areaStyle: {
            color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                    {
                        offset: 0,
                        color: '#fff' // 0% 处的颜色
                    },
                    {
                        offset: 1,
                        color: '#d8dae7' // 100% 处的颜色
                    }
                ],
                global: false // 缺省为 false
            },
            opacity: 1
        }
    }
}

// 折线图线条基本样式
export const lineChartSeriesOption = {
    type: 'line',
    showSymbol: false,
    symbol: 'circle',
    symbolSize: 4,
    connectNulls: true, // 连接空数据
    // 线条样式
    lineStyle: {
        width: 2,
        opacity: 1
    },
    // 高亮状态的线条样式
    emphasis: {
        lineStyle: {
            width: 2,
            opacity: 1
        }
    }
}

// 柱状图条形基本样式
export const barChartSeriesOption = {
    type: 'bar',
    barCategoryGap: '35%',
    barMaxWidth: 40,
    itemStyle: {
        opacity: 1
    },
    emphasis: {
        itemStyle: {
            opacity: 1
        }
    }
}

// 图表无数据时候的样式
export const chartNoDataStyle = [
    {
        type: 'group',
        left: 'center',
        top: 'center',
        children: [
            {
                type: 'text',
                z: 100,
                left: 'center',
                top: 'middle',
                style: {
                    fill: '#909399',
                    text: '暂无数据',
                    font: '12px Microsoft YaHei'
                }
            }
        ]
    }
]

// 柱状图公共配置
export const barChartConfig = {
    color: chartColors.barColors,
    grid: {
        top: 20,
        right: 30,
        bottom: 60,
        left: 60
    },
    legend: {
        bottom: 0,
        icon: 'rect'
    },
    tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {
            // 指示线
            type: 'shadow',
            lineStyle: {
                color: '#f14a51',
                type: 'solid'
            }
        },
        textStyle: {
            fontSize: 10,
            color: '#262626'
        },
        formatter(params) {
            const relVal = dateFormat(params[0].name)
            return `${relVal}
                <ul class="line-chart-tooltips">
                    ${params
                        .map(item => {
                            return `<li class="tooltips-item">
                            <span class="tips-box">
                                <i class="tips-icon icon-line" style="background-color: ${
                                    item.color
                                }"></i>
                                ${item.seriesName} ：
                            </span>
                            <span>${item.value === '0' ? '--' : addUnit({ val: item.value })}</span>
                        </li>`
                        })
                        .join('')}
                </ul>`
        }
    },
    // X轴
    xAxis: {
        type: 'category',
        axisLine: {
            lineStyle: {
                color: '#e0e0e3',
                width: 1
            }
        },
        axisTick: {
            show: false
        },
        axisLabel: {
            color: '#606266'
        }
    },
    // Y轴
    yAxis: {
        axisLine: {
            show: true,
            lineStyle: {
                color: '#e0e0e3',
                width: 1
            }
        },
        splitLine: {
            lineStyle: {
                color: '#e0e0e3',
                width: 1,
                type: 'dotted'
            }
        },
        axisLabel: {
            color: '#606266',
            fontSize: 12,
            formatter: '{value}%'
        }
    }
}

// 雷达图公共配置
export const radarChartConfig = {
    color: chartColors.line.colors,
    tooltip: {
        confine: true,
        textStyle: {
            fontSize: 10,
            color: '#262626'
        }
    },
    legend: {
        bottom: 0,
        icon: 'rect',
        itemWidth: 10,
        itemHeight: 3,
        padding: [20, 0, 0, 0],
        textStyle: {
            color: '#434549',
            fontSize: 12
        },
        formatter: val => convertDateToQuarter(val) + '  '
    },
    radar: {
        center: ['50%', '60%'],
        axisNameGap: 6, // 指标距离网线的距离
        // 指标文字
        axisName: {
            fontSize: 12,
            color: '#606266'
        },
        // 雷达分割区域
        splitArea: {
            areaStyle: {
                color: ['#fff']
            }
        },
        // 雷达分割线
        splitLine: {
            lineStyle: {
                width: 1,
                color: '#eaeaea'
            }
        },
        // 雷达半径指示线的颜色
        axisLine: {
            lineStyle: {
                width: 1,
                color: '#eaeaea'
            }
        }
    }
}
