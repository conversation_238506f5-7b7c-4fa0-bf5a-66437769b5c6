export interface IpsVisitRecord {
    conscustno: string // 投顾客户号
    custName: string // 客户姓名
    initialIpsAmt: string // 25年初IPS存量
    initialMainAccount: string // 25年初关联的主账户
    isIpsVisitComplete: string // 是否完成IPS面访
    completeDate: string // 完成日期
    completeAmt: string // 完成时的存量
    completeConsCode: string // 完成投顾code
    completeConsName: string // 完成投顾
    completeCenter: string // 完成中心
    completeArea: string // 完成区域
    completeBranch: string // 完成分公司
    initialConsCode: string // 25年初投顾code
    initialConsName: string // 25年初投顾
    initialCenter: string // 25年初中心
    initialArea: string // 25年初区域
    initialBranch: string // 25年初分公司
}

export interface QueryIpsVisitResponse {
    rows: IpsVisitRecord[]
    total: number
    curPage: string
    size: string
}

export interface ExportIpsVisitResponse {
    data: {
        fileByte: string // 文件字节数组的base64编码串
        name: string // 文件名
        type: string // 文件类型
        errorMsg: string // 错误信息
    }
}

export interface IpsVisitInitResponse {
    data: {
        lastUpdateTime: string // 数据更新时间
        dataDeadline: string // 数据截止日期
    }
}
