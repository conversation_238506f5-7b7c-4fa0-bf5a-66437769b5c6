/*
 * @Description: table表格展示
 * @Author: gang.zou
 * @Date: 2024-04-17 17:14:42
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, formatNumber } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const assetRepDownloadTableColumn: TableColumnItem[] = [
    {
        key: 'u1Name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consCode',
        label: '员工编码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custCnt',
        label: '2023年底存量客户数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'isKpi',
        label: '是否参与考核',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'overseaTarget',
        label: '海外产品-目标',
        width: 120,
        formatter: ({ overseaTarget }: any) => {
            return formatNumber({ num: overseaTarget })
        }
    },
    {
        key: 'overseaReach',
        label: '海外产品-结果',
        width: 120,
        formatter: ({ overseaReach }: any) => {
            return formatNumber({ num: overseaReach })
        }
    },
    {
        key: 'transferTarget',
        label: '公募转托管-目标',
        width: 120,
        formatter: ({ transferTarget }: any) => {
            return formatNumber({ num: transferTarget })
        }
    },
    {
        key: 'transferReach',
        label: '公募转托管-结果',
        width: 120,
        formatter: ({ transferReach }: any) => {
            return formatNumber({ num: transferReach })
        }
    },
    {
        key: 'nonAtarget',
        label: '非A资产-目标',
        width: 120,
        formatter: ({ nonAtarget }: any) => {
            return formatNumber({ num: nonAtarget })
        }
    },
    {
        key: 'nonAteach',
        label: '非A资产-结果',
        width: 120,
        formatter: ({ nonAteach }: any) => {
            return formatNumber({ num: nonAteach })
        }
    },
    {
        key: 'newCustTarget',
        label: '新增客户数-目标',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'newCustReach',
        label: '新增客户数-结果',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assetRepTarget',
        label: '存量客户资配报告-目标',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'upAssetRepReach',
        label: '存量客户资配报告（上）-结果',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'lowAssetAepAeach',
        label: '存量客户资配报告（下）-结果',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'wechatTarget',
        label: '存量客户企微添加率-目标',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'wechatReach',
        label: '存量客户企微添加率-结果',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'bxTarget',
        label: '创新继续率-目标',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'bxReach',
        label: '创新继续率-结果',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
