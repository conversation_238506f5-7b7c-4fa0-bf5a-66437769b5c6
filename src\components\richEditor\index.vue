<template>
    <div ref="editorFixedContainer" class="editor-box">
        <template v-if="flag && editor">
            <ckeditor
                ref="richText"
                class="crm_rich_text"
                tag-name="textarea"
                :editor="editor"
                :config="editorConfig"
                v-bind="$attrs"
                :model-value="editorData"
                @update:model-value="onUpdate"
                @ready="onReady"
            />
        </template>
        <!-- <div id="toolbar-container" ref="toolbar" />
        <div id="editor" ref="editor" /> -->
        <!-- v-if="!noExpand && textareaHeight && !isFull" -->
        <span v-show="false" class="crm_rich_more" @click="toggleFlag">
            {{ showFlag ? '展开' : '收起' }}
        </span>
    </div>
</template>

<script>
    import CKEditor from '@ckeditor/ckeditor5-vue'

    import ClassicEditor from '@ckeditor/ckeditor5-editor-classic/src/classiceditor'
    import Essentials from '@ckeditor/ckeditor5-essentials/src/essentials'
    import Paragraph from '@ckeditor/ckeditor5-paragraph/src/paragraph'
    import Bold from '@ckeditor/ckeditor5-basic-styles/src/bold'
    import Italic from '@ckeditor/ckeditor5-basic-styles/src/italic'
    import Underline from '@ckeditor/ckeditor5-basic-styles/src/underline'
    import { RemoveFormat } from '@ckeditor/ckeditor5-remove-format'
    import Alignment from '@ckeditor/ckeditor5-alignment/src/alignment'
    import Font from '@ckeditor/ckeditor5-font/src/font'
    import List from '@ckeditor/ckeditor5-list/src/list'
    import ListProperties from '@ckeditor/ckeditor5-list/src/listproperties'
    import Indent from '@ckeditor/ckeditor5-indent/src/indent'
    import IndentBlock from '@ckeditor/ckeditor5-indent/src/indentblock'
    import PasteFromOffice from '@ckeditor/ckeditor5-paste-from-office/src/pastefromoffice'
    import './lang'

    // import { checkKeyword } from '@/api/project/checkPre/checkPreList'
    // import { deepClone, fetchRes, messageBox } from '@/utils/index'

    // 已有
    // Table
    // Table toolbar
    // List properties
    // List
    // Image
    // Image upload
    // Image toolbar
    // Image style
    // Image resize
    // Image caption
    // Bold
    // Block quote
    // Autoformat
    // Alignment

    // 待验证
    // Underline To-do list
    // Table properties
    // Table cell properties
    // Strikethrough
    // Paste from Office
    // Media embed
    // Link
    // Italic
    // Indent block
    // Indent

    // Heading
    // Font size
    // Font family
    // Font color
    // Font background color
    // Cloud Services
    // CKFinder upload adapter
    // CKFinder

    export default {
        name: 'RichEditor',
        components: {
            // 编辑器组件的局部注册方式
            ckeditor: CKEditor.component
        },
        props: {
            checkHeight: {
                type: Boolean,
                default: false
            },
            label: {
                type: String,
                default: null
            },
            clearFlag: {
                // 某些时候强制清空输入框， 很多时候外部透传的值为’‘了，但是dom还是有显示  false代表可以输入   在尽调的场中   false代表已经添加了二级策略， 可以输入个人观点等特殊字段
                type: Boolean,
                default: false
            },
            fontSize: {
                type: String,
                default: '18px'
            },
            onUploadImage: {
                type: Function,
                default() {
                    return () => undefined
                }
            },
            insertChart: {
                type: Function,
                default: null
            },
            onImageSize: {
                type: Function,
                default() {
                    return () => undefined
                }
            },
            modelValue: {
                type: String,
                default: ''
            },
            noExpand: {
                type: Boolean,
                default: false
            }
        },
        emits: ['update:modelValue', 'colorEdit', 'saveEdit'],
        data() {
            return {
                editorData: this.modelValue || '', // editorData不能为null
                editor: ClassicEditor,
                editorConfig: {
                    language: 'zh-cn',
                    plugins: [
                        Font,
                        Alignment,
                        Essentials,
                        Paragraph,
                        Bold,
                        Italic,
                        List,
                        ListProperties,
                        RemoveFormat,
                        PasteFromOffice,
                        Indent,
                        IndentBlock,
                        Underline
                    ],
                    toolbar: [
                        'bold',
                        'italic',
                        'underline',
                        'fontColor',
                        'fontBackgroundColor',
                        'alignment',
                        'numberedList',
                        'bulletedList',
                        'outdent',
                        'indent',
                        'undo',
                        'redo'
                    ],
                    placeholder: this.$attrs.placeholder
                },
                showFlag: false, // 是否展开  true为收起状态
                textareaHeight: false, // 高度是否大于四行 ======>大于四行则展示  展开收起  按钮
                count: 0,
                flag: true,
                isFull: false
            }
        },
        computed: {
            // 将$attrs.value 和 checkHeight 二合一  同时都有才触发
            computeHeightFlag() {
                if (this.checkHeight) {
                    return !this.modelValue
                }
                return false
            }
        },
        watch: {
            modelValue: {
                handler: function (val) {
                    if (!val) {
                        this.editorData = ''
                    } else {
                        this.editorData = val
                    }
                    // if (this.clearFlag) {
                    //     try {
                    //         // const inputArea = this.$refs.richText.$el.nextSibling
                    //         const inputArea = this.getInputArea()
                    //         inputArea.childNodes[0].innerHTML = ''
                    //     } catch (e) {
                    //         console.log('清空富文本出了问题啦！')
                    //     }
                    // }
                    // 监听到value值变化  则计算input的高度是否超过四行
                    // if (val) {
                    //     this.getTextHeight()
                    // }
                },
                deep: true
            },
            // 每次收起时   富文本滚动到最底下
            showFlag(val) {
                if (val) {
                    const inputArea = this.getInputArea()
                    this.$nextTick(() => {
                        inputArea.scrollTop = inputArea.scrollHeight - 100
                    })
                }
            }
        },
        methods: {
            toggleFlag() {
                this.showFlag = !this.showFlag
            },
            showFocus() {
                if (this.$refs.richText) {
                    this.$refs.richText.instance.editing.view.focus()
                }
            },
            getInputArea() {
                return (
                    this.$refs.richText.$el.parentElement || this.$refs.richText.$el.parentNode
                ).querySelector('.ck-content')
            },
            // setChangeData() {
            //     this.$nextTick(() => {
            //         console.log('change', this.getInputArea())
            //         // this.$emit('saveEdit', `${this.getInputArea()}`)
            //     })
            // },
            onReady(editor) {
                // 自定义上传图片插件替换内部上传插件
                // eslint-disable-next-line no-debugger
                // editor.plugins.get('FileRepository').createUploadAdapter = loader => {
                //     console.log(loader)
                //     // eslint-disable-next-line no-debugger
                //     // getImageSize(loader).then(({ width, height }) => {
                //     //     this.onImageSize({ width, height })
                //     // })
                //     return new ImageUploader(loader, this.onUploadImage, this.onImageSize)
                // }
                // this.ckStickyPanel =
                //     this.$refs.editorFixedContainer.querySelector('.ck-sticky-panel')
                // this.ckStickyPanel.classList.remove('ck-sticky-panel')
            },
            // getTextHeight() {
            // try {
            //     const inputArea = this.getInputArea()
            //     const scrollHeight = inputArea ? inputArea.scrollHeight : 0
            //     // 因为富文本若被display： none时   获取到的scrollHeight为0  所以要兼容0的情况  至于会不会引发别的问题  暂时不知道
            //     console.log('scrollHeight', scrollHeight)
            //     this.textareaHeight = scrollHeight > 100
            //     // eslint-disable-next-line no-debugger
            //     // debugger
            // } catch (e) {
            //     console.log('富文本初始高度计算出了问题啦！')
            // }
            // },
            onUpdate(value, evt, editor) {
                this.$emit('update:modelValue', value)
            },
            onEditorFocus() {
                if (!this.value) {
                    this.$emit('colorEdit')
                }
            }
        }
    }
</script>
<style lang="less" scoped>
    :deep(.ck-editor) {
        --ck-font-size-base: 12px;
        --ck-font-face: microsoft yahei, '微软雅黑';
        --ck-focus-ring: 1px solid @border_focus; // focus border
        --ck-color-base-border: #d5d6d9;
        --ck-color-focus-border-coordinates: #d5d6d9;
        --ck-color-base-background: #ffffff;
        --ck-color-focus-outer-shadow: #eeeeee;
        --ck-color-button-on-color: #d20000;
        --ck-color-base-focus: #fdf6f6;
        --ck-color-button-on-background: #fdf6f6;
        --ck-focus-outer-shadow: 0 0 0 3px hsla(359deg, 63%, 48%, 0.14); // button外框
        --ck-focus-disabled-outer-shadow: 0 0 0 3px hsla(0deg, 0%, 100%, 1); // 禁用按钮外框
        --ck-color-focus-disabled-shadow: hsla(209deg, 90%, 72%, 0.3);
        --ck-color-button-on-active-background: #fdf6f6;
        --ck-color-button-on-hover-background: #fdf6f6;
        --ck-color-widget-type-around-button-active: #fdf6f6;
        --ck-color-button-default-hover-background: #fdf6f6;
        --ck-color-list-button-on-background: #d20000;
        --ck-color-list-button-on-background-focus: #d20000;
        --ck-color-color-grid-check-icon: #d20000;
        --ck-color-focus-border: hsl(0deg, 64%, 98%);
        --ck-color-resizer: hsl(0deg, 64%, 98%);
        --ck-color-shadow-inner: #fdf6f6;
        --ck-color-base-active: #d20000;
        --ck-color-base-foreground: hsl(0deg, 0%, 98%);
        --ck-color-base-action: hsl(104deg, 50.2%, 42.5%);
        --ck-color-base-text: hsl(0deg, 0%, 20%);
        --ck-color-base-active-focus: #d20000;
        --ck-color-base-error: hsl(15deg, 100%, 43%);
        // position: relative;
        ::marker {
            content: unset;
        }

        .ck.ck-reset,
        .ck.ck-reset_all,
        .ck-reset_all *:not(.ck-reset_all-excluded *) {
            background-color: #fafafa;
        }

        .ck.ck-button,
        a.ck.ck-button {
            outline: 0;
        }

        .ck-content {
            font-family: 'Microsoft YaHei', '微软雅黑';
            font-size: 14px;
            line-height: 22px;
            color: #333333;
        }

        // ol,ul默认样式
        ol,
        ul {
            padding-left: 30px;
            list-style: auto;
        }
    }
    // .editor-box.editor-fix {
    //     background-color: red;
    //     position: fixed;
    //     top: 0;
    //     right: 0;
    //     bottom: 0;
    //     left: 0;
    //     z-index: 1010;
    //     width: 100%;
    //     background: #fafafa;
    //     .ck-editor__editable {
    //         height: auto;
    //     }
    //     :deep(.ck-content) {
    //         height: auto !important;
    //     }
    // }
</style>
