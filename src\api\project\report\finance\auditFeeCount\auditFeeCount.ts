import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { auditFeeCountParam } from './type/apiReqType.js'

/**
 * @description: 审批情况统计的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const AuditFeeCountQuery = (params: auditFeeCountParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/AuditFeeCount/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审批情况统计的导出接口
 * @return {*}
 */
export const AuditFeeCountExport = (params: auditFeeCountParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/AuditFeeCount/export',
            method: 'post',
            data: params
        })
    )
}
