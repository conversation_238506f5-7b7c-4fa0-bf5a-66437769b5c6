/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const piggyHkDealTableColumn: TableColumnItem[] = [
    {
        label: '业务订单号',
        key: 'dealNo',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '香港客户号',
        key: 'hkCustNo',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '一账通号',
        key: 'hboneNo',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '投顾客户号',
        key: 'conscustno',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '审核状态',
        key: 'checkState',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '业务类型',
        key: 'busiName',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '签署/终止时间',
        key: 'signCancelTime',
        minWidth: 140,
        formatter: formatTableValue,
        sortable: 'custom'
    },
    {
        label: '签署/终止方式',
        key: 'signCancelType',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '签署/终止渠道',
        key: 'channel',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '客户类型(当前)',
        key: 'invstType',
        minWidth: 200,
        formatter: formatTableValue
    },
    {
        label: '投资者类型(当前)',
        key: 'investorQualification',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '储蓄罐签约状态(当前)',
        key: 'signState',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '香港客户状态(当前)',
        key: 'custStat',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '当前投顾',
        key: 'consname',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '当前投顾一级组织架构',
        key: 'u1Name',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '当前投顾二级组织架构',
        key: 'u2Name',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '当前投顾三级组织架构',
        key: 'u3Name',
        minWidth: 120,
        formatter: formatTableValue
    }
]
