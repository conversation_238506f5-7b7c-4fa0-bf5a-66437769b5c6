/*
 * @Description: 产品费率配置通用组件入口文件
 * @Author: hongdong.xie
 * @Date: 2025-06-06 09:42:20
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 09:42:20
 * @FilePath: /ds-report-web/src/components/common/ProductFeeRateConfig/index.ts
 */

// 导出组件
export { default as ProductFeeRateConfigList } from './ProductFeeRateConfigList.vue'
export { default as ProductFeeRateConfigAdd } from './ProductFeeRateConfigAdd.vue'
export { default as ProductFeeRateConfigAudit } from './ProductFeeRateConfigAudit.vue'

// 导出hooks
export { useProductFeeRateConfig } from './composables/useProductFeeRateConfig'
export { useProductFeeRateForm } from './composables/useProductFeeRateForm'
export { useProductFeeRateApi } from './composables/useProductFeeRateApi'
export { useProductFeeRateValidation } from './composables/useProductFeeRateValidation'

// 导出类型
export type {
    ProductType,
    RateType,
    DialogType,
    ApiConfig,
    PageConfig,
    ProductFeeRateConfig,
    BaseQueryParam,
    BaseExportParam,
    BaseDeleteParam,
    BaseAuditParam,
    BaseFormData,
    PageObj,
    QueryForm
} from './types'

// 导出工具函数
export {
    formatDateToBackend,
    formatDateFromBackend,
    formatDateForTable,
    formatTableValue,
    formatAuditStatus,
    formatFofOnlyFlag,
    formatTieredRateType,
    formatRateEffectiveType,
    formatPerformanceAccrualType,
    formatPerformanceAccrualForm,
    formatFixedAccrualMonthType,
    formatFixedAccrualDateType,
    formatFixedAccrualCalcType,
    formatRedemptionHoldingDaysRule,
    formatDividendHoldingDaysRule,
    formatPerformanceFormula,
    formatShareLockType,
    formatFundClosedType,
    formatRedemptionHoldingCalcRule,
    formatRedemptionSpecialRule,
    handleExportFile,
    handleApiError,
    handleApiCatch,
    resetFormData,
    copyFormData,
    validateSelectedCount
} from './utils'

// 导出配置
export {
    fofProductConfig,
    fixedIncomeProductConfig,
    secondaryProductConfig,
    overseasRmbProductConfig,
    getProductConfig,
    getAllProductConfigs
} from './configs/productConfigs'

export {
    getProductFeeRateConfigTableColumns,
    showTableColumn
} from './configs/tableColumns'
