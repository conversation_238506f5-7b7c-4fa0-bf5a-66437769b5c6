/*
 * @Description: vite默认配置项
 * @Author: chaohui.wu
 * @Date: 2023-06-20 16:43:31
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:12:21
 * @FilePath: /crm-template/vite.config.ts
 *
 */
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import autoprefixer from 'autoprefixer'
import eslintPlugin from 'vite-plugin-eslint'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import ckeditor5 from '@ckeditor/vite-plugin-ckeditor5'
import { createRequire } from 'node:module'
const require = createRequire(import.meta.url)
const path = require('path')
const resolve = (dir: string) => path.join(__dirname, dir)
process.env.VUE_APP_TIME = new Date().toLocaleString()

// https://vitejs.dev/config/
export default defineConfig({
    base: './',
    plugins: [
        vue(),
        ckeditor5({ theme: require.resolve('@ckeditor/ckeditor5-theme-lark') }),
        AutoImport({
            eslintrc: {
                enabled: true,
                filepath: '.eslintrc-auto-import.json',
                globalsPropValue: true
            },
            imports: ['vue', 'vue-router', 'pinia'],
            dts: true,
            resolvers: [ElementPlusResolver()]
        }),
        Components({
            dirs: ['src/components'],
            resolvers: [ElementPlusResolver()]
        }),
        createSvgIconsPlugin({
            // 要缓存的图标文件夹
            iconDirs: [resolve('src/assets/icons')],
            // 执行 icon name 的格式
            symbolId: 'icon-[name]'
        }),
        eslintPlugin({
            include: [
                'src/*.{js,jsx,vue,ts,tsx}',
                'src/**/*.{js,jsx,vue,ts,tsx}',
                'src/**/**/*.{js,jsx,vue,ts,tsx}',
                'src/**/**/**/*.{js,jsx,vue,ts,tsx}',
                'src/**/**/**/**/*.{js,jsx,vue,ts,tsx}',
                'src/**/**/**/**/**/*.{js,jsx,vue,ts,tsx}'
            ],
            cache: false
        })
    ],
    resolve: {
        extensions: ['.ts', '.js', '.vue', '.json'],
        alias: {
            '@': resolve('src')
        }
    },
    // 解决fsevents模块报错问题
    optimizeDeps: { exclude: ['fsevents'] },
    css: {
        // css预处理器
        preprocessorOptions: {
            less: {
                charset: false,
                additionalData: '@import "@/assets/css/variable.less";'
            }
        },
        modules: {
            scopeBehaviour: 'local',
            localsConvention: 'camelCase'
        },
        postcss: { plugins: [autoprefixer()] },
        devSourcemap: true
    },
    server: {
        // host: '',
        // port: 3088,
        // strictPort: false,
        open: true,
        proxy: {
            '/assetCurrent': {
                // target: 'http://**************:8087/crm-asset/',
                target: 'http://localhost:8080/crm-cgi/inner/',
                changeOrigin: true,
                rewrite: path => path.replace(/^\/assetCurrent/, '')
            },
            '/oldAsset': {
                // target: 'http://**************:8087/crm-asset/',
                target: 'http://crm.it34.k8s.howbuy.com/crm-asset/',
                changeOrigin: true,
                rewrite: path => path.replace(/^\/oldAsset/, '')
            },
            '/hdCurrent': {
                target: 'http://localhost:8085/',
                changeOrigin: true,
                rewrite: path => path.replace(/^\/hdCurrent/, '')
            }
        }
    },
    build: {
        assetsDir: 'static',
        minify: true,
        target: ['chrome89', 'edge89', 'firefox89', 'safari15'],
        sourcemap: false,
        // target: 'esnext',
        // cssCodeSplit: true,
        rollupOptions: {
            input: {
                index: resolve('index.html')
            },
            output: {
                chunkFileNames: 'static/js/[hash].js',
                entryFileNames: 'static/js/[hash].js',
                assetFileNames: 'static/[ext]/[hash].[ext]'
            }
        }
    }
})
