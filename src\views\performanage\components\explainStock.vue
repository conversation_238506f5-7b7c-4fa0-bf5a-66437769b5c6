<!--
 * @Description: 风险测评问卷弹框
 * @Author: chaohui.wu
 * @Date: 2023-03-23 13:26:04
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-21 13:39:50
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/explainStock.vue
 *  
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        title="说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <template #default>
            <div class="middle-content">
                <table>
                    <tr>
                        <th style="white-space: nowrap">业务类型</th>
                        <th>业务含义</th>
                        <th>业务说明</th>
                    </tr>
                    <tr>
                        <td>综拓 &nbsp;&nbsp;&nbsp;</td>
                        <td>机构给投顾的介绍费存续D</td>
                        <td>
                            ①介绍投顾在职:不管管理层是否离职换新,投顾和管理层都有存续D;<br />
                            ②介绍投顾离职:投顾和管理层不算存续D
                        </td>
                    </tr>
                    <tr>
                        <td>家办</td>
                        <td>家办业务壳的存续D</td>
                        <td>
                            ①成交投顾在职:不管管理层是否离职换新,投顾和管理层都有存续D;<br />②成交投顾离职:投顾和管理层不算存续D
                        </td>
                    </tr>
                    <tr>
                        <td>创新</td>
                        <td>创新业务的绩效方案选择“方案2”,且存续D总保费(RMB)≥40万单子的存续D</td>
                        <td>
                            ①存续D计算时间:只算1年<br />②成交时的投顾、管理层:算存续D;<br />③中途投顾或管理层离职,新投顾或新管理层不算存续D
                        </td>
                    </tr>
                    <tr>
                        <td>份额转让</td>
                        <td>份额转让前的存续D</td>
                        <td>
                            ①按份额转让发生的时间,将份额转让前的存量,按当月存续时间折算回补给原投顾<br />②新投顾不涉及手工(计算存续D时已经按日均折算过了)
                        </td>
                    </tr>
                </table>
            </div>
            <br />
            <b>核算说明：</b><br />
            <span style="font-weight: bold">1)仅核算审核通过的数据。</span>请在每月计算存续D之前处理
            「审核状态」=待审核/审核不通过 的数据。<br />
            <span style="font-weight: bold">2)根据录入的数据核算（规模可录入负数），计算公式：</span
            ><br />
            &nbsp;&nbsp;&nbsp;
            <span style="font-size: 13px"
                >理财师手工存续D=规模*产品存续D系数*客户折算系数*调整比例</span
            ><br />
            &nbsp;&nbsp;&nbsp;
            <span style="font-size: 13px">管理层手工存续D=规模*产品存续D系数*管理系数*调整比例</span
            ><br />
            <span style="font-weight: bold; color: red"
                >3)家办、综拓业务在投顾离职时,请注意调整各层级的存续D计算结束时间</span
            ><br />
            <span style="font-weight: bold; color: red"
                >4)在销售人员层级降级时,请注意调整存续D计算结束时间;当销售人员层级升级时,请注意增加对应层级的存续D数据</span
            ><br />
        </template>

        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">关闭</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { useVisible } from '@/views/common/scripts/useVisible'
    import { messageInfoTableColumn, showTableColumn } from './data/tableData'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )

    const tableData = [
        {
            bussType: '综拓',
            bussMean: '机构给投顾的介绍费存续D',
            bussInfo:
                '①介绍投顾在职:不管管理层是否离职换新,投顾和管理层都有存续D;<br> ②介绍投顾离职:投顾和管理层不算存续D'
        },
        {
            bussType: '家办',
            bussMean: '家办业务壳的存续D',
            bussInfo:
                '①成交投顾在职:不管管理层是否离职换新,投顾和管理层都有存续D;②成交投顾离职:投顾和管理层不算存续D'
        },
        {
            bussType: '创新',
            bussMean: '创新业务的绩效方案选择“方案2”,且存续D总保费(RMB)≥40万单子的存续D',
            bussInfo:
                '①存续D计算时间:只算1年②成交时的投顾、管理层:算存续D;③中途投顾或管理层离职,新投顾或新管理层不算存续D'
        },
        {
            bussType: '份额转让',
            bussMean: '份额转让前的存续D',
            bussInfo:
                '①按份额转让发生的时间,将份额转让前的存量,按当月存续时间折算回补给原投顾②新投顾不涉及手工(计算存续D时已经按日均折算过了)'
        }
    ]
    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            messageInfoTableColumn.map((item: { key: any }) => item.key),
            messageInfoTableColumn
        )
    })

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()

    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })
</script>

<style lang="less" scoped>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 8px;
        text-align: left;
        border: 1px solid black;
    }

    th {
        background-color: #f2f2f2;
    }

    .middle-content {
        padding: 10px 0;
    }
</style>
