import { transMapToArray } from '@/utils/dataChange'

export enum CUST_HK_INFO_OPER_PERMISSION {
    EXPORT = '1',
    SELECT_COLUMN = '2',
    VIEW_DETAIL = '3',
    VIEW_MOBILE_REAL = '4',
    VIEW_IDNO_REAL = '5'
}

export enum PIGGY_HK_DEAL_OPER_PERMISSION {
    EXPORT = '1',
    VIEW_DETAIL = '2'
}

// 健康客户统计权限常量
export const HEALTH_CUSTOMER_OPER_PERMISSION = {
    EXPORT: '1',
    CUST_EXPORT: '2',
    HEALTH_EXPORT: '3'
}

export enum NET_INCREASE_2025_OPER_PERMISSION {
    EXPORT = '1',
    DETAIL = '2'
}
