<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 投顾 -->
                <label-item label="投顾">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <!-- 是否参与考核 -->
                <label-item label="是否参与考核">
                    <crm-select
                        v-model="queryForm.isKpi"
                        :placeholder="isKpi.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="isKpi.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 考核项 -->
                <label-item label="考核项">
                    <crm-select
                        v-model="queryForm.kpiItem"
                        :placeholder="kpiItem.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="kpiItem.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 业务类型 -->
                <label-item label="业务类型">
                    <crm-select
                        v-model="queryForm.busiType"
                        :placeholder="busiType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="busiType.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 在职状态 -->
                <label-item :label="workState.label">
                    <crm-select
                        v-model="queryForm.workState"
                        :placeholder="workState.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="workState.selectList"
                        :style="{ width: '130px' }"
                        @change="handleworkState"
                    />
                </label-item>
                <!-- 交易时间选择框 -->
                <label-item :label="ackDt.label">
                    <date-range
                        v-model="queryForm.ackDt"
                        show-format="YYYY-MM-DD"
                        :placeholder="ackDt.placeholder"
                        style-type="fund"
                    />
                </label-item>

                <!-- 投顾code -->
                <label-item :label="userId.label" class-name="text-left">
                    <crm-input
                        v-model="queryForm.userId"
                        :placeholder="userId.placeholder"
                        :clearable="true"
                        :style="{ width: '110px' }"
                    />
                </label-item>
            </template>
            <template #operationBtns>
                <Text type="info" size="small" style="font-size: 14px"
                    >最后更新时间：
                    <span class="last-update-time">{{ lastUpdateTimeView }}</span>
                    , 统计截止时间:
                    <span class="last-update-time">{{ dataDeadlineView }}</span>
                </Text>
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="exportHandle"
                    >导出</crm-button
                >
                <crm-button
                    size="small"
                    :radius="true"
                    :icon="InfoFilled"
                    plain
                    @click="handleExplain"
                    >说明</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="true"
                    :no-select="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="100"
                >
                    <template #operation="{ scope }">
                        <el-button
                            v-if="detailShow"
                            size="small"
                            :text="true"
                            link
                            @click="handleDetail(scope.row)"
                            >数据明细</el-button
                        >
                    </template>
                </base-table>
            </template>
        </table-wrapper>
        <ExplainNetIncrease v-model="explainDialogVisiable"></ExplainNetIncrease>
    </div>
</template>

<script lang="ts" setup>
    import { Plus, Download, InfoFilled } from '@element-plus/icons-vue'
    import { fetchRes } from '@/utils'
    import { dataList } from './data/labelData'
    import { assetRepDownloadTableColumn, showTableColumn } from './data/tableData'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import ExplainNetIncrease from '@/views/report/kpi/components/explainNetIncrease.vue'
    import { NET_INCREASE_OPER_PERMISSION } from '@/constant/kpiConst'

    import {
        // eslint-disable-next-line camelcase
        NetIncreaseOverseasQuery,
        NetIncreaseOverseasExport,
        NetIncreaseOverseasInit
    } from '@/api/project/report/kpi/netIncreaseOverseas/netIncreaseOverseas'

    import { getMenuPermission } from '@/api/project/common/common'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import LabelItem from '@/components/moduleBase/LabelItem.vue'
    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const { workState, busiType, isKpi, kpiItem, ackDt, userId } = dataList
    const consStatus = ref<string>('1') //不包含离职人员
    const module = ref<string>('140804')
    const exportShow = ref<boolean>(false)
    const detailShow = ref<boolean>(false)

    const listLoading = ref<boolean>(false)

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        busiType = ''
        isKpi = ''
        kpiItem = ''
        workState = ''
        ackDt = {
            startDate: '',
            endDate: ''
        }
        userId = ''
    }

    const queryForm = reactive(new QueryForm())
    /**
     * @description: 上一次点了查询的条件列表
     * @return {*}
     */
    const queryFormAction = new QueryForm()

    /**
     * @description: 说明展示隐藏
     * @param explainDialogVisiable 展示隐藏
     * @method handleExplain 触发方法
     * @return {*}
     */
    const explainDialogVisiable = ref<boolean>(false)
    const handleExplain = () => {
        explainDialogVisiable.value = true
    }
    const detailDialogVisiable = ref<boolean>(false)
    /**
     * @description: 明细
     * @param val 明细数据
     * @method handleDetail 触发方法
     * @return {*}
     */
    const handleDetail = (val: any): void => {
        let { consCode } = val || {}
        console.log('handleDetail1' + consCode)
        if (!consCode) {
            console.log('handleDetail2' + queryFormAction.orgvalue.consCode)
            if (queryFormAction.orgvalue.consCode === undefined) {
                consCode = ''
            } else {
                consCode = queryFormAction.orgvalue.consCode
            }
            console.log('handleDetail3' + consCode)
        }
        window.open(`${
            window.location.origin + window.location.pathname
        }#/netincreaseoverseasdetail?consCode=${consCode}
        &orgCode=${queryFormAction.orgvalue.orgCode}&iskpi=${queryFormAction.isKpi}&kpiItem=${
            queryFormAction.kpiItem
        }&busiType=${queryFormAction.busiType}&ackDtStart=${
            queryFormAction.ackDt.startDate
        }&ackDtEnd=${queryFormAction.ackDt.endDate}&workState=${queryFormAction.workState}`)
    }

    /**
     * @description: 分页数据
     * @return {*}
     */
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    const lastUpdateTimeView = ref<any>()
    const dataDeadlineView = ref<any>()

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])
    const selectList = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            assetRepDownloadTableColumn.map(item => item.key),
            assetRepDownloadTableColumn
        )
    })

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            orgCode: queryForm.orgvalue.orgCode,
            consCode: queryForm.orgvalue.consCode,
            isKpi: queryForm.isKpi,
            kpiItem: queryForm.kpiItem,
            busiType: queryForm.busiType,
            workState: queryForm.workState,
            ackDtStart: queryForm.ackDt.startDate,
            ackDtEnd: queryForm.ackDt.endDate,
            userId: queryForm.userId
        }
        queryFormAction.busiType = queryForm.busiType
        queryFormAction.isKpi = queryForm.isKpi
        queryFormAction.kpiItem = queryForm.kpiItem
        queryFormAction.workState = queryForm.workState
        queryFormAction.orgvalue = queryForm.orgvalue
        queryFormAction.ackDt.startDate = queryForm.ackDt.startDate ? queryForm.ackDt.startDate : ''
        queryFormAction.ackDt.endDate = queryForm.ackDt.endDate ? queryForm.ackDt.endDate : ''
        fetchRes(NetIncreaseOverseasQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { list } = resObj
                tableData.value = list
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    const initData = async () => {
        listLoading.value = true
        const params = {
            menuCode: '140804'
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode === NET_INCREASE_OPER_PERMISSION.EXPORT &&
                            item.display === '1'
                        ) {
                            exportShow.value = true
                        }
                        if (
                            item.operateCode === NET_INCREASE_OPER_PERMISSION.DETAIL &&
                            item.display === '1'
                        ) {
                            detailShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
        fetchRes(NetIncreaseOverseasInit(), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { lastUpdateTime, dataDeadline } = resObj
                lastUpdateTimeView.value = lastUpdateTime
                dataDeadlineView.value = dataDeadline
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    const handleworkState = (val: string) => {
        queryForm.workState = val
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const params = {
            orgCode: queryForm.orgvalue.orgCode,
            consCode: queryForm.orgvalue.consCode,
            isKpi: queryForm.isKpi,
            kpiItem: queryForm.kpiItem,
            busiType: queryForm.busiType,
            workState: queryForm.workState,
            ackDtStart: queryForm.ackDt.startDate,
            ackDtEnd: queryForm.ackDt.endDate,
            userId: queryForm.userId
        }
        const res: any = await NetIncreaseOverseasExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }
    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        fetchConsOrgList('', module.value, '1')
        initData()
    })
</script>
<style lang="less" scoped></style>
@/views/common/scripts/consOrgListData
