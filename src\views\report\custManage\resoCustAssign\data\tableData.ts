/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const resoCustAssignTableColumn: TableColumnItem[] = [
    {
        key: 'origU1Name',
        label: '中心',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'origU2Name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'origU3Name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'origConsname',
        label: '分配投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'isWork',
        label: '是否在职',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'conscustno',
        label: '投顾客户号',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custname',
        label: '客户姓名',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assignTime',
        label: '分配日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'assignReason',
        label: '资源类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fcontactDt',
        label: '首次拜访日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'isInstanthandle',
        label: '是否及时处理',
        width: 120,
        formatter: formatTableValue
    },

    {
        key: 'isRepeat',
        label: '是否重复',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'isDeal',
        label: '是否成交',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ftradeDt',
        label: '高端首次交易日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'firstTradePeriod',
        label: '首次成交周期（天）',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ftradeConsname',
        label: '首次成交投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ftradeU1Name',
        label: '首次成交中心',
        width: 120,
        formatter: formatTableValue
    },

    {
        key: 'ftradeU2Name',
        label: '首次成交区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ftradeU3Name',
        label: '首次成交分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ftradeFundCode',
        label: '产品代码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ftradeFundName',
        label: '首次购买产品',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ftradeAckAmt',
        label: '首次成交金额(RMB)',
        width: 140,
        formatter: formatTableValue
    },
    {
        key: 'bqxlmc',
        label: '首交一级策略',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custSource',
        label: '客户来源',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'curU1Name',
        label: '当前中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'curU2Name',
        label: '当前区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'curU3Name',
        label: '当前分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'curConsname',
        label: '当前投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'curAssignTime',
        label: '当前投顾分配时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'isBindWeiXin',
        label: '添加企微',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'thisMonMsgCount',
        label: '本月企微沟通次数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'lastMonMsgCount',
        label: '上月企微沟通次数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'secondHand',
        label: '几手',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
