/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const consCombinationConfigTableColumn: TableColumnItem[] = [
    {
        key: 'productCode',
        label: '组合代码',
        width: 160,
        formatter: formatTableValue
    },
    {
        key: 'productName',
        label: '组合名称',
        width: 200,
        formatter: formatTableValue
    },
    {
        key: 'overseasRatio',
        label: '海外占比',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'overEighty',
        label: '海外占比>=80%',
        width: 140,
        formatter: formatTableValue
    },
    {
        key: 'startDt',
        label: '开始日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'endDt',
        label: '结束日期',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
