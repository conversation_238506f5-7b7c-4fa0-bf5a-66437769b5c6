<!--
 * @Description:操作记录弹框
 * @Author: chaohui.wu
 * @Date: 2023-03-24 21:07:35
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-27 19:14:16
 * @FilePath: /crm-template/src/views/components/dialogCommon/adjustAmt/adjustAmtIndex.vue
 * @ 当前未解耦，有时间解耦后可提取到dialogCommomn中
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="800px"
        height="500px"
        :border="true"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <el-table :data="tableData" class="crm-base-table" @sortChange="handleSortChange">
                <el-table-column prop="operatetype" label="操作类型"></el-table-column>
                <el-table-column prop="operator" label="操作人"></el-table-column>
                <el-table-column prop="operateDt" label="操作时间" sortable></el-table-column>
                <el-table-column prop="auditState" label="审核状态"></el-table-column>
                <el-table-column prop="auditRemark" label="审核备注"></el-table-column>
            </el-table>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import { fetchRes } from '@/utils/index'
    import { queryDealLog } from '@/api/project/performanage/performanceAssetHandConf/performanceAssetHandConf'
    import { auditTableColumn, showTableColumn, SortOrderCumstom } from './data/tableData'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                title: string
                type: string
                id: string
            }
            isAssetHandConfAdd?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    title: '操作记录',
                    type: 'deallog',
                    id: ''
                }
            },
            isAssetHandConfAdd: false
        }
    )

    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })
    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
    }>()

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])
    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            auditTableColumn.map((item: { key: any }) => item.key),
            auditTableColumn
        )
    })

    const fetchList = () => {
        // 初始化
        fetchRes(queryDealLog({ id: props.transData.id }), {
            successCB: (resObj: any) => {
                const { data } = resObj
                console.log('data' + JSON.stringify(resObj))
                tableData.value = resObj
            },
            errorCB: () => {
                ElMessage({
                    type: 'error',
                    message: '请求失败'
                })
            },
            catchCB: () => {
                ElMessage({
                    type: 'error',
                    message: '请求失败'
                })
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }
    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        busiType = ''
        order = 'descending'
        sort = 'operateDt'
    }
    const queryForm = reactive(new QueryForm())
    // 默认排序
    const defaultSort = computed(() => {
        return { prop: queryForm.sort, order: queryForm.order }
    })
    interface SortParams {
        order: SortOrderCumstom
        prop: string
    }

    const handleSortChange = (val: SortParams) => {
        const { prop, order } = val
        tableData.value.sort((a, b) => {
            const timestampA = new Date(prop).getTime()
            const timestampB = new Date(prop).getTime()
            if (order) {
                return timestampA - timestampB
            }
            return timestampB - timestampA
        })
    }

    onBeforeMount(() => {
        fetchList()
    })
</script>

<style lang="less" scoped>
    .crm-base-table {
        thead {
            th {
                position: relative;
                color: @font_color_01;
                content: '\e6ff';
                border-right: 1px solid @bg_main_02 !important;
            }

            &:hover {
                th {
                    border-right: 1px solid #f8f8f8 !important;
                }

                th.el-table-column--selection,
                th:last-child {
                    border-right: 1px solid @bg_main_02 !important;
                }
            }
        }
        // 公共表格样式
        &.el-table {
            font-size: 12px;
            color: @font_color_05;

            .el-table__header {
                tr {
                    th {
                        height: 38px;
                        padding: 0;
                        font-weight: bold;
                        line-height: 38px;
                        color: @bg_main_03;
                        background-color: @border_color_01;
                        // border: none;

                        .cell {
                            padding: 0 10px;
                            font-family: 'Microsoft YaHei', '微软雅黑';
                            // height: 18px;
                            font-size: 12px;
                            font-weight: 400;
                            line-height: 17px;
                            color: @font_color;
                            .text-overflow();

                            .caret-wrapper {
                                display: inline-block;
                                width: 16px;
                                height: 17px;

                                .sort-caret {
                                    &.ascending {
                                        top: -3px;
                                    }

                                    &.descending {
                                        bottom: -1px;
                                    }
                                }
                            }
                        }

                        &.is-sortable.is-right {
                            .cell {
                                display: flex;
                                align-items: center;
                                justify-content: flex-end;

                                .caret-wrapper {
                                    .sort-caret {
                                        &.ascending {
                                            top: -2px;
                                        }

                                        &.descending {
                                            bottom: -2px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .el-table__body {
                td {
                    height: 37px;
                    padding: 0;
                    color: @font_color_02;
                    border: none;

                    .cell {
                        .el-button {
                            &.is-round {
                                height: 20px;
                                padding: 2px 8px 2px 6px;
                                color: @font_link;
                                border: 1px solid @font_link;
                                border-radius: 8px;
                            }

                            &.is-link {
                                color: @font_link;
                                // text-decoration: underline;
                            }

                            &.el-button--danger {
                                color: @theme_main;
                            }
                        }

                        .el-input__icon {
                            line-height: inherit;
                        }
                    }

                    &.el-table-column--selection {
                        .cell {
                            padding-right: 10px;
                        }
                    }
                }

                tr.el-table__row {
                    &.off-line-bg {
                        background-color: #f5f6fa;

                        .el-table__cell {
                            background-color: #f5f6fa;
                        }
                    }
                }
            }

            &.el-table--striped .el-table__body tr.el-table__row--striped td {
                background-color: #f5f6fa;
            }

            .el-table__fixed-right {
                &::before {
                    display: none;
                }
            }
            // 表格hover背景色
            .el-table--striped .el-table__body tr.el-table__row--striped.current-row td,
            .el-table__body tr.current-row > td,
            .el-table__body tr.hover-row.current-row > td,
            .el-table__body tr.hover-row.el-table__row--striped.current-row > td,
            .el-table__body tr.hover-row.el-table__row--striped > td,
            .el-table__body tr.hover-row > td {
                background-color: #fafafa;
            }

            .el-checkbox {
                .el-checkbox__input {
                    .el-checkbox__inner {
                        width: 12px;
                        height: 12px;
                        background-color: #fbfcfe;
                        border: 1px solid #e7e7ea;

                        &:hover {
                            border-color: #c01b26;
                        }

                        &::after {
                            top: 0;
                            left: 3px;
                            width: 3px;
                            height: 6px;
                        }
                    }

                    &.is-indeterminate .el-checkbox__inner {
                        background-color: #c01b26;
                        border-color: #c01b26;
                    }

                    &.is-checked {
                        .el-checkbox__inner {
                            background-color: #c01b26;
                            border-color: #c01b26;
                        }
                    }
                }
            }
        }
    }

    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
