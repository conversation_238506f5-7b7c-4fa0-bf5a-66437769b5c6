import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { ResoCustAssignParam } from './type/apiReqType.js'

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const resoCustAssignQuery = (params: ResoCustAssignParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/custManage/resoCustAssign/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const resoCustAssignExport = (params: ResoCustAssignParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/custManage/resoCustAssign/export',
            method: 'post',
            data: params
        })
    )
}
