/*
 * @Description: cgi ts格式
 * @Author: chao<PERSON>.wu
 * @Date: 2023-06-28 16:52:01
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:11:40
 * @FilePath: /crm-template/src/type/cgi.d.ts
 *
 */
export {}
declare module '@/type/cgi' {
    interface CGIRes<T> {
        /**
         * 代码 成功-0000 失败-0001 参数错误-0002
         */
        code: string
        data: T
        /**
         * 描述
         */
        description: string
    }
    interface TableListData<T> {
        /**
         * 当前第几页
         */
        page: number
        rows: T
        /**
         * 返回数据量
         */
        size: number
        /**
         * 总数
         */
        total: number
    }
    // 香港客户列表页数据
    interface HkCustListData<T> {
        list: T
    }
    export {
        CGIRes,
        TableListData,
        HkDefaultFileListData,
        AuditListData,
        HkArWhiteListListData,
        HkCustListData
    }
}
