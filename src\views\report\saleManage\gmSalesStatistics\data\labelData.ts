/*
 * @Description: 定义搜索的label列表
 * @Author: jian<PERSON><PERSON>.yang
 * @Date: 2024-04-01 10:40:30
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-04-01 10:40:30
 * @FilePath: /src/views/report/kpi/assetRepDownload/data/labelData.ts
 *
 */
export const dataList = {
    isAckOrder: {
        label: '订单是否确认',
        selectList: [
            {
                key: '',
                label: '全部'
            },
            {
                key: '1',
                label: '是'
            },
            {
                key: '0',
                label: '否'
            }
        ]
    },

    appDt: {
        label: '申请日期',
        placeholder: ['开始日期', '结束日期']
    },
    ackDt: {
        label: '确认日期',
        placeholder: ['开始日期', '结束日期']
    },

    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
