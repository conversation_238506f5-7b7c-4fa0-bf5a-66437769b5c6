<template>
    <div class="report-list-module">
        <table-wrap-cust
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
        >
            <template #searchArea>
                <table class="table-cust-select">
                    <tr>
                        <td>
                            <!-- 产品名称 -->
                            <LabelItemCust label="产品名称" minwidth="160px">
                                <autocomplete
                                    v-model="queryForm.label"
                                    :search-func="autoProd"
                                    @handleSet="handleProdCode"
                                />
                            </LabelItemCust>
                        </td>

                        <td>
                            <LabelItemCust label="核算产品类型">
                                <crm-select
                                    v-model="queryForm.accountProductType"
                                    filterable
                                    clearable
                                    label-format="label"
                                    value-format="key"
                                    :option-list="acountProductType.selectList"
                                    :style="{ width: '150px' }"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 时间范围 -->
                            <LabelItemCust label="时间范围" class-name="text-left">
                                <date-range
                                    v-model="queryForm.dateRange"
                                    show-format="YYYY-MM-DD"
                                    style-type="fund"
                                />
                            </LabelItemCust>
                        </td>
                        <td>
                            <!-- 分销机构 -->
                            <LabelItemCust :label="disOrgan.label" class-name="text-left">
                                <crm-select
                                    v-model="queryForm.disOrgan"
                                    filterable
                                    clearable
                                    multiple
                                    label-format="label"
                                    value-format="key"
                                    :option-list="disOrgan.selectList"
                                    :style="{ width: '160px' }"
                                />
                            </LabelItemCust>
                        </td>
                    </tr>
                </table>
            </template>
            <template #operationBtns>
                <ButtonCust size="small" :radius="true" plain @click.stop="queryList"
                    >查询</ButtonCust
                >
                <button-cust size="small" :radius="true" plain @click.stop="exportHandle"
                    >导出</button-cust
                >
            </template>
            <template #tableContentMiddle>
                <base-table-cust
                    :is-loading="listLoading"
                    :columns="productCoefficientHisTableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    :pops="{ selectFixed: 'left' }"
                >
                </base-table-cust>
            </template>

            <template #tableContentBottom>
                <pagination
                    :page="pageObj"
                    :page-sizes="[100, 200, 500, 1000, 2000]"
                    :total="pageObj.total"
                    @change="handleCurrentChange"
                />
            </template>
        </table-wrap-cust>
    </div>
</template>

<script lang="ts" setup>
    import { fetchRes } from '@/utils'
    import { dataList } from './data/labelData'
    import { ElMessage, ElLoading } from 'element-plus'

    import { productCoefficientHisTableColumn } from './data/tableData'
    import {
        productCoefficientHisQuery,
        productCoefficientHisExport
    } from '@/api/project/performanage/productCoefficientHis/productCoefficientHis'
    import { autoProd } from '@/api/project/common/common'
    import Autocomplete from '@/components/moduleBase/Autocomplete.vue'
    import BaseTableCust from '@/views/modBusiness/pageModule/components/BaseTableCust.vue'
    import LabelItemCust from '@/views/modBusiness/pageModule/components/LabelItemCust.vue'
    import TableWrapCust from '@/views/modBusiness/pageModule/components/TableWrapCust.vue'
    import ButtonCust from '@/views/modBusiness/pageModule/components/ButtonCust.vue'
    import { defineComponent } from 'vue'

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 100
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    const { acountProductType, disOrgan } = dataList

    const listLoading = ref<boolean>(false)

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        accountProductType = ''
        productCode = ''
        dateRange = {
            startDate: '',
            endDate: ''
        }
        disOrgan = []
        label = ''
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    const handleProdCode = (val: { label: string; productCode: string }) => {
        queryForm.productCode = val.productCode
        queryForm.label = val.label
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            fundCode: queryForm.productCode,
            dtBegin: queryForm.dateRange.startDate,
            dtEnd: queryForm.dateRange.endDate,
            disOrganList: queryForm.disOrgan,
            accountProductType: queryForm.accountProductType,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(productCoefficientHisQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                tableData.value = rows
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const params = {
            fundCode: queryForm.productCode,
            dtBegin: queryForm.dateRange.startDate,
            dtEnd: queryForm.dateRange.endDate,
            disOrganList: queryForm.disOrgan,
            accountProductType: queryForm.accountProductType
        }
        const allLoading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)'
        })
        const res: any = await productCoefficientHisExport(params)
        const { fileByte, name, errorMsg } = res.data
        if (errorMsg) {
            ElMessage({
                message: errorMsg,
                type: 'warning',
                duration: 2000
            })
            allLoading.close()
            return
        }
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
        allLoading.close()
    }

    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        console.log('onMounted')
    })
</script>
<style lang="less" scoped>
    .table-cust-select {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #dddddd;

        tr {
            background-color: #ffffff;

            td {
                border: 1px solid #dddddd;
            }
        }
    }
</style>
