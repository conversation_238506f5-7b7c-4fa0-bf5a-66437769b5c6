import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '../../mock.js'
import {
    boardDataParam,
    yxsAndLcjzBoardParam,
    favorPvReportParam,
    custVisitTableDataParam,
    getConsParam
} from './type/apiReqType.js'

/**
 * @description: 获取机构信息
 * @return {*}
 */
export function getOrgConsList(params: { modules: number }) {
    return axiosRequest({
        url: '/api/OrganCons/listOrgCons_json',
        method: 'get',
        params
    })
}

/**
 * @description: 获取机构下的所有投顾
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getDsConsByOrgcode_json(params: getConsParam) {
    return axiosRequest({
        url: '/api/OrganCons/getDsConsByOrgcode_json',
        method: 'post',
        data: params
    })
}

/**
 * @description: 获取客户行为数据  浏览、认申购
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getCustActData(params: boardDataParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/databorad/getCustActData',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 投顾、部门下客户访问情况
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getActCrmCustUvData(params: boardDataParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/databorad/getActCrmCustUvData',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 客户访问信息
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getActCrmPvCustInfoData(params: custVisitTableDataParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/databorad/getActCrmPvCustInfoData',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 客户浏览行为   明细查询
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getListFavorPv(params: favorPvReportParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/databorad/listFavorPvReportVue_json',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 获取高端存量看板数据
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getGdclBoradData(params: boardDataParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/databorad/getGdclBoradData',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 获取零售存量看板数据
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getLsclBoardData(params: boardDataParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/databorad/getLsclBoardData',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 高端净新增
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getJxzBroadData(params: boardDataParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/databorad/getJxzBroadData',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 高端销量
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getCustBookingVData(params: boardDataParam) {
    return axiosRequest(
        paramsMerge({
            timeout: 120000,
            url: '/api/databorad/getCustBookingVData',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 其他信息  二手成交客户 、净划入、潜在客户数
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getGdOtherInfo(params: boardDataParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/otherInfo/getGdOtherInfo',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 研习社 投顾/部门名下客户学习情况统计
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getYxsCrmonsultantData(params: yxsAndLcjzBoardParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/yxsAndLcjz/getYxsCrmonsultantData',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 研习社 客户学习情况总览
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getYxsCrmCustData(params: yxsAndLcjzBoardParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/yxsAndLcjz/getYxsCrmCustData',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 研习社  客户学习详情表  课程明细
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getYxsCrmCustCourseData(params: yxsAndLcjzBoardParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/yxsAndLcjz/getYxsCrmCustCourseData',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 理财九章 投顾报名人数 汇总
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getActCrmLcjzInfoData(params: yxsAndLcjzBoardParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/yxsAndLcjz/getActCrmLcjzInfoData',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 理财九章 某客户参会情况
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getActCrmLcjzCustInfoData(params: yxsAndLcjzBoardParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/yxsAndLcjz/getActCrmLcjzCustInfoData',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description:理财九章 客户参会情况 明细表
 * @return {*}
 */
// eslint-disable-next-line camelcase
export function getActCrmLcjzCustData(params: yxsAndLcjzBoardParam) {
    return axiosRequest(
        paramsMerge({
            url: '/api/yxsAndLcjz/getActCrmLcjzCustData',
            method: 'post',
            data: params
        })
    )
}
