<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            :show-export-btn="true"
            @searchFn="handleLoading"
            @exportFn="exportHandle"
        >
            <template #searchArea>
                <label-item :label="startDt.label" required>
                    <el-date-picker
                        v-model="queryForm.startDt"
                        type="date"
                        placeholder="选择日期"
                        format="YYYYMMDD"
                        value-format="YYYYMMDD"
                        :style="{ width: '150px' }"
                        @change="handleStartDt"
                    ></el-date-picker>
                    <span class="required" style="color: red">*</span>
                </label-item>
                <label-item :label="endDt.label" required>
                    <el-date-picker
                        v-model="queryForm.endDt"
                        type="date"
                        placeholder="选择日期"
                        format="YYYYMMDD"
                        value-format="YYYYMMDD"
                        :style="{ width: '150px' }"
                        @change="handleEndDt"
                    ></el-date-picker>
                    <span class="required" style="color: red">*</span>
                </label-item>
                <label-item :label="fundCode.label">
                    <crm-input
                        v-model="queryForm.fundCode"
                        :placeholder="fundCode.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
                <label-item :label="fundMan.label">
                    <crm-input
                        v-model="queryForm.fundMan"
                        :placeholder="fundMan.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <label-item :label="auditState.label">
                    <crm-select
                        v-model="queryForm.auditState"
                        :placeholder="auditState.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="auditState.selectList"
                        :style="{ width: '120px' }"
                        @change="handleAuditState"
                    />
                </label-item>
                <label-item :label="feeType.label">
                    <crm-select
                        v-model="queryForm.feeType"
                        :placeholder="feeType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="feeType.selectList"
                        :style="{ width: '120px' }"
                        @change="handleFeeType"
                    />
                    <span class="required" style="color: red">*</span>
                </label-item>
                <label-item :label="fundName.label">
                    <crm-input
                        v-model="queryForm.fundName"
                        :placeholder="fundName.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
                <label-item :label="custName.label">
                    <crm-input
                        v-model="queryForm.custName"
                        :placeholder="custName.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
                <label-item :label="conscustNo.label">
                    <crm-input
                        v-model="queryForm.conscustNo"
                        :placeholder="conscustNo.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
                <label-item :label="settlePeriod.label">
                    <el-select
                        v-model="queryForm.settlePeriodType"
                        placeholder="结算周期"
                        @change="handleSettlePeriodTypeChange"
                    >
                        <el-option label="年度" value="annual"></el-option>
                        <el-option label="半年" value="half_year"></el-option>
                        <el-option label="季度" value="quarterly"></el-option>
                        <el-option label="月度" value="monthly"></el-option>
                    </el-select>
                    <el-select v-model="queryForm.settleYear" placeholder="周期年份">
                        <el-option
                            v-for="year in settleYearOptions"
                            :key="year.value"
                            :label="year.label"
                            :value="year.value"
                            :style="{ width: '80px' }"
                        ></el-option>
                    </el-select>
                    <el-select v-model="queryForm.settlePeriodDetail" placeholder="具体周期">
                        <el-option
                            v-for="period in settlePeriodDetailOptions"
                            :key="period.value"
                            :label="period.label"
                            :value="period.value"
                            :style="{ width: '80px' }"
                        ></el-option>
                    </el-select>
                </label-item>
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    :cell-style="getDeviationStyle"
                    style="width: 100%"
                    :show-operation="true"
                    :no-select="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    @select="changeSelectTable"
                    @selectionChange="changeSelectTable"
                >
                    <template #operation="{ scope }">
                        <el-button
                            v-if="addShow && scope.row.fundCode !== '合计:'"
                            size="small"
                            :text="true"
                            link
                            @click="handleEdit(scope.row)"
                            >新增审核</el-button
                        >
                        <el-button
                            v-if="detailShow && scope.row.fundCode !== '合计:'"
                            size="small"
                            :text="true"
                            link
                            @click="handleDetail(scope.row)"
                            >明细</el-button
                        >
                    </template>
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>
        <edit-product-index
            v-if="editDialogVisible"
            v-model="editDialogVisible"
            :finance-audit-edit="true"
            :finance-audit-data="editdata"
            @callback="handleClose()"
        >
        </edit-product-index>
    </div>
</template>

<script lang="ts" setup>
    import { downloadFile, fetchRes, message } from '@/utils'
    import { dataList } from './data/labelData'
    import { fixIncomeFeeTableColumn, showTableColumn } from './data/tableData'
    import {
        FixIncomeFeeExport,
        FixIncomeFeeQuery
    } from '@/api/project/report/finance/fixIncomeFee/fixIncomeFee'
    import { getMenuPermission } from '@/api/project/common/common'
    import { FINANCE_ORERATE_OPER_PERMISSION } from '@/constant/financeConst'
    import { ResVO } from '@/type'
    import EditProductIndex from '@/views/report/finance/auditComponents/financeAuditAdd.vue'
    import { ElMessage } from 'element-plus'

    const {
        startDt,
        endDt,
        fundCode,
        fundMan,
        feeType,
        auditState,
        settlePeriod,
        settleYear,
        settlePeriodDetail,
        fundName,
        custName,
        conscustNo
    } = dataList

    const module = ref<string>('B071213')
    const exportShow = ref<boolean>(true)
    const addShow = ref<boolean>(false)
    const detailShow = ref<boolean>(true)
    const editDialogVisible = ref<boolean>(false)

    const listLoading = ref<boolean>(false)

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        producType = ''
        startDt = ''
        endDt = ''
        fundCode = ''
        fundMan = ''
        feeType = '1'
        fundName = ''
        custName = ''
        conscustNo = ''
        auditState = '0'
        settlePeriodType = 'quarterly' // 默认季度
        settleYear = new Date().getFullYear() // 默认当年
        settlePeriodDetail = '' // 具体周期
        settleStartDt = ''
        settleEndDt = ''
    }
    const queryForm = reactive(new QueryForm())

    const getDefaultQuarterDates = () => {
        const now = new Date()
        const currentYear = now.getFullYear()
        let startMonth, endMonth

        // 根据当前月份确定上个季度的月份范围
        if (now.getMonth() < 3) {
            // 第四季度
            startMonth = 9 // 1月
            endMonth = 11 // 3月
            queryForm.settlePeriodDetail = '4'
        } else if (now.getMonth() < 6) {
            // 第一季度
            startMonth = 0 // 4月
            endMonth = 2 // 6月
            queryForm.settlePeriodDetail = '1'
        } else if (now.getMonth() < 9) {
            // 第二季度
            startMonth = 3 // 7月
            endMonth = 5 // 9月
            queryForm.settlePeriodDetail = '2'
        } else {
            // 第三季度
            startMonth = 6 // 10月
            endMonth = 8 // 12月
            queryForm.settlePeriodDetail = '3'
        }

        // 创建日期对象数组
        const startDate = new Date(currentYear, startMonth, 1)
        const endDate = new Date(currentYear, endMonth + 1, 0) // 加1 并用0作为天数可以获取到月底的日期
        return { startDate, endDate: endDate }
    }

    // 格式化日期为 YYYYMMDD 格式
    const formatDate = (date: any) => {
        const year = date.getFullYear()
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        return `${year}${month}${day}`
    }

    // 初始化日期范围
    const { startDate, endDate } = getDefaultQuarterDates()
    queryForm.startDt = formatDate(startDate) // 格式化日期函数
    queryForm.endDt = formatDate(endDate)

    const handleFeeType = (val: string) => {
        queryForm.feeType = val
    }
    const handleAuditState = (val: string) => {
        queryForm.auditState = val
    }
    const handleStartDt = (val: string) => {
        queryForm.startDt = val
    }
    const handleEndDt = (val: string) => {
        queryForm.endDt = val
    }
    /**
     * @description: 上一次点了查询的条件列表
     * @return {*}
     */
    const queryFormAction = new QueryForm()

    const currentYear = ref(new Date().getFullYear())

    const getDeviationStyle = (row: any, column: any) => {
        console.log(row.column.label)
        console.log(row.row.deviation)
        const deviation = row.row.deviation
        console.log(deviation)
        if (row.column.label == '误差(%)') {
            const color = Math.abs(deviation) > 5 ? 'red' : 'green'
            return { color } // 返回一个对象，包含动态生成的样式
        }
    }

    // 填充年份选项的计算属性
    const settleYearOptions = computed(() => {
        const options = []
        for (let year = currentYear.value; year >= 2018; year--) {
            options.push({ label: year.toString(), value: year })
        }
        return options
    })

    // 根据周期类型填充具体周期的选项
    const settlePeriodDetailOptions = computed(() => {
        let options = []
        switch (queryForm.settlePeriodType) {
            case 'annual':
                options = [{ label: '全年', value: '全年' }]
                break
            case 'half_year':
                options = ['上半年', '下半年'].map(label => ({ label, value: label }))
                break
            case 'quarterly':
                options = [
                    { label: '一季度', value: '1' },
                    { label: '二季度', value: '2' },
                    { label: '三季度', value: '3' },
                    { label: '四季度', value: '4' }
                ]
                break
            case 'monthly':
                for (let month = 1; month <= 12; month++) {
                    options.push({ label: `${month}月`, value: `${month}月` })
                }
                break
            default:
                options = [{ label: '请选择', value: '' }]
        }
        return options
    })

    const handleSettlePeriodTypeChange = (value: any) => {
        queryForm.settlePeriodType = value
        // 重置年份和具体周期为默认值
        queryForm.settleYear = currentYear.value
        queryForm.settlePeriodDetail = settlePeriodDetailOptions.value[0].value
    }

    const handleSettleYearChange = (value: any) => {
        queryForm.settleYear = value
        // 可以在这里添加基于年份变化的逻辑，如果需要
    }

    const generateDateString = () => {
        let settleStartDate = ''
        let settleEndDate = ''
        const year = queryForm.settleYear
        switch (queryForm.settlePeriodType) {
            case 'annual':
                // 年度默认为年初到年末
                settleStartDate = `${year}0101`
                settleEndDate = `${year}1231`
                break
            case 'half_year':
                // 半年：上半年为1月1日到6月30日，下半年为7月1日到12月31日
                const halfYear = queryForm.settlePeriodDetail === '上半年' ? 1 : 7
                settleStartDate = `${year}${halfYear}0101`
                settleEndDate = `${year}${halfYear > 1 ? '1231' : '0630'}`
                break
            case 'quarterly':
                // 季度：根据季度选择不同的开始和结束日期
                const quarter = parseInt(queryForm.settlePeriodDetail, 10)
                const startMonth = (quarter - 1) * 3 + 1
                const endMonth = quarter * 3
                settleStartDate = `${year}${String(startMonth).padStart(2, '0')}01`
                settleEndDate = `${year}${String(endMonth).padStart(2, '0')}31`
                break
            case 'monthly':
                // 月份：根据月份选择第一天和月末最后一天
                const month = parseInt(queryForm.settlePeriodDetail, 10) - 1
                settleStartDate = `${year}${String(month + 1).padStart(2, '0')}01`
                settleEndDate = `${year}${String(month + 1).padStart(2, '0')}31`
                break
            default:
                // 默认情况或未选择
                settleStartDate = `${year}0101`
                settleEndDate = `${year}1231`
                break
        }

        return {
            settleStartDt: settleStartDate,
            settleEndDt: settleEndDate
        }
    }

    watchEffect(() => {
        const dateRange = generateDateString()
        queryForm.settleStartDt = dateRange.settleStartDt
        queryForm.settleEndDt = dateRange.settleEndDt
    })

    /**
     * @description: 编辑
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleEdit = (val: any): void => {
        stockObj.value = {
            title: '新增审核',
            id: '1',
            type: 'add'
        }
        console.log(val)
        console.log(queryForm)
        editdata.value.id = val.id
        editdata.value.fundCode = val.fundCode
        editdata.value.fundName = val.fundName
        editdata.value.feeType = val.feeType
        editdata.value.preAmount = val.fee
        editdata.value.preAmountTax = val.feeTax
        editdata.value.realAmount = val.realAmount
        editdata.value.realAmountTax = val.realAmountTax
        editdata.value.deviation = val.deviation
        editdata.value.preStartdt = val.startDt
        editdata.value.preEnddt = val.endDt
        editdata.value.settlePeriod = val.settlePeriod
        editdata.value.settlePeriodType = queryForm.settlePeriodType.toString()
        editdata.value.settleYear = queryForm.settleYear.toString()
        editdata.value.settlePeriodDetail = queryForm.settlePeriodDetail
        editdata.value.remark = val.remark
        editdata.value.settleStartDt = queryForm.settleStartDt
        editdata.value.settleEndDt = queryForm.settleEndDt
        editDialogVisible.value = true
    }

    /**
     * @description: 关闭弹框刷新数据
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleClose = (): void => {
        queryList()
    }
    /**
     * @description: 新增
     * @return {*}
     */
    const stockObj = ref({
        title: '',
        id: '1',
        type: ''
    })

    /**
     * @description: 明细
     * @param val 明细数据
     * @method handleDetail 触发方法
     * @return {*}
     */
    const handleDetail = (val: any): void => {
        const {
            fundCode,
            fundName,
            manager,
            feeType,
            auditState,
            startDt,
            endDt,
            custName,
            conscustNo
        } = val || {}
        console.log(val)
        console.log(queryForm)
        if (queryForm.feeType == '10') {
            window.open(
                `${
                    window.location.origin + window.location.pathname
                }#/secondProductFeeDetail?fundCode=${
                    fundCode ? fundCode : queryForm.fundCode
                }&startDt=${startDt ? startDt : queryForm.startDt}&endDt=${
                    endDt ? endDt : queryForm.endDt
                }&feeType=${queryForm.feeType}&custName=${
                    custName ? custName : queryForm.custName
                }&conscustNo=${conscustNo ? conscustNo : queryForm.conscustNo}&auditState=${
                    queryForm.auditState
                }`
            )
        }
        if (queryForm.feeType != '10') {
            window.open(
                `${
                    window.location.origin + window.location.pathname
                }#/fixIncomeFeeDetail?fundCode=${
                    fundCode ? fundCode : queryForm.fundCode
                }&startDt=${startDt ? startDt : queryForm.startDt}&endDt=${
                    endDt ? endDt : queryForm.endDt
                }&feeType=${queryForm.feeType}&custName=${
                    custName ? custName : queryForm.custName
                }&conscustNo=${conscustNo ? conscustNo : queryForm.conscustNo}&auditState=${
                    queryForm.auditState
                }`
            )
        }
    }

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const panelActiveName = ref<string>('myReport')
    const tableData = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            fixIncomeFeeTableColumn.map(item => item.key),
            fixIncomeFeeTableColumn
        )
    })

    const checkDatepicker = (modelValue: any) => {
        if (!modelValue) {
            ElMessage.error('该日期字段是必填的')
            return false
        }
        return true
    }
    //查詢
    const queryList = async () => {
        if (!checkDatepicker(queryForm.startDt)) {
            return
        }
        if (!checkDatepicker(queryForm.endDt)) {
            return
        }
        if (!queryForm.feeType) {
            ElMessage.error('费用类型不能为空')
            return
        }
        listLoading.value = true
        const params = {
            startDt: queryForm.startDt,
            endDt: queryForm.endDt,
            fundCode: queryForm.fundCode,
            fundMan: queryForm.fundMan,
            feeType: queryForm.feeType,
            fundName: queryForm.fundName,
            custName: queryForm.custName,
            conscustNo: queryForm.conscustNo,
            auditState: queryForm.auditState,
            settleStartDt: queryForm.settleStartDt,
            settleEndDt: queryForm.settleEndDt,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(FixIncomeFeeQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                // debugger
                tableData.value = rows
                // TODO list接口待添加分页数据
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    // table选中数组
    const tableSelectList = ref<object[]>([])
    // table选中事件
    const changeSelectTable = (sel: ResVO[]): void => {
        // 默认选择按成立时间排序
        tableSelectList.value = unref(sel)
            .map((item: any) => {
                return item
            })
            .filter(item => item)
    }

    const initData = async () => {
        const params = {
            menuCode: 'B071213'
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode === FINANCE_ORERATE_OPER_PERMISSION.ADD &&
                            item.display === '1'
                        ) {
                            addShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {},
            catchCB: () => {},
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    // 到编辑页面能看到的数据
    const editdata = ref({
        id: '',
        productType: '1',
        fundCode: '',
        fundName: '',
        subFundName: '',
        feeType: '',
        preAmount: '',
        preAmountTax: '',
        realAmount: '',
        realAmountTax: '',
        deviation: '',
        preStartdt: '',
        preEnddt: '',
        settlePeriod: '',
        settlePeriodType: '',
        settleYear: '',
        settlePeriodDetail: '',
        remark: '',
        settleStartDt: '',
        settleEndDt: ''
    })

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const pdfUrl = ref<string>('')
    const exportList = async () => {
        const params = {
            startDt: queryForm.startDt,
            endDt: queryForm.endDt,
            fundCode: queryForm.fundCode,
            fundMan: queryForm.fundMan,
            feeType: queryForm.feeType,
            fundName: queryForm.fundName,
            custName: queryForm.custName,
            conscustNo: queryForm.conscustNo,
            auditState: queryForm.auditState,
            settleStartDt: queryForm.settleStartDt,
            settleEndDt: queryForm.settleEndDt
        }
        const res: any = await FixIncomeFeeExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }

    /**
     * @description 页面挂载的时候就初始化信息
     */
    onMounted(() => {
        listLoading.value = true
        initData()
        queryForm.feeType = '2'
        listLoading.value = false
    })
</script>
<style lang="less" scoped></style>
