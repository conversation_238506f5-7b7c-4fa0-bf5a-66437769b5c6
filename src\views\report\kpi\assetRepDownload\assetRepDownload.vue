<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 投顾 -->
                <label-item label="投顾">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <!-- 统计结束时间 -->
                <label-item label="统计结束时间">
                    <crm-select
                        v-model="queryForm.endDt"
                        :placeholder="endDt.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="selectList"
                        :style="{ width: '130px' }"
                        @change="handleconslevel"
                    />
                </label-item>
                <!-- 在职状态 -->
                <label-item :label="workState.label">
                    <crm-select
                        v-model="queryForm.workState"
                        :placeholder="workState.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="workState.selectList"
                        :style="{ width: '130px' }"
                        @change="handleworkState"
                    />
                </label-item>

                <!-- 投顾code -->
                <label-item :label="userId.label" class-name="text-left">
                    <crm-input
                        v-model="queryForm.userId"
                        :placeholder="userId.placeholder"
                        :clearable="true"
                        :style="{ width: '110px' }"
                    />
                </label-item>
            </template>
            <template #operationBtns>
                <Text type="info" size="small" style="font-size: 14px"
                    >最后更新时间：
                    <span class="last-update-time">{{ lastUpdateTimeView }}</span>
                    , 统计截止时间:
                    <span class="last-update-time">{{ dataDeadlineView }}</span>
                </Text>
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="exportHandle"
                    >导出</crm-button
                >
                <crm-button
                    size="small"
                    :radius="true"
                    :icon="InfoFilled"
                    plain
                    @click="handleExplain"
                    >说明</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                >
                </base-table>
            </template>
        </table-wrapper>
        <ExplainAssetRep v-model="explainDialogVisiable"></ExplainAssetRep>
    </div>
</template>

<script lang="ts" setup>
    import { Plus, Download, InfoFilled } from '@element-plus/icons-vue'
    import { fetchRes } from '@/utils'
    import { dataList } from './data/labelData'
    import { assetRepDownloadTableColumn, showTableColumn } from './data/tableData'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import ExplainAssetRep from '@/views/report/kpi/components/explainAssetRep.vue'

    import {
        // eslint-disable-next-line camelcase
        AssetRepDownloadQuery,
        AssetRepDownloadExport,
        AssetRepDownloadInit
    } from '@/api/project/report/kpi/assetRepDownload/assetRepDownload'

    import { getMenuPermission } from '@/api/project/common/common'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import LabelItem from '@/components/moduleBase/LabelItem.vue'
    import { use } from 'echarts'
    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const { endDt, workState, userId } = dataList
    const consStatus = ref<string>('1') //不包含离职人员
    const module = ref<string>('140803')
    const exportShow = ref<boolean>(false)

    const listLoading = ref<boolean>(false)

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        endDt = ''
        workState = ''
        userId = ''
    }
    const handleconslevel = (val: string) => {
        queryForm.endDt = val
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: 说明展示隐藏
     * @param explainDialogVisiable 展示隐藏
     * @method handleExplain 触发方法
     * @return {*}
     */
    const explainDialogVisiable = ref<boolean>(false)
    const handleExplain = () => {
        explainDialogVisiable.value = true
    }

    /**
     * @description: 分页数据
     * @return {*}
     */
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    const lastUpdateTimeView = ref<any>()
    const dataDeadlineView = ref<any>()

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])
    const selectList = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            assetRepDownloadTableColumn.map(item => item.key),
            assetRepDownloadTableColumn
        )
    })

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            orgCode: queryForm.orgvalue.orgCode,
            consCode: queryForm.orgvalue.consCode,
            endDt: queryForm.endDt,
            workState: queryForm.workState,
            userId: queryForm.userId
        }
        fetchRes(AssetRepDownloadQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { list } = resObj
                tableData.value = list
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    const initData = async () => {
        listLoading.value = true
        const params = {
            menuCode: '140803'
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (item.operateCode === '1' && item.display === '1') {
                            exportShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
        fetchRes(AssetRepDownloadInit(), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { endDtList, lastUpdateTime, dataDeadline } = resObj
                selectList.value = endDtList
                queryForm.endDt = endDtList[0].key
                lastUpdateTimeView.value = lastUpdateTime
                dataDeadlineView.value = dataDeadline
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    const handleworkState = (val: string) => {
        queryForm.workState = val
    }
    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const params = {
            orgCode: queryForm.orgvalue.orgCode,
            consCode: queryForm.orgvalue.consCode,
            endDt: queryForm.endDt,
            workState: queryForm.workState,
            userId: queryForm.userId
        }
        const res: any = await AssetRepDownloadExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }
    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        fetchConsOrgList('', module.value, '1')

        initData()
    })
</script>
<style lang="less" scoped></style>
