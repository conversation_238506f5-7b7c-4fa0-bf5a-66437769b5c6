<!--
 * @Description: 通用table列表页面容器
 * @Author: chao<PERSON>.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-14 15:33:49
 * @FilePath: /ds-report-web/src/views/modBusiness/pageModule/components/TableWrapCust.vue
 * @slot searchArea, 搜索条件区域
 * @slot operationBtns, 操作按钮栏
 * @slot tableContentMiddle, 表格列表区域
 * @slot tableContentBottom, table列表底部区域, 分页器等
-->
<template>
    <div :class="`table-wrapper ${className}`">
        <div :class="['table-top', { pack_up: !topAreaExpand }]">
            <!-- searchArea -->
            <div v-if="showSearchArea" class="search-area">
                <div ref="searchArea" class="area-content">
                    <slot name="searchArea" />
                </div>
                <!-- 右侧按钮本次删除 -->
                <!-- <div class="right">
                    <el-button plain :icon="Search" @click="$emit('searchFn')">查询</el-button>
                </div>
                <div v-if="showExportBtn" class="right">
                    <el-button plain :icon="Folder" @click="$emit('exportFn')">导出</el-button>
                </div> -->
                <!-- 折叠按钮 -->
                <!-- <div v-show="showPackupBtn" class="pack_up_btn" @click="handleExpand">
                    <el-icon size="12px"><arrow-up /></el-icon>
                </div> -->
            </div>
            <!-- operationBtns 按钮展示 -->
            <div
                v-if="showOperationBtns"
                class="operation-btns"
                :class="{ 'align-right': !showOperationLeft, 'margin-top0': true }"
            >
                <slot name="operationBtns" />
            </div>
            <!-- tabsPanel table切换展示 -->
            <div v-if="showTabsPanel" class="wrapper-tabs-panel">
                <slot name="tabsPanel" />
            </div>

            <!-- 文案提示 -->
            <div v-if="showNotice" class="notice-text"  :class="{ 'margin-top0': showTabsPanel }">
                <slot name="noticeText" />
            </div>
        </div>
        <div class="crm_table_middle">
            <div class="crm_table_panel">
                <slot name="tablePanel" />
            </div>
            <div class="crm_table_list">
                <slot name="tableContentMiddle" />
            </div>
        </div>
        <div class="crm_table_bottom">
            <slot name="tableContentBottom" />
        </div>
        <slot />
    </div>
</template>

<script>
    import { Search, ArrowUp, Folder } from '@element-plus/icons-vue'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'TableWrapper',
        // components: { ArrowUp },
        props: {
            // 搜索条目标签名称的宽度
            labelWidth: {
                type: String,
                default: ''
            },
            // 是否展示操作按钮模块，默认展示
            showOperationBtns: {
                type: Boolean,
                default: true
            },
            showOperationLeft: {
                type: Boolean,
                default: false
            },
            showSearchArea: {
                type: Boolean,
                default: true
            },
            showTabsPanel: {
                type: Boolean,
                default: false
            },
            showNotice: {
                type: Boolean,
                default: false
            },
            className: {
                type: String,
                default: ''
            },
            // 是否展示导出按钮模块，默认不展示
            showExportBtn: {
                type: Boolean,
                default: false
            }
        },
        emits: ['searchFn', 'exportFn'],
        setup() {
            return { Search, Folder }
        },
        data() {
            return {
                showPackupBtn: true, // 是否显示收起展开按钮
                topAreaExpand: true // 顶部区域是否展开
            }
        },
        watch: {
            labelWidth: {
                handler(newVal) {
                    if (newVal) {
                        this.$nextTick(() => {
                            const labelList =
                                this.$refs.searchArea.querySelectorAll('.crm-input-item .label') ||
                                []
                            labelList.forEach(item => {
                                item.style.width = newVal
                            })
                        })
                    }
                },
                immediate: true
            }
        },
        mounted() {
            this.resizeFn()
            window.addEventListener('resize', this.resizeFn)
        },
        methods: {
            handleExpand() {
                this.topAreaExpand = !this.topAreaExpand
            },
            resizeFn() {
                if (this.$refs.searchArea) {
                    // this.showPackupBtn = this.$refs.searchArea.offsetHeight > 56
                }
            },
            beforeUnmount() {
                window.removeEventListener('resize', this.resizeFn)
            }
        }
    })
</script>

<style lang="less" scoped>
    .table-wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;

        &.tabs-panel-wraper {
            height: calc(~'100vh - 142px');
        }

        &.crm_wraper {
            height: calc(~'100vh');
        }
        
        &.title-wraper {
            height: calc(~'100vh - 52px');
        }

        .table-top {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            padding: 0;
            background-color: @bg_main;

            &.pack_up {
                // .search-area {
                //     .left {
                //         height: 54px;
                //     }
                // }

                .pack_up_btn {
                    i {
                        transform: rotate(180deg);
                    }
                }
            }

            .pack_up_btn {
                position: absolute;
                right: 0;
                bottom: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 16px;
                cursor: pointer;
                background-color: #b5b5bc;

                &:hover {
                    background-color: @theme_main_hover;
                }

                i {
                    color: @font_color_01;
                    transition: all 0.2s;
                }
            }

            .search-area {
                position: relative;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                justify-content: flex-start;
                width: 100%;
                // width: calc(100% - 20px);
                padding: 0 10px;
                margin: 5px auto 0;
                overflow: hidden;
                // background-color: @font_color_01;
                // border-bottom: 1px solid @border_color;
                // box-shadow: 0 0 12px 0 rgba(210, 211, 224, 0.47);

                // .left {
                //     display: flex;
                //     flex: 1;
                //     flex-wrap: wrap;
                //     align-items: flex-start;
                //     padding-bottom: 15px;
                //     transition: height 0.2s;
                // }
                .area-content {
                    flex: 1;
                }
            }

            .wrapper-tabs-panel {
                width: calc(100% - 20px);
                margin: 7px auto 0;
            }

            .operation-btns,
            .notice-text {
                display: flex;
                align-items: center;
                // justify-content: space-between;
                width: calc(100% - 20px);
                height: 40px;
                padding: 0 10px;
                margin: 7px auto 0;
                background-color: @font_color_01;
                // box-shadow: 0 0 12px 0 #e6e8eb;

                &.align-right {
                    justify-content: flex-end;
                }

                &.margin-top0 {
                    margin-top: 0;
                }

                .el-button {
                    margin-left: 6px;
                    // border-radius: 0;
                }
            }
        }

        .crm_table_middle {
            flex: 1;
            padding: 0 10px;
            overflow: hidden;
            background-color: @bg_main;
            // 通用table列表
            .crm_table_list {
                display: flex;
                height: 100%;
                min-height: 100px;

                .el-table__body {
                    td {
                        border: none;
                    }
                }
            }
        }
    }

    .blank-view-container {
        .table-wrapper {
            &.tabs-panel-wraper {
                height: calc(100vh - 56px);
            }
        }
    }
</style>
