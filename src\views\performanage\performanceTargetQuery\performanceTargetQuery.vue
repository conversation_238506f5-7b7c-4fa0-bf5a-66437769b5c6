<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 投顾 -->
                <label-item label="入职/管理日期">
                    <el-date-picker
                        v-model="queryForm.queryDt"
                        type="date"
                        size="small"
                        :disabled="queryDtDisabled"
                        format="YYYYMMDD"
                        value-format="YYYYMMDD"
                        @change="handleQueryDt"
                    ></el-date-picker>
                </label-item>
                <!-- 是否参与考核 -->
                <label-item label="职级">
                    <crm-select
                        v-model="queryForm.consRank"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="consRankselectList"
                        :style="{ width: '130px' }"
                        @change="handleConsRank"
                    />
                </label-item>
                <!-- 在职状态 -->
                <label-item label="考核节点">
                    <el-date-picker
                        v-model="queryForm.performanceDt"
                        type="month"
                        size="small"
                        format="YYYYMM"
                        value-format="YYYYMM"
                        @change="handlePerformanceDt"
                    ></el-date-picker>
                </label-item>
            </template>
            <template #operationBtns>
                <crm-button
                    size="small"
                    :radius="true"
                    :icon="InfoFilled"
                    plain
                    @click="handleExplain"
                    >说明</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <div class="myclass">
                    <theme-table
                        v-if="showTableData"
                        :is-loading="listLoading"
                        :columns="tableColumn"
                        :data="tableData"
                        style="width: 100%;min-height: 74px;height: auto;"
                        :show-operation="false"
                        :no-select="false"
                        :no-index="false"
                        :stripe="true"
                        :border="true"
                        operation-width="100"
                        class-name="theme-gray"
                    >
                    </theme-table>
                    <theme-table
                        v-if="showTableDataMoreYear"
                        :is-loading="listLoading"
                        :columns="tableColumnMoreYear"
                        :data="tableDataMoreYear"
                        style="width: 100%"
                        :show-operation="false"
                        :no-select="false"
                        :no-index="false"
                        :stripe="true"
                        height="100%"
                        :border="true"
                        operation-width="100"
                        class-name="theme-gray"
                    >
                    </theme-table>
                    <theme-table
                        v-if="showTableDataLessYear"
                        :is-loading="listLoading"
                        :columns="tableColumnLessYear"
                        :data="tableDataLessYear"
                        style="width: 100%"
                        :show-operation="false"
                        :no-select="false"
                        :no-index="false"
                        :stripe="true"
                        height="100%"
                        :border="true"
                        operation-width="100"
                        class-name="theme-gray"
                    >
                    </theme-table>
                </div>
            </template>
        </table-wrapper>
        <ExplainStock v-model="explainDialogVisiable"></ExplainStock>
    </div>
</template>

<script lang="ts" setup>
    import { InfoFilled } from '@element-plus/icons-vue'
    import { fetchRes } from '@/utils'
    import {
        performanceTargetTableColumn,
        performanceTargetMoreYearTableColumn,
        performanceTargetLessYearTableColumn,
        showTableColumn
    } from './data/tableData'
    import ExplainStock from '@/views/performanage/performanceTargetQuery/explainStock.vue'
    import {
        performanceTargetQuery,
        performanceTargetInitConsRank,
        performanceTargetInitConsRankConfig
    } from '@/api/project/performanage/performanceTargetQuery/performanceTargetQuery'
    import LabelItem from '@/components/moduleBase/LabelItem.vue'
    import { ElMessage } from 'element-plus'
    import ThemeTable from '@/components/module/ThemeTable.vue'
    let queryDtDisabled = false
    const showTableData = ref<boolean>(false)
    const showTableDataMoreYear = ref<boolean>(false)
    const showTableDataLessYear = ref<boolean>(false)
    const listLoading = ref<boolean>(false)

    const module = ref<string>('B140103')
    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        queryDt = ''
        consRank = ''
        performanceDt = ''
    }

    const queryForm = reactive(new QueryForm())

    /**
     * @description: 说明展示隐藏
     * @param explainDialogVisiable 展示隐藏
     * @method handleExplain 触发方法
     * @return {*}
     */
    const explainDialogVisiable = ref<boolean>(false)
    const handleExplain = () => {
        explainDialogVisiable.value = true
    }

    const handleQueryDt = (val: string) => {
      console.log(val)
        queryForm.queryDt = val
    }
    const handleConsRank = (val: string) => {
        queryForm.consRank = val
    }
    const handlePerformanceDt = (val: string) => {
        queryForm.performanceDt = val
    }
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])
    const tableDataMoreYear = ref<object[]>([])
    const tableDataLessYear = ref<object[]>([])
    const selectList = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            performanceTargetTableColumn.map(item => item.key),
            performanceTargetTableColumn
        )
    })
    const tableColumnMoreYear = computed(() => {
        return showTableColumn(
            performanceTargetMoreYearTableColumn.map(item => item.key),
            performanceTargetMoreYearTableColumn
        )
    })
    const tableColumnLessYear = computed(() => {
        return showTableColumn(
            performanceTargetLessYearTableColumn.map(item => item.key),
            performanceTargetLessYearTableColumn
        )
    })

    //查詢
    const queryList = async () => {
        listLoading.value = true
        if (!queryForm.queryDt) {
            ElMessage.error('请选择入职/管理日期')
            listLoading.value = false
            return
        }
        if (!queryForm.consRank) {
            ElMessage.error('请选择职级')
            listLoading.value = false
            return
        }
        if (!queryForm.performanceDt) {
            ElMessage.error('请选择考核节点')
            listLoading.value = false
            return
        }
        const params = {
            queryDt: queryForm.queryDt,
            consRank: queryForm.consRank,
            performanceDt: queryForm.performanceDt
        }
        fetchRes(performanceTargetQuery(params), {
            successCB: (resObj: any) => {
                const retVO = resObj
                listLoading.value = false
                tableData.value = retVO.performanceTarget
                tableDataLessYear.value = retVO.performanceTargetLessYear
                tableDataMoreYear.value = retVO.performanceTargetMoreYear
                showTableData.value = true
                if (retVO && retVO.inMonth <= 12) {
                    showTableDataLessYear.value = true
                    showTableDataMoreYear.value = false
                } else {
                    showTableDataLessYear.value = false
                    showTableDataMoreYear.value = true
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }
    const consRankselectList = ref<object[]>([])
    const initData = async () => {
        listLoading.value = true
        fetchRes(performanceTargetInitConsRankConfig(), {
            /**
             * 用户在花名册中，且层级符合以下其中一种情况：默认显示当前用户在花名册中的入职日期 或 管理日期，格式YYYYMMDD。
             * 1、若用户的层级=理财师1，且只有一个层级：取用户在花名册中配置的入职日期，入职日期不可编辑
             * 2、若用户的层级=分总、区域执行副总、区域总：取用户在花名册中配置的管理日期，可编辑。可选择年月日，选择数据后显示格式YYYYMMDD
             * 用户在花名册但不符合上述情况，或用户不在花名册中：默认显示空值，入职/管理日期可编辑。
             */
            successCB: (resObj: any) => {
                //当前年月
                handlePerformanceDt(
                    new Date().getFullYear() +
                        '' +
                        (new Date().getMonth() + 1).toString().padStart(2, '0')
                )
                if (resObj && resObj.consLevel === '1') {
                    queryForm.queryDt = resObj.startDt
                    queryDtDisabled = true
                }
                if (
                    resObj &&
                    (resObj.consLevel === '5' ||
                        resObj.consLevel === '3' ||
                        resObj.consLevel === '4')
                ) {
                    queryForm.queryDt = resObj.promoteDate
                }
            },
            errorCB: (res: any) => {
                listLoading.value = false
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
        fetchRes(performanceTargetInitConsRank(), {
            successCB: (resObj: any) => {
                listLoading.value = false
                consRankselectList.value = resObj
            },
            errorCB: (res: any) => {
                listLoading.value = false
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }
    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        listLoading.value = true
        initData()
        listLoading.value = false
    })
</script>
<style lang="less" scoped>
    .myclass {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
</style>
