/*
 * @Description: 全局公共方法
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 16:09:50
 * @FilePath: /crm-template/src/utils/globalMethods.js
 *
 */
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { removeToken } from '@/utils/auth'
import { axiosRequest } from '@/utils/request.ts'
import { responseCode } from '@/constant/index'
import { paramsMerge } from '@/api/mock.js'

// 支持预览的文件类型
const previewTypes = ['doc', 'docx', 'xls', 'xlsx', 'xlsm', 'ppt', 'pptx', 'pdf']

/**
 * localStorge存值
 */
export const setLocalItem = (key, val) => {
    return window.localStorage.setItem(key, val)
}

/**
 * localStorge删值
 */
export const deleteLocalItem = key => {
    return window.localStorage.removeItem(key)
}

/**
 * localStorge取值
 */
export const getLocalItem = key => {
    return window.localStorage.getItem(key)
}

/**
 * sessionStorage存值
 */
export const setSessionItem = (key, val) => {
    return window.sessionStorage.setItem(key, val)
}

/**
 * sessionStorage取值
 */
export const getSessionItem = key => {
    return window.sessionStorage.getItem(key)
}
/**
 * sessionStorage删除
 */
export const delateSessionItem = key => {
    return window.sessionStorage.removeItem(key)
}

/**
 * @description: 未登录逻辑
 * @return {*}
 */
export const logoutWithoutRequest = () => {
    const { origin, port, pathname } = window.location
    let redirect = location.hash.slice(1)
    redirect =
        redirect && !redirect.includes('error/') && redirect !== '/'
            ? decodeURIComponent(redirect)
            : ''

    // // 已在登录页情况下，不再重复跳转登录页
    // if (redirect.startsWith('/login')) {
    //     return
    // }

    removeToken()
    deleteLocalItem('USER_INFO')
    deleteLocalItem('USER_MENU')
    deleteLocalItem('hb_crm_token')

    window.location.href = `${origin}/crm-sys/login`
    // window.location.reload()
}

/**
 * 深拷贝
 */
export const deepClone = data => {
    if (!data || typeof data !== 'object') {
        return data
    }

    const newData = data instanceof Array ? [] : {}
    for (const key in data) {
        const tmpKey = typeof data[key] === 'object' ? deepClone(data[key]) : data[key]
        newData[key] = tmpKey
    }
    return newData
}

/**
 * 把嵌套tree数组扁平化
 * @param data <Array>
 * @param childName <String> 子集节点的名称
 * @return newData <Array>
 */
export const flatTree = (data, childName = 'children') => {
    if (!Array.isArray(data)) {
        console.warn('只支持传入数组')
        return data
    }
    return data.reduce((prev, curt) => {
        // 有子节点的话把子节点作为一级节点递归遍历
        const childList = curt[childName]?.length ? flatTree(curt[childName]) : []
        return [...prev, curt, ...childList]
    }, [])
}

/**
 * @description: 二维转一维
 * @param {any} arr
 * @return {*}
 */
export const flatten = arr => {
    return [].concat(...arr.map(x => (Array.isArray(x) ? flatten(x) : x)))
}

/**
 * 判断表格列是否显示
 * @param {*} columns
 * @param {*} key
 * @return {*}
 */
const getTableColumnState = (columns, key) => {
    return columns.some(item => item.key === key)
}

/**
 * @description: 导出导出单文件流
 * @param {*} data 数据对象
 * @return {*}
 */
export const singleFileUpload = data => {
    const { fileByte, name, type } = data
    const bstr = atob(fileByte)
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }
    const blob = new Blob([u8arr], {
        // 下载的文件类型
        type: type
    })
    if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob)
    } else {
        const link = document.createElement('a')
        link.download = name
        link.href = window.URL.createObjectURL(blob)
        link.click()
        window.URL.revokeObjectURL(link.href) // 释放内存
    }
}

/**
 * @description: 下载文件并导出
 * @param {*} params
 * @param {*} afterDownload
 * @param {*} successFunc
 * @return {*}
 */
export const downloadFile = async (params, afterDownload = null, successFunc = null) => {
    // 错误回调
    const callbacks = {
        errorCB: err => {
            afterDownload && afterDownload()
            ElMessage.error({
                message: err?.desc || '文件下载失败',
                duration: 3000
            })
        }
    }
    // 请求参数
    const requestParams = {
        callbacks,
        timeout: 600 * 1000, // 大文件下载时间较长，超时时间设置为10分钟
        ...params
    }

    const res = await axiosRequest(paramsMerge(requestParams)).finally(afterDownload)
    if (res.code === responseCode.SUCCESS || res.code === 'C030000') {
        if (Array.isArray(res.data)) {
            res.data.map(item => {
                return singleFileUpload(item)
            })
        } else {
            singleFileUpload(res.data)
        }
        successFunc && successFunc()
    } else {
        callbacks.errorCB(res.desc)
    }
}

/**
 * 获取操作按钮权限
 * @param elementName: 操作类型名称，如add, delete等
 * @param moduleName: 页面路由名称，非必传，不传默认取当前访问的路由名
 * @example 是否显示操作按钮：getElementPermission('delete') 返回true为显示，否则不显示
 */
export function getElementPermission(elementName, moduleName) {
    let localMenus = localStorage.getItem('USER_MENU')
    localMenus = localMenus ? JSON.parse(localMenus) : null
    // const menus = store?.getters?.user?.menus || localMenus || null
    const menus = localMenus || null

    if (!elementName || !menus) {
        return false
    }
    const currentPath = window.location.hash.split('#')[1]
    const menuList = flatTree(menus) || []
    const activeMenu = menuList.find(item => {
        if (moduleName) {
            return moduleName === item.name
        }
        return item.path && item.path !== '/' && currentPath.includes(item.path)
    })

    /**
     * @modify xiang.zhou 尽调任务池是写死的路由，不在 menus 数据里，导致 exts 为空，前端抛异常
     */
    // console.log(elementName, moduleName, activeMenu)
    if (activeMenu) {
        const exts = JSON.parse(activeMenu.exts)
        if (exts && exts.action) {
            return exts.action.indexOf(elementName) > -1
        }
    }

    return false
}
