/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const twoLevelConfProductPateTableColumn: TableColumnItem[] = [
    {
        key: 'fundCode',
        label: '产品代码',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundName',
        label: '产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'company',
        label: '公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'subRate',
        label: '认购费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'manageRate',
        label: '管理费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'feedRate',
        label: '存续D折算系数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'feedRemark',
        label: '存续D备注',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'manageFormula',
        label: '管理费公式',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'manageRemark',
        label: '管理费备注',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceRate',
        label: '业绩报酬费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceShareRate',
        label: '业绩报酬分成费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fixedType',
        label: '固定日期类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fixeDate',
        label: '固定日日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceDate',
        label: '业绩报酬提取日',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceFormula',
        label: '业绩报酬公式',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceType',
        label: '业绩报酬计提类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceBenchmark',
        label: '业绩报酬计提基准',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'closedPeriodDays',
        label: '份额锁定期天数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'closedPeriodType',
        label: '份额锁定期类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceRemark',
        label: '业绩报酬备注',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'rateConfType',
        label: '费率配置类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'transMinAmount',
        label: '配置下限',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'transMaxAmount',
        label: '配置上限',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'transStartDate',
        label: '交易开始日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'transEndDate',
        label: '交易结束日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'redemRate',
        label: '赎回费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'redemFormula',
        label: '赎回费公式',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'redemRemark',
        label: '赎回费备注',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'economicIncome',
        label: '经纪收入',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'productManage',
        label: '产品经理',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'signDate',
        label: '签约日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'signSubject',
        label: '好买签约主体',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'oppositeContact',
        label: '对方联系人',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'rateStartDate',
        label: '费率开始日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'rateEndDate',
        label: '费率结束日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'reviewState',
        label: '审核状态',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'reviewtime',
        label: '审核时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'reviewer',
        label: '审核人',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'creator',
        label: '创建者',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'createtime',
        label: '创建时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'updater',
        label: '更新者',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'updattime',
        label: '更新时间',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
