/*
 * @Description: 产品费率配置校验逻辑hooks
 * @Author: hongdong.xie
 * @Date: 2025-06-06 09:42:20
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 09:42:20
 * @FilePath: /ds-report-web/src/components/common/ProductFeeRateConfig/composables/useProductFeeRateValidation.ts
 */

import { reactive, computed } from 'vue'
import type { FormRules } from 'element-plus'

/**
 * @description: 产品费率配置校验逻辑hooks
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @return {*}
 */
export const useProductFeeRateValidation = () => {
    /**
     * @description: 基础表单校验规则
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     */
    const formRules = reactive<FormRules>({
        // 基础属性校验
        filingCode: [
            { required: true, message: '请输入备案代码', trigger: 'blur' },
            { min: 6, max: 6, message: '备案代码必须为6位', trigger: 'blur' }
        ],
        productCode: [
            { required: true, message: '请输入产品code', trigger: 'blur' },
            { min: 6, max: 6, message: '产品code必须为6位', trigger: 'blur' }
        ],
        shareCode: [
            { required: true, message: '请输入份额代码', trigger: 'blur' },
            { min: 6, max: 6, message: '份额代码必须为6位', trigger: 'blur' }
        ],
        productFullName: [
            { required: true, message: '请输入产品全称', trigger: 'blur' }
        ],
        fofOnlyFlag: [
            { required: true, message: '请选择仅限FOF', trigger: 'change' }
        ],
        payerFullName: [
            { required: true, message: '请输入付款方全称', trigger: 'blur' }
        ],
        productManager: [
            { required: true, message: '请输入产品经理', trigger: 'blur' }
        ],
        howbuySigningEntity: [
            { required: true, message: '请输入好买签约主体', trigger: 'blur' }
        ],
        counterpartContact: [
            { required: true, message: '请输入对方联系人', trigger: 'blur' }
        ],
        durationDCoefficient: [
            { required: true, message: '请输入存续D系数', trigger: 'blur' }
        ],
        rateStartDate: [
            { required: true, message: '请选择费率开始日期', trigger: 'change' }
        ],
        rateEndDate: [
            { required: true, message: '请选择费率结束日期', trigger: 'change' }
        ],

        // 阶梯费率校验
        tieredRateType: [
            { required: true, message: '请选择费率配置类型', trigger: 'change' }
        ],
        rateEffectiveType: [
            { required: true, message: '请选择费率配置生效日', trigger: 'change' }
        ],

        // 交易管理费率校验
        subscriptionRate: [
            { required: true, message: '请输入认申购费率', trigger: 'blur' }
        ],
        managementRate: [
            { required: true, message: '请输入管理费率', trigger: 'blur' }
        ],

        // 业绩报酬校验
        performanceSharingRate1: [
            { required: true, message: '请输入业绩报酬分成费率1', trigger: 'blur' }
        ],

        // 赎回费率校验
        redemptionRate1: [
            { required: true, message: '请输入赎回费率1', trigger: 'blur' }
        ]
    })

    /**
     * @description: 自定义校验函数 - 日期范围校验
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {any} formData 表单数据
     * @return {boolean} 校验结果
     */
    const validateDateRange = (formData: any): { valid: boolean; message?: string } => {
        const { rateStartDate, rateEndDate } = formData
        
        if (rateStartDate && rateEndDate) {
            if (rateStartDate >= rateEndDate) {
                return {
                    valid: false,
                    message: '费率开始日期必须小于费率结束日期'
                }
            }
        }

        // 如果选择了交易日类型，校验费率生效日期
        if (formData.tieredRateType === '3') {
            const { rateEffectiveStartDate, rateEffectiveEndDate } = formData
            if (rateEffectiveStartDate && rateEffectiveEndDate) {
                if (rateEffectiveStartDate >= rateEffectiveEndDate) {
                    return {
                        valid: false,
                        message: '费率生效日期必须小于费率结束日期'
                    }
                }
            }
        }

        return { valid: true }
    }

    /**
     * @description: 自定义校验函数 - 费率范围校验
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {any} formData 表单数据
     * @return {boolean} 校验结果
     */
    const validateRateRange = (formData: any): { valid: boolean; message?: string } => {
        // 校验认申购费率
        if (formData.subscriptionRate < 0 || formData.subscriptionRate > 1) {
            return {
                valid: false,
                message: '认申购费率必须在0-1之间'
            }
        }

        // 校验管理费率
        if (formData.managementRate < 0 || formData.managementRate > 1) {
            return {
                valid: false,
                message: '管理费率必须在0-1之间'
            }
        }

        // 校验业绩报酬分成费率
        if (formData.performanceSharingRate1 < 0 || formData.performanceSharingRate1 > 1) {
            return {
                valid: false,
                message: '业绩报酬分成费率1必须在0-1之间'
            }
        }

        // 校验赎回费率
        const redemptionRates = [
            formData.redemptionRate1,
            formData.redemptionRate2,
            formData.redemptionRate3,
            formData.redemptionRate4
        ]

        for (let i = 0; i < redemptionRates.length; i++) {
            const rate = redemptionRates[i]
            if (rate !== undefined && rate !== null && (rate < 0 || rate > 1)) {
                return {
                    valid: false,
                    message: `赎回费率${i + 1}必须在0-1之间`
                }
            }
        }

        return { valid: true }
    }

    /**
     * @description: 自定义校验函数 - 配置上下限校验
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {any} formData 表单数据
     * @return {boolean} 校验结果
     */
    const validateConfigLimits = (formData: any): { valid: boolean; message?: string } => {
        // 只有在特定费率配置类型下才需要校验配置上下限
        const needConfigLimits = ['1', '2', '4', '5'].includes(formData.tieredRateType)
        
        if (needConfigLimits) {
            const { configLowerLimit, configUpperLimit } = formData
            
            if (configLowerLimit !== undefined && configUpperLimit !== undefined) {
                if (configLowerLimit >= configUpperLimit) {
                    return {
                        valid: false,
                        message: '配置下限必须小于配置上限'
                    }
                }
            }
        }

        return { valid: true }
    }

    /**
     * @description: 自定义校验函数 - 赎回费率逻辑校验
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {any} formData 表单数据
     * @return {boolean} 校验结果
     */
    const validateRedemptionRateLogic = (formData: any): { valid: boolean; message?: string } => {
        // 如果赎回费率1为0，其他赎回费率也应该为0
        if (formData.redemptionRate1 === 0) {
            const otherRates = [
                formData.redemptionRate2,
                formData.redemptionRate3,
                formData.redemptionRate4
            ]
            
            for (let i = 0; i < otherRates.length; i++) {
                if (otherRates[i] > 0) {
                    return {
                        valid: false,
                        message: '当赎回费率1为0时，其他赎回费率也应该为0'
                    }
                }
            }
        }

        // 校验赎回费率递减逻辑（可选）
        const rates = [
            formData.redemptionRate1,
            formData.redemptionRate2,
            formData.redemptionRate3,
            formData.redemptionRate4
        ].filter(rate => rate > 0)

        // 如果有多个费率，应该是递减的
        for (let i = 1; i < rates.length; i++) {
            if (rates[i] > rates[i - 1]) {
                return {
                    valid: false,
                    message: '赎回费率应该按持有天数递减设置'
                }
            }
        }

        return { valid: true }
    }

    /**
     * @description: 综合表单校验函数
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {any} formData 表单数据
     * @return {boolean} 校验结果
     */
    const validateForm = (formData: any): { valid: boolean; message?: string } => {
        // 执行所有自定义校验
        const validations = [
            validateDateRange(formData),
            validateRateRange(formData),
            validateConfigLimits(formData),
            validateRedemptionRateLogic(formData)
        ]

        for (const validation of validations) {
            if (!validation.valid) {
                return validation
            }
        }

        return { valid: true }
    }

    return {
        formRules,
        validateDateRange,
        validateRateRange,
        validateConfigLimits,
        validateRedemptionRateLogic,
        validateForm
    }
}
