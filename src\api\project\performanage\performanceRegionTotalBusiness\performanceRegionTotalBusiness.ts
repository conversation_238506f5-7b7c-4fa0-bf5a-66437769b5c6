import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import {
    performanceRegionTotalBusinessParam,
    performanceRegionTotalSaveParam,
    performanceManageRegionTotalBatchUpdateParam
} from './type/apiReqType.js'

/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const performanceRegionTotalBusinessQuery = (
    params: performanceRegionTotalBusinessParam
): any => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/manage/node/conf/regiontotal/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 工作日接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const performanceRegionTotalBusinessCurrWorkDays = (): any => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/manage/node/conf/regiontotal/currWorkDays',
            method: 'post'
        })
    )
}

/**
 * @description: 导出接口
 * @return {*}
 */
export const performanceRegionTotalBusinessExport = (
    params: performanceRegionTotalBusinessParam
) => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/manage/node/conf/regiontotal/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 保 存
 * @return {*}
 */
export const performanceRegionTotalBusinessSave = (params: performanceRegionTotalSaveParam) => {
    return axiosRequest(
        paramsMerge({
            timeout: 600000,
            url: '/api/report/performance/manage/node/conf/regiontotal/save',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 批量修改页面保存接口
 * @return {*}
 */
export const performanceManageBatchUpdate = (
    params: performanceManageRegionTotalBatchUpdateParam
): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/regiontotal/batchupdate',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 批量修改初始化
 * @return {*}
 */
export const performanceManageBatchUpdateInitData = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/regiontotal/batchupdateinitdata',
            method: 'post'
            //    data: params
        })
    )
}
