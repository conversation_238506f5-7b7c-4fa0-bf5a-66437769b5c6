<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            :show-export-btn="true"
            @searchFn="handleLoading"
            @exportFn="exportHandle"
        >
            <template #searchArea>
                <label-item :label="assetMon.label">
                    <el-date-picker
                        v-model="queryForm.assetMon"
                        class="popperClass"
                        type="month"
                        size="small"
                        style="width: 150px; font-size: 12px"
                        placeholder="选择日期"
                    >
                    </el-date-picker>
                </label-item>
                <label-item :label="fundCode.label">
                    <crm-input
                        v-model="queryForm.fundCode"
                        :placeholder="fundCode.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <label-item :label="fundName.label">
                    <crm-input
                        v-model="queryForm.fundName"
                        :placeholder="fundName.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-select="false"
                    :no-index="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                >
                </base-table>
            </template>
            <template #tableContentBottom>
                <Pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>
    </div>
</template>

<script lang="ts" setup>
    import { fetchRes } from '@/utils'
    import { dataList } from './data/labelData'
    import { assetGdHkTableColumn, showTableColumn } from './data/tableData'

    import {
        // eslint-disable-next-line camelcase
        assetGdHkQuery,
        assetGdHkExport
    } from '@/api/project/report/saleManage/assetGdHk/assetGdHk'
    const { fundName, fundCode, assetMon } = dataList

    const listLoading = ref<boolean>(false)

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        assetMon = ''
        fundCode = ''
        fundName = ''
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: 分页数据
     * @return {*}
     */
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            assetGdHkTableColumn.map(item => item.key),
            assetGdHkTableColumn
        )
    })

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            mon: queryForm.assetMon,
            fundCode: queryForm.fundCode,
            fundName: queryForm.fundName,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(assetGdHkQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                // debugger
                tableData.value = rows
                // TODO list接口待添加分页数据
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const params = {
            mon: queryForm.assetMon,
            fundCode: queryForm.fundCode,
            fundName: queryForm.fundName
        }
        const res: any = await assetGdHkExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }
</script>
<style lang="less" scoped></style>
