<!--
 * @Description: 下拉选项
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-03-16 14:33:15
 * @FilePath: /crm-asset-web/src/components/moduleBase/CrmSelectItem.vue
 * @eg:
    <crm-select-item
        v-model="data.fids"
        label="所属用户"
        label-format="cn"
        value-format="fid"
        :option-list="belongUser.belongUserList"
    />
-->
<template>
    <div class="crm_input_item">
        <div class="label">{{ label ? `${label}：` : '' }}</div>
        <div class="value">
            <crm-select v-bind="$attrs" />
        </div>
        <slot name="suffix" />
        <slot />
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CrmSelectItem',
        props: {
            label: {
                type: String,
                default: ''
            }
        }
    })
</script>
<style lang="less" scoped>
    .crm_input_item {
        display: flex;
        align-items: flex-start;
        margin: 15px 30px 0 0;

        .label {
            min-width: 72px;
            font-size: 12px;
            line-height: 26px;
            color: @font_color;
            text-align: right;
            white-space: nowrap;
        }

        .value {
            flex: 1;
            font-size: 12px;
        }
    }
</style>
