/*
 * @Description: 海外人民币产品费率配置 API接口
 * @Author: hongdong.xie
 * @Date: 2025-06-05 18:53:18
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-05 18:53:18
 * @FilePath: /ds-report-web/src/api/project/report/finance/overseasRmbProductFeeRateConfig/overseasRmbProductFeeRateConfig.ts
 */

import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '../../../../mock.js'
import {
    OverseasRmbProductFeeRateConfigQueryParam,
    OverseasRmbProductFeeRateConfigAddParam,
    OverseasRmbProductFeeRateConfigUpdateParam,
    OverseasRmbProductFeeRateConfigDeleteParam,
    OverseasRmbProductFeeRateConfigAuditParam,
    OverseasRmbProductFeeRateConfigExportParam
} from './type/apiReqType.js'

/**
 * @description: 海外人民币产品费率配置查询接口
 * @param {OverseasRmbProductFeeRateConfigQueryParam} params 查询参数
 * @return {*}
 */
export const overseasRmbProductFeeRateConfigQuery = (params: OverseasRmbProductFeeRateConfigQueryParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/overseasrmb/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 海外人民币产品费率配置导出接口
 * @param {OverseasRmbProductFeeRateConfigExportParam} params 导出参数
 * @return {*}
 */
export const overseasRmbProductFeeRateConfigExport = (params: OverseasRmbProductFeeRateConfigExportParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/overseasrmb/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 海外人民币产品费率配置新增接口
 * @param {OverseasRmbProductFeeRateConfigAddParam} params 新增参数
 * @return {*}
 */
export const overseasRmbProductFeeRateConfigAdd = (params: OverseasRmbProductFeeRateConfigAddParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/overseasrmb/add',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 海外人民币产品费率配置修改接口
 * @param {OverseasRmbProductFeeRateConfigUpdateParam} params 修改参数
 * @return {*}
 */
export const overseasRmbProductFeeRateConfigUpdate = (params: OverseasRmbProductFeeRateConfigUpdateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/overseasrmb/update',
            method: 'post',
            data: params,
            // Mock数据配置
            mockData: {
                code: '200',
                description: '修改成功',
                data: null
            }
        })
    )
}

/**
 * @description: 海外人民币产品费率配置删除接口
 * @param {OverseasRmbProductFeeRateConfigDeleteParam} params 删除参数
 * @return {*}
 */
export const overseasRmbProductFeeRateConfigDelete = (params: OverseasRmbProductFeeRateConfigDeleteParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/overseasrmb/delete',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 海外人民币产品费率配置审核接口
 * @param {OverseasRmbProductFeeRateConfigAuditParam} params 审核参数
 * @return {*}
 */
export const overseasRmbProductFeeRateConfigAudit = (params: OverseasRmbProductFeeRateConfigAuditParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/overseasrmb/audit',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 获取海外人民币产品费率配置操作按钮权限
 * @return {*}
 */
export const getOverseasRmbProductFeeRateConfigAuth = () => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/overseasrmb/getAuth',
            method: 'post'
        })
    )
} 