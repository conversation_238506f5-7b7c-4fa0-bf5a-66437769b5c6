/*
 * @Author: ch<PERSON><PERSON>.<EMAIL>
 * @Date: 2023-02-22 19:50:51
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-14 13:11:55
 * @FilePath: /ds-report-web/public/msEnv.js
 * @Description: 宙斯配置项
 */
var _msEnvFlag = '2' // 1 线上环境  2 测试环境  3 mock环境  4 测试 doc 预览
var _msApiPrefix = window.origin + '/hdCurrent'
var _msApiPrefixCGI = window.origin + '/assetCurrent'
// var _msPdfViewerPrefix = window.origin + '/pdfViewer/web/viewer.html?file='
// var _msApiPrefix_oldAsset = window.origin + '/oldAsset'

if (_msEnvFlag === '2') {
    _msApiPrefix = window.origin + '/hdCurrent'
    _msApiPrefixCGI = 'https://m1.apifoxmock.com/m1/2810603-1007980-default'
    // _msApiPrefix = 'http://**************:8087'
    // _msPdfViewerPrefix = 'http://**************:8087/pdfViewer/web/viewer.html?file='
} else if (_msEnvFlag === '3') {
    _msApiPrefix = 'https://apifoxmock.com/m1/5285670-0-default'
    _msApiPrefixCGI = 'https://apifoxmock.com/m1/2810603-0-default'
}

