import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { performanceConsultFinalParam } from './type/apiReqType.js'

/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const performanceConsultFinalQuery = (params: performanceConsultFinalParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/consult/final/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 导出接口
 * @return {*}
 */
export const performanceConsultFinalExport = (params: performanceConsultFinalParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/consult/final/export',
            method: 'post',
            data: params
        })
    )
}

export const performanceConsultFinalListInitData = (): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/consult/final/listinitdata',
            method: 'post'
        })
    )
}
