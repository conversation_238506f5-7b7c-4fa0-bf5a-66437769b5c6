/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, numberWithThousandSeparatorTwoDecimalPlaces } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const tradeNumTableColumn: TableColumnItem[] = [
    {
        key: 'u1Name',
        label: '业务中心',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '分公司',
        width: 110,
        formatter: formatTableValue
    },
    {
        key: 'consCode',
        label: '投顾code',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '投顾',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'consCustNo',
        label: '投顾客户号',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'custName',
        label: '客户姓名',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'consStartDt',
        label: '分配日期',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'preId',
        label: '预约ID',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'productCode',
        label: '产品/组合代码',
        width: 110,
        formatter: formatTableValue
    },
    {
        key: 'productName',
        label: '产品/组合名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundType',
        label: '产品分类',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'accountProductType',
        label: '核算产品类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ackDt',
        label: '确认日期',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'tradeStartDt',
        label: '开始日期',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'tradeEndDt',
        label: '结束日期',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'currency',
        label: '币种',
        width: 90,
        formatter: formatTableValue
    },
    {
        key: 'realPayAmt',
        label: '实际打款金额(本币)',
        width: 130,
        formatter: formatTableValue
    },
    {
        key: 'realPayAmtRmb',
        label: '实际打款金额(人民币)',
        width: 140,
        formatter: formatTableValue
    },
    {
        key: 'balanceVol',
        label: '购买/存续份额',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'buyNav',
        label: '购买净值',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'buyAmt',
        label: '购买/存续市值(本币)',
        width: 130,
        formatter: numberWithThousandSeparatorTwoDecimalPlaces
    },
    {
        key: 'buyAmtRmb',
        label: '购买/存续市值(人民币)',
        width: 140,
        formatter: numberWithThousandSeparatorTwoDecimalPlaces
    },
    {
        key: 'intervalAverageBalance',
        label: '180天日均存量',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'markFlag',
        label: '是否计入成交次数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'tradeNum',
        label: '成交次数',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'remark',
        label: '备注',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
