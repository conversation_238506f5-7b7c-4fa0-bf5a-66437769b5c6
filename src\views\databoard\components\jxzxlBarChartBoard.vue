﻿<!-- eslint-disable no-undef -->
<!-- eslint-disable vue/require-default-prop -->
<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="board-s">
        <div :id="vId" style="width: 100%; height: 100%"></div>
    </div>
</template>

<script>
    import * as echarts from 'echarts'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'JxzxlBarChartBoard',
        props: {
            // eslint-disable-next-line vue/require-default-prop
            vId: {
                type: String
            },
            title: {
                type: String,
                default: 'title'
            },
            // eslint-disable-next-line vue/require-default-prop
            monthDataX: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            monthData: {
                type: Array
            }
        },
        data() {
            return {}
        },
        watch: {
            monthData: {
                handler(newVal) {
                    if (newVal) {
                        this.$nextTick(() => {
                            this.renderGauge<PERSON>hart()
                        })
                    }
                },
                immediate: true,
                deep: true
            }
        },
        mounted() {
            this.renderGaugeChart()
        },
        methods: {
            renderGaugeChart() {
                const option = {
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '10%',
                        top: '10%',
                        containLabel: true
                    },
                    legend: {
                        y: 'bottom',
                        x: 'center',
                        itemWidth: 6,
                        itemHeight: 6,
                        itemGap: 10
                    },
                    //下载图片
                    toolbox: {
                        show: true,
                        feature: {
                            saveAsImage: {
                                pixelRatio: 15
                            } //值越大分辨率越高
                        }
                    },
                    // 提示框
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        showContent: true,
                        // 自定义提示  显示千位符
                        formatter(params) {
                            let relVal = params[0].name + '(单位:万)'
                            for (let i = 0, l = params.length; i < l; i++) {
                                relVal += `<br/>${params[i].marker}${'净值'} : ${params[
                                    i
                                ].value.toLocaleString()}`
                            }
                            return relVal
                        }
                    },
                    xAxis: {
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            textStyle: {
                                fontSize: 9
                            }
                        },
                        //data: ['1月', '2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月']
                        data: this.monthDataX
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                fontSize: 9
                            }
                        }
                    },
                    series: [
                        {
                            type: 'bar',
                            barwidth: '40%', //柱的宽度
                            barGap: 0,
                            color: '#e54c5e',
                            label: {
                                show: true, // 是否可见
                                rotate: 0, // 旋转角度
                                position: 'top', // 显示位置
                                fontSize: 8
                            },
                            data: this.monthData
                        }
                    ]
                }
                const gaugeChart = echarts.init(document.getElementById(this.vId))
                gaugeChart.setOption(option)
            }
        }
    })
</script>

<style scoped>
    .board-s {
        height: 95%;
        margin: 2px;
    }

    .box-card {
        height: 95%;
        font-size: 12px;
        background: #8cc5ff;
    }

    .card-title {
        display: block;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }
</style>
