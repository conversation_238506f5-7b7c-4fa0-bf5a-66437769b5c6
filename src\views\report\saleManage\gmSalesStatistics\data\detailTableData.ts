/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import {
    formatTableValue,
    formatNumber,
    numberWithThousandSeparatorTwoDecimalPlaces
} from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const gmSalesStatisticsDetailTableColumn: TableColumnItem[] = [
    {
        key: 'centerName',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'uporgName',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'orgName',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '投顾姓名',
        width: 120,
        formatter: formatTableValue
    },

    {
        key: 'consCustNo',
        label: '投顾客户号',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custName',
        label: '客户姓名',
        width: 120,
        formatter: formatTableValue
    },

    {
        key: 'midBusiCode',
        label: '中台业务名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'productCode',
        label: '产品代码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'productName',
        label: '产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'productClass1',
        label: '一级分类',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'productClass2',
        label: '二级分类',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'productClass3',
        label: '三级分类',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'payStatus',
        label: '付款状态',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'appDtStr',
        label: '申请日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'appTmStr',
        label: '申请时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'appAmt',
        label: '申请金额',
        width: 120,
        formatter: numberWithThousandSeparatorTwoDecimalPlaces
    },
    {
        key: 'ackDtStr',
        label: '确认日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ackAmt',
        label: '确认金额',
        width: 120,
        formatter: numberWithThousandSeparatorTwoDecimalPlaces
    },

    {
        key: 'orderStatus',
        label: '订单状态',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
