export interface QueryFollowStatisticRequest {
    orgCode?: string // 部门编码
    consCode?: string // 投顾编码
    consCustNo?: string // 投顾客户号
    allocationDtStart?: string // 分配日期起始 格式：YYYYMMDD
    allocationDtEnd?: string // 分配日期截止 格式：YYYYMMDD
    custType?: string[] // 客户类型 1:leads-一手 2:leads-二手 3:继承-潜客（投顾） 4:继承-0存量 5:继承-存量
    allocationCustState?: string[] // 分配日客户状态 1:潜客 2:0存量 3:存量
    followStage?: string[] // 跟进阶段 1-一阶段 2-二阶段 3-三阶段
    followFlag?: string[] // 继续跟进 0-否 1-是
    startDtStart?: string // 开始时间起始 格式：YYYYMMDD
    startDtEnd?: string // 开始时间截止 格式：YYYYMMDD
    endDtStart?: string // 结束时间起始 格式：YYYYMMDD
    endDtEnd?: string // 结束时间截止 格式：YYYYMMDD
    forecastResult?: string[] // 预计结果 0:成交-归属投顾 1:达标-进入次月 2:未达标-回收
    finalResult?: string[] // 最终结果 0:成交-归属投顾 1:达标-进入次月 2:未达标-回收 3:未达标-放弃
    page?: number // 页码
    rows?: number // 数据行数
}

export type ExportFollowStatisticRequest = Omit<QueryFollowStatisticRequest, 'page' | 'rows'>

export interface ModifyFollowStatisticRequest {
    id?: string // 修改的数据id
    idList?: string[] // 批量修改的数据id列表
    startDt?: string // 开始时间
    endDt?: string // 结束时间
    feedbackIsSatisfied?: string // 客服回访-是否满意
    finalResult?: string // 最终结果
    remark?: string // 备注
}
