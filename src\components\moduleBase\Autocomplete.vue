<!--
 * @Description: 通用输入框组件
 * @Author: chaohui.wu
 * @Date: 2023-04-11 18:45:34
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-04-18 15:23:20
 * @FilePath: /crm-asset-web/src/components/moduleBase/Autocomplete.vue
 *  
-->

<template>
    <el-autocomplete
        class="crm_autocomplete"
        size="small"
        :popper-class="popperClss"
        :value-key="valueFormat"
        :fetch-suggestions="querySearchAsync"
        v-bind="$attrs"
        @select="handleSelect"
        @change="handleChange"
    >
        <template v-if="suffix" #suffix>
            <i :class="suffix" @click="$emit('suffixIconClick')" />
        </template>
        <template #default="{ item }">
            <div v-if="hasSearchOrg && item.fullName" class="item-span item-span1">
                <span v-html="highlightSearchKey(item.fullName)"></span>
            </div>
            <div class="item-span fund-name" v-html="highlightSearchKey(item[valueFormat])"></div>
        </template>
    </el-autocomplete>
</template>

<script>
    import { searchTypeParams } from '@/constant'
    export default defineComponent({
        name: 'Autocomplete',
        props: {
            searchFunc: {
                // 远程搜索函数  必传
                type: Function,
                required: true
            },
            params: {
                // 搜索中额外的参数
                type: Object,
                default: () => {
                    return {}
                }
            },
            labelFormat: {
                type: String,
                default: 'productName'
            },
            valueFormat: {
                type: String,
                default: 'label'
            },
            inputFormat: {
                type: String,
                default: 'label'
            },
            suffix: {
                type: String,
                default: ''
            }
        },
        emits: ['suffixIconClick', 'handleSet'],
        data() {
            return {
                results: [],
                lastQueryString: '',
                labelStr: ''
            }
        },
        computed: {
            hasSearchOrg() {
                if (!this?.params?.type) {
                    return false
                }
                return searchTypeParams.org.indexOf(this.params.type) > -1
            },
            popperClss() {
                return `crm_autocomplete_popper ${this.hasSearchOrg ? ' crm-auto-org' : ' '}`
            }
        },
        methods: {
            querySearchAsync(queryString, cb) {
                // 如果搜索的关键字为空  则直接cb
                if (!queryString) {
                    cb([])
                } else {
                    // 如果关键字不一致  则搜索  成功后将result和lastQueryString更新
                    this.searchFunc({ ...this.params, searchParam: queryString }).then(res => {
                        this.lastQueryString = queryString
                        const list = res.data
                        this.results = list.map(item => {
                            if (item) {
                                item.label = `${item.productCode} ${item.productName}`
                            }
                            return item
                        })
                        cb(this.results)
                    })
                }
            },
            // 替换搜索关键字
            highlightSearchKey(text) {
                if (!text) {
                    return text
                }
                const searchKey = this.lastQueryString || ''
                if (!searchKey) {
                    return text
                }
                const replaceReg = new RegExp(searchKey, 'g') // 匹配关键字正则
                const replaceString = '<span class="highlight_search_key">' + searchKey + '</span>' // 高亮替换v-html值
                return text.replace(replaceReg, replaceString) // 开始替换
            },
            // 选中字段
            handleSelect(item) {
                if (item) {
                    console.log(item)
                    const { productCode, productName, establishFund } = item
                    this.labelStr = `${productCode} ${productName}`
                    this.$emit('handleSet', { ...item })
                }
            },
            handleChange(item) {
                if (!item) {
                    this.$emit('handleSet', { ...item })
                }
            }
        }
    })
</script>
<style lang="less" scoped>
    .el-input {
        --el-input-focus-border-color: @border_focus;
    }

    .crm_autocomplete {
        width: 100%;
        font-size: 12px;
        line-height: 24px;

        .el-input--small {
            .el-input__inner {
                height: 24px;
                padding: 0 8px 0 0;

                &::placeholder {
                    font-size: 12px;
                }
            }

            .el-input__icon {
                line-height: 24px;
            }
        }

        .is-disabled {
            .el-input__inner {
                color: @font_color_02;
            }
        }

        .el-input__inner {
            // border-color: #d5d6da;
            color: @font_color_02;
            border-radius: 2px;

            &:focus {
                border-color: @border_focus;
            }
        }
    }

    .crm_autocomplete_popper {
        li {
            display: flex;
            justify-content: space-between;

            .highlight_search_key {
                color: @theme_main;
            }

            .el-icon.is-loading {
                margin: auto;
            }
        }

        &.crm-auto-org {
            li {
                line-height: 1.2;
                white-space: inherit;
            }

            .el-autocomplete-suggestion__list > li {
                line-height: 1.2;
            }
        }
    }
</style>
<style lang="less">
    .el-autocomplete-suggestion__list {
        li {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            line-height: 24px;

            .item-span {
                min-width: 80px;
                padding: 5px 0;

                &:last-child {
                    padding-right: 0;
                }
            }
        }
    }

    .crm-auto-org {
        .item-span1 {
            width: 150px;
        }
    }
</style>
