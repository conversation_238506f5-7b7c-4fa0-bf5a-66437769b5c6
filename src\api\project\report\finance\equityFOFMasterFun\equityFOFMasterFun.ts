import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { EquityFOFMasterFunOrderParam } from './type/apiReqType.js'

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const equityFOFMasterFunOrder_Json = (params: EquityFOFMasterFunOrderParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/equityFOFMasterFun/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const equityFOFMasterFunOrderExport = (params: EquityFOFMasterFunOrderParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/equityFOFMasterFun/export',
            method: 'post',
            data: params
        })
    )
}
