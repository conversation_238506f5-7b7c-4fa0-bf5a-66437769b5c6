<!--
 * @Description: 产品费率配置页面测试导航
 * @Author: hongdong.xie
 * @Date: 2025-06-06 13:06:36
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 13:06:36
 * @FilePath: /ds-report-web/src/views/test/productFeeRateConfigTest.vue
-->
<template>
    <div class="test-container">
        <h1>产品费率配置页面测试</h1>
        <div class="nav-buttons">
            <el-button type="primary" @click="navigateTo('/fofProductFeeRateConfig')">
                FOF产品费率配置
            </el-button>
            <el-button type="success" @click="navigateTo('/secondaryProductFeeRateConfig')">
                二级产品费率配置
            </el-button>
            <el-button type="warning" @click="navigateTo('/overseasRmbProductFeeRateConfig')">
                海外人民币产品费率配置
            </el-button>
        </div>
        
        <div class="info-section">
            <h2>页面说明</h2>
            <ul>
                <li>所有页面都使用相同的通用组件实现</li>
                <li>页面字段和功能完全一样</li>
                <li>只是后端API接口不同</li>
                <li>包含查询、导出、新增、复制新增、修改、审核、删除功能</li>
            </ul>
        </div>
    </div>
</template>

<script lang="ts" setup>
    import { useRouter } from 'vue-router'
    
    const router = useRouter()
    
    const navigateTo = (path: string) => {
        router.push(path)
    }
</script>

<style lang="less" scoped>
    .test-container {
        padding: 20px;
        max-width: 800px;
        margin: 0 auto;
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            
            .el-button {
                padding: 12px 24px;
                font-size: 16px;
            }
        }
        
        .info-section {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            
            h2 {
                color: #666;
                margin-bottom: 15px;
            }
            
            ul {
                color: #666;
                line-height: 1.6;
                
                li {
                    margin-bottom: 8px;
                }
            }
        }
    }
</style>
