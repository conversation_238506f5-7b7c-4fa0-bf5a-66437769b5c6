<template>
    <div class="pageContainer">
        <header>
            <div class="headerBar">
                <div>
                    <Logo />
                    <Navigation />
                    <SettingBar />
                </div>
            </div>
            <tags-view v-if="!hideTagsBar" />
        </header>
        <article :class="{ noPaddingTop: hideTagsBar }">
            <router-view v-slot="{ Component }">
                <keep-alive :include="includeList" :max="10">
                    <component :is="Component" />
                </keep-alive>
            </router-view>
        </article>
    </div>
</template>

<script>
    import { useVisitedViewStore } from './stores/tagsView'
    import Logo from './components/Logo'
    import Navigation from './components/Navigation'
    import SettingBar from './components/SettingBar'
    import TagsView from './components/TagsView'

    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'Layout',
        components: {
            Logo,
            Navigation,
            SettingBar,
            TagsView
        },
        props: {
            // 默认显示tab历史，新页面展示详情的时候不展示
            showTagsView: {
                type: Boolean,
                default: true
            }
        },
        setup() {
            const visitedViewStore = useVisitedViewStore()
            return { visitedViewStore }
        },
        data() {
            return {
                // 使用newPagelayout组件和openNewPageType=1的时候不显示访问历史tab栏
                hideTagsBar: this.$route.meta.openNewPageType === '1' || !this.showTagsView
            }
        },
        computed: {
            // ...mapGetters(['visitedViews']),
            includeList() {
                const list = []
                this.visitedViewStore.visitedViews.forEach(item => {
                    const name = item.name
                    if (name.length > 1) {
                        const newName = name.slice(0, 1).toUpperCase() + name.slice(1)
                        list.push(newName)
                    }
                })
                return list
            }
        },
        watch: {
            $route(route) {
                this.hideTagsBar = this.$route.meta.openNewPageType === '1' || !this.showTagsView
            }
        }
    })
</script>

<style lang="less">
    .pageContainer {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;

        header {
            .headerBar {
                width: 100%;
                height: @header_bar_height;
                background-color: #3b3e5b;
                box-shadow: 0 1px 8px 0 #e8e8e8;

                > div {
                    box-sizing: border-box;
                    display: flex;
                    align-items: center;
                    max-width: @max_content_width;
                    height: 100%;
                    margin: 0 auto;
                }
            }
        }

        article {
            flex: 1;
            width: 100%;
            min-width: @min_content_width;
            height: 100%;
            margin: 0 auto;
            overflow: hidden;

            &.noPaddingTop {
                padding-top: 0;
            }
        }
    }
</style>
