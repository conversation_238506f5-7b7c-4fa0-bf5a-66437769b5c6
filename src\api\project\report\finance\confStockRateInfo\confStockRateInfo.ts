import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '../../../../mock.js'
import {
    StockConfProductRateParam,
    StockConfProductRateAddOrUpdateParam,
    BatchDeleteId,
    AduitProduct
} from './type/apiReqType.js'
/**
 * @description: 股权产品费率配置查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const confStockRateInfoQuery = (params: StockConfProductRateParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/confStockRateInfo/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 股权产品费率配置导出接口
 * @return {*}
 */
export const confStockRateInfoExport = (params: StockConfProductRateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/confStockRateInfo/export',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 股权产品费率配置新增接口
 * @return {*}
 */
export const confStockRateInfoInsert = (params: StockConfProductRateAddOrUpdateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/confStockRateInfo/insert',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 股权产品费率配置修改接口
 * @return {*}
 */
export const confStockRateInfoUpdate = (params: StockConfProductRateAddOrUpdateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/confStockRateInfo/update',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 股权产品费率配置删除接口
 * @return {*}
 */
export const confStockRateInfoDelete = (params: BatchDeleteId) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/confStockRateInfo/delete',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 股权产品费率配置复核接口
 * @return {*}
 */
export const confStockRateInfoReview = (params: AduitProduct) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/confStockRateInfo/review',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核-init
 * @param {object} params
 * @return {*}
 */
export const getAuth = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/confStockRateInfo/getAuth',
            method: 'post'
        })
    )
}
