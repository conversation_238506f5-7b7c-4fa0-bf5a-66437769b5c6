<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
    <div>
        <el-table
            ref="tableSort"
            height="400"
            :data="tableData"
            border
            :cell-style="getCellColor"
            :header-cell-style="{
                background: '#b7c0b5 !important',
                color: 'black',
                'font-size': '12px !important'
            }"
            style="width: 100%"
            @cell-click="clickcell"
        >
            <el-table-column
                v-for="(item, index) in tableColumns"
                :key="index"
                header-align="center"
                align="center"
                :prop="item.value"
                :label="item.label"
                :width="item.width"
                :formatter="item.formatter"
            >
                <template v-if="item.popText" #default="scope">
                    <div>
                        <el-popover :visible="visible" placement="top" :width="220">
                            <p>{{scope.row[item.popText]}}</p>
                            <template #reference>
                            {{scope.row[item.value]}}
                            </template>
                        </el-popover>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'DetailTable',
        props: {
            // eslint-disable-next-line vue/require-default-prop
            tableColumns: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            tableData: {
                type: Array
            }
        },
        methods: {
            // 点击一个单元格
            // eslint-disable-next-line max-params
            clickcell(row, column, event, cell) {
                if (column.property === 'consCustNo') {
                    const url =
                        'http://crm.intelnal.howbuy.com/report/report/listKhMarketCapForBoardReport.do?consCustNo=' +
                        row.consCustNo
                    window.open(url, '_blank')
                }
                if (column.property === 'conscustNo') {
                    const url =
                        'http://crm.intelnal.howbuy.com/report/report/listKhMarketCapForBoardReport.do?consCustNo=' +
                        row.conscustNo
                    window.open(url, '_blank')
                }
            },
            getCellColor({ row, column, rowIndex, columnIndex }) {
                if (columnIndex === 0) {
                    return { color: ' red ', 'text-decoration': 'underline', 'font-size': '12px' }
                }
                return { 'font-size': '12px' }
            }
        }
    })
</script>

<style scoped></style>
