/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const custHkInfoTableColumn: TableColumnItem[] = [
    {
        label: '客户姓名(香港)',
        key: 'hkCustName',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '香港客户号',
        key: 'hkCustNo',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '投顾客户号',
        key: 'conscustno',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '客户类型',
        key: 'invstType',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '投资者类型（香港）',
        key: 'investorQualification',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '香港客户状态',
        key: 'custStat',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '风险等级',
        key: 'riskToleranceLevel',
        minWidth: 120,
        formatter: formatTableValue,
        sortable: 'custom'
    },
    {
        label: '是否有衍生工具知识',
        key: 'derivativeKnowledge',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '香港开户日期',
        key: 'openDate',
        minWidth: 120,
        formatter: formatTableValue,
        sortable: 'custom'
    },
    {
        label: '当前投顾',
        key: 'consname',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '海外储蓄罐签约状态',
        key: 'signState'
    },
    {
        label: '资产证明到期日期',
        key: 'assetCertExpiredDate',
        minWidth: 120,
        formatter: formatTableValue,
        sortable: 'custom'
    },
    {
        label: '线上开户资料提交时间',
        key: 'submitTime',
        minWidth: 120,
        formatter: formatTableValue,
        sortable: 'custom'
    },
    {
        label: '线上开户资料审核通过时间',
        key: 'passDt',
        minWidth: 120,
        formatter: formatTableValue,
        sortable: 'custom'
    },
    {
        label: '开户申请成功日期',
        key: 'accountTime',
        minWidth: 120,
        formatter: formatTableValue,
        sortable: 'custom'
    },
    {
        label: '开户入金审核提交时间',
        key: 'depositSubmitTime',
        minWidth: 120,
        formatter: formatTableValue,
        sortable: 'custom'
    },
    {
        label: '开户入金审核通过时间',
        key: 'depositPassDt',
        minWidth: 120,
        formatter: formatTableValue,
        sortable: 'custom'
    },
    {
        label: '风险测评日期',
        key: 'kycDt',
        minWidth: 120,
        formatter: formatTableValue,
        sortable: 'custom'
    },
    {
        label: '风险测评到期日期',
        key: 'riskToleranceTerm',
        minWidth: 120,
        formatter: formatTableValue,
        sortable: 'custom'
    },
    {
        label: '一级组织架构(当前投顾)',
        key: 'u1Name',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        label: '二级组织架构(当前投顾)',
        key: 'u2Name',
        minWidth: 120
    },
    {
        label: '三级组织架构(当前投顾)',
        key: 'u3Name',
        minWidth: 120
    },
    {
        label: '首次打款不低1万美元日期',
        key: 'firstAckDt',
        sortable: 'custom'
    },
    {
        label: 'KPI达标日期',
        key: 'kpi2024Dt',
        sortable: 'custom'
    },
    {
        label: '投顾(KPI达标日)',
        key: 'consnameKpi'
    },
    {
        label: '一级组织架构(KPI)',
        key: 'u1NameKpi'
    },
    {
        label: '二级组织架构(KPI)',
        key: 'u2NameKpi'
    },
    {
        label: '三级组织架构(KPI)',
        key: 'u3NameKpi'
    },
    {
        label: '海外储蓄罐签约有效期',
        key: 'agreementSignExpiredDt',
        sortable: 'custom'
    }
]

export const defaultTableColumn: string[] = [
    'hkCustName',
    'hkCustNo',
    'conscustno',
    'invstType',
    'investorQualification',
    'custStat',
    'riskToleranceLevel',
    'derivativeKnowledge',
    'openDate',
    'consname',
    'signState',
    'assetCertExpiredDate',
    'u1Name',
    'u2Name',
    'u3Name',
    'firstAckDt',
    'kpi2024Dt',
    'consnameKpi',
    'u1NameKpi',
    'u2NameKpi',
    'u3NameKpi',
    'agreementSignExpiredDt'
]
