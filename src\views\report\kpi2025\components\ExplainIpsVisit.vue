<template>
    <crm-dialog
        v-model="dialogVisible"
        width="598px"
        title="IPS面访记录说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <div class="explain-content">
            <b>此报表用于统计：</b><br />
            1、客户IPS面访完成情况统计<br />
            2、包含25年初IPS存量、完成时存量等关键信息<br />
            3、支持按部门编码、投顾编码、完成日期等条件查询<br />
            4、支持按是否完成IPS面访状态筛选<br /><br />

            <b>字段说明：</b><br />
            • 投顾客户号：满足统计客户范围客户的投顾客户号<br />
            • 25年初IPS存量：客户在25年初的IPS存量<br />
            • 完成日期：客户首次完成IPS面访的时间<br />
            • 完成时的存量：客户首次完成IPS面访时的存量<br />
            • 完成投顾：客户首次完成IPS面访时的投顾信息<br /><br />

            其他统计规则，以相关文件为准
        </div>
        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">确定</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script setup lang="ts">
    import { useVisible } from '@/views/common/scripts/useVisible'
    import { watch } from 'vue'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            modelValue: boolean
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()

    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })

    // 监听 dialogVisible 变化，同步到父组件
    watch(dialogVisible, val => {
        emit('update:modelValue', val)
    })
</script>

<style lang="less" scoped>
    .explain-content {
        line-height: 1.6;
        font-size: 14px;
    }
</style>
