<!--
 * @Description: label组件
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-15 10:32:06
 * @FilePath: /ds-report-web/src/views/modBusiness/pageModule/components/LabelItemCust.vue
-->
<template>
    <div class="input-item" :class="boxClassName">
        <div class="label" :class="[classNameStr]">
            <aside v-if="custom" class="lable-cust-box">
                <slot name="labelCust"></slot>
            </aside>
            <aside v-else class="lable-cust-box">
                {{ label ? `${label}：` : '' }}
            </aside>
        </div>
        <div class="value">
            <slot></slot>
        </div>
    </div>
</template>

<script>
    export default defineComponent({
        name: 'LabelItem',
        props: {
            label: {
                type: String,
                default: ''
            },
            custom: {
                type: [Boolean],
                default: false
            },
            className: {
                type: [String],
                default: ''
            },
            boxClassName: {
                type: [String],
                default: ''
            },
            minwidth: {
                type: [String],
                default: '100%'
            },
            width: {
                type: [String],
                default: 'auto'
            },
            labelwidth: {
                type: [String],
                default: '100px'
            }
        },
        computed: {
            classNameStr() {
                switch (this.className) {
                    case 'white':
                        return 'color-white'
                    case 'red':
                        return 'color-red'
                    default:
                        return this.className
                }
            }
        }
    })
</script>
<style lang="less" scoped>
    .input-item {
        display: flex;
        align-items: center;
        width: v-bind(width);
        min-width: v-bind(minwidth);
        // padding: 0 2px;
        // margin: 15px 30px 0 0;

        &.no-margin {
            margin: 0;
        }

        &.col-center {
            display: flex;
            align-items: center;
        }

        .label {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: v-bind(labelwidth);
            height: 100%;
            min-height: 30px;
            padding: 0 0 0 8px;
            font-size: 12px;
            line-height: 16px;
            color: @font_color;
            text-align: right;
            white-space: nowrap;
            background-color: #C1D5EA;
            

            .lable-cust-box {
                flex: 1;
            }

            &.color-white {
                color: @font_color_01;
            }

            &.color-red {
                color: @theme_main;
            }

            &.bold {
                font-weight: bold;
            }

            &.text-left {
                text-align: left;
            }
        }

        .value {
            display: flex;
            flex: 1;
            align-items: center;
            padding: 0 2px;
            font-size: 12px;
        }
    }
</style>
