import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { ResoCustAssignParam } from './type/apiReqType.js'

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const resoCustAssignConvRateQuery = (params: ResoCustAssignParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/custManage/resoCustAssignConvRate/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 审核页面的查询接口
 * @return {*}
 */
export const resoCustAssignConvRateExport = (params: ResoCustAssignParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/custManage/resoCustAssignConvRate/export',
            method: 'post',
            data: params
        })
    )
}
