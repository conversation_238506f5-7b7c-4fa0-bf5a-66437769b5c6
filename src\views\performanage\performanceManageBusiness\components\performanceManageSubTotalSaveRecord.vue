<!--
 * @Description: 修改弹框
 * @Author: jianjian.yang
 * @Date: 2024-08-29 20:03:35
 400px
-->
<template>

    <crm-dialog
        v-model="dialogVisible"
        width="900px"
        height="600px"
        :border="true"
        title="操作记录"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <table-wrapper
            class-name="crm_wraper"
            :show-search-area="false"
            :show-operation-btns="false"
            :show-tabs-panel="false"
        >
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="saveRecordTableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="false"
                    :no-index="false"
                    :no-select="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                >
                </base-table>
            </template>
        </table-wrapper>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import { fetchRes } from '@/utils'
    import {
        performanceManageBusinessQuerySaveRecord
    } from '@/api/project/performanage/performanceManageBusiness/performanceManageBusiness'
    import { saveRecordTableColumn } from './data/tableData'

    const listLoading = ref<boolean>(false)

    /**
     * @description: table表格切换
     * @return {*}
     */
     const tableData = ref<object[]>([])

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                pageType: string
            }
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    pageType: '',
                }
            },
        },
    )


    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()


    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = () => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        dialogVisible.value = false
        // formEl.resetFields()
    }

    const initData = async () => {
        fetchRes(performanceManageBusinessQuerySaveRecord({pageType: props.transData.pageType}), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { detailVOList } = resObj
                tableData.value = detailVOList
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    onMounted(() => {
        // 初始化
        initData()    
    })
    
</script>

<style lang="less" scoped>
    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
