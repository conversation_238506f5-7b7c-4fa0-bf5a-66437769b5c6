export {}
declare module './apiReqType' {
    type performanceManageNodeConfParam = {
        /**
         * 投顾code
         */
        userId?: string
        /**
         * 层级
         */
        userLevel?: string
        /**
         * 部门
         */
        orgCode?: string
        /**
         * 投顾号
         */
        consCode?: string
        /**
         * 考核节点开始时间
         */
        exaimneNodeStart?: string
        /**
         * 考核节点结束时间
         */
        exaimneNodeEnd?: string
        /**
         * 周期 1-试用3M 2-试用6M 3-跟踪-12M 4-正式-年中 5-正式年末 6-观察期-12M以上 7-观察期-12M以内 8-理1B
         */
        periodExplain?: string
        /**
         * 是否考核
         */
        status?: string
        /**
         * 职级
         */
        curMonthLevel?: string
        /**
         * 计入人力
         */
        calcDepartment?: string
        /**
         * 计入净新增人力
         */
        calcNewDepartment?: string
    }

    
    type updateNodeConfParam = {
        
        /**
         * id
         */
        id?: string
        /**
         * 考核节点
         */
        exaimneNode?: string
        /**
         * 管理日期
         */
        promoteDate?: string
        /**
         * 考核节点结束时间
         */
        startDt?: string
        /**
         * 周期 1-试用3M 2-试用6M 3-跟踪-12M 4-正式-年中 5-正式年末 6-观察期-12M以上 7-观察期-12M以内 8-理1B
         */
        periodExplain?: string
        /**
         * 司龄月
         */
        exaimneMonth?: string
        /**
         * 计入人力
         */
        calcDepartment?: string
        /**
         * 计入净新增人力
         */
        calcNewDepartment?: string
        /**
         * 备注
         */
        remark?: string
    }
    
    
    type batchUpdateNodeConfParam = {
        
        /**
         * ids
         */
        ids?: string[]
        /**
         * 考核节点
         */
        exaimneNode?: string
        /**
         * 管理日期
         */
        promoteDate?: string
        /**
         * 考核节点结束时间
         */
        startDt?: string
        /**
         * 周期 1-试用3M 2-试用6M 3-跟踪-12M 4-正式-年中 5-正式年末 6-观察期-12M以上 7-观察期-12M以内 8-理1B
         */
        periodExplain?: string
        /**
         * 计入人力
         */
        calcDepartment?: string
        /**
         * 计入净新增人力
         */
        calcNewDepartment?: string
        /**
         * 备注
         */
        remark?: string
    }

    // 删除参数
    type adjustParam = {
        /**
         * id
         */
        id?: string
    }

    export {
        performanceManageNodeConfParam,
        adjustParam,
        updateNodeConfParam,
        batchUpdateNodeConfParam
    }
}
