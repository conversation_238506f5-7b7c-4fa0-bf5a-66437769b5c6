<!--
 * @Description: 二级产品费率配置页面（使用通用组件）
 * @Author: hongdong.xie
 * @Date: 2025-06-06 13:06:36
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 13:06:36
 * @FilePath: /ds-report-web/src/views/report/finance/secondaryProductFeeRateConfig/secondaryProductFeeRateConfig.vue
-->
<template>
    <ProductFeeRateConfigList
        :config="productConfig"
        :table-columns="tableColumns"
    />
</template>

<script lang="ts" setup>
    import { computed } from 'vue'
    import {
        ProductFeeRateConfigList,
        getProductConfig,
        getProductFeeRateConfigTableColumns,
        showTableColumn
    } from '@/components/common/ProductFeeRateConfig'

    // 获取二级产品配置
    const productConfig = getProductConfig('secondary')

    // 获取完整的表格列配置
    const allTableColumns = getProductFeeRateConfigTableColumns()

    // 定义要显示的列（排除ID字段，包含审核备注字段）
    const displayColumns = [
        'filingCode',
        'productCode',
        'shareCode',
        'productFullName',
        'fofOnlyFlag',
        'payerFullName',
        'productManager',
        'signingDate',
        'howbuySigningEntity',
        'counterpartContact',
        'durationDCoefficient',
        'durationDRemark',
        'rateStartDate',
        'rateEndDate',
        'tieredRateType',
        'rateEffectiveType',
        'configLowerLimit',
        'configUpperLimit',
        'rateEffectiveStartDate',
        'rateEffectiveEndDate',
        'subscriptionRate',
        'subscriptionRemark',
        'managementRate',
        'managementFormula',
        'managementRemark',
        'performanceSharingRate1',
        'performanceRate1',
        'performanceBenchmark1',
        'performanceSharingRate2',
        'performanceRate2',
        'performanceBenchmark2',
        'performanceSharingRate3',
        'performanceRate3',
        'performanceBenchmark3',
        'performanceAccrualType',
        'performanceAccrualForm',
        'fixedAccrualMonthType',
        'fixedAccrualDateType',
        'fixedAccrualCalcType',
        'redemptionHoldingDaysRule',
        'dividendHoldingDaysRule',
        'performanceFormula',
        'shareLockType',
        'shareLockDays',
        'fundClosedType',
        'fundClosedDays',
        'noAccrualNavBenchmark',
        'performanceRemark',
        'redemptionRate1',
        'redemptionHoldingDays1',
        'redemptionRate2',
        'redemptionHoldingDays2',
        'redemptionRate3',
        'redemptionHoldingDays3',
        'redemptionRate4',
        'redemptionHoldingDays4',
        'redemptionFormula',
        'redemptionHoldingCalcRule',
        'redemptionSpecialRule',
        'redemptionRemark',
        'creator',
        'createTime',
        'modor',
        'updateTime',
        'auditor',
        'auditTime',
        'auditStatus',
        'auditRemark'
    ]

    // 计算要显示的表格列
    const tableColumns = computed(() => {
        return showTableColumn(displayColumns, allTableColumns)
    })
</script>

<style lang="less" scoped></style>
