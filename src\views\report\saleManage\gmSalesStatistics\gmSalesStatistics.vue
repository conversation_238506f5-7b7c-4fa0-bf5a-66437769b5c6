<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 所属投顾 -->
                <label-item label="所属投顾">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <!-- 产品名称 -->
                <label-item label="产品名称">
                    <crm-select
                        v-model="queryForm.fundCodeList"
                        filterable
                        clearable
                        multiple
                        label-format="label"
                        value-format="key"
                        placeholder="全部"
                        :option-list="selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 订单是否确认 -->
                <label-item label="订单是否确认">
                    <crm-select
                        v-model="queryForm.isAckOrder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="isAckOrder.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 申请日期 -->
                <label-item :label="appDt.label">
                    <date-range
                        v-model="queryForm.appDt"
                        show-format="YYYY-MM-DD"
                        :placeholder="appDt.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 确认日期 -->
                <label-item :label="ackDt.label">
                    <date-range
                        v-model="queryForm.ackDt"
                        show-format="YYYY-MM-DD"
                        :placeholder="ackDt.placeholder"
                        style-type="fund"
                    />
                </label-item>
            </template>
            <template #operationBtns>
                <crm-button size="small" :radius="true" :icon="Download" plain :loading="isExporting" @click="exportHandle"
                    >导出
                </crm-button>
                <crm-button
                    size="small"
                    :radius="true"
                    plain
                    :icon="RemoveFilled"
                    @click="clearHandle"
                    >清空
                </crm-button>
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 75%"
                    :show-operation="true"
                    :no-select="false"
                    :no-index="true"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="100"
                >
                    <template #operation="{ scope }">
                        <el-button size="small" :text="true" link @click="handleDetail(scope.row)"
                            >明细
                        </el-button>
                    </template>
                </base-table>
            </template>
        </table-wrapper>
        <ExplainNetIncrease v-model="explainDialogVisiable"></ExplainNetIncrease>
    </div>
</template>

<script lang="ts" setup>
    import { Plus, Download, InfoFilled, RemoveFilled } from '@element-plus/icons-vue'
    import { fetchRes } from '@/utils'
    import { dataList } from './data/labelData'
    import { gmSalesStatisticsTableColumn, showTableColumn } from './data/tableData'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import ExplainNetIncrease from '@/views/report/kpi/components/explainNetIncrease.vue'

    import {
        FundListQuery,
        GmSalesStatisticsQuery,
        GmSalesStatisticsDetailExport
    } from '@/api/project/report/saleManage/gmSalesStatistics/gmSalesStatistics'

    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import LabelItem from '@/components/moduleBase/LabelItem.vue'
    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const { isAckOrder, appDt, ackDt } = dataList
    const consStatus = ref<string>('1') //不包含离职人员
    const module = ref<string>('B070518')

    const listLoading = ref<boolean>(false)

    const isExporting = ref<boolean>(false)

    const clearHandle = () => {
        queryForm.isAckOrder = ''
        queryForm.fundCodeList = []
        queryForm.appDt.startDate = ''
        queryForm.appDt.endDate = ''
        queryForm.ackDt.startDate = ''
        queryForm.ackDt.endDate = ''
        ;(queryForm.orgvalue.orgCode = ''), (queryForm.orgvalue.consCode = '')
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }

        fundCodeList = []
        isAckOrder = ''
        appDt = {
            startDate: '',
            endDate: ''
        }
        ackDt = {
            startDate: '',
            endDate: ''
        }
    }

    const queryForm = reactive(new QueryForm())
    /**
     * @description: 上一次点了查询的条件列表
     * @return {*}
     */
    const queryFormAction = new QueryForm()

    /**
     * @description: 说明展示隐藏
     * @param explainDialogVisiable 展示隐藏
     * @method handleExplain 触发方法
     * @return {*}
     */
    const explainDialogVisiable = ref<boolean>(false)
    const handleExplain = () => {
        explainDialogVisiable.value = true
    }
    const detailDialogVisiable = ref<boolean>(false)
    /**
     * @description: 明细
     * @param val 明细数据
     * @method handleDetail 触发方法
     * @return {*}
     */
    const handleDetail = (val: any): void => {
        debugger
        console.log(typeof val.productCode + '===' + val.productCode)
        let fundCode = val.productCode
        if (val.productCode === null || val.productCode === '') {
            fundCode = queryFormAction.fundCodeList.join('|')
        }
        // eslint-disable-next-line eqeqeq
        if (queryFormAction.appDt.startDate == null || queryFormAction.appDt.startDate === 'null') {
            queryFormAction.appDt.startDate = ''
        }
        // eslint-disable-next-line eqeqeq
        if (queryFormAction.appDt.endDate == null || queryFormAction.appDt.endDate === 'null') {
            queryFormAction.appDt.endDate = ''
        }
        // eslint-disable-next-line eqeqeq
        if (queryFormAction.ackDt.startDate == null || queryFormAction.ackDt.startDate === 'null') {
            queryFormAction.ackDt.startDate = ''
        }
        // eslint-disable-next-line eqeqeq
        if (queryFormAction.ackDt.endDate == null || queryFormAction.ackDt.endDate === 'null') {
            queryFormAction.ackDt.endDate = ''
        }

        window.open(
            `${
                window.location.origin + window.location.pathname
            }#/gmSalesStatisticsDetail?fundCode=${fundCode}&orgCode=${
                queryFormAction.orgvalue.orgCode
            }&consCode=${queryFormAction.orgvalue.consCode}&isAckOrder=${
                queryFormAction.isAckOrder
            }&startApplyDt=${queryFormAction.appDt.startDate}&endApplyDt=${
                queryFormAction.appDt.endDate
            }&startAckDt=${queryFormAction.ackDt.startDate}&endAckDt=${
                queryFormAction.ackDt.endDate
            }`
        )
    }

    /**
     * @description: 分页数据
     * @return {*}
     */
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }

    const pageObj = shallowRef(new PageObj())

    const lastUpdateTimeView = ref<any>()

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])
    const selectList = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            gmSalesStatisticsTableColumn.map(item => item.key),
            gmSalesStatisticsTableColumn
        )
    })

    //查詢
    const queryList = async () => {
        listLoading.value = true

        queryFormAction.orgvalue.consCode = queryForm.orgvalue.consCode
        queryFormAction.orgvalue.orgCode = queryForm.orgvalue.orgCode
        queryFormAction.fundCodeList = queryForm.fundCodeList
        queryFormAction.isAckOrder = queryForm.isAckOrder
        queryFormAction.appDt.startDate = queryForm.appDt.startDate
        queryFormAction.appDt.endDate = queryForm.appDt.endDate
        queryFormAction.ackDt.startDate = queryForm.ackDt.startDate
        queryFormAction.ackDt.endDate = queryForm.ackDt.endDate

        const params = {
            orgCode: queryForm.orgvalue.orgCode,
            consCode: queryForm.orgvalue.consCode,
            isAckOrder: queryForm.isAckOrder,
            fundCodeList: queryForm.fundCodeList,
            startApplyDt: queryForm.appDt.startDate,
            endApplyDt: queryForm.appDt.endDate,
            startAckDt: queryForm.ackDt.startDate,
            endAckDt: queryForm.ackDt.endDate
        }
        fetchRes(GmSalesStatisticsQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { list } = resObj
                tableData.value = list
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    const initData = async () => {
        listLoading.value = true
        fetchRes(FundListQuery(), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { fundList } = resObj
                selectList.value = fundList
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        isExporting.value = true
        const params = {
            orgCode: queryFormAction.orgvalue.orgCode,
            consCode: queryFormAction.orgvalue.consCode,
            isAckOrder: queryFormAction.isAckOrder,
            fundCodeList: queryFormAction.fundCodeList,
            startApplyDt: queryFormAction.appDt.startDate,
            endApplyDt: queryFormAction.appDt.endDate,
            startAckDt: queryFormAction.ackDt.startDate,
            endAckDt: queryFormAction.ackDt.endDate
        }
        const res: any = await GmSalesStatisticsDetailExport(params)
        isExporting.value = false

        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }
    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        fetchConsOrgList('', module.value)
        initData()
    })
</script>
<style lang="less" scoped></style>
@/views/common/scripts/consOrgListData
