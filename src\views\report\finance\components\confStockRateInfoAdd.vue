<!--
 * @Description: 添加产品弹框
 * @Author: chaohui.wu
 * @Date: 2023-03-24 21:07:35
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-27 19:14:16
 * @FilePath: /crm-template/src/views/components/dialogCommon/adjustAmt/adjustAmtIndex.vue
 * @ 当前未解耦，有时间解耦后可提取到dialogCommomn中
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        height="500px"
        :border="true"
        :title="title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <!-- 产品费率的弹框 -->
            <el-form
                v-if="confStockRateInfoAdd"
                ref="ruleFormRef"
                :model="ruleForm"
                label-width="100px"
                :rules="rules"
                status-icon
            >
                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            label-width="auto"
                            prop="fundCode"
                            label="产品代码"
                            style="margin-left: 40%"
                        >
                            <crm-input
                                v-model="ruleForm.fundCode"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            label-width="auto"
                            prop="fundName"
                            label="产品名称"
                            style="margin-left: 40%"
                        >
                            <crm-input
                                v-model="ruleForm.fundName"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            label-width="auto"
                            prop="bottomFundName"
                            label="底层产品名称"
                            style="margin-left: 34%"
                        >
                            <crm-input
                                v-model="ruleForm.bottomFundName"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            label-width="auto"
                            prop="productRange"
                            label="产品范围"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.productRange"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            label-width="auto"
                            prop="term"
                            label="期限"
                            style="margin-left: 24%"
                        >
                            <crm-input
                                v-model="ruleForm.term"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="contractDate"
                            label-width="auto"
                            label="合同日期"
                            style="margin-left: 18%"
                        >
                            <el-date-picker
                                v-model="ruleForm.contractDate"
                                class="popperClass"
                                type="date"
                                size="small"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="fundType"
                            label-width="auto"
                            label="股权产品类型"
                            style="margin-left: 34%"
                        >
                            <crm-select
                                v-model="ruleForm.fundType"
                                :placeholder="fundTypeList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="fundTypeList.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item
                            prop="collectSales"
                            label-width="auto"
                            label="收款销量"
                            style="margin-left: 42%"
                        >
                            <crm-input
                                v-model="ruleForm.collectSales"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="sales"
                            label-width="auto"
                            label="销量(万元)"
                            style="margin-left: 40%"
                        >
                            <crm-input
                                v-model="ruleForm.sales"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="subRate"
                            label="认购/销售服务费率"
                            label-width="auto"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.subRate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="subLowerLimit"
                            label-width="auto"
                            label="认购下限"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.subLowerLimit"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="subUpLimit"
                            label-width="auto"
                            label="认购上限"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.subUpLimit"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="subIncome"
                            label-width="auto"
                            label="认购收入"
                            style="margin-left: 42%"
                        >
                            <crm-input
                                v-model="ruleForm.subIncome"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="subPayDate"
                            label-width="auto"
                            label="认购支付时间"
                            style="margin-left: 36%"
                        >
                            <crm-input
                                v-model="ruleForm.subPayDate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="fixedRate"
                            label-width="auto"
                            label="固定费率"
                            style="margin-left: 42%"
                        >
                            <crm-input
                                v-model="ruleForm.fixedRate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="fixedPayFrequency"
                            label-width="auto"
                            label="固定费用支付频率"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.fixedPayFrequency"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="periodRate1"
                            label-width="auto"
                            label="投资期费率1"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.periodRate1"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="investmentPeriod1"
                            label-width="auto"
                            label="投资期期限1"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.investmentPeriod1"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="periodRate2"
                            label-width="auto"
                            label="投资期费率2"
                            style="margin-left: 37%"
                        >
                            <crm-input
                                v-model="ruleForm.periodRate2"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="investmentPeriod2"
                            label-width="auto"
                            label="投资期期限2"
                            style="margin-left: 37%"
                        >
                            <crm-input
                                v-model="ruleForm.investmentPeriod2"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="quitRate"
                            label-width="auto"
                            label="退出期费率"
                            style="margin-left: 37%"
                        >
                            <crm-input
                                v-model="ruleForm.quitRate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="quitPeriod"
                            label-width="auto"
                            label="退出期期限"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.quitPeriod"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="quitRemark"
                            label-width="auto"
                            label="退出期备注"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.quitRemark"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="manageFormula"
                            label-width="auto"
                            label="管理费公式"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.manageFormula"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="fixedManage"
                            label-width="auto"
                            label="固定管理费"
                            style="margin-left: 37%"
                        >
                            <crm-input
                                v-model="ruleForm.fixedManage"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="fixedPayDate"
                            label-width="auto"
                            label="固定管理费支付时间"
                            style="margin-left: 25%"
                        >
                            <crm-input
                                v-model="ruleForm.fixedPayDate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="performance"
                            label-width="auto"
                            label="业绩提成"
                            style="margin-left: 39%"
                        >
                            <crm-input
                                v-model="ruleForm.performance"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="performanceFrequency"
                            label-width="auto"
                            label="业绩提成支付频率"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.performanceFrequency"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="performanceSupple"
                            label-width="auto"
                            label="业绩提成补充"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.performanceSupple"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="performancePayDate"
                            label-width="auto"
                            label="业绩提成支付时间"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.performancePayDate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            prop="investmentAfter"
                            label-width="auto"
                            label="投后"
                            style="margin-left: 44%"
                        >
                            <crm-input
                                v-model="ruleForm.investmentAfter"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="agarement"
                            label-width="auto"
                            label="协议主体"
                            style="margin-left: 38%"
                        >
                            <crm-input
                                v-model="ruleForm.agarement"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12"> </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 43%">
                            <el-button type="primary" @click="submitForm(ruleFormRef)">
                                提交
                            </el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item style="margin-left: 10%">
                            <el-button type="primary" @click="resetForm(ruleFormRef)"
                                >重置</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { fetchRes } from '@/utils/index'
    import { dataList } from './data/labelData'
    import { confStockRateInfoInsert } from '@/api/project/report/finance/confStockRateInfo/confStockRateInfo'
    const {
        isRedemDate2,
        isSettleDate2,
        isShareDate2,
        manageFormulaList2,
        redemFormulaList2,
        consultFormulaList,
        selfCustList,
        hswTypeList,
        fundTypeList,
        performancejtTypeList
    } = dataList
    const loadingFlag = ref<boolean>(false)
    // 表单数据共用的实体类
    interface RuleForm {
        taskId: string
        fundCode: string
        fundName: string
        bottomFundName: string
        productRange: string
        term: string
        contractDate: string
        fundType: string
        collectSales: string
        sales: string
        subRate: string
        subLowerLimit: string
        subUpLimit: string
        subIncome: string
        subPayDate: string
        fixedRate: string
        fixedPayFrequency: string
        periodRate1: string
        investmentPeriod1: string
        periodRate2: string
        investmentPeriod2: string
        quitRate: string
        quitPeriod: string
        quitRemark: string
        manageFormula: string
        fixedManage: string
        fixedPayDate: string
        performance: string
        performanceFrequency: string
        performanceSupple: string
        performancePayDate: string
        investmentAfter: string
        agarement: string
    }

    const ruleFormRef = ref<FormInstance>()
    const ruleForm = reactive<RuleForm>({
        taskId: '',
        fundCode: '',
        fundName: '',
        bottomFundName: '',
        productRange: '',
        term: '',
        contractDate: '',
        fundType: '',
        collectSales: '',
        sales: '',
        subRate: '',
        subLowerLimit: '',
        subUpLimit: '',
        subIncome: '',
        subPayDate: '',
        fixedRate: '',
        fixedPayFrequency: '',
        periodRate1: '',
        investmentPeriod1: '',
        periodRate2: '',
        investmentPeriod2: '',
        quitRate: '',
        quitPeriod: '',
        quitRemark: '',
        manageFormula: '',
        fixedManage: '',
        fixedPayDate: '',
        performance: '',
        performanceFrequency: '',
        performanceSupple: '',
        performancePayDate: '',
        investmentAfter: '',
        agarement: ''
    })
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                conscustNo: string
                amount: number
            }
            confStockRateInfoAdd?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    conscustNo: '',
                    amount: 0
                }
            },
            confStockRateInfoAdd: false
        }
    )

    // 弹窗标题配置
    // eslint-disable-next-line vue/return-in-computed-property
    const title = computed(() => {
        if (props.confStockRateInfoAdd) {
            return '新增股权产品费率配置'
        }
    })
    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })
    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()
    const rules = reactive<FormRules<RuleForm>>({
        fundCode: [
            { required: true, message: '请输入产品代码', trigger: 'blur' },
            { pattern: /^.{0,6}$/, message: '产品代码不能超过6个字符', trigger: 'blur' }
        ],
        fundName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        bottomFundName: [{ required: true, message: '请输入底层产品名称', trigger: 'blur' }],
        productRange: [{ required: true, message: '请输入产品范围', trigger: 'blur' }],
        term: [{ required: true, message: '请输入期限', trigger: 'blur' }],
        contractDate: [{ required: true, message: '请输入合同日期', trigger: 'blur' }],
        fundType: [{ required: true, message: '请输入股权产品类型', trigger: 'blur' }],
        periodRate1: [
            { required: true, message: '请输入投资期费率1', trigger: 'blur' },
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '请输入正确的数字格式',
                trigger: 'blur'
            }
        ],
        collectSales: [
            {
                pattern: /^\d+(\.\d{1,6})?$/,
                message: '只能输入数字',
                trigger: 'blur'
            }
        ],
        sales: [
            {
                pattern: /^\d+(\.\d{1,6})?$/,
                message: '只能输入数字',
                trigger: 'blur'
            }
        ],
        subRate: [
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '请输入正确的数字格式',
                trigger: 'blur'
            }
        ],
        periodRate2: [
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '请输入正确的数字格式',
                trigger: 'blur'
            }
        ],
        quitRate: [
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '请输入正确的数字格式',
                trigger: 'blur'
            }
        ],
        quitPeriod: [{ pattern: /^\d+$/, message: '请输入正确的数字格式', trigger: 'blur' }],
        investmentPeriod2: [{ pattern: /^\d+$/, message: '请输入正确的数字格式', trigger: 'blur' }],
        investmentPeriod1: [{ pattern: /^\d+$/, message: '请输入正确的数字格式', trigger: 'blur' }],
        subLowerLimit: [{ pattern: /^\d+$/, message: '请输入正确的数字格式', trigger: 'blur' }],
        subUpLimit: [{ pattern: /^\d+$/, message: '请输入正确的数字格式', trigger: 'blur' }]
    })
    //提交方法
    const submitForm = async (formEl: FormInstance | undefined) => {
        console.log('submit11111111111111')
        if (!formEl) {
            return
        }
        await formEl.validate((valid, fields) => {
            if (valid) {
                console.log('submit!', fields)
                confStockRateInfoInsertSubmit(ruleForm)
            } else {
                console.log('error submit!', fields)
            }
        })
    }
    //重置方法
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        formEl.resetFields()
    }
    /**
     * 产品费率新增方法
     * @param params
     */
    function confStockRateInfoInsertSubmit(params: any) {
        console.log('params' + JSON.stringify(params))
        fetchRes(confStockRateInfoInsert(params), {
            successCB: (res: any) => {
                loadingFlag.value = false
                dialogVisible.value = false
                return emit('callback')
            },
            errorCB: (res: any) => {
                loadingFlag.value = false
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => {
                loadingFlag.value = false
            },
            successTxt: '添加成功',
            failTxt: '',
            fetchKey: ''
        })
    }
    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        dialogVisible.value = false
        // formEl.resetFields()
    }
</script>

<style lang="less" scoped>
    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
