import { defineStore } from 'pinia'
import {
    login,
    logout,
    getUserInfo,
    getUserMenu
    // changeLocalDataRole
} from '@/api/base/users'
import {
    getToken,
    setToken,
    setLocalItem,
    deleteLocalItem,
    delateSessionItem,
    logoutWithoutRequest
} from '@/utils/index.js'
import { responseCode } from '@/constant/index'
export const useUserStore = defineStore({
    id: 'user',
    state: () => ({
        token: getToken(),
        menus: null,
        userInfo: null
    }),

    actions: {
        // 用户登录
        async userLogin(userInfo) {
            const response = await login(userInfo).catch()
            return new Promise((resolve, reject) => {
                if (response) {
                    const { body } = response

                    this.token = body
                    setToken(body)
                    setLocalItem('hb_crm_token', body)

                    // 因为重新登录了 所以清除之前的离线数据
                    this.menus = null
                    this.userInfo = null
                    deleteLocalItem('USER_INFO')
                    delateSessionItem('USER_INFO')
                    deleteLocalItem('USER_MENU')

                    resolve(response)
                } else {
                    reject(response)
                }
            })
        },

        // 获取用户信息
        async getUserInfo() {
            const response = await getUserInfo().catch()
            return new Promise((resolve, reject) => {
                if (response) {
                    const { body } = response
                    this.userInfo = body
                    localStorage.setItem('USER_INFO', JSON.stringify(body))
                    sessionStorage.setItem('USER_INFO', JSON.stringify(body))
                    resolve(body)
                } else {
                    reject(response)
                }
            })
        },

        // 获取用户菜单
        async getUserMenu() {
            const { body } = await getUserMenu().catch()
            return new Promise((resolve, reject) => {
                if (!body || !Array.isArray(body)) {
                    reject()
                } else {
                    const newMenuList = body || []
                    this.menus = newMenuList
                    setLocalItem('USER_MENU', JSON.stringify(newMenuList))
                    resolve(newMenuList)
                }
            })
        },

        // user logout
        async logout() {
            const res = await logout().catch()
            if (
                (res && res.code === responseCode.SUCCESS) ||
                res?.code === responseCode.CRMSUCCESS
            ) {
                this.token = ''
                logoutWithoutRequest()
            }
        }
        // 切换用户账号权限
        // async chageUserLocalDataRole(dataRoleId) {
        //     const response = await changeLocalDataRole({ dataRoleId: dataRoleId }).catch()
        //     return new Promise((resolve, reject) => {
        //         if (response) {
        //             const { body } = response
        //             this.userInfo = {
        //                 ...this.userInfo,
        //                 dataRoleId: body.id,
        //                 dataRoleName: body.title
        //             }
        //             localStorage.setItem('USER_INFO', JSON.stringify(this.userInfo))
        //             resolve(body)
        //         } else {
        //             reject()
        //         }
        //     })
        // }
    }
})
