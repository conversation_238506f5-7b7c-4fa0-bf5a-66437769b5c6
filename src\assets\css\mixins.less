/*
 * @Description: el公共样式
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-08-18 19:45:23
 * @FilePath: /dtms-product-web/src/assets/css/mixins.less
 *  
 */

.table_has_border (@borderColor: #ebeef5) {
    &.el-table {
        &::before {
            display: block;
            background-color: @borderColor;
        }

        .el-table__header {
            tr {
                th {
                    border-right: 1px solid @borderColor;
                    border-bottom: 1px solid @borderColor;
                }
            }
        }

        .el-table__body {
            td {
                border-right: 1px solid @borderColor;
                border-bottom: 1px solid @borderColor;
            }
        }

        &.el-table--border,
        &.el-table--group {
            border: 1px solid @borderColor;
        }
    }
}

.research_report_icon {
    margin: auto 10px;
    font-size: 18px;
    color: #6272af;
    cursor: pointer;
}

.research_report_input {
    position: relative;
    width: 400px;
    margin: auto auto auto 0;

    .crm_input.el-input--small .el-input__inner {
        height: 40px;
        padding-right: 90px;
    }

    .input_suffix_btn {
        position: absolute;
        top: 6px;
        right: 12px;
        height: 29px;
    }
}

.crm-web-app {
    position: relative;
    height: 100%;
    // fix:日期文本的上下边框看不见的问题
    .crm_date_picker.el-input--small .el-input__inner {
        height: 22px;
        line-height: 22px;
    }
    // fix:cursor时 input变宽

    .el-input__suffix {
        position: absolute;
        right: 5px;
    }
    // 时间选择控件 顶部上下布局中问题fix
    .el-picker-panel__icon-btn {
        margin-top: 0;
    }

    // better-scroll滚动条的样式
    .bscroll-vertical-scrollbar {
        .bscroll-indicator {
            right: 2px;
            width: 6px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
        }
    }

    // 下拉选择框样式
    .el-select-dropdown {
        .el-select-dropdown__item {
            .fs(12);

            height: 30px;
            padding: 0 15px;
            line-height: 30px;
        }
    }

    // 基础表格样式
    .crm-base-table {
        &.el-table {
            .fs(12);

            color: @font_color_05;

            &::before {
                display: none;
            }

            .el-table__header {
                tr {
                    th {
                        height: 38px;
                        padding: 0;
                        font-weight: bold;
                        line-height: 38px;
                        color: @bg_main_03;
                        background-color: @bg_main_02;
                        border: none;

                        .cell {
                            padding: 0 10px;
                            .text-overflow();

                            .caret-wrapper {
                                display: inline-block;
                                width: 16px;
                                height: 17px;

                                .sort-caret {
                                    &.ascending {
                                        top: -3px;
                                    }

                                    &.descending {
                                        bottom: -1px;
                                    }
                                }
                            }
                        }

                        &.is-sortable.is-right {
                            .cell {
                                display: flex;
                                align-items: center;
                                justify-content: flex-end;

                                .caret-wrapper {
                                    .sort-caret {
                                        &.ascending {
                                            top: -2px;
                                        }

                                        &.descending {
                                            bottom: -2px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .el-table__body {
                td {
                    height: 37px;
                    padding: 0;
                    color: @font_color_02;
                    // border: none;
                    .cell {
                        // min-height: 37px;
                        // line-height: 1.4;
                        // line-height: 37px;
                        .el-button {
                            color: @font_link;
                        }

                        .el-input__icon {
                            line-height: inherit;
                        }
                    }

                    &.el-table-column--selection {
                        .cell {
                            padding-right: 10px;
                        }
                    }
                }
            }

            &.el-table--striped .el-table__body tr.el-table__row--striped td {
                background-color: #f5f6fa;
            }

            .el-table__fixed-right {
                &::before {
                    display: none;
                }
            }
            // 表格hover背景色
            .el-table--striped .el-table__body tr.el-table__row--striped.current-row td,
            .el-table__body tr.current-row > td,
            .el-table__body tr.hover-row.current-row > td,
            .el-table__body tr.hover-row.el-table__row--striped.current-row > td,
            .el-table__body tr.hover-row.el-table__row--striped > td,
            .el-table__body tr.hover-row > td {
                background-color: #fafafa;
            }

            // 去掉element-plus里面新增的table底边border
            &:not(.table_has_border) .el-table__inner-wrapper::before {
                display: none;
            }

            // &.el-table--border {
            //     .el-table__inner-wrapper {
            //         tr:first-child td:first-child {
            //             border-left: 0;
            //         }
            //     }
            // }
            // 表格内tooltip的最大宽度400
            .el-popper {
                max-width: 400px;
            }
        }
    }
    // 主题色table表格
    .theme-table {
        &.el-table {
            font-size: 14px;
            color: @font_color_05;
            --el-table-border-color: @font_color_01;

            &::before {
                display: none;
            }

            .el-table__header {
                tr {
                    th {
                        padding: 3px 0 4px;
                        font-weight: normal;
                        color: @font_color_01;
                        background-color: @theme_main;

                        .cell {
                            padding: 0 10px;
                            font-family: 'Microsoft YaHei';
                            line-height: 24px;
                            .text-overflow();

                            .caret-wrapper {
                                display: inline-block;
                                width: 16px;
                                height: 17px;

                                .sort-caret {
                                    &.ascending {
                                        top: -3px;
                                    }

                                    &.descending {
                                        bottom: -1px;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .el-table__body {
                tr {
                    &.theme-active {
                        background-color: @bg_main_hover;
                    }

                    td {
                        padding: 5px 0;
                        font-family: 'Microsoft YaHei';
                        line-height: 24px;
                        background-color: @bg_main_table;

                        &.theme-active {
                            background-color: @bg_main_hover;
                        }

                        &.theme-font {
                            color: @theme_main;
                        }

                        &.theme-red-active {
                            font-weight: normal;
                            color: @font_color_01;
                            background-color: @theme_main;
                        }

                        &.theme-null {
                            font-weight: normal;
                            background-color: #f5f5f5;
                        }

                        &.font-bold {
                            font-weight: bold;
                        }

                        .cell {
                            .el-button {
                                color: @font_link;
                            }

                            .el-input__icon {
                                line-height: inherit;
                            }
                        }

                        &.el-table-column--selection {
                            .cell {
                                padding-right: 10px;
                            }
                        }
                    }
                }
            }

            &.el-table--striped .el-table__body tr.el-table__row--striped td {
                background-color: #f5f6fa;
            }

            .el-table__fixed-right {
                &::before {
                    display: none;
                }
            }
            // 表格hover背景色
            .el-table--striped .el-table__body tr.el-table__row--striped.current-row td,
            .el-table__body tr.current-row > td,
            .el-table__body tr.hover-row.current-row > td,
            .el-table__body tr.hover-row.el-table__row--striped.current-row > td,
            .el-table__body tr.hover-row.el-table__row--striped > td,
            .el-table__body tr.hover-row > td {
                background-color: #fdf6f6;
            }

            // 去掉element-plus里面新增的table底边border
            &:not(.table_has_border) .el-table__inner-wrapper::before {
                display: none;
            }
            // 表格内tooltip的最大宽度400
            .el-popper {
                max-width: 400px;
            }
        }

        &.theme-blue {
            font-size: 14px;
            color: @font_color_05;
            --el-table-border-color: @font_color_01;

            &::before {
                display: none;
            }

            .el-table__header {
                tr {
                    th {
                        padding: 3px 0 4px;
                        font-weight: normal;
                        color: @font_color_01;
                        background-color: @theme_main_blue;

                        .cell {
                            padding: 0 10px;
                            font-family: 'Microsoft YaHei';
                            line-height: 24px;
                            .text-overflow();

                            .caret-wrapper {
                                display: inline-block;
                                width: 16px;
                                height: 17px;

                                .sort-caret {
                                    &.ascending {
                                        top: -3px;
                                    }

                                    &.descending {
                                        bottom: -1px;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .el-table__body {
                tr {
                    &.theme-active {
                        background-color: @bg_main_hover_blue;
                    }

                    td {
                        padding: 5px 0;
                        font-family: 'Microsoft YaHei';
                        line-height: 24px;
                        background-color: @bg_main_table_blue;

                        &.theme-active {
                            background-color: @bg_main_hover_blue;
                        }

                        &.theme-red-active {
                            font-weight: normal;
                            color: @font_color_01;
                            background-color: @theme_main_blue;
                        }

                        &.theme-null {
                            font-weight: normal;
                            background-color: #f5f5f5;
                        }

                        .cell {
                            .el-button {
                                color: @font_link;
                            }

                            .el-input__icon {
                                line-height: inherit;
                            }
                        }

                        &.el-table-column--selection {
                            .cell {
                                padding-right: 10px;
                            }
                        }
                    }
                }
            }
        }

        &.theme-gray {
            font-size: 13px;
            --el-table-border-color: #f1f1f1;

            &-default {
                font-size: 14px;
                --el-table-border-color: #f1f1f1;
            }

            &::before {
                display: none;
            }

            .el-table__header {
                tr {
                    th {
                        padding: 3px 0 4px;
                        font-family: 'Microsoft YaHei';
                        font-size: 13px;
                        font-weight: normal;
                        color: @font_color;
                        background-color: #e4e5ec;

                        &.border-red {
                            &-l {
                                border-left: 1px solid #d0021b;
                            }

                            &-r {
                                border-right: 1px solid #d0021b;
                            }

                            &-b {
                                border-bottom: 1px solid #d0021b;
                            }
                        }

                        &.border-blue {
                            &-l {
                                border-left: 1px solid #004360;
                            }

                            &-r {
                                border-right: 1px solid #004360;
                            }

                            &-b {
                                border-bottom: 1px solid #004360;
                            }
                        }

                        &.bg-gray {
                            font-family: 'Microsoft YaHei-Bold', 'Microsoft YaHei';
                            font-weight: bold;
                            background-color: rgba(228, 229, 236, 1);
                        }

                        &.bg-red {
                            font-family: 'Microsoft YaHei-Bold', 'Microsoft YaHei';
                            font-weight: bold;
                            color: #ffffff;
                            background-color: #d0021b;
                        }

                        &.bg-blue {
                            font-family: 'Microsoft YaHei-Bold', 'Microsoft YaHei';
                            font-weight: bold;
                            color: #ffffff;
                            background-color: #004360;
                        }

                        .cell {
                            padding: 0 10px;
                            font-family: 'Microsoft YaHei';
                            line-height: 24px;
                            .text-overflow();

                            .caret-wrapper {
                                display: inline-block;
                                width: 16px;
                                height: 17px;

                                .sort-caret {
                                    &.ascending {
                                        top: -3px;
                                    }

                                    &.descending {
                                        bottom: -1px;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .el-table__body {
                tr {
                    &.theme-active {
                        background-color: @bg_main_hover;
                    }

                    td {
                        padding: 5px 0;
                        font-family: 'Microsoft YaHei';
                        line-height: 24px;
                        background-color: #ffffff;

                        &.theme-active {
                            background-color: @bg_main_hover;
                        }

                        &.theme-red-active {
                            font-weight: normal;
                            color: @font_color_01;
                            background-color: @theme_main;
                        }

                        &.border-red {
                            &-l {
                                border-left: 1px solid #d0021b;
                            }

                            &-r {
                                border-right: 1px solid #d0021b;
                            }

                            &-b {
                                z-index: 10;
                                border-bottom: 1px solid #d0021b;
                            }
                        }

                        &.border-blue {
                            &-l {
                                border-left: 1px solid #004360;
                            }

                            &-r {
                                border-right: 1px solid #004360;
                            }

                            &-b {
                                z-index: 10;
                                border-bottom: 1px solid #004360;
                            }
                        }

                        &.bg-gray {
                            font-family: 'Microsoft YaHei';
                            font-family: 'Microsoft YaHei-Bold', 'Microsoft YaHei';
                            font-weight: bold;
                            background-color: rgba(228, 229, 236, 0.6);
                        }

                        &.bg-gray1 {
                            font-family: 'Microsoft YaHei';
                            font-weight: normal;
                            color: #666666;
                            background-color: rgba(228, 229, 236, 0.4);
                        }

                        &.bg-red {
                            font-family: 'Microsoft YaHei-Bold', 'Microsoft YaHei';
                            font-weight: bold;
                            color: #d0021b;
                            background-color: rgba(208, 2, 27, 0.09);
                        }

                        &.bg-blue {
                            font-family: 'Microsoft YaHei-Bold', 'Microsoft YaHei';
                            font-weight: bold;
                            color: #d0021b;
                            background-color: rgba(0, 67, 96, 0.09);
                        }

                        &.bg-null {
                            font-weight: normal;
                            background-color: #ffffff;
                        }

                        &.theme-null {
                            font-weight: normal;
                            background-color: #f5f5f5;
                        }

                        .cell {
                            .el-button {
                                color: @font_link;
                            }

                            .el-input__icon {
                                line-height: inherit;
                            }
                        }

                        &.el-table-column--selection {
                            .cell {
                                padding-right: 10px;
                            }
                        }
                    }
                }
            }
        }
    }
    // 基于主题色定制table表格

    // 下拉选择样式(202105ui改版)
    .el-dropdown-menu {
        color: @font_color_04;
        text-align: left;

        .el-dropdown-menu__item {
            .fs(12);

            padding: 0 15px;
            line-height: 30px;

            &.is-disabled {
                color: #c1c4cc;
            }

            &:not(.is-disabled):hover {
                color: @theme_main;
                background-color: rgba(200, 45, 48, 0.1);
            }
        }
    }

    // 输入建议下拉框
    .el-autocomplete-suggestion {
        color: @font_color_04;
        text-align: left;

        .el-autocomplete-suggestion__list > li {
            .fs(12);

            padding: 0 15px;
            line-height: 30px;

            &.is-disabled {
                color: #c1c4cc;
            }

            &:not(.is-disabled):hover {
                color: @theme_main;
                background-color: rgba(200, 45, 48, 0.1);
            }
        }
    }

    // checkbox字体调整
    .el-checkbox__label {
        .fs(13);
    }

    // 详情副表格样式
    .subTable {
        position: sticky;
        top: 0;
        height: 100%;

        th {
            padding: 4px 0;
            font-weight: normal;
            background-color: @bg_main;
            .fs(13);
        }

        .el-table__row {
            td {
                padding: 4px 0;
            }

            .el-input {
                input {
                    .fs(13);

                    height: 26px;
                }
            }
        }
    }

    // 小提示的文字颜色修改
    .el-message {
        &.el-message--info {
            --el-message-text-color: @font_color_05;
        }

        &.el-message--success {
            --el-message-text-color: @font_success;
        }
    }

    // toast位置下移
    .is-message-box {
        top: 90px;
        z-index: 99999; // 信息提示z-index权重最高
        // 文本提示弹窗
        .el-overlay-message-box {
            position: absolute;
            z-index: 99;
            display: flex;
            align-items: center;
            justify-content: center;
            // left: 50%;
            // top: 50%;
            // transform: translate(-50%, -50%);
            width: 100%;
        }

        .message-box-of-alert {
            min-width: 400px;
            max-width: 800px;
            background-color: #ffffff;

            &.el-message-box {
                width: auto;
                padding-bottom: 0;
                --el-messagebox-content-color: @font_color;
            }

            .el-message-box__header {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 12px 20px;
                background-color: @bg_main_01;

                .el-message-box__title {
                    font-size: 15px;
                    font-weight: 700;
                    line-height: var(--el-dialog-font-line-height);
                    color: var(--el-text-color-primary);
                }

                .el-message-box__headerbtn {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 54px;
                    height: 54px;
                    padding: 0;
                    font-size: 18px;
                    cursor: pointer;
                    background: 0 0;
                    border: none;
                    outline: 0;
                }
            }

            .el-message-box__content {
                min-height: 96px;
                max-height: 360px;
                padding: 34px 20px;
                margin-bottom: 10px;
                overflow-y: auto;
                text-align: left;

                .el-message-box__container {
                    display: flex;
                    align-items: center;
                }
            }

            .el-message-box__btns {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                width: 100%;
                padding: 10px 20px;
                background-color: @bg_main_01;

                .el-button {
                    height: 26px;
                    padding: 0 10px;
                    // --el-button-text-color:#fff;
                    // --el-button-bg-color:@theme_main;
                    // --el-button-border-color: @theme_main;
                    // --el-button-hover-text-color: @theme_main;
                    // --el-button-hover-bg-color: rgba(200, 45, 48, 0.1);
                    // --el-button-hover-border-color: #ead8d8;
                    // --el-button-active-text-color: @theme_main;
                    // --el-button-active-bg-color: rgba(200, 45, 48, 0.1);
                    // --el-button-active-border-color: #ead8d8;
                    font-size: 12px;
                    font-weight: normal;
                    line-height: 26px;
                    color: #231815;
                    vertical-align: baseline;
                    background-color: @font_color_01;
                    border: 1px solid @border_color_02;
                    border-radius: 0;

                    &.el-button--primary {
                        font-weight: normal;
                        color: #ffffff;
                        vertical-align: baseline;
                        background-color: @theme_main;
                        border-color: @theme_main;
                    }
                }
            }
        }

        .blank-message-box {
            z-index: 99;
            min-width: 400px;
            max-width: 700px;

            &.el-message-box {
                width: auto;
                padding-bottom: 0;
                --el-messagebox-content-color: @font_color;
            }

            .el-message-box__header {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 12px 20px;
                background-color: @bg_main_01;

                .el-message-box__title {
                    font-size: 15px;
                    font-weight: 700;
                    line-height: var(--el-dialog-font-line-height);
                    color: var(--el-text-color-primary);
                }

                .el-message-box__headerbtn {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 54px;
                    height: 54px;
                    padding: 0;
                    font-size: 18px;
                    cursor: pointer;
                    background: 0 0;
                    border: none;
                    outline: 0;
                }
            }

            .el-message-box__content {
                width: 100%;
                min-height: 80px;
                max-height: 500px;
                padding: 20px 20px 10px;
                margin-bottom: 10px;
                overflow-y: auto;
                font-size: 12px;
                font-weight: normal;
                text-align: left;

                .el-message-box__container {
                    display: flex;
                    align-items: baseline;

                    .el-icon {
                        align-self: center;
                        margin-right: 5px;
                    }
                }
            }

            .el-message-box__btns {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                width: 100%;
                padding: 10px 20px;
                background-color: @bg_main_01;

                .el-button {
                    --el-button-text-color: #231815;
                    --el-button-bg-color: @font_color_01;
                    --el-button-border-color: @border_color_02;
                    --el-button-hover-text-color: @theme_main;
                    --el-button-hover-bg-color: rgba(200, 45, 48, 0.1);
                    --el-button-hover-border-color: #ead8d8;

                    height: 26px;
                    padding: 0 10px;
                    font-size: 12px;
                    font-weight: normal;
                    line-height: 26px;
                    vertical-align: baseline;
                    border-radius: 3px;

                    &:focus {
                        color: @theme_main;
                        background-color: #ead8d8;
                        border-color: #dddddd;
                    }

                    &:focus-visible {
                        outline: 0;
                    }

                    &.el-button--primary {
                        --el-button-text-color: #ffffff;
                        --el-button-bg-color: @theme_main;
                        --el-button-border-color: @theme_main;

                        font-weight: normal;
                        vertical-align: baseline;

                        &:focus {
                            color: @font_color_01;
                            background-color: @theme_main;
                            border-color: @theme_main;
                        }

                        &:focus-visible {
                            outline: 0;
                        }
                    }
                }
            }
        }
    }

    .flex {
        display: flex;
        height: 100%;
    }

    .flex-1 {
        flex: 1;
    }

    // 折线图tooltip的样式
    .lineChartToolTipItems {
        margin-top: 5px;

        li {
            display: flex;
            line-height: 20px;
            color: #262626;
            .fs(10);

            span {
                &:first-child {
                    padding-right: 10px;

                    i {
                        display: inline-block;
                        width: 6px;
                        height: 6px;
                        margin-right: 4px;
                        border-radius: 50%;
                    }
                }

                &:last-of-type {
                    margin-left: auto;
                    text-align: right;
                }
            }
        }
    }

    // 在页面上隐藏
    .hideInPage {
        position: absolute;
        top: -99999px;
    }

    .el-textarea__inner {
        font-family: Arial, sans-serif;

        &:focus {
            box-shadow: 0 0 0 1px @border_focus inset;
        }
    }

    // 可点击跳转的条目
    .link_item_g {
        color: @font_link;
        cursor: pointer;

        &:hover {
            color: @theme_main;
            text-decoration: underline;
        }
    }

    // 复选框
    .el-tree-node__content {
        > label.el-checkbox {
            margin-right: 6px;
        }
    }

    .el-checkbox {
        .el-checkbox__input {
            .el-checkbox__inner {
                width: 12px;
                height: 12px;
                background-color: #fbfcfe;
                border: solid 1px #e7e7ea;
                border-color: #adadb9;

                &:hover {
                    border-color: #c01b26;
                }

                &::after {
                    top: 0;
                    left: 3px;
                    width: 3px;
                    height: 6px;
                }
            }

            &.is-indeterminate .el-checkbox__inner {
                background-color: #c01b26;
                border-color: #c01b26;
            }

            &.is-checked {
                .el-checkbox__inner {
                    background-color: #c01b26;
                    border-color: #c01b26;
                }
            }
        }
    }

    // table详情的表单样式
    .crm_table_detail_form {
        display: flex;
        flex-flow: column wrap;

        .el-form-item {
            width: 50%;
            margin-bottom: 22px;

            &.large {
                width: 100%;
            }

            &.half {
                width: 50%;
            }

            &.default {
                width: 90%;
            }

            &.without_error_tip {
                margin-bottom: 6px;
            }

            .el-form-item__label {
                .fs(12);
                // fixbug:表单label文本上下不局中
                height: 24px;
                padding-right: 8px;
                line-height: 24px;
                color: @font_color;
                text-align: right;
                white-space: nowrap;
            }

            .el-form-item__content {
                line-height: 24px;

                .el-input {
                    &.is-disabled {
                        .el-input__inner {
                            color: @font_color_02;
                        }
                    }
                }
                // 表单详情的单选框样式
                .el-radio-group {
                    padding-left: 4px;
                    // margin-bottom: 6px;
                    .el-radio {
                        height: auto;

                        .el-radio__label {
                            .fs(12);

                            padding-left: 5px;
                            line-height: 24px;
                        }

                        .el-radio__input {
                            .el-radio__inner {
                                width: 12px;
                                height: 12px;
                            }
                        }
                    }
                }
                // input-number
                .el-input-number {
                    &.el-input-number--small {
                        width: 100%;
                        min-width: 200px;
                        font-size: 12px;

                        .el-input__inner {
                            text-align: left;
                        }
                    }
                }

                .el-form-item__error {
                    min-width: 300px;
                }

                .el-input__wrapper {
                    width: 100%;
                    --el-input-focus-border: @border_focus;
                    --el-input-focus-border-color: @border_focus;
                }
            }
        }
    }

    // 尽调录入页面的tooltip
    .diligence_task_tooltip.is-light {
        max-width: 400px;
        color: #f8672b;
        background-color: #ffe7e1;
        border-color: #f8672b;

        .popper__arrow {
            border-top-color: #f8672b;
            border-bottom-color: #f8672b;

            &::after {
                border-top-color: #ffe7e1;
                border-bottom-color: #ffe7e1;
            }
        }
    }

    // 上升、下降箭头
    .arrow_up_and_down {
        display: inline-flex;
        flex-wrap: wrap;
        justify-content: center;
        width: 4px;
        height: 10px;
        margin: 7px 0 0 2px;
        vertical-align: top;

        &::before,
        &::after {
            display: block;
            content: '';
        }

        &.up {
            &::before {
                border-right: 2px solid transparent;
                border-bottom: 4px solid @theme_rise;
                border-left: 2px solid transparent;
            }

            &::after {
                border-top: 6px solid @theme_rise;
                border-right: 1px solid transparent;
                border-left: 1px solid transparent;
            }
        }

        &.down {
            margin-top: 6px;

            &::after {
                border-top: 4px solid @theme_down;
                border-right: 2px solid transparent;
                border-left: 2px solid transparent;
            }

            &::before {
                border-right: 1px solid transparent;
                border-bottom: 6px solid @theme_down;
                border-left: 1px solid transparent;
            }
        }
    }

    // pdf相关的主体样式
    .global_pdf_content {
        width: 100%;

        .pdf_content {
            padding: 12px 0;
            margin-bottom: 10px;
            background-color: #ffffff;

            .research_report_icon {
                margin: auto 10px;
                font-size: 18px;
                color: #6272af;
                cursor: pointer;
            }

            .first_title {
                display: flex;
                padding-right: 30px;

                .title {
                    position: relative;
                    height: 30px;
                    padding: 0 10px 0 15px;
                    font-size: 18px;
                    font-weight: 700;
                    line-height: 30px;
                    color: #ffffff;
                    background-color: @theme_main;

                    &::after {
                        position: absolute;
                        top: 0;
                        right: -16px;
                        bottom: 0;
                        width: 16px;
                        content: '';
                        // background-image: url(../images/report_review/first_title_bg.png);
                        background-repeat: no-repeat;
                        background-size: contain;
                    }
                }
            }

            .content_text {
                padding: 0 15px;
                margin-top: 10px;
                font-size: 16px;
                line-height: 25px;
                color: #4b4e61;
            }
        }
    }

    // 模块无数据样式
    .noData,
    .no_data {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-size: 12px;
        color: #909399;
    }

    // 表格无数据样式
    .el-table__empty-text {
        .no_data();
    }

    // 左右拖拽式布局的预览整体样式
    .drag_report_preview {
        display: flex;
        flex-direction: column;
        // height: 100%;
        height: 100vh;

        .check_btn {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            box-shadow: 0 3px 15px 0 rgba(229, 34, 37, 0.35);
        }

        .check_back_btn {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            box-shadow: 0 3px 15px 0 rgba(134, 141, 217, 0.35);
        }

        .report_pdf_preview {
            box-shadow: 0 0 24px 0 #e6e8eb;
        }

        .right_content {
            min-width: 300px;
            background-color: #ffffff;

            &_header {
                padding: 0 15px;
                margin: 15px 0;
                font-size: 18px;
                font-weight: 700;
                color: @font_color;

                .text {
                    position: relative;
                    display: inline-block;

                    &::after {
                        position: absolute;
                        right: 0;
                        bottom: -2px;
                        left: 0;
                        height: 8px;
                        content: '';
                        background-image: linear-gradient(to right, @theme_main, #ffffff);
                        border-radius: 4px;
                    }
                }
            }

            &_body {
                padding: 0 15px;
                margin-bottom: 15px;
            }
        }

        .pdf_logo {
            padding: 32px 0 80px;
            text-align: center;
            background-color: #f5f6fa;

            img {
                width: 230px;
            }
        }
    }

    // 提示使用Chrome浏览器访问
    .google_chrome_notification {
        width: 285px;

        .tip_text {
            color: @font_color_04;

            a:first-child {
                color: @font_link;
            }
        }
    }

    .el-button [class*='el-icon-'] + span {
        margin-left: 0;
    }

    .el-scrollbar .el-scrollbar__wrap {
        overflow-x: auto;
    }

    // 富文本编辑的表格 样式被重置none了，要恢复
    .fof_report_rich_text {
        table td,
        table th {
            border: 1px solid #bfbfbf;
        }
    }
}

.blank-view-box {
    // toast位置下移
    .is-message-box {
        top: 0;
        z-index: 99999; // 信息提示z-index权重最高
    }
}

.line-chart-tooltips {
    .tooltips-item {
        display: flex;
        align-items: center;
        color: #333333;

        .tips-box {
            display: flex;
            align-items: center;

            .tips-icon {
                display: inline-block;
                margin-right: 5px;

                &.icon-line {
                    width: 10px;
                    height: 1px;
                }
            }
        }
    }
}
