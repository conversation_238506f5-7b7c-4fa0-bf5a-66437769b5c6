import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { performanceTargetQueryParam } from './type/apiReqType.js'

/**
 * @description:查询接口
 * @return {*}
 */
export const performanceTargetQuery = (params: performanceTargetQueryParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/performancetarget/performancetargetquery',
            method: 'post',
            data: params
        })
    )
}
/**
 * 考核目标查询页面职级选项的初始化
 */
export const performanceTargetInitConsRank = (): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/performancetarget/performancetargetinitconsrank',
            method: 'get',
            data: null
        })
    )
}
/**
 * 考核目标查询页面的初始化
 */
export const performanceTargetInitConsRankConfig = (): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/performancetarget/performancetargetinitconsrankconfig',
            method: 'get',
            data: null
        })
    )
}
