/*
 * @Description: FOF产品费率配置API响应类型定义
 * @Author: hongdong.xie
 * @Date: 2025-06-03 17:19:56
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-03 17:19:56
 * @FilePath: /ds-report-web/src/api/project/report/finance/fofProductFeeRateConfig/type/apiResType.d.ts
 */

/**
 * FOF产品费率配置数据项
 */
export interface FofProductFeeRateConfigItem {
    /** 主键ID */
    id: number
    /** 备案代码 */
    filingCode: string
    /** 产品code（好买内部） */
    productCode: string
    /** 份额代码 */
    shareCode: string
    /** 产品全称 */
    productFullName: string
    /** 仅限FOF，0-否 1-是 */
    fofOnlyFlag: string
    /** 付款方全称 */
    payerFullName: string
    /** 产品经理 */
    productManager: string
    /** 签约日期 */
    signingDate: string
    /** 好买签约主体 */
    howbuySigningEntity: string
    /** 对方联系人 */
    counterpartContact: string
    /** 存续D系数 */
    durationDCoefficient: number
    /** 存续D备注 */
    durationDRemark: string
    /** 费率开始日期 */
    rateStartDate: string
    /** 费率结束日期 */
    rateEndDate: string
    /** 费率配置类型 */
    tieredRateType: string
    /** 费率配置生效日 */
    rateEffectiveType: string
    /** 配置下限 */
    configLowerLimit: number
    /** 配置上限 */
    configUpperLimit: number
    /** 费率生效日期 */
    rateEffectiveStartDate: string
    /** 费率结束日期 */
    rateEffectiveEndDate: string
    /** 认申购费率 */
    subscriptionRate: number
    /** 认申购费备注 */
    subscriptionRemark: string
    /** 管理费率 */
    managementRate: number
    /** 管理费公式 */
    managementFormula: string
    /** 管理费备注 */
    managementRemark: string
    /** 业绩报酬分成费率1 */
    performanceSharingRate1: number
    /** 业绩报酬费率1 */
    performanceRate1: number
    /** 业绩报酬计提基准1 */
    performanceBenchmark1: string
    /** 业绩报酬分成费率2 */
    performanceSharingRate2: number
    /** 业绩报酬费率2 */
    performanceRate2: number
    /** 业绩报酬计提基准2 */
    performanceBenchmark2: string
    /** 业绩报酬分成费率3 */
    performanceSharingRate3: number
    /** 业绩报酬费率3 */
    performanceRate3: number
    /** 业绩报酬计提基准3 */
    performanceBenchmark3: string
    /** 业绩报酬计提类型 */
    performanceAccrualType: string
    /** 业绩报酬计提形式 */
    performanceAccrualForm: string
    /** 固定计提日月份类型 */
    fixedAccrualMonthType: string
    /** 固定计提日日期类型 */
    fixedAccrualDateType: string
    /** 固定计提日计算类型 */
    fixedAccrualCalcType: string
    /** 赎回业绩报酬持有天数规则 */
    redemptionHoldingDaysRule: string
    /** 分红业绩报酬持有天数规则 */
    dividendHoldingDaysRule: string
    /** 业绩报酬公式 */
    performanceFormula: string
    /** 份额锁定期类型 */
    shareLockType: string
    /** 份额锁定期天数 */
    shareLockDays: number
    /** 基金封闭期类型 */
    fundClosedType: string
    /** 基金封闭期天数 */
    fundClosedDays: number
    /** 不计提净值基准 */
    noAccrualNavBenchmark: number
    /** 业绩报酬备注 */
    performanceRemark: string
    /** 赎回费率1 */
    redemptionRate1: number
    /** 赎回费持有天数1 */
    redemptionHoldingDays1: number
    /** 赎回费率2 */
    redemptionRate2: number
    /** 赎回费持有天数2 */
    redemptionHoldingDays2: number
    /** 赎回费率3 */
    redemptionRate3: number
    /** 赎回费持有天数3 */
    redemptionHoldingDays3: number
    /** 赎回费率4 */
    redemptionRate4: number
    /** 赎回费持有天数4 */
    redemptionHoldingDays4: number
    /** 赎回费公式 */
    redemptionFormula: string
    /** 赎回费持有天数计算规则 */
    redemptionHoldingCalcRule: string
    /** 赎回费特例 */
    redemptionSpecialRule: string
    /** 赎回费备注 */
    redemptionRemark: string
    /** 创建者 */
    creator: string
    /** 创建时间 */
    createTime: string
    /** 修改者 */
    modor: string
    /** 修改时间 */
    updateTime: string
    /** 审核者 */
    auditor: string
    /** 审核时间 */
    auditTime: string
    /** 审核状态 */
    auditStatus: string
    /** 审核备注 */
    auditRemark: string
    /** 有效状态 */
    validStatus: string
    /** 费率类型 */
    rateType: string
}

/**
 * FOF产品费率配置查询响应
 */
export interface FofProductFeeRateConfigQueryResponse {
    /** 总记录数 */
    total: number
    /** 当前页码 */
    curPage: number
    /** 每页记录数 */
    size: number
    /** 数据列表 */
    rows: FofProductFeeRateConfigItem[]
}

/**
 * 导出文件响应
 */
export interface ExportToFileVO {
    /** 返回码 */
    returnCode: string
    /** 描述信息 */
    description: string
    /** 文件字节流 */
    fileByte: string
    /** 文件名 */
    name: string
    /** 文件类型 */
    type: string
    /** 错误信息 */
    errorMsg: string
}
