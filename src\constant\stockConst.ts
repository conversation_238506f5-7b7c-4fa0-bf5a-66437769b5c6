/*
 * @Description: 存量分成枚举
 * @Author: ch<PERSON><PERSON>.wu
 * @Date: 2023-08-03 15:44:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-21 14:08:11
 * @FilePath: /crm-web/packages/crm-performance-web/src/constant/stockConst.ts
 *
 */
import { transMapToArray } from '@/utils/dataChange'
/**
 * @description: 产品和机构类型
 * @return {*}
 */
enum IS_FLAG {
    YES = '1',
    NO = '0',
    ALL = ''
}

/**
 * @description: 分成列表枚举集合
 * @return {*}
 */
export enum CONFIG_TYPE_SELECT {
    ALL = '',
    YC = '2',
    YD_XS = '3',
    YD_GZ = '4',
    CFHZ_XS = '5',
    CFHZ_GZ = '6',
    CFHZ_XLHD = '7',
    JG_DKH = '8',
    JG_RL = '9'
}

/**
 * @description: 层级枚举枚举集合
 * @return {*}
 */
export enum CONFIG_LEVEL_SELECT {
    ALL = '',
    AFP = '1',
    FZ = '3',
    QY_FZ = '4',
    QY_Z = '5',
    XSZJ = '6'
}

/**
 * @description: 行级数据 对应的 操作列表
 * @return {*}
 */
export enum ORERATE_LIST_SELECT {
    EDIT = '3',
    DELATE = '4',
    AUDT = '5'
}

/**
 * @description: 分成列表枚举集合
 * @return {*}
 */
export const STOCK_LIST_MAP = {
    // 分成枚举
    CONFIG_TYPE_MAP: new Map([
        ['', '全部'],
        ['2', '育成'],
        ['3', '异动-协商'],
        ['4', '异动-规则'],
        ['5', '重复划转-协商'],
        ['6', '重复划转-规则'],
        ['7', '重复划转-新老划断'],
        ['8', '接管-单客户'],
        ['9', '接管-人力']
    ]),
    // 层级枚举
    CONFIG_LEVEL_MAP: new Map([
        ['', '全部'],
        ['1', '理财师'],
        ['3', '分总'],
        ['4', '区域执行副总'],
        ['5', '区域总'],
        ['6', '销售总监']
    ]),
    // 审核状态
    AYUDT_STATUS_MAP: new Map([
        ['', '全部'],
        ['0', '待审核'],
        ['1', '审核通过'],
        ['2', '审核不通过']
    ]),
    // 操作类型
    OPT_TYPE_MAP: new Map([
        ['0', '新增'],
        ['1', '修改'],
        ['2', '删除'],
        ['3', '审核']
    ]),
    // 在职状态
    JOB_STATUS_MAP: new Map([
        [IS_FLAG.ALL, '全部'],
        [IS_FLAG.YES, '在职'],
        [IS_FLAG.NO, '离职']
    ]),
    // 是否生效
    VALID_FLAG_MAP: new Map([
        [IS_FLAG.ALL, '全部'],
        [IS_FLAG.YES, '是'],
        [IS_FLAG.NO, '否']
    ]),
    VALID_FLAG_MAP_1: new Map([
        [IS_FLAG.YES, '是'],
        [IS_FLAG.NO, '否']
    ]),
    // 客户分配原因
    CUST_REASON_MAP: new Map([
        ['', '全部'],
        ['1', '重复客户'],
        ['2', '日常划转'],
        ['3', '离职划转'],
        ['4', '在途新人入职'],
        ['5', '客户400呼入'],
        ['6', 'Leads分配'],
        ['7', '20w手动划转'],
        ['8', '深潜'],
        ['9', '其他（客服）'],
        ['10', '分享链接'],
        ['11', '同行分配'],
        ['99', '其他']
    ])
}

/**
 * @description: 分成类型
 * @param {*} Array
 * @return {*}
 */
export const CONFIG_TYPE = transMapToArray(STOCK_LIST_MAP.CONFIG_TYPE_MAP)

/**
 * @description: 层级
 * @return {*}
 */
export const CONFIG_LEVEL = transMapToArray(STOCK_LIST_MAP.CONFIG_LEVEL_MAP)

/**
 * @description: 审核状态
 * @return {*}
 */
export const AYUDT_STATUS = transMapToArray(STOCK_LIST_MAP.AYUDT_STATUS_MAP)

/**
 * @description: 在职状态
 * @return {*}
 */
export const JOB_STATUS = transMapToArray(STOCK_LIST_MAP.JOB_STATUS_MAP)

/**
 * @description: 是否生效
 * @return {*}
 */
export const VALID_FLAG = transMapToArray(STOCK_LIST_MAP.VALID_FLAG_MAP)

/**
 * @description: 客户分配原因
 * @return {*}
 */
export const CUST_REASON = transMapToArray(STOCK_LIST_MAP.CUST_REASON_MAP)
