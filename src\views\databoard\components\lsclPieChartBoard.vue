﻿<!-- eslint-disable vue/prop-name-casing -->
<!-- eslint-disable no-undef -->
<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="board-s">
        <div :id="vId" style="width: 100%; height: 100%"></div>
    </div>
</template>

<script>
    import * as echarts from 'echarts'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'LsclPieChartBoard',
        props: {
            // eslint-disable-next-line vue/require-default-prop
            vId: {
                type: String
            },
            title: {
                type: String,
                default: 'title'
            },
            // eslint-disable-next-line vue/prop-name-casing, vue/require-default-prop
            circleData: {
                type: Array
            }
        },
        data() {
            return {}
        },
        watch: {
            circleData: {
                handler(newVal) {
                    if (newVal) {
                        this.$nextTick(() => {
                            this.renderPie<PERSON><PERSON>()
                        })
                    }
                },
                immediate: true,
                deep: true
            }
        },
        mounted() {
            this.renderPie<PERSON><PERSON>()
        },
        methods: {
            renderPie<PERSON>hart() {
                const option = {
                    title: {
                        text: this.title,
                        left: 'left',
                        textStyle: {
                            color: '#ba4949',
                            fontSize: 13
                        }
                    },
                    //下载图片
                    toolbox: {
                        show: true,
                        feature: {
                            saveAsImage: {
                                pixelRatio: 15
                            } //值越大分辨率越高
                        }
                    },
                    tooltip: {
                        trigger: 'item'
                    },
                    legend: {
                        orient: 'vertical',
                        y: 'center',
                        x: '70%',
                        itemWidth: 8,
                        itemHeight: 8,
                        itemGap: 8,
                        selected: false,
                        textStyle: {
                            fontSize: 9
                        }
                    },
                    series: [
                        {
                            name: '零售存量(单位:万)',
                            type: 'pie',
                            color: ['#bf7033', '#f2ba02', '#4874cb'],
                            radius: '70%',
                            data: this.circleData,
                            center: ['35%', '50%'],
                            itemStyle: {
                                borderColor: '#fff',
                                borderWidth: 1
                            },
                            // 设置值域的那指向线
                            labelLine: {
                                normal: {
                                    show: true // show设置线是否显示，默认为true，可选值：true | false
                                }
                            },
                            label: {
                                normal: {
                                    position: 'inner', // 设置标签位置，默认在饼状图外 可选值：'outer' | 'inner（饼状图上）'
                                    formatter: '{d}%'
                                }
                            }
                        }
                    ]
                }
                const pieChart = echarts.init(document.getElementById(this.vId))
                pieChart.setOption(option)
            }
        }
    })
</script>

<style scoped>
    .board-s {
        height: 95%;
        margin: 2px;

        /* border: 1px solid cornflowerblue; */
    }
</style>
