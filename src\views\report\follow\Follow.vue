<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 分配投顾 -->
                <label-item label="分配投顾">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <!-- 分配日期 -->
                <label-item :label="allocationTime.label">
                    <date-range
                        v-model="queryForm.allocationTime"
                        show-format="YYYYMMDD"
                        :placeholder="allocationTime.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 客户类型 -->
                <label-item :label="custType.label">
                    <crm-select
                        v-model="queryForm.custType"
                        :placeholder="custType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        multiple
                        collapse-tags
                        :option-list="custType.selectList"
                        :style="{ width: '220px' }"
                    />
                </label-item>
                <!-- 分配日客户状态 -->
                <label-item :label="allocationCustState.label">
                    <crm-select
                        v-model="queryForm.allocationCustState"
                        :placeholder="allocationCustState.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        multiple
                        collapse-tags
                        :option-list="allocationCustState.selectList"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <!-- 跟进阶段 -->
                <label-item :label="followStage.label">
                    <crm-select
                        v-model="queryForm.followStage"
                        :placeholder="followStage.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        multiple
                        collapse-tags
                        :option-list="followStage.selectList"
                        :style="{ width: '160px' }"
                    />
                </label-item>
                <!-- 继续跟进 -->
                <label-item :label="followFlag.label">
                    <crm-select
                        v-model="queryForm.followFlag"
                        :placeholder="followFlag.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        multiple
                        collapse-tags
                        :option-list="followFlag.selectList"
                        :style="{ width: '160px' }"
                    />
                </label-item>
                <!-- 开始时间 -->
                <label-item :label="startTime.label">
                    <date-range
                        v-model="queryForm.startTime"
                        show-format="YYYYMMDD"
                        :placeholder="startTime.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 结束时间 -->
                <label-item :label="endTime.label">
                    <date-range
                        v-model="queryForm.endTime"
                        show-format="YYYYMMDD"
                        :placeholder="endTime.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 预计结果 -->
                <label-item :label="forecastResult.label">
                    <crm-select
                        v-model="queryForm.forecastResult"
                        :placeholder="forecastResult.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        multiple
                        collapse-tags
                        :option-list="forecastResult.selectList"
                        :style="{ width: '190px' }"
                    />
                </label-item>
                <!-- 最终结果 -->
                <label-item :label="finalResult.label">
                    <crm-select
                        v-model="queryForm.finalResult"
                        :placeholder="finalResult.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        multiple
                        collapse-tags
                        :option-list="finalResult.selectList"
                        :style="{ width: '190px' }"
                    />
                </label-item>
                <!-- 投顾客户号 -->
                <label-item :label="consCustNo.label">
                    <crm-input
                        v-model="queryForm.consCustNo"
                        :placeholder="consCustNo.placeholder"
                    />
                </label-item>
            </template>

            <template #operationLeft>
                <Text type="info" size="small" style="padding-left: 10px; font-size: 14px">
                    最后更新时间：
                    <span class="last-update-time">{{ lastUpdateTimeView }}</span>
                    , 统计截止时间:
                    <span class="last-update-time">{{ dataDeadlineView }}</span>
                </Text>
            </template>

            <template #operationBtns>
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="exportHandle"
                    >导出</crm-button
                >
                <crm-button
                    v-if="batchEditShow"
                    size="small"
                    :radius="true"
                    :icon="Edit"
                    plain
                    @click="handleBatchEdit"
                    >批量编辑</crm-button
                >
                <crm-button
                    size="small"
                    :radius="true"
                    :icon="InfoFilled"
                    plain
                    @click="handleExplain"
                    >说明</crm-button
                >
            </template>

            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="followTableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="true"
                    :no-index="false"
                    :no-select="true"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    :pops="{ selectFixed: 'left' }"
                    :cell-style="handleCellStyle"
                    operation-width="100"
                    @selectionChange="handleSelectionChange"
                >
                    <!-- 添加操作列 -->
                    <template #operation="{ scope }">
                        <el-button
                            v-if="editShow"
                            type="text"
                            size="small"
                            @click="handleEdit(scope.row)"
                            >编辑</el-button
                        >
                    </template>
                </base-table>
            </template>

            <template #tableContentBottom>
                <pagination
                    :page="pageObj"
                    :total="pageObj.total"
                    :page-sizes="[100, 200, 500, 1000, 2000]"
                    @change="handleCurrentChange"
                />
            </template>
        </table-wrapper>

        <!-- 说明弹窗 -->
        <ExplainFollow v-if="explainDialogVisible" v-model="explainDialogVisible"></ExplainFollow>

        <!-- 编辑，批量编辑弹窗 -->
        <BatchEditDialog
            v-model="batchEditDialogVisible"
            :visible-cus="batchEditDialogVisible"
            :trans-data="transData"
            @refresh="queryList"
        >
        </BatchEditDialog>
    </div>
</template>

<script lang="ts" setup>
    import { ref, reactive, onMounted, nextTick } from 'vue'
    import { storeToRefs } from 'pinia'
    import { Download, Edit, InfoFilled } from '@element-plus/icons-vue'
    import { fetchRes } from '@/utils'
    import { ElMessage } from 'element-plus'
    import { dataList } from './data/labelData'
    import { followTableColumn } from './data/tableData'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import {
        queryFollowStatistic,
        exportFollowStatistic,
        followStatisticInit
    } from '@/api/project/report/follow/follow'
    import { getMenuPermission } from '@/api/project/common/common'
    import type { SortOrderCumstom } from '@/type/index'
    import type {
        QueryFollowStatisticResponse,
        ExportFollowStatisticResponse
    } from '@/api/project/report/follow/type/response'
    import type {
        QueryFollowStatisticRequest,
        ExportFollowStatisticRequest
    } from '@/api/project/report/follow/type/request'
    import ExplainFollow from './components/ExplainFollow.vue'
    import BatchEditDialog from './components/BatchEditDialog.vue'

    const {
        custType,
        consCustNo,
        allocationCustState,
        followStage,
        followFlag,
        allocationTime,
        startTime,
        endTime,
        finalResult,
        forecastResult
    } = dataList

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const listLoading = ref<boolean>(false)
    const exportShow = ref<boolean>(false)
    const batchEditShow = ref<boolean>(false)
    const editShow = ref<boolean>(false)
    const consStatus = ref<string>('0') //包含离职人员
    const module = ref<string>('020154') // 模块编码

    // 弹窗控制
    const explainDialogVisible = ref<boolean>(false)
    const batchEditDialogVisible = ref<boolean>(false)
    const editDialogVisible = ref<boolean>(false)

    // 当前编辑的行数据
    const currentRow = ref<any>(null)

    // 最后更新时间
    const lastUpdateTimeView = ref<string>('')

    // 统计截止时间
    const dataDeadlineView = ref<string>('')

    // 批量编辑数据
    let selectIds: string[] = []

    const handleSelectionChange = (val: any) => {
        selectIds = val.map((item: any) => item.id)
    }

    /**
     * @description: 查询条件列表
     */
    class QueryForm {
        consCustNo = ''
        custType = []
        allocationCustState = []
        followStage = []
        followFlag = []
        allocationTime = {
            startDate: '',
            endDate: ''
        }
        startTime = {
            startDate: '',
            endDate: ''
        }
        endTime = {
            startDate: '',
            endDate: ''
        }
        finalResult = []
        forecastResult = []
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        sort = ''
        order = 'descending'
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: table表格数据
     */
    const tableData = ref<object[]>([])

    /**
     * @description: 分页数据
     */
    class PageObj {
        page = 1
        size = 100
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    // 查询列表
    const queryList = async () => {
        listLoading.value = true
        const params: QueryFollowStatisticRequest = {
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            custType: queryForm.custType,
            allocationCustState: queryForm.allocationCustState,
            followStage: queryForm.followStage,
            followFlag: queryForm.followFlag,
            consCustNo: queryForm.consCustNo,
            allocationDtStart: queryForm.allocationTime.startDate,
            allocationDtEnd: queryForm.allocationTime.endDate,
            startDtStart: queryForm.startTime.startDate,
            startDtEnd: queryForm.startTime.endDate,
            endDtStart: queryForm.endTime.startDate,
            endDtEnd: queryForm.endTime.endDate,
            forecastResult: queryForm.forecastResult,
            finalResult: queryForm.finalResult,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }

        fetchRes(queryFollowStatistic(params), {
            successCB: (resObj: QueryFollowStatisticResponse) => {
                listLoading.value = false
                const { rows, total } = resObj
                tableData.value = rows
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 分页联动
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 初始化查询
     */
    const handleLoading = () => {
        queryList()
    }

    /**
     * @description: 导出
     */
    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 导出请求
     */
    const exportList = async () => {
        const params: ExportFollowStatisticRequest = {
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            custType: queryForm.custType,
            consCustNo: queryForm.consCustNo,
            followFlag: queryForm.followFlag,
            allocationCustState: queryForm.allocationCustState,
            followStage: queryForm.followStage,
            allocationDtStart: queryForm.allocationTime.startDate,
            allocationDtEnd: queryForm.allocationTime.endDate,
            startDtStart: queryForm.startTime.startDate,
            startDtEnd: queryForm.startTime.endDate,
            endDtStart: queryForm.endTime.startDate,
            endDtEnd: queryForm.endTime.endDate,
            forecastResult: queryForm.forecastResult,
            finalResult: queryForm.finalResult
        }
        const res = (await exportFollowStatistic(params)) as ExportFollowStatisticResponse
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }

    /**
     * @description: 默认值初始化
     */
    watch(
        [orgCodeDefault, consCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.orgvalue.orgCode = orgCodeDefault.value
                queryForm.orgvalue.consCode = consCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    // 处理单元格样式
    const handleCellStyle = ({ row, column }: { row: any; column: any }) => {
        // 需要检查结果变色的字段列表
        const resultFields = [
            'phoneVisit.result',
            'wechatBind.result',
            'wechatComm.result',
            'faceVisit.result',
            'offlineActivity.result',
            'ips.result',
            'trade.result'
        ]

        // 如果是结果字段且需要变色
        if (resultFields.includes(column.property)) {
            const [parentKey, childKey] = column.property.split('.')
            if (row[parentKey]?.resultChangeColor) {
                return {
                    backgroundColor: '#6293bb',
                    color: '#ffffff'
                }
            }
        }
        return {}
    }

    // 按钮处理函数
    const handleExplain = () => {
        explainDialogVisible.value = true
    }

    const transData = ref<any>({
        ids: [],
        type: '1',
        id: '',
        title: '批量编辑'
    })

    const handleBatchEdit = () => {
        if (selectIds.length === 0) {
            ElMessage({
                message: '请选择要编辑的数据',
                type: 'warning'
            })
            return
        }
        transData.value.ids = selectIds
        transData.value.id = ''
        transData.value.title = '批量编辑'
        transData.value.type = '1'
        batchEditDialogVisible.value = true
    }

    const handleEdit = (row: any) => {
        transData.value.id = row.id
        transData.value.title = '编辑'
        transData.value.type = '2'
        transData.value.ids = []
        batchEditDialogVisible.value = true
    }

    // 初始化权限
    const initPermission = async () => {
        const params = {
            menuCode: module.value
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (item.operateCode === '1' && item.display === '1') {
                            exportShow.value = true
                        }
                        if (item.operateCode === '2' && item.display === '1') {
                            editShow.value = true
                        }
                        if (item.operateCode === '3' && item.display === '1') {
                            batchEditShow.value = true
                        }
                    })
                }
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    // 初始化数据
    const initData = async () => {
        const params = {}
        fetchRes(followStatisticInit(), {
            successCB: (resObj: any) => {
                const { lastUpdateTime, dataDeadline } = resObj
                lastUpdateTimeView.value = lastUpdateTime
                dataDeadlineView.value = dataDeadline
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    onMounted(() => {
        initPermission()
        initData()
        fetchConsOrgList('', module.value, '1')
    })
</script>

<style lang="less" scoped>
    :deep(.crm-base-table.el-table) {
        // 覆盖表头样式
        .el-table__header-wrapper {
            .el-table__header {
                thead {
                    tr {
                        th {
                            height: 28px; // 设置单元格高度
                            padding: 4px 0; // 减小上下内边距
                            // 添加表头边框
                            border-right: 1px solid #eaeef1 !important;
                            border-bottom: 1px solid #eaeef1 !important;

                            // 处理最后一列右边框
                            &:last-child {
                                border-right: none !important;
                            }
                        }
                    }
                }
            }
        }

        // 调整表格内容的高度
        .el-table__body-wrapper {
            .el-table__body {
                td {
                    padding: 4px 0; // 减小上下内边距

                    .cell {
                        height: 20px; // 设置单元格高度
                        line-height: 20px; // 行高与高度一致
                    }
                }
            }
        }

        // 添加结果单元格的hover样式
        .el-table__body {
            tr:hover {
                td {
                    &.highlight-result {
                        color: #ffffff !important;
                        background-color: #6293bb !important;
                    }
                }
            }
        }
    }

    .last-update-time {
        margin-left: 5px;
    }
</style>
