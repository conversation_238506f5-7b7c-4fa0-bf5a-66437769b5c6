/*
 * @Description: 数据转换初始化页面
 * @Author: chaohui.wu
 * @Date: 2024-06-19 15:36:18
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-08-05 18:07:13
 * @FilePath: /crm-asset-web/vSnippet/index.js
 *
 */
const path = require('path')
const fs = require('fs-extra')
const { transListData } = require('./transHtmlToStr.js')
const custDir = path.resolve(__dirname, '../.vscode')
// 获取列表数据并在当前文件夹下生成文件
// 命名规则：
// vue3组基础模版 v3-ts-xxx
// 基础组件：hb-xxx-xxx  hb[howbuy-baseModule]
// 模版组件：ht-xxx-xxx ht[howbuy-template]
// 方法类: hu-xxx-xxx hu[howbuy-utils]
// 样式类: hs-xxx-xxx hs[howbuy-style]
// 埋点类：he-xxx-xxx he[howbuy-event]
// 遍历数据并创建文件
transListData.map(item => {
    const { key, content } = item || {}
    const filePath = `${custDir}/${key}`
    fs.writeFileSync(filePath, `${content}`, { encoding: 'utf8', flag: 'w', mode: 438 }, error => {
        console.log('写入失败:' + error)
    })
})
