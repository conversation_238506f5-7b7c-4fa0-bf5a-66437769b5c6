<!--
 * @Description: 添加产品弹框
 * @Author: chaohui.wu
 * @Date: 2023-03-24 21:07:35
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-27 19:14:16
 * @FilePath: /crm-template/src/views/components/dialogCommon/adjustAmt/adjustAmtIndex.vue
 * @ 当前未解耦，有时间解耦后可提取到dialogCommomn中
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        height="500px"
        :border="true"
        :title="transData?.title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <div>
            <!--手工存续D的弹框 -->
            <el-form
                v-if="isAssetHandConfAdd"
                ref="ruleFormRef"
                :model="formList"
                label-width="100px"
                :rules="rules"
                status-icon
                class="elForm"
            >
                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="busitype" label="业务类型" style="margin-left: 40%">
                            <crm-select
                                v-model="formList.busitype"
                                :placeholder="busitype.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="busitype.selectList"
                                :style="{ width: '150px' }"
                                @change="selectTrigger(formList.busitype)"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="conslevel" label="层级" style="margin-left: 18%">
                            <crm-select
                                v-model="formList.conslevel"
                                :placeholder="conslevel.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="conslevel.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item
                            prop="formerConstObj"
                            label="管理层/投顾"
                            style="margin-top: 25px; margin-bottom: 50px; margin-left: 16.6%"
                        >
                            <ReleatedSelect
                                v-model="formList.formerConstObj"
                                :organization-list="formerOrganizationList"
                                :cons-list-default="[]"
                                :default-org-code="formerOrgCode"
                                :default-cons-code="formerConsCode"
                                :cons-status="consStatus"
                                :module="module"
                            ></ReleatedSelect>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="custInfo" label="客户" style="margin-left: 40%">
                            <crm-select
                                v-model="formList.custInfo"
                                label-format="custInfo"
                                value-format="custInfo"
                                filterable
                                clearable
                                remote
                                remote-show-suffix
                                :remote-method="getAllCustINFO"
                                :option-list="custList"
                                :style="{ width: '150px' }"
                                @change="selectTrigger2"
                            />
                        </el-form-item>
                        <el-form-item prop="firstSource" label="第一来源" style="margin-left: 40%">
                            <crm-select
                                v-model="formList.firstSource"
                                :placeholder="firstSource.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="firstSource.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>

                        <el-form-item
                            prop="foldcoeff"
                            label="客户折算系数"
                            label-width="auto"
                            style="margin-left: 39%"
                        >
                            <crm-input
                                v-model="formList.foldcoeff"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>

                        <el-form-item prop="managecoeff" label="管理系数" style="margin-left: 40%">
                            <crm-input
                                v-model="formList.managecoeff"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="assetcalcstartdt"
                            label-width="auto"
                            label="存D计算起始月"
                            style="margin-left: 36%"
                        >
                            <el-date-picker
                                v-model="formList.assetcalcstartdt"
                                class="popperClass"
                                type="month"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="fundname" label="产品名称" style="margin-left: 18%">
                            <crm-input
                                v-model="formList.fundname"
                                :clearable="true"
                                :style="{ width: '150px' }"
                                :disabled="isdisabled"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item prop="scale" label="规模(万)" style="margin-left: 18%">
                            <crm-input
                                v-model="formList.scale"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="stockfeed"
                            label-width="auto"
                            label="产品存续D系数"
                            style="margin-left: 15%"
                        >
                            <crm-input
                                v-model="formList.stockfeed"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item prop="otherrate" label="调整比例" style="margin-left: 18%">
                            <crm-input
                                v-model="formList.otherrate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="assetcalcenddt"
                            label-width="auto"
                            label="存D计算结束月"
                            style="margin-left: 15%"
                        >
                            <el-date-picker
                                v-model="formList.assetcalcenddt"
                                class="popperClass"
                                type="month"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item
                            prop="remark"
                            label="备注"
                            style="margin-top: 25px; margin-bottom: 50px; margin-left: 16.6%"
                        >
                            <crm-input
                                v-model="formList.remark"
                                :clearable="true"
                                :style="{ width: '335px', height: '25px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <span
                    >注： <br />
                    1、必填项:「业务类型」~「存D计算结束月」 <br />
                    2、录入数据前
                    <span style="font-weight: bold; color: red">请复核</span> 业务数据是否已录入
                </span>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 55%">
                            <el-button type="primary" @click="submitForm(ruleFormRef)">
                                提交
                            </el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item style="margin-left: 10%">
                            <el-button type="primary" @click="resetForm(ruleFormRef)"
                                >重置</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { fetchRes } from '@/utils/index'
    import { dataList } from './data/labelData'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import {
        performanceAssetHandConfInsert,
        getAllCust,
        getDefaultInfo,
        queryEdit
    } from '@/api/project/performanage/performanceAssetHandConf/performanceAssetHandConf'
    const { busitype, firstSource, conslevel } = dataList
    const loadingFlag = ref<boolean>(false)
    const consStatus = ref<string>('1') //不包含离职人员
    const module = ref<string>('B140119')
    class FormList {
        busitype = ''
        conslevel = '0'
        custInfo = ''
        formerConstObj = {
            orgCode: '',
            consCode: ''
        }
        fundname = ''
        scale = ''
        stockfeed = ''
        firstSource = ''
        foldcoeff = ''
        managecoeff = ''
        otherrate = '1'
        assetcalcstartdt = ''
        assetcalcenddt = ''
        remark = ''
    }

    const formList = reactive<any>(new FormList())

    const ruleFormRef = ref<FormInstance>()

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            formerConsCode?: string
            formerOrgCode?: string
            formerConsultList?: any[]
            formerOrganizationList?: any[]
            consultList?: any[]
            transData?: {
                title: string
                type: string
                id: string
            }
            isAssetHandConfAdd?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    title: '新增',
                    type: 'add',
                    id: ''
                }
            },
            formerConsCode: '',
            formerOrgCode: '',
            formerConsultList: () => [],
            consultList: () => [],
            formerOrganizationList: () => [],
            isAssetHandConfAdd: false
        }
    )
    const isdisabled = ref<boolean>(false)
    // 默认值显示
    const selectTrigger = (val: any): void => {
        console.log('selectTrigger' + val)
        if (val === '0') {
            formList.fundname = '家办'
            isdisabled.value = true
        }
        if (val === '1') {
            formList.fundname = '综拓'
            isdisabled.value = true
        }
        if (val !== '0' && val !== '1') {
            formList.fundname = ''
            isdisabled.value = false
        }
    }
    // 默认值显示
    const selectTrigger2 = async () => {
        const { consCode: formerConsCode, orgCode: formerOrgCode } = formList.formerConstObj || {}
        if (formList.custInfo !== '') {
            const requestParams = {
                conslevel: formList.conslevel,
                conscode: formerConsCode,
                custInfo: formList.custInfo
            }
            const res: any = await getDefaultInfo(requestParams)
            const { data } = res
            formList.firstSource = data.firstSource
            formList.foldcoeff = data.foldcoeff
            formList.managecoeff = data.managecoeff
        }
    }

    // 弹窗标题配置
    const title = computed(() => {
        if (props.isAssetHandConfAdd) {
            return '新增'
        }
    })

    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })
    const custList = ref<object[]>()
    const getAllCustINFO = async (query: string) => {
        const custVO = {
            queryStr: query,
            page: 1,
            rows: 10
        }
        if (query !== '') {
            fetchRes(getAllCust(custVO), {
                successCB: (res: any) => {
                    const { rows } = res
                    custList.value = rows
                },
                errorCB: (res: any) => {
                    dialogVisible.value = false
                    ElMessage({
                        type: 'error',
                        message: res?.description || '请求失败'
                    })
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        }
    }

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()
    const rules = reactive<FormRules<FormList>>({
        busitype: [{ required: true, message: '请选择业务类型', trigger: 'blur' }],
        conslevel: [{ required: true, message: '请选择层级', trigger: 'blur' }],
        custInfo: [{ required: true, message: '请输入客户信息', trigger: 'blur' }],
        formerConstObj: [{ required: true, message: '请选择管理层/投顾', trigger: 'blur' }],
        scale: [
            { required: true, message: '请输入规模', trigger: 'blur' },
            { pattern: /^-?\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        stockfeed: [
            { required: true, message: '请输入产品存续D系数', trigger: 'blur' },
            { pattern: /^[0-9]+(\.[0-9]+)?$/, message: '请输入正确的数字格式', trigger: 'blur' }
        ],
        foldcoeff: [
            { required: true, message: '请输入客户折算系数', trigger: 'blur' },
            { pattern: /^[0-9]+(\.[0-9]+)?$/, message: '请输入正确的数字格式', trigger: 'blur' }
        ],
        managecoeff: [
            { required: true, message: '请输入管理系数', trigger: 'blur' },
            { pattern: /^[0-9]+(\.[0-9]+)?$/, message: '请输入正确的数字格式', trigger: 'blur' }
        ],
        otherrate: [
            { required: true, message: '请输入调整比例', trigger: 'blur' },
            { pattern: /^[0-9]+(\.[0-9]+)?$/, message: '请输入正确的数字格式', trigger: 'blur' }
        ],
        firstSource: [{ required: true, message: '请选择第一来源', trigger: 'blur' }],
        fundname: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        assetcalcstartdt: [{ required: true, message: '请选择存D计算起始月', trigger: 'blur' }],
        assetcalcenddt: [{ required: true, message: '请选择存D计算结束月', trigger: 'blur' }]
    })

    //提交方法
    const submitForm = async (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        await formEl.validate((valid, fields) => {
            if (valid) {
                console.log('submit!', fields)
                performanceAssetHandConfSubmit()
            } else {
                console.log('error submit!', fields)
            }
        })
    }
    //重置方法
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        formList.formerConstObj.consCode = props.formerConsCode
        formList.formerConstObj.orgCode = props.formerOrgCode
        if (isdisabled.value) {
            isdisabled.value = false
        }
        formEl.resetFields()
    }

    /**
     *手工存续D新增方法
     * @param params
     */

    const performanceAssetHandConfSubmit = async () => {
        const { consCode: formerConsCode, orgCode: formerOrgCode } = formList.formerConstObj || {}
        if (formerConsCode === '') {
            ElMessage({
                message: '必须选择一个投顾',
                type: 'warning',
                duration: 2000
            })
            return
        }
        const startDate = new Date(formList.assetcalcstartdt)
        const endDate = new Date(formList.assetcalcenddt)
        if (startDate.getTime() - endDate.getTime() > 0) {
            ElMessage({
                message: '选择的开始日期大于结束日期',
                type: 'warning',
                duration: 2000
            })
            return
        }
        const requestParams = {
            busitype: formList.busitype,
            conslevel: formList.conslevel,
            conscode: formerConsCode,
            fundname: formList.fundname,
            scale: formList.scale,
            stockfeed: formList.stockfeed,
            firstSource: formList.firstSource,
            foldcoeff: formList.foldcoeff,
            managecoeff: formList.managecoeff,
            otherrate: formList.otherrate,
            assetcalcstartdt: formList.assetcalcstartdt,
            assetcalcenddt: formList.assetcalcenddt,
            remark: formList.remark,
            custInfo: formList.custInfo,
            orgCode: formerOrgCode
        }
        const res: any = await performanceAssetHandConfInsert(requestParams)
        if (res.code === 'C030000') {
            ElMessage({
                type: 'success',
                message: res.description
            })
            loadingFlag.value = false
            // dialogVisible.value = false
            return emit('callback')
        }
        if (res.code !== 'C030000') {
            ElMessage({
                type: 'error',
                message: res?.description || '请求失败'
            })
        }
    }
    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        dialogVisible.value = false
    }
    /**
     * @description: 编辑初始化
     * @return {*}
     */
    const fetchList = () => {
        // 初始化
        fetchRes(queryEdit({ id: props.transData.id }), {
            successCB: (res: any) => {
                // 编辑初始化
                const {
                    busitype,
                    conslevel,
                    conscode,
                    fundname,
                    scale,
                    stockfeed,
                    firstSource,
                    foldcoeff,
                    managecoeff,
                    otherrate,
                    assetcalcstartdt,
                    assetcalcenddt,
                    remark,
                    custInfo
                } = res || {}
                formList.busitype = busitype
                formList.conslevel = conslevel
                formList.conscode = conscode
                formList.fundname = fundname
                formList.scale = scale
                formList.stockfeed = stockfeed
                formList.firstSource = firstSource
                formList.foldcoeff = foldcoeff
                formList.managecoeff = managecoeff
                formList.otherrate = otherrate
                formList.assetcalcstartdt = assetcalcstartdt
                formList.assetcalcenddt = assetcalcenddt
                formList.remark = remark
                formList.custInfo = custInfo
            },
            errorCB: () => {
                formList.value = new FormList()
            },
            catchCB: () => {
                formList.value = new FormList()
            },
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [() => props?.formerConsCode, () => props?.formerOrgCode],
        (newVal, oldVal) => {
            const { formerOrgCode, formerConsCode } = props || {}

            if (formerOrgCode || formerConsCode) {
                formList.formerConstObj.orgCode = formerOrgCode
                formList.formerConstObj.consCode = formerConsCode
            }
        },
        {
            immediate: true
        }
    )
    onBeforeMount(() => {
        formList.formerConstObj.consCode = props.formerConsCode
        formList.formerConstObj.orgCode = props.formerOrgCode
    })
</script>

<style lang="less" scoped>
    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
