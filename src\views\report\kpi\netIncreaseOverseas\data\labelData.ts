/*
 * @Description: 定义搜索的label列表
 * @Author: ji<PERSON><PERSON><PERSON>.yang
 * @Date: 2024-04-01 10:40:30
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-04-01 10:40:30
 * @FilePath: /src/views/report/kpi/assetRepDownload/data/labelData.ts
 *
 */
export const dataList = {
    busiType: {
        label: '业务类型',
        placeholder: '请选择业务类型',
        selectList: [
            {
                key: '私募',
                label: '私募'
            },
            {
                key: '公募',
                label: '公募'
            },
            {
                key: '创新',
                label: '创新'
            }
        ]
    },
    // 0-离职，1-在职
    workState: {
        label: '在职状态',
        placeholder: '请选择在职状态',
        selectList: [
            {
                key: '0',
                label: '离职'
            },
            {
                key: '1',
                label: '在职'
            }
        ]
    },
    // 是否参与考核
    isKpi: {
        label: '是否参与考核',
        placeholder: '请选择是否参与考核',
        selectList: [
            {
                key: '是',
                label: '是'
            },
            {
                key: '否',
                label: '否'
            }
        ]
    },
    // 考核项
    kpiItem: {
        label: '考核项',
        placeholder: '请选择考核项',
        selectList: [
            {
                key: '海外',
                label: '海外'
            },
            {
                key: '非A资产',
                label: '非A'
            }
        ]
    },
    userId: {
        label: '员工编码',
        placeholder: '请输入员工编码'
    },
    orgData: {
        label: '投顾管理层',
        placeholder: '请选择所在组织'
    },
    ackDt: {
        label: '交易日期',
        placeholder: ['开始日期', '结束日期']
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
