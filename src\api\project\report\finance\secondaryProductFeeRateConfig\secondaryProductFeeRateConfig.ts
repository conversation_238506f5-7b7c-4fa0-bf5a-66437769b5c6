/*
 * @Description: 二级产品费率配置 API接口
 * @Author: hongdong.xie
 * @Date: 2025-06-05 18:53:18
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-05 18:53:18
 * @FilePath: /ds-report-web/src/api/project/report/finance/secondaryProductFeeRateConfig/secondaryProductFeeRateConfig.ts
 */

import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '../../../../mock.js'
import {
    SecondaryProductFeeRateConfigQueryParam,
    SecondaryProductFeeRateConfigAddParam,
    SecondaryProductFeeRateConfigUpdateParam,
    SecondaryProductFeeRateConfigDeleteParam,
    SecondaryProductFeeRateConfigAuditParam,
    SecondaryProductFeeRateConfigExportParam
} from './type/apiReqType.js'

/**
 * @description: 二级产品费率配置查询接口
 * @param {SecondaryProductFeeRateConfigQueryParam} params 查询参数
 * @return {*}
 */
export const secondaryProductFeeRateConfigQuery = (params: SecondaryProductFeeRateConfigQueryParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/secondary/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 二级产品费率配置导出接口
 * @param {SecondaryProductFeeRateConfigExportParam} params 导出参数
 * @return {*}
 */
export const secondaryProductFeeRateConfigExport = (params: SecondaryProductFeeRateConfigExportParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/secondary/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 二级产品费率配置新增接口
 * @param {SecondaryProductFeeRateConfigAddParam} params 新增参数
 * @return {*}
 */
export const secondaryProductFeeRateConfigAdd = (params: SecondaryProductFeeRateConfigAddParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/secondary/add',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 二级产品费率配置修改接口
 * @param {SecondaryProductFeeRateConfigUpdateParam} params 修改参数
 * @return {*}
 */
export const secondaryProductFeeRateConfigUpdate = (params: SecondaryProductFeeRateConfigUpdateParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/secondary/update',
            method: 'post',
            data: params,
            // Mock数据配置
            mockData: {
                code: '200',
                description: '修改成功',
                data: null
            }
        })
    )
}

/**
 * @description: 二级产品费率配置删除接口
 * @param {SecondaryProductFeeRateConfigDeleteParam} params 删除参数
 * @return {*}
 */
export const secondaryProductFeeRateConfigDelete = (params: SecondaryProductFeeRateConfigDeleteParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/secondary/delete',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 二级产品费率配置审核接口
 * @param {SecondaryProductFeeRateConfigAuditParam} params 审核参数
 * @return {*}
 */
export const secondaryProductFeeRateConfigAudit = (params: SecondaryProductFeeRateConfigAuditParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/secondary/audit',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 获取二级产品费率配置操作按钮权限
 * @return {*}
 */
export const getSecondaryProductFeeRateConfigAuth = () => {
    return axiosRequest(
        paramsMerge({
            url: '/feerateconfig/secondary/getAuth',
            method: 'post'
        })
    )
} 