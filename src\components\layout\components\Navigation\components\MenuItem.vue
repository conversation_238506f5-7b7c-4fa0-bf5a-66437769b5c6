<!-- <script setup>
    import { computed } from 'vue'
    // 菜单icon
    const iconClass = computed(() => {
        const { menuInfo, parentInfo } = props
        return menuInfo.icon || parentInfo?.icon || ''
    })
</script> -->
<template>
    <el-sub-menu v-if="menuItem?.children?.length" :index="`${menuItem.id}`">
        <template #title>
            <!-- <el-icon :size="17" color="@font_color_01">
                <component :is="getMenuIcon(menuItem.icon)" />
            </el-icon> -->
            <svg-icons :name="menuItem.icon" :size="15" />
            <span>{{ menuItem.title }}</span>
        </template>
        <template v-for="subItem in menuItem.children" :key="subItem.id">
            <menu-item :menu-info="subItem" :parent-info="menuItem" />
        </template>
    </el-sub-menu>
    <el-menu-item v-else :index="`${menuItem.id}`" @click.capture="handleToPage(menuItem)">
        <template #title>
            <a class="navMenuItem" :href="getPageLink(menuItem)" @click.prevent="handleTagClick">
                <!-- <el-icon :size="17" color="@font_color_01">
                    <component :is="getMenuIcon(menuItem.icon)" />
                </el-icon> -->
                <svg-icons :name="iconClass" :size="15" />
                <el-badge :value="menuItem.num" class="badge-item">
                    <span>{{ menuItem.title }}</span>
                </el-badge>
            </a>
        </template>
    </el-menu-item>
</template>

<script>
    import {
        Tickets,
        Failed,
        TurnOff,
        Message,
        Menu,
        Mouse,
        User,
        SetUp,
        Tools,
        Operation
    } from '@element-plus/icons-vue'
    import { defineComponent } from 'vue'
    import { openUrl } from '@/utils/openUrl'
    export default defineComponent({
        name: 'MenuItem',
        props: {
            menuInfo: {
                type: Object,
                default: () => {
                    return {}
                }
            },
            parentInfo: {
                type: Object,
                default: () => {
                    return {}
                }
            }
        },
        data() {
            return {
                treeList: []
            }
        },
        computed: {
            iconClass() {
                return this.menuInfo?.icon || this.parentInfo?.icon || ''
            }
        },
        watch: {
            menuInfo: {
                handler(data) {
                    this.menuItem = data || {}
                },
                deep: true,
                immediate: true
            }
        },
        methods: {
            // 点击导航跳转到对应的路由
            handleToPage(item) {
                if (item.openNewPageType === '1') {
                    // 新开tag标签展示ams页面
                    // 用name解析出path
                    const { href } = this.$router.resolve({ name: item.name })
                    openUrl({ type: 1, path: href.replace('#/', '/') })
                    return false
                } else if (item.openNewPageType === '2') {
                    // iframe打开外链
                    this.$router.push({
                        name: item.name
                    })
                    return false
                } else if (item.openNewPageType === '3') {
                    // 直接跳转到外链
                    openUrl({ type: 3, path: item.link })
                    return false
                }

                this.openRouterName(item)
                return false
            },

            // 路由方式跳转
            openRouterName(item) {
                let params = {}
                // 动态路由参数 (形式：a=1,b=2）
                if (item.dynamicParams) {
                    try {
                        item.dynamicParams.split(',').forEach(v => {
                            const [key, value] = v.split('=')
                            params[key] = value
                        })
                    } catch (e) {
                        params = {}
                    }
                }
                this.$router.push({
                    name: item.name,
                    params
                })
            },

            // 菜单icon
            getMenuIcon(iconName) {
                const menuIcons = {
                    'el-icon-s-order': Tickets,
                    'el-icon-s-grid': Tickets,
                    'el-icon-document-checked': Tickets,
                    'el-icon-s-cooperation': Tickets,
                    'el-icon-collection': Tickets,
                    'el-icon-document': Tickets,
                    'el-icon-s-marketing': Tickets,
                    'el-icon-s-help': Tickets,
                    'el-icon-s-ticket': Tickets,
                    'el-icon-mouse': Mouse,
                    'el-icon-user-solid': User,
                    'el-icon-turn-off': TurnOff, // 开关控制
                    'el-icon-news': Tickets,
                    'el-icon-menu': Menu, // 菜单
                    'el-icon-edit-outline': Tickets,
                    'el-icon-message': Message, // 消息
                    'el-icon-document-delete': Failed, // 错误日志
                    'el-icon-set-up': SetUp,
                    'el-icon-tools': Tools,
                    'el-icon-operation': Operation
                }

                return iconName && menuIcons[iconName] ? menuIcons[iconName] : Tickets
            },

            // 获取导航条目的路由链接、可用于右键点击在新窗口打开
            getPageLink(item) {
                // openNewPageType === '2' 直接跳转到外链
                return item.openNewPageType === '2' ? item.externalLink : `/#${item.path}`
            },

            // 阻止菜单a标签的href跳转事件，这里的a标签加href只是为了鼠标右键的时候显示在新标签打开的选项
            handleTagClick() {
                return false
            }
        }
    })
</script>
<style lang="less" scoped>
    .svg-icon {
        margin-right: 5px;
    }
</style>
