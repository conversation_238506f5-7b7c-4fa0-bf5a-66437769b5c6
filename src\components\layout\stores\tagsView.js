import { defineStore } from 'pinia'

// 不在tabview上显示的路由名单
const blackList = ['loading', 'login', '404', 'noPermission', 'officePreview', 'filePreview']

export const useVisitedViewStore = defineStore({
    id: 'tagsView',
    state: () => ({
        visitedViews: []
    }),
    actions: {
        // 新增标签
        addVisitedView(view) {
            if (!this.visitedViews.some(v => v.name === view.name)) {
                if (view.meta.title && !blackList.includes(view.name)) {
                    this.visitedViews.push({
                        ...view,
                        title: view.meta.title
                    })
                }
            }
        },

        // 删除标签
        delVisitedView(view) {
            return new Promise(resolve => {
                const visitedViews = this.visitedViews.filter(item => item.path !== view.path)
                this.visitedViews = visitedViews
                resolve({ visitedViews })
            })
        },

        // 清空标签--重登录等情况下
        clearVisitedView(view) {
            this.visitedViews = []
        }

        // 更新标签导航栏
        // updateVisitedView(view) {
        //     const visitedViews = this.visitedViews
        //     const index = visitedViews.findIndex((v) => v.path === view.path)
        //     visitedViews[index] = {
        //         ...visitedViews[index],
        //         ...view
        //     }
        //     this.visitedViews = visitedViews
        //     // for (let v of this.visitedViews) {
        //     //     if (v.path === view.path) {
        //     //         v = Object.assign(v, view)
        //     //         break
        //     //     }
        //     // }
        // }
    }
})
