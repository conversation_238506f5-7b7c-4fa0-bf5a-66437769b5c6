import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { ConsCombinationConfigParam } from './type/apiReqType.js'

/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const ConsCombinationConfigQuery = (params: ConsCombinationConfigParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/reward/conscombinationconfig/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 导出接口
 * @return {*}
 */
export const ConsCombinationConfigExport = (params: ConsCombinationConfigParam) => {
    return axiosRequest(
        paramsMerge({
            timeout: 60000,
            url: '/api/reward/conscombinationconfig/export',
            method: 'post',
            data: params
        })
    )
}
