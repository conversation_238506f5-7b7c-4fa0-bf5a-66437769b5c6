<!--
 * @Description: 统计说明
 * @Author: gang.zou
 * @Date: 2024-04-12 19:46:04
 * @LastEditors: gang.zou
 * @LastEditTime: 2024-04-12 19:46:04
 * @FilePath: /src/views/report/kpi/components/explainNetIncreaseCust.vue
 *  
-->
<template>
    <crm-dialog
        width="824px"
        title="统计说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <template #default>
            <div style="height: 300px">
                <b>此报表用于统计投顾2025年KPI的目标值和结果值。</b><br />
                <span>①KPI数据统计规则请以2025年KPI文件为准</span><br />
                <span>②数据对应的明细数据，可参考各明细数据表</span><br />
                <!-- <span
                    >③资配报告完成率、企微添加率、创新继续率的结果值如显示“-”，代表此投顾：无此项考核要求，同时此项考核结果没有数据</span
                > -->
            </div>
        </template>

        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">确定</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { useVisible } from '@/views/common/scripts/useVisible'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()
    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })
</script>

<style lang="less" scoped>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 8px;
        text-align: left;
        border: 1px solid black;
    }

    th {
        background-color: #f2f2f2;
    }

    .middle-content {
        padding: 10px 0;
    }
</style>
