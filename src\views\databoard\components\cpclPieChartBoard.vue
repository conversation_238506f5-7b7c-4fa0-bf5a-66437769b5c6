﻿<!-- eslint-disable no-undef -->
<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="board-s">
        <div :id="vId" style="float: left; width: 60%; height: 100%"></div>
        <div style="float: right; width: 40%; padding-top: 40px">
            <div>
                <span style="font-size: 12px; font-weight: bold; white-space: nowrap"> 时间 </span>
                &nbsp;&nbsp;&nbsp;
                <span style="font-size: 12px; font-weight: bold; white-space: nowrap">
                    主要产品策略
                </span>
            </div>
            <div>
                <span style="font-size: 12px; white-space: nowrap"> 第一季度 </span>
                &nbsp;&nbsp;&nbsp;
                <!-- <span style="font-size: 12px; white-space: nowrap"> 股票(98%) </span> -->
                <span style="font-size: 12px; white-space: nowrap">
                    {{ firstJdType }}({{ firstJdTypePer }})
                </span>
            </div>
            <div>
                <span style="font-size: 12px; white-space: nowrap"> 第二季度 </span>
                &nbsp;&nbsp;&nbsp;
                <!-- <span style="font-size: 12px; white-space: nowrap"> CTA(80%) </span> -->
                <span style="font-size: 12px; white-space: nowrap">
                    {{ secondJdType }}({{ secondJdTypePer }})
                </span>
            </div>
            <div>
                <span style="font-size: 12px; white-space: nowrap"> 第三季度 </span>
                &nbsp;&nbsp;&nbsp;
                <!-- <span style="font-size: 12px; white-space: nowrap"> CTA(74%) </span> -->
                <span style="font-size: 12px; white-space: nowrap">
                    {{ thirdJdType }}({{ thirdJdTypePer }})
                </span>
            </div>
            <div>
                <span style="font-size: 12px; white-space: nowrap"> 第四季度 </span>
                &nbsp;&nbsp;&nbsp;
                <!-- <span style="font-size: 12px; white-space: nowrap"> 股票(89%) </span> -->
                <span style="font-size: 12px; white-space: nowrap">
                    {{ fourthJdType }}({{ fourthJdTypePer }})
                </span>
            </div>
        </div>
    </div>
</template>

<script>
    import * as echarts from 'echarts'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'CpclPieChartBoard',
        props: {
            // eslint-disable-next-line vue/require-default-prop
            vId: {
                type: String
            },
            title: {
                type: String,
                default: 'title'
            },
            // eslint-disable-next-line vue/require-default-prop
            dataItem: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            circleData: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            firstJdType: {
                type: String
            },
            // eslint-disable-next-line vue/require-default-prop
            firstJdTypePer: {
                type: String
            },
            // eslint-disable-next-line vue/require-default-prop
            secondJdType: {
                type: String
            },
            // eslint-disable-next-line vue/require-default-prop
            secondJdTypePer: {
                type: String
            },
            // eslint-disable-next-line vue/require-default-prop
            thirdJdType: {
                type: String
            },
            // eslint-disable-next-line vue/require-default-prop
            thirdJdTypePer: {
                type: String
            },
            // eslint-disable-next-line vue/require-default-prop
            fourthJdType: {
                type: String
            },
            // eslint-disable-next-line vue/require-default-prop
            fourthJdTypePer: {
                type: String
            }
        },
        data() {
            return {
                // option: {
                //     title: {
                //         text: this.title,
                //         left: 'left',
                //         textStyle: {
                //             fontSize: 10
                //         }
                //     },
                //     tooltip: {
                //         trigger: 'item'
                //     },
                //     legend: {
                //         width: 300,
                //         data: this.dataItem,
                //         orient: 'horizontal',
                //         y: 'bottom',
                //         x: '10%',
                //         itemWidth: 8,
                //         itemHeight: 8,
                //         itemGap: 8,
                //         textStyle: {
                //             fontSize: 6
                //         }
                //     },
                //     series: [
                //         {
                //             name: '零售存量',
                //             type: 'pie',
                //             radius: '60%',
                //             data: this.circleData,
                //             center: ['60%', '45%'],
                //             // 设置值域的那指向线
                //             labelLine: {
                //                 normal: {
                //                     show: true // show设置线是否显示，默认为true，可选值：true | false
                //                 }
                //             },
                //             label: {
                //                 normal: {
                //                     position: 'inner', // 设置标签位置，默认在饼状图外 可选值：'outer' | 'inner（饼状图上）'
                //                     formatter: '{d}%'
                //                 }
                //             }
                //         }
                //     ]
                // }
            }
        },
        watch: {
            circleData: {
                handler(newVal) {
                    if (newVal) {
                        this.$nextTick(() => {
                            this.renderPieChart()
                        })
                    }
                },
                immediate: true,
                deep: true
            }
        },
        mounted() {
            this.renderPieChart()
        },
        methods: {
            renderPieChart() {
                const option = {
                    title: {
                        text: this.title,
                        left: 'left',
                        textStyle: {
                            color: '#ba4949',
                            fontSize: 13
                        }
                    },
                    //下载图片
                    toolbox: {
                        show: true,
                        y: 'center',
                        x: 'left',
                        feature: {
                            saveAsImage: {
                                pixelRatio: 15
                            } //值越大分辨率越高
                        }
                    },
                    tooltip: {
                        trigger: 'item'
                    },
                    legend: {
                        width: 300,
                        data: this.dataItem,
                        orient: 'horizontal',
                        y: 'bottom',
                        x: '10%',
                        itemWidth: 8,
                        itemHeight: 8,
                        itemGap: 8,
                        selected: false,
                        textStyle: {
                            fontSize: 8
                        }
                    },
                    series: [
                        {
                            name: '高端销量(单位:万)',
                            type: 'pie',
                            radius: '60%',
                            data: this.circleData,
                            center: ['55%', '45%'],
                            itemStyle: {
                                borderColor: '#fff',
                                borderWidth: 1
                            },
                            // 设置值域的那指向线
                            labelLine: {
                                normal: {
                                    show: true // show设置线是否显示，默认为true，可选值：true | false
                                }
                            },
                            label: {
                                normal: {
                                    position: 'inner', // 设置标签位置，默认在饼状图外 可选值：'outer' | 'inner（饼状图上）'
                                    formatter: '{d}%'
                                }
                            }
                        }
                    ]
                }
                const pieChart = echarts.init(document.getElementById(this.vId))
                pieChart.setOption(option)
            }
        }
    })
</script>

<style scoped>
    .board-s {
        height: 95%;
        margin: 2px;

        /* border: 1px solid cornflowerblue; */
    }
</style>
