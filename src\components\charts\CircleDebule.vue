<!--
    * 双环形饼图
    * 使用场景：echarts
-->
<template>
    <chart-wrapper :width="width" :height="height" :options="chartOptions" v-bind="$attrs" />
</template>
<script setup lang="ts">
    import { watch, ref } from 'vue'
    import { deepMergeObj, getCurColor } from './scripts/methods'
    interface Props {
        width?: string
        height?: string
        chartData?: any
        colorType?: number
    }
    const props = withDefaults(defineProps<Props>(), {
        width: '100%',
        height: '300px',
        colorType: 1,
        chartData: () => {
            return {
                seriesData: {
                    chartData: [],
                    position: 'inner',
                    radius: ['55%', '100%'],
                    fontColor: '#fff',
                    graphicText: '当前\n配置'
                },
                seriesData1: {
                    chartData: [],
                    radius: ['35%', '65%']
                }
            }
        }
    })

    const chartOptions = ref()
    interface commonChartsSeroes {
        radius: string[]
        position?: string
        fontColor?: Object
        graphicText?: string
    }
    interface chartsSeriesData extends commonChartsSeroes {
        chartData:
            | {
                  code: string
                  amount?: number
                  value: number
                  name: string
                  minNum: number
                  labelVal?: number
                  itemStyle?: object
              }[]
            | null[]
    }
    interface chartsSeriesData1 extends commonChartsSeroes {
        chartData:
            | {
                  code: string
                  amount?: number
                  value: number
                  name: string
                  labelVal?: number
                  itemStyle?: object
              }[]
            | null[]
    }

    const getChartOptions = (colorType = 1) => {
        const seriesData: chartsSeriesData = props.chartData?.seriesData || []
        const seriesData1: chartsSeriesData1 = props.chartData?.seriesData1 || []

        // 初始化options
        const chartLen = seriesData.chartData?.length || 0
        const chartLen1 = seriesData1?.chartData.length || 0
        let borderWidth = 1
        let borderWidth1 = 1
        let minAngle = 5
        let minAngle1 = 5

        const seriesDataTpl = seriesData.chartData
            .map((item: any) => {
                if (item) {
                    // 当值为100时重置样式
                    if (!item.amount && item.value === 0) {
                        borderWidth = 0
                        minAngle = 0
                        item = null
                    } else if (chartLen === 1 && item.value === 100) {
                        borderWidth = 0
                        minAngle = 0
                    }
                    if (item) {
                        if (item.value < 0.5) {
                            item.value = 0
                        }
                        item.labelVal = item.value
                        if (item.minNum > 0 && item.value >= 0) {
                            item.value += item.minNum * 1.4
                        }
                        // 获取当前的code,并初始化颜色值
                        item.itemStyle = {
                            color: getCurColor({ type: item.code, colorType })
                        }
                    }

                    return item
                }
            })
            .filter(item => item)

        const series1DataTpl = seriesData1.chartData
            .map((item: any) => {
                if (item) {
                    // 当值为100时重置样式
                    if (item.amount === 0 && item.value === 0) {
                        borderWidth1 = 0
                        minAngle1 = 0
                        item = null
                    } else if (chartLen1 === 1 && item.value === 100) {
                        borderWidth1 = 0
                        minAngle1 = 0
                        if (seriesData.position === 'inner') {
                            item = null
                        }
                    }
                    // 获取当前的code,并初始化颜色值
                    if (item) {
                        if (item.value < 0.5) {
                            item.value = 0
                        }
                        item.itemStyle = {
                            color: getCurColor({ type: item.code, colorType })
                        }
                    }
                    return item
                }
            })
            .filter(item => item)

        /**
         * @description: 合并配置项
         * @return {*}
         */
        chartOptions.value = deepMergeObj(
            {},
            {
                grid: {
                    left: 40,
                    right: 30,
                    top: 30,
                    bottom: 5
                },
                legend: {
                    show: false,
                    icon: 'react',
                    itemWidth: 10,
                    itemHeight: 10,
                    type: 'scroll',
                    orient: 'vertical',
                    right: 10,
                    top: 20,
                    bottom: 20
                    // type: 'scroll'
                },
                series: [
                    {
                        type: 'pie',
                        radius: seriesData.radius,
                        center: ['50%', '50%'],
                        emphasis: {
                            scale: false
                        },
                        legendHoverLink: false,
                        avoidLabelOverlap: true,
                        selectedMode: false,
                        silent: true,
                        minAngle,
                        percentPrecision: 0,
                        // top: 10,
                        // right: 10,
                        // left: 10,
                        // bottom: 10,
                        // stillShowZeroSum: false,
                        itemStyle: {
                            backgroundColor: '#ccc',
                            bordeType: 'solid',
                            borderColor: '#fff',
                            borderWidth: borderWidth
                        },
                        label: {
                            show: true,
                            position: seriesData.position,
                            formatter: (params: any) => {
                                if (seriesData.position === 'outside') {
                                    if (params.data.labelVal === 0 && params.data.amount <= 0) {
                                        return ``
                                    } else if (params.data.labelVal === 100) {
                                        return `{b1|${params.data.name}}\n{c1| 100%}`
                                    } else if (params.data.labelVal < 0.5) {
                                        return `{b1|${params.data.name}}\n{c2| <0.5%}`
                                    } else if (params.data.labelVal > 99.5) {
                                        return `{b1|${params.data.name}}\n{c2| >99.5%}`
                                    } else if (params.data.labelVal > 90) {
                                        return `{b1|${
                                            params.data.name
                                        }}\n{c1|${params.data.labelVal.toFixed(0)}%}`
                                    } else if (params.data.labelVal < 10) {
                                        return `{b1|${
                                            params.data.name
                                        }}\n{c1|${params.data.labelVal.toFixed(0)}%}`
                                    }
                                    return `{b|${
                                        params.data.name
                                    }}\n{c|${params.data.labelVal.toFixed(0)}%}`
                                }
                                if (params.data.labelVal === 0 && params.data.amount > 0) {
                                    return `{a|${params.data.name}\n<0.5%}`
                                } else if (
                                    params.data.labelVal === 100 &&
                                    params.data.minNum === 0
                                ) {
                                    return `{a|${params.data.name}\n100%}`
                                } else if (params.data.labelVal < 0.5) {
                                    // return `{a|${params.data.name}\n<0.5%}`
                                    return ``
                                } else if (params.data.labelVal > 99.5) {
                                    return `{a|${params.data.name}\n>99.5%}`
                                }
                                return params.data.labelVal >= 45 && params.data.labelVal <= 65
                                    ? `{a1|${params.data.name}}\n{a1|${params.data.labelVal.toFixed(
                                          0
                                      )}%}`
                                    : `{a|${params.data.name}}\n{a|${params.data.labelVal.toFixed(
                                          0
                                      )}%}`
                            },
                            rich: {
                                a: {
                                    color: seriesData.fontColor,
                                    fontSize: 10,
                                    lineHeight: 13,
                                    fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei',
                                    fontWeight: 'bold',
                                    padding: [-5, -90, -5, -90]
                                },
                                a1: {
                                    color: seriesData.fontColor,
                                    fontSize: 9,
                                    lineHeight: 13,
                                    fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei',
                                    fontWeight: 'bold',
                                    padding: [-5, -85, -5, -90]
                                },
                                b: {
                                    color: seriesData.fontColor,
                                    fontSize: 10,
                                    lineHeight: 14,
                                    fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei',
                                    fontWeight: 'bold',
                                    padding: [0, -90]
                                },
                                b1: {
                                    color: seriesData.fontColor,
                                    fontSize: 10,
                                    lineHeight: 14,
                                    fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei',
                                    fontWeight: 'bold',
                                    padding: [0, -35]
                                },
                                c: {
                                    color: seriesData.fontColor,
                                    fontSize: 10,
                                    lineHeight: 14,
                                    fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei',
                                    fontWeight: 'bold',
                                    padding: [0, -90]
                                },
                                c1: {
                                    color: seriesData.fontColor,
                                    fontSize: 10,
                                    lineHeight: 14,
                                    fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei',
                                    fontWeight: 'bold',
                                    padding: [0, -30]
                                },
                                c2: {
                                    color: seriesData.fontColor,
                                    fontSize: 10,
                                    lineHeight: 14,
                                    fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei',
                                    fontWeight: 'bold',
                                    padding: [0, -40]
                                },
                                d: {
                                    color: seriesData.fontColor,
                                    fontSize: 10,
                                    lineHeight: 11,
                                    fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei',
                                    fontWeight: 'bold',
                                    padding: [0, -92]
                                },
                                e: {
                                    color: '#333',
                                    fontSize: 10,
                                    lineHeight: 14,
                                    fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei',
                                    fontWeight: 'bold',
                                    padding: [0, -92]
                                }
                            },
                            color: seriesData.fontColor,
                            align: 'center'
                        },
                        labelLine: {
                            // 设置提示线长度
                            length: 5, // pdf模式缩小线长
                            length2: 90,
                            // length2: 100,
                            // length2: 80,
                            minTurnAngle: 45,
                            maxSurfaceAngle: 145,
                            // showAbove:true,
                            lineStyle: {
                                type: 'solid',
                                width: 1
                                // cap: 'round',
                            }
                        },
                        data: seriesDataTpl
                    },
                    {
                        type: 'pie',
                        radius: seriesData1.radius,
                        center: ['50%', '50%'],
                        emphasis: {
                            scale: false
                        },
                        legendHoverLink: false,
                        avoidLabelOverlap: true,
                        selectedMode: false,
                        minAngle: minAngle1,
                        // silent:true,
                        // stillShowZeroSum: false,
                        itemStyle: {
                            // shadowColor: 'rgba(0, 0, 0, 0.2)',
                            // shadowBlur: 10,
                            bordeType: 'solid',
                            borderColor: '#fff',
                            borderWidth: borderWidth1
                        },
                        label: {
                            show: true,
                            position: 'inner',
                            formatter: (params: any) => {
                                if (seriesData.position === 'outside') {
                                    if (!params.data.value) {
                                        // return `{b| 0%}`
                                        return ``
                                    } else if (
                                        params.data.value === 100 &&
                                        series1DataTpl.length === 1
                                    ) {
                                        return `{b|${params.data.name} 100%}`
                                    } else if (params.data.value < 0.5) {
                                        // return `{b| <0.5%}`
                                        return ``
                                    } else if (params.data.value > 99.5) {
                                        return `{b| >99.5%}`
                                    }
                                    return `{b| ${params.data.value.toFixed(0)}%}`
                                }

                                if (!params.data.value) {
                                    return ``
                                } else if (
                                    params.data.value === 100 &&
                                    series1DataTpl.length === 1
                                ) {
                                    return `{a| 100%}`
                                } else if (params.data.value < 0.5) {
                                    // return `{a| <0.5%}`
                                    return ``
                                } else if (params.data.value > 99.5) {
                                    return `{a| 99.5%}`
                                }

                                return `{a| ${params.data.value.toFixed(0)}%}`
                            },
                            rich: {
                                a: {
                                    color: '#fff',
                                    fontSize: 11,
                                    lineHeight: 8,
                                    fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei',
                                    fontWeight: 'bold'
                                },
                                b: {
                                    color: '#fff',
                                    fontSize: 11,
                                    lineHeight: 10,
                                    fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei',
                                    fontWeight: 'bold'
                                }
                            },
                            color: '#fff',
                            fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei',
                            fontWeight: 'bold'
                        },
                        data: series1DataTpl
                        // data: seriesData1.chartData
                    }
                ],
                graphic: {
                    type: 'text',
                    left: 'center',
                    top: 'center',
                    style: {
                        text: seriesData.graphicText,
                        textAlign: 'center',
                        fill: '#333',
                        fontSize: 14,
                        textLineHeight: 16,
                        fontWeight: 'bold',
                        fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei'
                    }
                }
            }
        )
        return chartOptions
    }
    watch(
        [() => props.chartData],
        (newVal, oldVal) => {
            if (newVal) {
                getChartOptions(props.colorType)
            } else {
                chartOptions.value = null
            }
        },
        {
            immediate: true
            // deep: true
        }
    )
</script>
<style lang="less">
    @import './styles/index';
</style>
