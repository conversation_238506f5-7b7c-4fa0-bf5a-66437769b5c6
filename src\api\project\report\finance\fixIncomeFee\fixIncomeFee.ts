import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { FixIncomeFeeParam } from './type/apiReqType.js'

/**
 * @description: 固收产品的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const FixIncomeFeeQuery = (params: FixIncomeFeeParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/fixincomefee/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 固收产品的导出接口
 * @return {*}
 */
export const FixIncomeFeeExport = (params: FixIncomeFeeParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/fixincomefee/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 详细页面的查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const FixIncomeFeeDetailQuery = (params: FixIncomeFeeParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/fixincomefee/querydetail',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 明细页面的导出
 * @return {*}
 */
export const FixIncomeFeeDetailExport = (params: FixIncomeFeeParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/finance/fixincomefee/exportdetail',
            method: 'post',
            data: params
        })
    )
}
