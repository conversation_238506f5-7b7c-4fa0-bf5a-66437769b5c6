<!--
 * @Description: 业务组件-关联选择
 * @Author: chaohui.wu
 * @Date: 2023-09-07 10:03:35
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-09-21 19:17:45
 * @FilePath: /crm-web/packages/crm-performance-web/src/views/performance/stockSplit/components/releatedSelect.vue
 *  
-->
<template>
    <div class="releated-select-module">
        <tree-select
            v-model="queryForm.orgCode"
            placeholder="请选择"
            :check-strictly="true"
            label-format="orgName"
            value-format="orgCode"
            :data="organizationList"
            :style="{ width: '200px' }"
            :disabled="disabled"
            @nodeClick="handleTreeSelect(1)"
        />
        <crm-select
            v-model="queryForm.consCode"
            placeholder="请选择"
            label-format="consname"
            value-format="conscode"
            :disabled="disabled"
            filterable
            clearable
            :option-list="queryForm.consList"
            :style="{ width: '120px' }"
            @change="handleSelect"
        />
    </div>
</template>

<script lang="ts" setup>
    import {
        // eslint-disable-next-line camelcase
        getDsConsByOrgcode_json
    } from '@/api/project/databorad/databoard'
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            organizationList: any[]
            consListDefault: any[]
            defaultOrgCode?: string
            defaultConsCode?: string
            disabled?: boolean
            addAll?: boolean
        }>(),
        {
            organizationList: () => {
                return []
            },
            consListDefault: () => {
                return []
            },
            defaultOrgCode: '',
            defaultConsCode: '',
            disabled: false,
            addAll: false
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: {}): void
    }>()

    const isDisabled = ref(false)

    /**
     * @description: 联动
     * @return {*}
     */
    const queryForm = ref<any>({
        orgCode: '',
        consCode: '',
        consList: []
    })

    /**
     * @description: 获取机构下投顾
     * @return {*}
     */
    // const getConsCode = (orgCode: string, type: number) => {
    //     fetchRes(queryConsultant({ orgCode, recursive: '1' }), {
    //         successCB: (res: any) => {
    //             isSelect.value = true
    //             const { rows } = res || { rows: [] }
    //             let rowsTpl: any = rows
    //             if (rows?.length > 0) {
    //                 if (props.addAll) {
    //                     rowsTpl = [{ consCode: '', consName: '全部', status: '0' }, ...rows]
    //                 }
    //             }
    //             if (type === 2) {
    //                 queryForm.value.consList = rowsTpl
    //                 queryForm.value.consCode = props.defaultConsCode || ''
    //             } else {
    //                 queryForm.value.consList = rowsTpl
    //                 queryForm.value.consCode = rowsTpl[0]?.consCode || ''
    //             }

    //             nextTick(() => {
    //                 const { orgCode, consCode, consList } = queryForm.value
    //                 emit('update:modelValue', { orgCode, consCode, consList })
    //             })
    //         },
    //         errorCB: () => {
    //             queryForm.value.consList = []
    //         },
    //         catchCB: () => {
    //             queryForm.value.consList = []
    //         },
    //         successTxt: '',
    //         failTxt: '请求失败请重试！',
    //         fetchKey: ''
    //     })
    // }

    //获取部门下的所有投顾
    const changeCons = async (orgCode: string) => {
        isSelect.value = true
        const params = {
            selectOrgCode: orgCode,
            multCheck: '0'
        }
        const res: any = await getDsConsByOrgcode_json(params)
        let conslist = res.data
        conslist = [{ conscode: '', consname: '全部', status: '0' }, ...conslist]
        console.log('conslist:' + JSON.stringify(conslist))
        queryForm.value.consList = conslist
        queryForm.value.consCode = conslist[0]?.conscode || ''
    }
    /**
     * @description: 部门选择后联动
     * @param type 1-新管理层/投顾 2-原管理层/投顾
     * @return {*}
     */
    const isSelect = ref(false)
    const handleTreeSelect = (type = 1) => {
        queryForm.value.consCode = ''
        queryForm.value.consList = []
        // 判断当投顾所属部门选中
        nextTick(() => {
            const { orgCode } = queryForm.value
            //getConsCode(orgCode, type)
            changeCons(orgCode)
        })
    }

    /**
     * @description: 投顾选中后联动
     * @return {*}
     */
    const handleSelect = () => {
        emit('update:modelValue', queryForm.value)
    }

    /**
     * @description: 管理层数据初始化
     * @param {*} orgList
     * @param {*} consList
     * @return {*}
     */
    const consInit = (
        orgListTpl: { value: string }[],
        consListTpl: { consCode: string }[],
        type = 1
    ) => {
        const { defaultOrgCode, defaultConsCode } = props || {}
        if (type === 1) {
            //  默认选中第一个
            // eslint-disable-next-line no-lonely-if
            if (orgListTpl[0]) {
                const { value } = orgListTpl[0] || {}
                queryForm.value.orgCode = value
            }
            // 初始化选中第一个
            // eslint-disable-next-line no-lonely-if
            if (consListTpl && consListTpl[0]) {
                const { consCode } = consListTpl[0] || {}
                queryForm.value.consCode = consCode
            }
        } else {
            if (defaultOrgCode) {
                queryForm.value.orgCode = defaultOrgCode
            }

            if (defaultConsCode) {
                queryForm.value.consCode = defaultConsCode
            }
            queryForm.value.consList = consListTpl
        }
    }

    /**
     * @description: 重置
     * @return {*}
     */
    const resetSelect = () => {
        consInit(props.organizationList, props.consListDefault)
        emit('update:modelValue', queryForm.value)
    }

    /**
     * @description: 初始化默认值
     * @return {*}
     */
    watchEffect(() => {
        if (props.defaultOrgCode || props.defaultConsCode) {
            if (!isSelect.value) {
                consInit(props.organizationList, props.consListDefault, 2)
            }
        } else {
            consInit(props.organizationList, props.consListDefault, 1)
        }
    })

    /**
     * @description: 组件导出方法
     * @return {*}
     */
    defineExpose({
        resetSelect
    })
    onBeforeMount(() => {
        changeCons(props.defaultOrgCode)
    })
</script>
<style lang="less" scoped></style>
