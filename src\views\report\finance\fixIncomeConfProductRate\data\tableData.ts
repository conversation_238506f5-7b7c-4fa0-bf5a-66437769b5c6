/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const fixIncomeConfProductRateTableColumn: TableColumnItem[] = [
    {
        key: 'fundCode',
        label: '产品代码',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundName',
        label: '产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'valueStartDate',
        label: '产品起息日',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'valueEndDate',
        label: '产品到期日',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'countDay',
        label: '计费天数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'yearDay',
        label: '年化天数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consultRate',
        label: '咨询费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'manageRate',
        label: '管理费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'redemRate',
        label: '赎回费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'redemDay',
        label: '赎回天数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consultFormula',
        label: '咨询费公式',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'manageFormula',
        label: '管理费公式',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'redemFormula',
        label: '赎回费公式',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'adjustAmount',
        label: '调整金额',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'selfCust',
        label: '自有客户',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'agarement',
        label: '协议主体',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'settleStartDate',
        label: '结算起始日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'settleEndDate',
        label: '结算结束日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'remark',
        label: '备注',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceFormula',
        label: '业绩报酬公式',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'hswType',
        label: '高水位类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'hbPerformanceRate1',
        label: '好买业绩报酬费率1',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'hbPerformanceRate2',
        label: '好买业绩报酬费率2',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceRate1',
        label: '业绩报酬费率1',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceRate2',
        label: '业绩报酬费率2',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performancejtType',
        label: '业绩报酬计提类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'redemDate',
        label: '赎回日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'settleDate',
        label: '清算日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'shareDate',
        label: '分红日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'closedPeriodDays',
        label: '封闭期天数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceBase1',
        label: '业绩报酬基准1',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'performanceBase2',
        label: '业绩报酬基准2',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'rateStartDate',
        label: '费率开始日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'rateEndDate',
        label: '费率结束日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'reviewState',
        label: '审核状态',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'reviewtime',
        label: '审核时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'reviewer',
        label: '审核人',
        width: 120,
        formatter: formatTableValue
    },

    {
        key: 'creator',
        label: '创建者',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'createtime',
        label: '创建时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'updater',
        label: '更新者',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'updattime',
        label: '更新时间',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
