/*
 * @Description: 产品费率配置API操作hooks
 * @Author: hongdong.xie
 * @Date: 2025-06-06 09:42:20
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 09:42:20
 * @FilePath: /ds-report-web/src/components/common/ProductFeeRateConfig/composables/useProductFeeRateApi.ts
 */

import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { fetchRes } from '@/utils'
import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import type { ProductFeeRateConfig, BaseFormData, BaseAuditParam, DialogType } from '../types'
import { formatDateToBackend, handleApiError, handleApiCatch } from '../utils'

/**
 * @description: 产品费率配置API操作hooks
 * @author: hongdong.xie
 * @date: 2025-06-06 09:42:20
 * @param {ProductFeeRateConfig} config 配置对象
 * @return {*}
 */
export const useProductFeeRateApi = (config: ProductFeeRateConfig) => {
    const submitLoading = ref(false)

    /**
     * @description: 创建API函数
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {string} url API URL
     * @param {string} method HTTP方法
     * @return {Function} API函数
     */
    const createApiFunction = (url: string, method = 'post') => {
        return (params: any) => {
            return axiosRequest(
                paramsMerge({
                    url,
                    method,
                    data: params
                })
            )
        }
    }

    /**
     * @description: 处理表单数据，转换日期格式并确保所有字段都被包含
     * @author: hongdong.xie
     * @date: 2025-01-16 15:00:00
     * @param {BaseFormData} formData 表单数据
     * @return {any} 处理后的数据
     */
    const processFormData = (formData: BaseFormData): any => {
        console.log('🔧 开始处理表单数据:', formData)

        // 深拷贝表单数据，避免修改原始数据
        const processedData = JSON.parse(JSON.stringify(formData))

        // 转换日期字段格式
        const dateFields = [
            'signingDate',
            'rateStartDate',
            'rateEndDate',
            'rateEffectiveStartDate',
            'rateEffectiveEndDate'
        ]

        dateFields.forEach(field => {
            const fieldValue = processedData[field]
            // 只有当字段值不为空字符串时才进行日期格式转换
            if (fieldValue && fieldValue !== '') {
                processedData[field] = formatDateToBackend(fieldValue)
            }
        })

        // 定义所有字段的默认值映射
        const defaultValues = {
            // 基础信息
            filingCode: '',
            productCode: '',
            shareCode: '',
            productFullName: '',
            fofOnlyFlag: '0',
            payerFullName: '',
            productManager: '',
            signingDate: '',
            howbuySigningEntity: '',
            counterpartContact: '',
            durationDCoefficient: 0,
            durationDRemark: '',

            // 费率配置
            rateStartDate: '',
            rateEndDate: '2999-12-31',
            tieredRateType: '0',
            rateEffectiveType: '0',
            configLowerLimit: 0,
            configUpperLimit: 0,
            rateEffectiveStartDate: '',
            rateEffectiveEndDate: '',

            // 认申购费率
            subscriptionRate: 0,
            subscriptionRemark: '',

            // 管理费率
            managementRate: 0,
            managementFormula: '',
            managementRemark: '',

            // 业绩报酬费率
            performanceSharingRate1: 0,
            performanceRate1: 0,
            performanceBenchmark1: '',
            performanceSharingRate2: 0,
            performanceRate2: 0,
            performanceBenchmark2: '',
            performanceSharingRate3: 0,
            performanceRate3: 0,
            performanceBenchmark3: '',
            performanceAccrualType: '',
            performanceAccrualForm: '',
            fixedAccrualMonthType: '',
            fixedAccrualDateType: '',
            fixedAccrualCalcType: '',
            redemptionHoldingDaysRule: '',
            dividendHoldingDaysRule: '',
            performanceFormula: '',
            shareLockType: '',
            shareLockDays: 0,
            fundClosedType: '',
            fundClosedDays: 0,
            noAccrualNavBenchmark: 0,
            performanceRemark: '',

            // 赎回费率
            redemptionRate1: 0,
            redemptionHoldingDays1: 0,
            redemptionRate2: 0,
            redemptionHoldingDays2: 0,
            redemptionRate3: 0,
            redemptionHoldingDays3: 0,
            redemptionRate4: 0,
            redemptionHoldingDays4: 0,
            redemptionFormula: '',
            redemptionHoldingCalcRule: '',
            redemptionSpecialRule: '',
            redemptionRemark: ''
        }

        // 确保所有字段都存在，并且有正确的值
        Object.keys(defaultValues).forEach(key => {
            const currentValue = (processedData as any)[key]
            const defaultValue = (defaultValues as any)[key]

            // 如果当前值为undefined、null，或者对于数值字段为NaN，则使用默认值
            if (currentValue === undefined || currentValue === null ||
                (typeof defaultValue === 'number' && isNaN(currentValue))) {
                (processedData as any)[key] = defaultValue
                console.log(`🔧 字段 ${key} 设置为默认值:`, defaultValue)
            } else {
                // 确保数值字段是数字类型
                if (typeof defaultValue === 'number' && typeof currentValue !== 'number') {
                    const numValue = Number(currentValue)
                    ;(processedData as any)[key] = isNaN(numValue) ? defaultValue : numValue
                }
            }
        })

        console.log('🔧 处理后的表单数据:', processedData)

        // 验证关键字段
        const criticalFields = ['configLowerLimit', 'configUpperLimit', 'subscriptionRate', 'managementRate']
        criticalFields.forEach(field => {
            console.log(`🔍 关键字段 ${field}:`, (processedData as any)[field], typeof (processedData as any)[field])
        })

        return processedData
    }

    /**
     * @description: 新增数据
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {BaseFormData} formData 表单数据
     * @return {Promise<boolean>} 操作结果
     */
    const addData = async (formData: BaseFormData): Promise<boolean> => {
        return new Promise((resolve) => {
            console.log(`🆕 开始新增${config.pageConfig.title}数据...`)
            submitLoading.value = true

            const processedData = processFormData(formData)
            // 移除ID字段（新增时不需要）
            delete processedData.id

            console.log('📤 新增参数:', processedData)

            const addFunction = createApiFunction(config.apiConfig.addUrl)

            fetchRes(addFunction(processedData), {
                successCB: (res: any) => {
                    console.log('✅ 新增成功:', res)
                    submitLoading.value = false
                    ElMessage({
                        type: 'success',
                        message: '新增成功'
                    })
                    resolve(true)
                },
                errorCB: (error: any) => {
                    submitLoading.value = false
                    handleApiError(error, '新增失败，请重试')
                    resolve(false)
                },
                catchCB: (error: any) => {
                    submitLoading.value = false
                    handleApiCatch(error)
                    resolve(false)
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        })
    }

    /**
     * @description: 修改数据
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {BaseFormData} formData 表单数据
     * @return {Promise<boolean>} 操作结果
     */
    const updateData = async (formData: BaseFormData): Promise<boolean> => {
        return new Promise((resolve) => {
            console.log(`🔄 开始修改${config.pageConfig.title}数据...`)
            submitLoading.value = true

            const processedData = processFormData(formData)
            console.log('📤 修改参数:', processedData)

            const updateFunction = createApiFunction(config.apiConfig.updateUrl)

            fetchRes(updateFunction(processedData), {
                successCB: (res: any) => {
                    console.log('✅ 修改成功:', res)
                    submitLoading.value = false
                    ElMessage({
                        type: 'success',
                        message: '修改成功'
                    })
                    resolve(true)
                },
                errorCB: (error: any) => {
                    submitLoading.value = false
                    handleApiError(error, '修改失败，请重试')
                    resolve(false)
                },
                catchCB: (error: any) => {
                    submitLoading.value = false
                    handleApiCatch(error)
                    resolve(false)
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        })
    }

    /**
     * @description: 审核数据
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {BaseAuditParam} auditParam 审核参数
     * @return {Promise<boolean>} 操作结果
     */
    const auditData = async (auditParam: BaseAuditParam): Promise<boolean> => {
        return new Promise((resolve) => {
            console.log(`🔍 开始审核${config.pageConfig.title}数据...`)
            submitLoading.value = true

            console.log('📤 审核参数:', auditParam)

            const auditFunction = createApiFunction(config.apiConfig.auditUrl)

            fetchRes(auditFunction(auditParam), {
                successCB: (res: any) => {
                    console.log('✅ 审核成功:', res)
                    submitLoading.value = false
                    const statusText = auditParam.auditStatus === '2' ? '审核通过' : '审核不通过'
                    ElMessage({
                        type: 'success',
                        message: `${statusText}成功`
                    })
                    resolve(true)
                },
                errorCB: (error: any) => {
                    submitLoading.value = false
                    handleApiError(error, '审核失败，请重试')
                    resolve(false)
                },
                catchCB: (error: any) => {
                    submitLoading.value = false
                    handleApiCatch(error)
                    resolve(false)
                },
                successTxt: '',
                failTxt: '',
                fetchKey: ''
            })
        })
    }

    /**
     * @description: 根据操作类型提交数据
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @param {DialogType} dialogType 弹框类型
     * @param {BaseFormData} formData 表单数据
     * @return {Promise<boolean>} 操作结果
     */
    const submitData = async (dialogType: DialogType, formData: BaseFormData): Promise<boolean> => {
        switch (dialogType) {
            case 'add':
            case 'copyAdd':
                return addData(formData)
            case 'edit':
                return updateData(formData)
            default:
                console.error('未知的操作类型:', dialogType)
                return false
        }
    }

    return {
        submitLoading,
        addData,
        updateData,
        auditData,
        submitData
    }
}
