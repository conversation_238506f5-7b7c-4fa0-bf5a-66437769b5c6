<!--
 * @Description: 添加产品弹框
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-24 21:07:35
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-27 19:14:16
 * @FilePath: /crm-template/src/views/components/dialogCommon/adjustAmt/adjustAmtIndex.vue
 * @ 当前未解耦，有时间解耦后可提取到dialogCommomn中
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="400px"
        height="500px"
        :border="true"
        :title="title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <el-form
                v-if="fixIncomeConfProductAduit"
                ref="ruleFormRef"
                :model="ruleForm"
                label-width="100px"
                status-icon
            >
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="审核" style="margin-left: 15%">
                            <crm-select
                                v-model="ruleForm.reviewState"
                                :placeholder="reviewStateList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="reviewStateList.selectList"
                                :style="{ width: '100px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="submitForm(ruleFormRef)"
                    >提 交</crm-button
                >
                <crm-button size="small" :radius="true" @click="handleClose(ruleFormRef)"
                    >取 消</crm-button
                >
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { fetchRes } from '@/utils/index'
    import { dataList } from './data/labelData'
    import { fixIncomeConfProductRateReview } from '@/api/project/report/finance/fixIncomeConfProductRate/fixIncomeConfProductRate'
    const {
        reviewStateList,
    } = dataList
    const loadingFlag = ref<boolean>(false)
    // 表单数据共用的实体类
    interface RuleForm {
        taskId: string
        reviewState: string
    }

    const ruleFormRef = ref<FormInstance>()
    const ruleForm = reactive<RuleForm>({
        taskId: '',
        reviewState: '' // 审核状态
    })
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            fixIncomeConfProductData?: {
                taskId: string
            }
            fixIncomeConfProductAduit?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            fixIncomeConfProductData: () => {
                return {
                    taskId: '' // 任务id
                }
            },
            fixIncomeConfProductAduit: false
        }
    )
    watchEffect(() => {
        ruleForm.taskId = props.fixIncomeConfProductData.taskId
    })

    // 弹窗标题配置
    const title = computed(() => {
        if (props.fixIncomeConfProductAduit) {
            return '审核固收产品费率配置'
        }
    })
    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })
    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()

    // 取消方法
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        dialogVisible.value = false
    }
    // 提交方法
    const submitForm = async (formEl: FormInstance | undefined) => {
        fixIncomeConfProductDataSubmit(ruleForm)
    }
    /**
     * 产品费率新增方法
     * @param params
     */
    function fixIncomeConfProductDataSubmit(params: any) {
        console.log('params' + JSON.stringify(params))
        fetchRes(fixIncomeConfProductRateReview(params), {
            successCB: (res: any) => {
                loadingFlag.value = false
                dialogVisible.value = false
                return emit('callback')
            },
            errorCB: (res: any) => {
                loadingFlag.value = false
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => {
                loadingFlag.value = false
            },
            successTxt: '审核成功',
            failTxt: '',
            fetchKey: ''
        })
    }
</script>

<style lang="less" scoped>
    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
