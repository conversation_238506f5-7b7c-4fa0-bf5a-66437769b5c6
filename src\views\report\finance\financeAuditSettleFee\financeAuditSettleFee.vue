<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            :show-export-btn="true"
            @searchFn="handleLoading"
            @exportFn="exportHandle"
        >
            <template #searchArea>
                <label-item :label="settlePeriod.label">
                    <el-select
                        v-model="queryForm.settlePeriodType"
                        placeholder="结算周期"
                        @change="handleSettlePeriodTypeChange"
                    >
                        <el-option label="请选择" value=""></el-option>
                        <el-option label="年度" value="annual"></el-option>
                        <el-option label="半年" value="half_year"></el-option>
                        <el-option label="季度" value="quarterly"></el-option>
                        <el-option label="月度" value="monthly"></el-option>
                    </el-select>
                    <el-select
                        v-if="queryForm.settlePeriodType != ''"
                        v-model="queryForm.settleYear"
                        placeholder="周期年份"
                        :disabled="settleYearDisabled"
                        @change="handleSettleYearChange"
                    >
                        <el-option
                            v-for="year in settleYearOptions"
                            :key="year.value"
                            :label="year.label"
                            :value="year.value"
                            :style="{ width: '80px' }"
                        ></el-option>
                    </el-select>
                    <el-select
                        v-if="queryForm.settlePeriodType != ''"
                        v-model="queryForm.settlePeriodDetail"
                        placeholder="具体周期"
                        :disabled="settlePeriodDetailDisabled"
                        @change="handleSettlePeriodDetailChange"
                    >
                        <el-option
                            v-for="period in settlePeriodDetailOptions"
                            :key="period.value"
                            :label="period.label"
                            :value="period.value"
                            :style="{ width: '80px' }"
                        ></el-option>
                    </el-select>
                </label-item>
                <label-item :label="productType.label">
                    <crm-select
                        v-model="queryForm.productType"
                        :placeholder="productType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="productType.selectList"
                        :style="{ width: '120px' }"
                        @change="handleProductType"
                    />
                </label-item>
                <label-item :label="fundCode.label">
                    <crm-input
                        v-model="queryForm.fundCode"
                        :placeholder="fundCode.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
                <label-item :label="fundMan.label">
                    <crm-input
                        v-model="queryForm.fundMan"
                        :placeholder="fundMan.placeholder"
                        :clearable="true"
                        :style="{ width: '150px' }"
                    />
                </label-item>
                <label-item :label="feeType.label">
                    <crm-select
                        v-model="queryForm.feeType"
                        :placeholder="feeType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="feeTypeList"
                        :style="{ width: '120px' }"
                        @change="handleFeeType"
                    />
                </label-item>
                <label-item :label="auditState.label">
                    <crm-select
                        v-model="queryForm.auditState"
                        :placeholder="auditState.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="auditState.selectList"
                        :style="{ width: '120px' }"
                        @change="handleAuditState"
                    />
                </label-item>
                <label-item :label="fundName.label">
                    <crm-input
                        v-model="queryForm.fundName"
                        :placeholder="fundName.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
                <label-item :label="subFundName.label">
                    <crm-input
                        v-model="queryForm.subFundName"
                        :placeholder="subFundName.placeholder"
                        :clearable="true"
                        :style="{ width: '100px' }"
                    />
                </label-item>
            </template>
            <template #operationBtns>
                <crm-button
                    v-if="passShow"
                    size="small"
                    :icon="Edit"
                    type="primary"
                    :radius="true"
                    @click="handleBatchPass"
                    >批量通过</crm-button
                >
                <crm-button
                    v-if="rejectShow"
                    size="small"
                    :icon="Check"
                    type="primary"
                    :radius="true"
                    @click="handleBatchReject"
                    >批量驳回</crm-button
                >
                <crm-button
                    v-if="deleteShow"
                    size="small"
                    :icon="Minus"
                    type="primary"
                    :radius="true"
                    @click="handleBatchDelete"
                    >批量删除</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :cell-style="getDeviationStyle"
                    :show-operation="true"
                    :no-select="true"
                    :no-index="true"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="250"
                    @select="changeSelectTable"
                    @selectionChange="changeSelectTable"
                >
                    <template #operation="{ scope }">
                        <el-button
                            v-if="editShow"
                            size="small"
                            :text="true"
                            link
                            @click="handleEdit(scope.row)"
                            >编辑</el-button
                        >
                        <el-button
                            v-if="passShow"
                            size="small"
                            :text="true"
                            link
                            :disabled="scope.row.auditState !== '待审核'"
                            class="custom-button"
                            @click="handlePass(scope.row)"
                            >通过</el-button
                        >
                        <el-button
                            v-if="rejectShow"
                            size="small"
                            :text="true"
                            link
                            :disabled="scope.row.auditState !== '待审核'"
                            class="custom-button"
                            @click="handleReject(scope.row)"
                            >驳回</el-button
                        >
                        <el-button
                            v-if="deleteShow"
                            size="small"
                            :text="true"
                            link
                            @click="handleDelete(scope.row)"
                            >删除</el-button
                        >
                        <el-button
                            v-if="detailShow"
                            size="small"
                            :text="true"
                            link
                            @click="handleDetail(scope.row)"
                            >明细</el-button
                        >
                    </template>
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>
        <edit-product-index
            v-if="editDialogVisible"
            v-model="editDialogVisible"
            :finance-audit-edit="true"
            :finance-audit-data="editdata"
            @callback="handleClose()"
        >
        </edit-product-index>
        <delete-product-index
            v-if="deleteDialogVisible"
            v-model="deleteDialogVisible"
            :finance-audit-delete="true"
            :finance-audit-data="deletedata"
            @callback="handleClose()"
        >
        </delete-product-index>
    </div>
</template>

<script lang="ts" setup>
    import { downloadFile, fetchRes, message, messageBox } from '@/utils'
    import { Plus, Edit, Minus, Check } from '@element-plus/icons-vue'
    import { dataList } from './data/labelData'
    import { financeAuditSettleFeeTableColumn, showTableColumn } from './data/tableData'
    import {
        FinanceAuditSettleFeeQuery,
        FinanceAuditSettleFeeExport,
        financeAuditUpdate,
        financeAuditReview,
        financeAuditlBatchDeleteById,
        financeAuditBatchReview
    } from '@/api/project/report/finance/financeAudit/financeAudit'
    import { getMenuPermission } from '@/api/project/common/common'
    import { FINANCE_ORERATE_OPER_PERMISSION } from '@/constant/financeConst'
    import EditProductIndex from '@/views/report/finance/auditComponents/financeAuditEdit.vue'
    import DeleteProductIndex from '@/views/report/finance/auditComponents/financeAuditDelete.vue'
    import { ElMessage, ElMessageBox } from 'element-plus'
    import { ResFinceVO } from '@/type'
    import moment from 'moment'

    const {
        startDt,
        endDt,
        fundCode,
        fundMan,
        productType,
        auditState,
        feeType,
        fundName,
        subFundName,
        custName,
        conscustNo,
        settlePeriod,
        settleYear,
        settlePeriodDetail
    } = dataList

    const module = ref<string>('B071216')
    const exportShow = ref<boolean>(true)
    const editShow = ref<boolean>(false)
    const passShow = ref<boolean>(false)
    const rejectShow = ref<boolean>(false)
    const deleteShow = ref<boolean>(false)
    const detailShow = ref<boolean>(true)
    const editDialogVisible = ref<boolean>(false)
    const deleteDialogVisible = ref<boolean>(false)
    const feeTypeList = ref<object[]>(feeType.fixedList)

    const listLoading = ref<boolean>(false)

    const getDeviationStyle = (row: any, column: any) => {
        console.log(row.column.label)
        console.log(row.row.deviation)
        const deviation = row.row.deviation
        console.log(deviation)
        if (row.column.label == '误差(%)') {
            const color = Math.abs(deviation) > 5 ? 'red' : 'green'
            return { color } // 返回一个对象，包含动态生成的样式
        }
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        id = ''
        productType = '1'
        startDt = moment().format('YYYYMMDD')
        endDt = moment().add(30, 'day').format('YYYYMMDD')
        fundCode = ''
        fundMan = ''
        feeType = '1'
        fundName = ''
        subFundName = ''
        custName = ''
        conscustNo = ''
        auditState = '1'
        settlePeriodType = '' // 默认季度
        settleYear = '' // 默认当年
        settlePeriodDetail = '' // 具体周期
        settleStartDt = ''
        settleEndDt = ''
        settlePeriod = ''
    }
    const queryForm = reactive(new QueryForm())
    /**
     * @description: 上一次点了查询的条件列表
     * @return {*}
     */
    const queryFormAction = new QueryForm()

    const handleProductType = (val: string) => {
        queryForm.productType = val
        if (val === '1') {
            feeTypeList.value = feeType.fixedList
            queryForm.feeType = '1'
        }
        if (val === '2') {
            feeTypeList.value = feeType.secondList
            queryForm.feeType = '4'
        }
        if (val === '3') {
            feeTypeList.value = feeType.stockList
            queryForm.feeType = '7'
        }
    }
    const handleFeeType = (val: string) => {
        queryForm.feeType = val
    }
    const handleAuditState = (val: string) => {
        queryForm.auditState = val
    }

    const currentYear = ref(new Date().getFullYear())

    // 填充年份选项的计算属性
    const settleYearOptions = computed(() => {
        const options = []
        for (let year = currentYear.value; year >= 2018; year--) {
            options.push({ label: year.toString(), value: year })
        }
        return options
    })

    // 根据周期类型填充具体周期的选项
    const settlePeriodDetailOptions = computed(() => {
        let options = []
        switch (queryForm.settlePeriodType) {
            case 'annual':
                options = [{ label: '全年', value: '全年' }]
                break
            case 'half_year':
                options = ['上半年', '下半年'].map(label => ({ label, value: label }))
                break
            case 'quarterly':
                options = [
                    { label: '一季度', value: '1' },
                    { label: '二季度', value: '2' },
                    { label: '三季度', value: '3' },
                    { label: '四季度', value: '4' }
                ]
                break
            case 'monthly':
                for (let month = 1; month <= 12; month++) {
                    options.push({ label: `${month}月`, value: `${month}月` })
                }
                break
            default:
                options = [{ label: '请选择', value: '' }]
        }
        return options
    })

    const handleSettlePeriodTypeChange = (value: any) => {
        queryForm.settlePeriodType = value
        // 重置年份和具体周期为默认值
        if (value == '') {
            queryForm.settleYear = ''
            queryForm.settlePeriodDetail = ''
        }
    }

    const handleSettleYearChange = (value: any) => {
        queryForm.settleYear = value
        // 可以在这里添加基于年份变化的逻辑，如果需要
    }
    const handleSettlePeriodDetailChange = (value: any) => {
        queryForm.settlePeriodDetail = value
    }

    const settleYearDisabled = computed(() => {
        // 检查是否选择了“请选择”
        return queryForm.settlePeriodType === null
    })
    const settlePeriodDetailDisabled = computed(() => {
        // 检查是否选择了“请选择”
        return queryForm.settlePeriodType === null
    })

    const generateDateString = () => {
        let settleStartDate = ''
        let settleEndDate = ''
        const year = queryForm.settleYear
        switch (queryForm.settlePeriodType) {
            case 'annual':
                // 年度默认为年初到年末
                settleStartDate = `${year}0101`
                settleEndDate = `${year}1231`
                break
            case 'half_year':
                // 半年：上半年为1月1日到6月30日，下半年为7月1日到12月31日
                const halfYear = queryForm.settlePeriodDetail === '上半年' ? 1 : 7
                settleStartDate = `${year}${halfYear}0101`
                settleEndDate = `${year}${halfYear > 1 ? '1231' : '0630'}`
                break
            case 'quarterly':
                // 季度：根据季度选择不同的开始和结束日期
                const quarter = parseInt(queryForm.settlePeriodDetail, 10)
                const startMonth = (quarter - 1) * 3 + 1
                const endMonth = quarter * 3
                settleStartDate = `${year}${String(startMonth).padStart(2, '0')}01`
                settleEndDate = `${year}${String(endMonth).padStart(2, '0')}31`
                break
            case 'monthly':
                // 月份：根据月份选择第一天和月末最后一天
                const month = parseInt(queryForm.settlePeriodDetail, 10) - 1
                settleStartDate = `${year}${String(month + 1).padStart(2, '0')}01`
                settleEndDate = `${year}${String(month + 1).padStart(2, '0')}31`
                break
            default:
                // 默认情况或未选择
                settleStartDate = `${year}0101`
                settleEndDate = `${year}1231`
                break
        }

        return {
            settleStartDt: settleStartDate,
            settleEndDt: settleEndDate
        }
    }

    watchEffect(() => {
        const dateRange = generateDateString()
        queryForm.settleStartDt = dateRange.settleStartDt
        queryForm.settleEndDt = dateRange.settleEndDt
        const settlePeriodQueryType = queryForm.settlePeriodType
        let settlePeriodDetailQuery = queryForm.settlePeriodDetail
        if (queryForm.settlePeriodDetail == '1' && settlePeriodQueryType == 'quarterly') {
            settlePeriodDetailQuery = '一季度'
        }
        if (queryForm.settlePeriodDetail == '2' && settlePeriodQueryType == 'quarterly') {
            settlePeriodDetailQuery = '二季度'
        }
        if (queryForm.settlePeriodDetail == '3' && settlePeriodQueryType == 'quarterly') {
            settlePeriodDetailQuery = '三季度'
        }
        if (queryForm.settlePeriodDetail == '4' && settlePeriodQueryType == 'quarterly') {
            settlePeriodDetailQuery = '四季度'
        }
        if (settlePeriodQueryType == '') {
            queryForm.settlePeriod = ''
        }
        if (settlePeriodQueryType != '') {
            queryForm.settlePeriod = queryForm.settleYear + '年' + settlePeriodDetailQuery
        }
    })

    /**
     * @description: 编辑
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleEdit = (val: any): void => {
        stockObj.value = {
            title: '编辑审核',
            id: '2',
            type: 'edit'
        }
        console.log(val.fundCode)
        editdata.value.id = val.id
        editdata.value.productType = val.productType
        editdata.value.fundCode = val.fundCode
        editdata.value.fundName = val.fundName
        editdata.value.subFundName = val.subFundName
        editdata.value.feeType = val.feeType
        editdata.value.preAmount = val.preAmount
        editdata.value.preAmountTax = val.preAmountTax
        editdata.value.realAmount = val.realAmount
        editdata.value.realAmountTax = val.realAmountTax
        editdata.value.deviation = val.deviation
        editdata.value.preStartdt = val.preStartdt
        editdata.value.preEnddt = val.preEnddt
        editdata.value.settlePeriod = val.settlePeriod
        editdata.value.remark = val.remark
        editdata.value.auditState = val.auditState
        editdata.value.settlePeriodType = val.settlePeriodType
        editdata.value.settleYear = val.settleYear
        editdata.value.settlePeriodDetail = val.settlePeriodDetail
        editdata.value.settleStartDt = val.settleStartDt
        editdata.value.settleEndDt = val.settleEndDt

        editDialogVisible.value = true
    }

    /**
     * @description: 删除
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleDelete = (val: any): void => {
        const { id } = val || {}
        deletedata.value = {
            id: id
        }
        deleteDialogVisible.value = true
    }

    /**
     * @description: 关闭弹框刷新数据
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleClose = (): void => {
        queryList()
    }
    /**
     * @description: 新增
     * @return {*}
     */
    const stockObj = ref({
        title: '',
        id: '2',
        type: ''
    })

    /**
     * @description: 明细
     * @param val 明细数据
     * @method handleDetail 触发方法
     * @return {*}
     */
    const handleDetail = (val: any): void => {
        const { fundCode, fundName, subFundName, settlePeriod, productType, feeType, auditState } =
            val || {}
        window.open(
            `${
                window.location.origin + window.location.pathname
            }#/financeAuditSettleFeeDetail?fundCode=${
                fundCode ? fundCode : queryForm.fundCode
            }&fundName=${fundName ? fundName : queryForm.fundName}&settlePeriod=${
                settlePeriod ? settlePeriod : queryForm.settlePeriod
            }&productType=${queryForm.productType}&feeType=${queryForm.feeType}&auditState=${
                queryForm.auditState
            }&subFundName=${subFundName}`
        )
    }

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const panelActiveName = ref<string>('myReport')
    const tableData = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            financeAuditSettleFeeTableColumn.map(item => item.key),
            financeAuditSettleFeeTableColumn
        )
    })

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()

    /**
     * 审核方法
     * @param params
     */
    function handlePass(params: any) {
        listLoading.value = true
        const paramPass = {
            auditState: '2',
            id: params.id
        }
        ElMessageBox.confirm('确认审核通过？', '审核操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })
            .then(() => {
                console.log('params' + JSON.stringify(params))
                console.log('params' + JSON.stringify(paramPass))
                fetchRes(financeAuditReview(paramPass), {
                    successCB: (res: any) => {
                        listLoading.value = false
                        editDialogVisible.value = false
                        queryList()
                        return emit('callback')
                    },
                    errorCB: (res: any) => {
                        listLoading.value = false
                        ElMessage({
                            type: 'error',
                            message: res?.description || '请求失败'
                        })
                    },
                    catchCB: () => {
                        listLoading.value = false
                    },
                    successTxt: '审核通过',
                    failTxt: '',
                    fetchKey: ''
                })
            })
            .catch(() => {
                // 用户点击“取消”，可以在这里执行取消操作的逻辑
                ElMessage.info('已取消审核')
                queryList()
            })
    }

    /**
     * 审核驳回方法
     * @param params
     */
    function handleReject(params: any) {
        listLoading.value = true
        const paramPass = {
            auditState: '3',
            id: params.id
        }
        ElMessageBox.confirm('确认驳回？', '审核操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })
            .then(() => {
                console.log('params' + JSON.stringify(params))
                fetchRes(financeAuditReview(paramPass), {
                    successCB: (res: any) => {
                        listLoading.value = false
                        editDialogVisible.value = false
                        queryList()
                        return emit('callback')
                    },
                    errorCB: (res: any) => {
                        listLoading.value = false
                        ElMessage({
                            type: 'error',
                            message: res?.description || '请求失败'
                        })
                    },
                    catchCB: () => {
                        listLoading.value = false
                    },
                    successTxt: '已驳回',
                    failTxt: '',
                    fetchKey: ''
                })
            })
            .catch(() => {
                // 用户点击“取消”，可以在这里执行取消操作的逻辑
                ElMessage.info('已取消驳回')
                queryList()
            })
    }

    //查询
    const queryList = async () => {
        listLoading.value = true
        const params = {
            startDt: queryForm.startDt,
            endDt: queryForm.endDt,
            productType: queryForm.productType,
            fundCode: queryForm.fundCode,
            fundMan: queryForm.fundMan,
            feeType: queryForm.feeType,
            fundName: queryForm.fundName,
            subFundName: queryForm.subFundName,
            custName: queryForm.custName,
            conscustNo: queryForm.conscustNo,
            auditState: queryForm.auditState,
            settlePeriod: queryForm.settlePeriod,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(FinanceAuditSettleFeeQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                // debugger
                tableData.value = rows
                // TODO list接口待添加分页数据
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    // table选中数组
    const tableSelectList = ref<object[]>([])
    // table选中事件
    const changeSelectTable = (sel: ResFinceVO[]): void => {
        // 默认选择按成立时间排序
        tableSelectList.value = unref(sel)
            .map((item: any) => {
                return item
            })
            .filter(item => item)
    }

    const initData = async () => {
        const params = {
            menuCode: 'B071216'
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode === FINANCE_ORERATE_OPER_PERMISSION.EDIT &&
                            item.display === '1'
                        ) {
                            editShow.value = true
                        }
                        if (
                            item.operateCode === FINANCE_ORERATE_OPER_PERMISSION.PASS &&
                            item.display === '1'
                        ) {
                            passShow.value = true
                        }
                        if (
                            item.operateCode === FINANCE_ORERATE_OPER_PERMISSION.REJECT &&
                            item.display === '1'
                        ) {
                            rejectShow.value = true
                        }
                        if (
                            item.operateCode === FINANCE_ORERATE_OPER_PERMISSION.DELETE &&
                            item.display === '1'
                        ) {
                            deleteShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {},
            catchCB: () => {},
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    // 到编辑页面能看到的数据
    const editdata = ref({
        id: '',
        productType: '',
        fundCode: '',
        fundName: '',
        subFundName: '',
        feeType: '',
        preAmount: '',
        preAmountTax: '',
        realAmount: '',
        realAmountTax: '',
        deviation: '',
        preStartdt: '',
        preEnddt: '',
        auditState: '',
        settlePeriodType: '',
        settlePeriod: '',
        settleYear: '',
        settlePeriodDetail: '',
        remark: '',
        settleStartDt: '',
        settleEndDt: ''
    })

    // 到编辑页面能看到的数据
    const deletedata = ref({
        id: ''
    })

    // 批量删除操作
    const handleBatchDelete = (): void => {
        const taskIdList = tableSelectList.value.map((item: any) => {
            return item.id
        })
        if (taskIdList.length === 0) {
            ElMessage({
                message: '请至少选择一条数据进行删除',
                type: 'warning',
                duration: 2000
            })
            return
        }
        messageBox(
            {
                confirmBtn: '确定',
                content: `确认要删除这些记录吗?`
            },
            () => {
                confirmDelete()
            },
            () => false
        )
    }
    // 确认删除操作
    const confirmDelete = async () => {
        const taskIdList = tableSelectList.value.map((item: any) => {
            return item.id
        })
        const batchDeleteId = {
            taskIdList: taskIdList
        }
        fetchRes(financeAuditlBatchDeleteById(batchDeleteId), {
            successCB: (res: any) => {
                ElMessage({
                    type: 'success',
                    message: '删除成功'
                })
                queryList()
            },
            errorCB: (res: any) => {
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => true,
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }

    // 批量通过操作
    const handleBatchPass = (): void => {
        const taskIdList = tableSelectList.value.map((item: any) => {
            return item.id
        })
        if (taskIdList.length === 0) {
            ElMessage({
                message: '请至少选择一条数据进行删除',
                type: 'warning',
                duration: 2000
            })
            return
        }
        messageBox(
            {
                confirmBtn: '确定',
                content: `确认要批量通过这些记录吗?`
            },
            () => {
                console.log(taskIdList)
                confirmPass()
            },
            () => false
        )
    }
    // 确认通过除操作
    const confirmPass = async () => {
        const taskIdList = tableSelectList.value.map((item: any) => {
            return item.id
        })
        const batchDeleteId = {
            taskIdList: taskIdList,
            auditState: '2'
        }
        fetchRes(financeAuditBatchReview(batchDeleteId), {
            successCB: (res: any) => {
                ElMessage({
                    type: 'success',
                    message: '操作成功'
                })
                queryList()
            },
            errorCB: (res: any) => {
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => true,
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }

    // 批量驳回操作
    const handleBatchReject = (): void => {
        const taskIdList = tableSelectList.value.map((item: any) => {
            return item.id
        })
        if (taskIdList.length === 0) {
            ElMessage({
                message: '请至少选择一条数据进行删除',
                type: 'warning',
                duration: 2000
            })
            return
        }
        messageBox(
            {
                confirmBtn: '确定',
                content: `确认要批量驳回这些记录吗?`
            },
            () => {
                confirmReject()
            },
            () => false
        )
    }
    // 确认通过驳回操作
    const confirmReject = async () => {
        const taskIdList = tableSelectList.value.map((item: any) => {
            return item.id
        })
        const batchDeleteId = {
            taskIdList: taskIdList,
            auditState: '3'
        }
        fetchRes(financeAuditBatchReview(batchDeleteId), {
            successCB: (res: any) => {
                ElMessage({
                    type: 'success',
                    message: '操作成功'
                })
                queryList()
            },
            errorCB: (res: any) => {
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => true,
            successTxt: '',
            failTxt: '',
            fetchKey: ''
        })
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const pdfUrl = ref<string>('')
    const exportList = async () => {
        const params = {
            startDt: queryForm.startDt,
            endDt: queryForm.endDt,
            productType: queryForm.productType,
            fundCode: queryForm.fundCode,
            fundMan: queryForm.fundMan,
            feeType: queryForm.feeType,
            fundName: queryForm.fundName,
            subFundName: queryForm.subFundName,
            custName: queryForm.custName,
            conscustNo: queryForm.conscustNo,
            auditState: queryForm.auditState,
            settlePeriod: queryForm.settlePeriod
        }
        const res: any = await FinanceAuditSettleFeeExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }

    /**
     * @description 页面挂载的时候就初始化信息
     */
    onMounted(() => {
        listLoading.value = true
        initData()
        listLoading.value = false
    })
</script>
<style>
    /* 例如，在您的样式文件中 */
    .deviation-positive {
        color: green;
    }

    .deviation-negative {
        color: red;
    }

    /* 使用更具体的选择器来增加特异性 */
    .custom-button.el-button.is-disabled {
        color: #ffffff !important;

        /* 禁用状态下的样式 */
        background-color: #cccccc !important;
        border-color: #cccccc !important;
    }
</style>
