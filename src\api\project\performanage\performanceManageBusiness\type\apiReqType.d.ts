export {}
declare module './apiReqType' {
    type performanceManageBusinessParam = {
        /**
         * 投顾code
         */
        userId?: string
        /**
         * 部门
         */
        orgCode?: string
        /**
         * 投顾号
         */
        consCode?: string
        /**
         * 考核节点开始时间
         */
        exaimneNodeStart?: string
        /**
         * 考核节点结束时间
         */
        exaimneNodeEnd?: string
        /**
         * 周期 1-试用3M 2-试用6M 3-跟踪-12M 4-正式-年中 5-正式年末 6-观察期-12M以上 7-观察期-12M以内 8-理1B
         */
        periodExplain?: string
        /**
         * 预计考核结果
         */
        exaimneResult?: string
        /**
         * 最终考核结果
         */
        exaimneEndresult?: string
        /**
         * 合格分公司
         */
        competentDepartment?: string
        /**
         * 非观察分公司
         */
        qualifiedDepartment?: string
        /**
         * 是否tp
         */
        istp?: string
    }

    type performanceManageSubTotalBatchUpdateParam = {
        /**
         * 投顾code
         */
        ids?: string[]
        /**
         * 预计新职级
         */
        forecastNewRank?: string
        /**
         * 最终新职级
         */
        newEndRank?: string
        /**
         * 预计考核结果
         */
        forecastExaimneResult?: string
        /**
         * 最终考核结果
         */
        exaimneEndResult?: string
        /**
         * 合格分公司
         */
        competentDepartment?: string
        /**
         * 非观察分公司
         */
        qualifiedDepartment?: string
        /**
         * 备注
         */
        remark?: string
    }

    interface saveResult {
        /**
         * 中心
         */
        u1Name?: string
        /**
         * 区域
         */
        u2Name?: string
        /**
         * 分公司
         */
        u3Name?: string
        /**
         * 管理人员
         */
        consName?: string
        /**
         * 投顾号
         */
        consCode?: string
        /**
         * 投顾code
         */
        userId?: string
        /**
         * 入职日期
         */
        regDt?: string
        /**
         * 转正日期
         */
        regularDt?: string
        /**
         * 管理日期
         */
        promoteDate?: string
        /**
         * 在职状态
         */
        workState?: string
        /**
         * 周期 1-试用3M 2-试用6M 3-跟踪-12M 4-正式-年中 5-正式年末 6-观察期-12M以上 7-观察期-12M以内 8-理1B
         */
        periodExplain?: string
        /**
         * 层级
         */
        userLevel?: string
        /**
         * 当前职级
         */
        curMonthLevel?: string
        /**
         * 开始时间
         */
        startDt?: string
        /**
         * 考核节点
         */
        exaimneNode?: string
        /**
         * 考核节点司龄月（管）
         */
        exaimneMonth?: string
        /**
         * 管理存续D目标
         */
        targetAsset?: string
        /**
         * 管理存续D结果
         */
        resultAsset?: string
        /**
         * 管理存续D目标差距
         */
        gapAsset?: string
        /**
         * 管理月均净新增存续D
         */
        monthAddAsset?: string
        /**
         * 人力目标-本人
         */
        humanTargetSelf?: string
        /**
         * 人力目标-理财师
         */
        humanTargetCons?: string
        /**
         * 人力结果-本人
         */
        humanResultSelf?: string
        /**
         * 人力结果-理财师
         */
        humanResultCons?: string
        /**
         * 预计新职级
         */
        newRank?: string
        /**
         * 预计考核结果
         */
        exaimneResult?: string
        /**
         * 非观察分公司
         */
        qualifiedDepartment?: string
        /**
         * 最终考核结果
         */
        modEndresult?: string
        /**
         * 最终新职级
         */
        modEndrank?: string
        /**
         * 合格分公司
         */
        competentDepartment?: string
        /**
         * 是否tp
         */
        istp?: string
        /**
         * 备注
         */
        remark?: string
    }

    type performanceManageSubTotalSaveParam = {
        orgCode?: string
    }

    type performanceManageSubTotalCalSaveDSumParam = {}

    type querySaveRecordParam = {
        pageType?: string
    }

    export {
        performanceManageBusinessParam,
        performanceManageSubTotalBatchUpdateParam,
        performanceManageSubTotalSaveParam,
        performanceManageSubTotalCalSaveDSumParam,
        querySaveRecordParam
    }
}
