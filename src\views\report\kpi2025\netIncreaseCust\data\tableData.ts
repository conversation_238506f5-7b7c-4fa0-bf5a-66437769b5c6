/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, formatNumber } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const assetRepDownloadTableColumn: TableColumnItem[] = [
    {
        key: 'curU1Name',
        label: '当前投顾所属中心',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'curU2Name',
        label: '当前投顾所属区域',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'curU3Name',
        label: '当前投顾所属分公司',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'curConsname',
        label: '当前投顾姓名',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'curConscode',
        label: '当前投顾编码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'conscustno',
        label: '投顾客户号',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custname',
        label: '客户姓名',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'isNewKpiCust',
        label: '是否KPI新客户',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'kpiAchieveDate',
        label: 'KPI达标时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'hongkongEarliest',
        label: '香港基金最早购买时间',
        width: 170,
        formatter: formatTableValue
    },
    {
        key: 'chuangxinEarliest',
        label: '海外创新最早购买时间',
        width: 170,
        formatter: formatTableValue
    },
    {
        key: 'isNewHkCust',
        label: '是否香港新购买客户',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'hkHisAmt',
        label: '香港基金存量(美元)',
        width: 150,
        formatter: ({ hkHisAmt }: any) => {
            return formatNumber({ num: hkHisAmt })
        }
    },
    {
        key: 'chuangxinHisAmt',
        label: '海外创新存量(美元)',
        width: 170,
        formatter: ({ chuangxinHisAmt }: any) => {
            return formatNumber({ num: chuangxinHisAmt })
        }
    },
    {
        key: 'isNewCust50000Amt',
        label: '是否满足存量要求',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'u1Name',
        label: 'KPI达标时投顾所属中心',
        width: 180,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: 'KPI达标时投顾所属区域',
        width: 180,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: 'KPI达标时投顾所属分公司',
        width: 180,
        formatter: formatTableValue
    },
    {
        key: 'consname',
        label: 'KPI达标时投顾姓名',
        width: 150,
        formatter: formatTableValue
    },
    {
        key: 'conscode',
        label: 'KPI达标时投顾编码',
        width: 150,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
