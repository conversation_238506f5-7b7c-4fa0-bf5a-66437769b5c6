<!--
 * @Description: 产品费率配置通用新增/编辑弹框组件
 * @Author: hongdong.xie
 * @Date: 2025-06-06 09:42:20
 * @LastEditors: hongdong.xie
 * @LastEditTime: 2025-06-06 09:42:20
 * @FilePath: /ds-report-web/src/components/common/ProductFeeRateConfig/ProductFeeRateConfigAdd.vue
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1200px"
        height="600px"
        :border="true"
        :title="title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div class="fof-add-form-container">
            <FormTabs
                v-model="activeTab"
                :tabs-list="formTabs"
                class="form-tabs-container"
            >
                <!-- 基础属性 -->
                <template #basic>
                    <el-form
                        ref="basicFormRef"
                        :model="formData"
                        :rules="formRules"
                        label-width="140px"
                        class="form-section"
                    >
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="备案代码" prop="filingCode">
                                    <crm-input
                                        v-model="formData.filingCode"
                                        placeholder="请输入备案代码(6位)"
                                        :maxlength="6"
                                        :clearable="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="产品code（好买内部）" prop="productCode">
                                    <crm-input
                                        v-model="formData.productCode"
                                        placeholder="请输入产品code(6位)"
                                        :maxlength="6"
                                        :clearable="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="份额代码" prop="shareCode">
                                    <crm-input
                                        v-model="formData.shareCode"
                                        placeholder="请输入份额代码(6位)"
                                        :maxlength="6"
                                        :clearable="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="产品全称" prop="productFullName">
                                    <crm-input
                                        v-model="formData.productFullName"
                                        placeholder="请输入产品全称"
                                        :clearable="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="仅限FOF" prop="fofOnlyFlag">
                                    <crm-select
                                        v-model="formData.fofOnlyFlag"
                                        placeholder="请选择仅限FOF"
                                        :option-list="selectOptions.fofOnlyFlag"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="付款方全称" prop="payerFullName">
                                    <crm-input
                                        v-model="formData.payerFullName"
                                        placeholder="请输入付款方全称"
                                        :clearable="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="产品经理" prop="productManager">
                                    <crm-input
                                        v-model="formData.productManager"
                                        placeholder="请输入产品经理(联系人+联系方式)"
                                        :clearable="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="签约日期">
                                    <crm-date-picker
                                        v-model="formData.signingDate"
                                        placeholder="请选择签约日期"
                                        show-format="YYYY-MM-DD"
                                        value-format="YYYYMMDD"
                                        class-name="date-picker-enhanced"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="好买签约主体" prop="howbuySigningEntity">
                                    <crm-input
                                        v-model="formData.howbuySigningEntity"
                                        placeholder="请输入好买签约主体"
                                        :clearable="true"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="对方联系人" prop="counterpartContact">
                                    <crm-input
                                        v-model="formData.counterpartContact"
                                        placeholder="请输入对方联系人(联系人+联系方式)"
                                        :clearable="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="存续D系数" prop="durationDCoefficient">
                                    <el-input-number
                                        v-model="formData.durationDCoefficient"
                                        placeholder="请输入存续D系数"
                                        :precision="6"
                                        :min="0"
                                        :max="999999"
                                        size="small"
                                        style="width: 100%"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="存续D备注">
                                    <crm-input
                                        v-model="formData.durationDRemark"
                                        placeholder="请输入存续D备注"
                                        :clearable="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="费率开始日期" prop="rateStartDate">
                                    <crm-date-picker
                                        v-model="formData.rateStartDate"
                                        placeholder="请选择费率开始日期"
                                        show-format="YYYY-MM-DD"
                                        value-format="YYYYMMDD"
                                        class-name="date-picker-enhanced"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="费率结束日期" prop="rateEndDate">
                                    <crm-date-picker
                                        v-model="formData.rateEndDate"
                                        placeholder="请选择费率结束日期"
                                        show-format="YYYY-MM-DD"
                                        value-format="YYYYMMDD"
                                        class-name="date-picker-enhanced"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>

                <!-- 阶梯费率 -->
                <template #tiered>
                    <el-form
                        ref="tieredFormRef"
                        :model="formData"
                        :rules="formRules"
                        label-width="140px"
                        class="form-section"
                    >
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="费率配置类型" prop="tieredRateType">
                                    <crm-select
                                        v-model="formData.tieredRateType"
                                        placeholder="请选择费率配置类型"
                                        :option-list="selectOptions.tieredRateType"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                        @change="handleTieredRateTypeChange"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="费率配置生效日" prop="rateEffectiveType">
                                    <crm-select
                                        v-model="formData.rateEffectiveType"
                                        placeholder="请选择费率配置生效日"
                                        :option-list="selectOptions.rateEffectiveType"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row v-if="showConfigLimits" :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="配置下限">
                                    <el-input-number
                                        v-model="formData.configLowerLimit"
                                        placeholder="请输入配置下限"
                                        :min="0"
                                        size="small"
                                        style="width: 100%"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="配置上限">
                                    <el-input-number
                                        v-model="formData.configUpperLimit"
                                        placeholder="请输入配置上限"
                                        :min="0"
                                        size="small"
                                        style="width: 100%"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row v-if="showRateEffectiveDates" :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="费率生效日期">
                                    <crm-date-picker
                                        v-model="formData.rateEffectiveStartDate"
                                        placeholder="请选择费率生效日期"
                                        show-format="YYYY-MM-DD"
                                        value-format="YYYYMMDD"
                                        class-name="date-picker-enhanced"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="费率结束日期">
                                    <crm-date-picker
                                        v-model="formData.rateEffectiveEndDate"
                                        placeholder="请选择费率结束日期"
                                        show-format="YYYY-MM-DD"
                                        value-format="YYYYMMDD"
                                        class-name="date-picker-enhanced"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>

                <!-- 交易管理费率 -->
                <template #tradingManagement>
                    <el-form
                        ref="tradingManagementFormRef"
                        :model="formData"
                        :rules="formRules"
                        label-width="140px"
                        class="form-section"
                    >
                        <!-- 认申购费率区域 -->
                        <div class="rate-section">
                            <div class="rate-section-title">认申购费率</div>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="认申购费率" prop="subscriptionRate">
                                        <el-input-number
                                            v-model="formData.subscriptionRate"
                                            placeholder="请输入认申购费率"
                                            :precision="6"
                                            :min="0"
                                            :max="1"
                                            size="small"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="认申购费备注">
                                        <crm-input
                                            v-model="formData.subscriptionRemark"
                                            placeholder="请输入认申购费备注"
                                            :clearable="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>

                        <!-- 管理费率区域 -->
                        <div class="rate-section">
                            <div class="rate-section-title">管理费率</div>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="管理费率" prop="managementRate">
                                        <el-input-number
                                            v-model="formData.managementRate"
                                            placeholder="请输入管理费率"
                                            :precision="6"
                                            :min="0"
                                            :max="1"
                                            size="small"
                                            style="width: 100%"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="管理费公式">
                                        <crm-input
                                            v-model="formData.managementFormula"
                                            placeholder="请输入管理费公式"
                                            :clearable="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="管理费备注">
                                        <crm-input
                                            v-model="formData.managementRemark"
                                            type="textarea"
                                            placeholder="请输入管理费备注"
                                            :rows="3"
                                            :clearable="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>

                        <!-- 赎回费率区域 -->
                        <div class="rate-section">
                            <div class="rate-section-title">赎回费率</div>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="赎回费率1" prop="redemptionRate1">
                                        <el-input-number
                                            v-model="formData.redemptionRate1"
                                            placeholder="请输入赎回费率1"
                                            :precision="6"
                                            :min="0"
                                            :max="1"
                                            size="small"
                                            style="width: 100%"
                                            @change="handleRedemptionRate1Change"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="赎回费持有天数1">
                                        <el-input-number
                                            v-model="formData.redemptionHoldingDays1"
                                            placeholder="请输入赎回费持有天数1"
                                            :min="0"
                                            size="small"
                                            style="width: 100%"
                                            :disabled="formData.redemptionRate1 === 0"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="赎回费率2">
                                        <el-input-number
                                            v-model="formData.redemptionRate2"
                                            placeholder="请输入赎回费率2"
                                            :precision="6"
                                            :min="0"
                                            :max="1"
                                            size="small"
                                            style="width: 100%"
                                            :disabled="formData.redemptionRate1 === 0"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="赎回费持有天数2">
                                        <el-input-number
                                            v-model="formData.redemptionHoldingDays2"
                                            placeholder="请输入赎回费持有天数2"
                                            :min="0"
                                            size="small"
                                            style="width: 100%"
                                            :disabled="formData.redemptionRate1 === 0"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="赎回费率3">
                                        <el-input-number
                                            v-model="formData.redemptionRate3"
                                            placeholder="请输入赎回费率3"
                                            :precision="6"
                                            :min="0"
                                            :max="1"
                                            size="small"
                                            style="width: 100%"
                                            :disabled="formData.redemptionRate1 === 0"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="赎回费持有天数3">
                                        <el-input-number
                                            v-model="formData.redemptionHoldingDays3"
                                            placeholder="请输入赎回费持有天数3"
                                            :min="0"
                                            size="small"
                                            style="width: 100%"
                                            :disabled="formData.redemptionRate1 === 0"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="赎回费率4">
                                        <el-input-number
                                            v-model="formData.redemptionRate4"
                                            placeholder="请输入赎回费率4"
                                            :precision="6"
                                            :min="0"
                                            :max="1"
                                            size="small"
                                            style="width: 100%"
                                            :disabled="formData.redemptionRate1 === 0"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="赎回费持有天数4">
                                        <el-input-number
                                            v-model="formData.redemptionHoldingDays4"
                                            placeholder="请输入赎回费持有天数4"
                                            :min="0"
                                            size="small"
                                            style="width: 100%"
                                            :disabled="formData.redemptionRate1 === 0"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="赎回费公式">
                                        <crm-input
                                            v-model="formData.redemptionFormula"
                                            placeholder="请输入赎回费公式"
                                            :clearable="true"
                                            :disabled="formData.redemptionRate1 === 0"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="赎回费持有天数计算规则">
                                        <crm-select
                                            v-model="formData.redemptionHoldingCalcRule"
                                            placeholder="请选择赎回费持有天数计算规则"
                                            :option-list="selectOptions.redemptionHoldingCalcRule"
                                            label-format="label"
                                            value-format="key"
                                            :clearable="true"
                                            :disabled="formData.redemptionRate1 === 0"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="赎回费特例">
                                        <crm-select
                                            v-model="formData.redemptionSpecialRule"
                                            placeholder="请选择赎回费特例"
                                            :option-list="selectOptions.redemptionSpecialRule"
                                            label-format="label"
                                            value-format="key"
                                            :clearable="true"
                                            :disabled="formData.redemptionRate1 === 0"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="赎回费备注">
                                        <crm-input
                                            v-model="formData.redemptionRemark"
                                            type="textarea"
                                            placeholder="请输入赎回费备注"
                                            :rows="3"
                                            :clearable="true"
                                        />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-form>
                </template>

                <!-- 业绩报酬 -->
                <template #performance>
                    <el-form
                        ref="performanceFormRef"
                        :model="formData"
                        :rules="formRules"
                        label-width="140px"
                        class="form-section"
                    >
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="业绩报酬分成费率1" prop="performanceSharingRate1">
                                    <el-input-number
                                        v-model="formData.performanceSharingRate1"
                                        placeholder="请输入业绩报酬分成费率1"
                                        :precision="6"
                                        :min="0"
                                        :max="1"
                                        size="small"
                                        style="width: 100%"
                                        @change="handlePerformanceRate1Change"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="业绩报酬费率1">
                                    <el-input-number
                                        v-model="formData.performanceRate1"
                                        placeholder="请输入业绩报酬费率1"
                                        :precision="6"
                                        :min="0"
                                        :max="1"
                                        size="small"
                                        style="width: 100%"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item label="业绩报酬计提基准1">
                                    <crm-input
                                        v-model="formData.performanceBenchmark1"
                                        placeholder="请输入业绩报酬计提基准1"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <!-- 业绩报酬费率2 -->
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="业绩报酬分成费率2">
                                    <el-input-number
                                        v-model="formData.performanceSharingRate2"
                                        placeholder="请输入业绩报酬分成费率2"
                                        :precision="6"
                                        :min="0"
                                        :max="1"
                                        size="small"
                                        style="width: 100%"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="业绩报酬费率2">
                                    <el-input-number
                                        v-model="formData.performanceRate2"
                                        placeholder="请输入业绩报酬费率2"
                                        :precision="6"
                                        :min="0"
                                        :max="1"
                                        size="small"
                                        style="width: 100%"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item label="业绩报酬计提基准2">
                                    <crm-input
                                        v-model="formData.performanceBenchmark2"
                                        placeholder="请输入业绩报酬计提基准2"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <!-- 业绩报酬费率3 -->
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="业绩报酬分成费率3">
                                    <el-input-number
                                        v-model="formData.performanceSharingRate3"
                                        placeholder="请输入业绩报酬分成费率3"
                                        :precision="6"
                                        :min="0"
                                        :max="1"
                                        size="small"
                                        style="width: 100%"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="业绩报酬费率3">
                                    <el-input-number
                                        v-model="formData.performanceRate3"
                                        placeholder="请输入业绩报酬费率3"
                                        :precision="6"
                                        :min="0"
                                        :max="1"
                                        size="small"
                                        style="width: 100%"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item label="业绩报酬计提基准3">
                                    <crm-input
                                        v-model="formData.performanceBenchmark3"
                                        placeholder="请输入业绩报酬计提基准3"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="业绩报酬计提类型">
                                    <crm-select
                                        v-model="formData.performanceAccrualType"
                                        placeholder="请选择业绩报酬计提类型"
                                        :option-list="selectOptions.performanceAccrualType"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="业绩报酬计提形式">
                                    <crm-select
                                        v-model="formData.performanceAccrualForm"
                                        placeholder="请选择业绩报酬计提形式"
                                        :option-list="selectOptions.performanceAccrualForm"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <!-- 固定计提日配置 -->
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="固定计提日月份类型">
                                    <crm-select
                                        v-model="formData.fixedAccrualMonthType"
                                        placeholder="请选择固定计提日月份类型"
                                        :option-list="selectOptions.fixedAccrualMonthType"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="固定计提日日期类型">
                                    <crm-select
                                        v-model="formData.fixedAccrualDateType"
                                        placeholder="请选择固定计提日日期类型"
                                        :option-list="selectOptions.fixedAccrualDateType"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="固定计提日计算类型">
                                    <crm-select
                                        v-model="formData.fixedAccrualCalcType"
                                        placeholder="请选择固定计提日计算类型"
                                        :option-list="selectOptions.fixedAccrualCalcType"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="赎回业绩报酬持有天数规则">
                                    <crm-select
                                        v-model="formData.redemptionHoldingDaysRule"
                                        placeholder="请选择赎回业绩报酬持有天数规则"
                                        :option-list="selectOptions.redemptionHoldingDaysRule"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="分红业绩报酬持有天数规则">
                                    <crm-select
                                        v-model="formData.dividendHoldingDaysRule"
                                        placeholder="请选择分红业绩报酬持有天数规则"
                                        :option-list="selectOptions.dividendHoldingDaysRule"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="业绩报酬公式">
                                    <crm-select
                                        v-model="formData.performanceFormula"
                                        placeholder="请选择业绩报酬公式"
                                        :option-list="selectOptions.performanceFormula"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="份额锁定期类型">
                                    <crm-select
                                        v-model="formData.shareLockType"
                                        placeholder="请选择份额锁定期类型"
                                        :option-list="selectOptions.shareLockType"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="份额锁定期天数">
                                    <el-input-number
                                        v-model="formData.shareLockDays"
                                        placeholder="请输入份额锁定期天数"
                                        :min="0"
                                        size="small"
                                        style="width: 100%"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="基金封闭期类型">
                                    <crm-select
                                        v-model="formData.fundClosedType"
                                        placeholder="请选择基金封闭期类型"
                                        :option-list="selectOptions.fundClosedType"
                                        label-format="label"
                                        value-format="key"
                                        :clearable="true"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="基金封闭期天数">
                                    <el-input-number
                                        v-model="formData.fundClosedDays"
                                        placeholder="请输入基金封闭期天数"
                                        :min="0"
                                        size="small"
                                        style="width: 100%"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="不计提净值基准">
                                    <el-input-number
                                        v-model="formData.noAccrualNavBenchmark"
                                        placeholder="请输入不计提净值基准"
                                        :precision="6"
                                        :min="0"
                                        size="small"
                                        style="width: 100%"
                                        :disabled="formData.performanceSharingRate1 === 0"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item label="业绩报酬备注">
                                    <crm-input
                                        v-model="formData.performanceRemark"
                                        type="textarea"
                                        placeholder="请输入业绩报酬备注"
                                        :rows="3"
                                        :clearable="true"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </template>
            </FormTabs>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <crm-button @click="handleCancel">取消</crm-button>
                <crm-button type="primary" :loading="submitLoading" @click="handleSubmit">
                    {{ submitLoading ? '提交中...' : '确定' }}
                </crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { computed, watch } from 'vue'
    import { ElMessage } from 'element-plus'
    import type { ProductFeeRateConfig, DialogType } from './types'
    import { useProductFeeRateForm } from './composables/useProductFeeRateForm'
    import { useProductFeeRateApi } from './composables/useProductFeeRateApi'

    // 组件props定义
    interface Props {
        visible?: boolean
        config: ProductFeeRateConfig
        dialogType?: DialogType
        editData?: any
    }

    const props = withDefaults(defineProps<Props>(), {
        visible: false,
        dialogType: 'add',
        editData: null
    })

    // 组件emits定义
    const emit = defineEmits<{
        'update:visible': [value: boolean]
        'success': []
    }>()

    // 使用表单逻辑hooks
    const {
        // 响应式数据
        activeTab,
        basicFormRef,
        tieredFormRef,
        tradingManagementFormRef,
        performanceFormRef,
        formData,
        selectOptions,
        formTabs,
        formRules,

        // 计算属性
        showConfigLimits,
        showRateEffectiveDates,

        // 方法
        resetForm,
        fillFormData,
        handleTieredRateTypeChange,
        handleRedemptionRate1Change,
        handlePerformanceRate1Change,
        validateFormData
    } = useProductFeeRateForm(props.config.rateType)

    // 使用API操作hooks
    const { submitLoading, submitData } = useProductFeeRateApi(props.config)

    // 弹框显示状态
    const dialogVisible = computed({
        get: () => props.visible,
        set: (value: boolean) => {
            emit('update:visible', value)
        }
    })

    // 弹框标题
    const title = computed(() => {
        const titles = props.config.pageConfig.dialogTitle
        switch (props.dialogType) {
            case 'add':
                return titles.add
            case 'copyAdd':
                return titles.copyAdd
            case 'edit':
                return titles.edit
            case 'audit':
                return titles.audit
            default:
                return titles.add
        }
    })

    // 监听编辑数据变化
    watch(
        () => props.editData,
        (newData) => {
            console.log('📝 数据监听调试信息:')
            console.log('操作类型:', props.dialogType)
            console.log('传入数据 newData:', newData)

            if (newData && props.visible) {
                fillFormData(newData, props.dialogType)
            }
        },
        { immediate: true, deep: true }
    )

    // 监听弹框显示状态
    watch(
        () => props.visible,
        (newVisible) => {
            if (newVisible) {
                if (props.editData) {
                    fillFormData(props.editData, props.dialogType)
                } else if (props.dialogType === 'add') {
                    resetForm()
                }
            } else {
                // 弹框关闭时重置表单
                resetForm()
            }
        }
    )

    /**
     * @description: 取消操作
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @return {void}
     */
    const handleCancel = (): void => {
        dialogVisible.value = false
    }

    /**
     * @description: 提交操作
     * @author: hongdong.xie
     * @date: 2025-06-06 09:42:20
     * @return {Promise<void>}
     */
    const handleSubmit = async (): Promise<void> => {
        console.log('🚀 开始提交表单数据...')
        console.log('操作类型:', props.dialogType)
        console.log('表单数据:', formData)

        // 表单验证
        const isValid = await validateFormData()
        if (!isValid) {
            console.log('❌ 表单验证失败')
            return
        }

        // 提交数据
        const success = await submitData(props.dialogType, formData)
        if (success) {
            console.log('✅ 提交成功')
            dialogVisible.value = false
            emit('success')
        }
    }
</script>

<style lang="less" scoped>
    .fof-add-form-container {
        height: 480px; // 增加高度，确保基础属性标签页无需滚动，优化用户体验 @author: hongdong.xie @date: 2025-06-05 15:43:50
        overflow: hidden;

        .form-tabs-container {
            height: 100%;

            :deep(.el-tabs__content) {
                height: calc(100% - 40px);
                overflow-y: auto;
                padding: 20px;
            }

            .form-section {
                .el-form-item {
                    margin-bottom: 14px; // 增加表单项间距，为错误提示留出足够空间 @author: hongdong.xie @date: 2025-06-05 15:43:50

                    .el-form-item__label {
                        font-weight: 500;
                        color: #303133;
                    }
                }

                .el-row {
                    margin-bottom: 12px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }

                // 日期选择器所在行的特殊样式
                .el-row:has(.date-picker-enhanced) {
                    .el-col {
                        display: flex;
                        align-items: center;

                        .el-form-item {
                            width: 100%;
                            // 移除margin-bottom: 0，保持与其他表单项一致的间距 @author: hongdong.xie @date: 2025-06-05 15:43:50
                        }
                    }
                }
            }
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        padding: 10px 0;
    }

    // 日期选择器美化样式
    :deep(.date-picker-enhanced) {
        width: 100%;

        &.el-date-editor {
            width: 100%;

            .el-input__wrapper {
                width: 100%;
                min-width: 180px;
                padding: 1px 11px;
                border-radius: 4px;
                transition: all 0.3s ease;

                &:hover {
                    border-color: #c0c4cc;
                }

                &.is-focus {
                    border-color: #409eff;
                    box-shadow: 0 0 0 2px fade(#409eff, 20%);
                }
            }

            .el-input__inner {
                height: 32px;
                line-height: 32px;
                font-size: 14px;
                color: #606266;
                text-align: left;
                padding: 0 8px;

                &::placeholder {
                    color: #c0c4cc;
                    font-size: 14px;
                }
            }

            .el-input__suffix {
                .el-input__suffix-inner {
                    .el-icon {
                        color: #c0c4cc;
                        font-size: 14px;

                        &:hover {
                            color: #909399;
                        }
                    }
                }
            }
        }

        &.is-disabled {
            .el-input__wrapper {
                background-color: #f5f7fa;
                border-color: #e4e7ed;
                cursor: not-allowed;

                .el-input__inner {
                    color: #c0c4cc;
                    cursor: not-allowed;
                }
            }
        }
    }

    // 响应式设计优化
    @media (max-width: 768px) {
        .fof-add-form-container {
            .form-tabs-container {
                :deep(.el-tabs__content) {
                    padding: 15px;
                }

                .form-section {
                    .el-row {
                        .el-col {
                            &[class*="span-12"] {
                                flex: 0 0 100%;
                                max-width: 100%;
                            }
                        }
                    }
                }
            }
        }

        :deep(.date-picker-enhanced) {
            .el-input__wrapper {
                min-width: 150px;
            }
        }
    }

    // 表单项间距优化
    .form-section {
        :deep(.el-form-item__content) {
            display: flex;
            align-items: center;
            min-height: 32px;
        }

        // 表单验证错误状态样式优化
        :deep(.el-form-item.is-error) {
            .el-input__wrapper {
                border-color: #f56c6c;
                box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
                animation: shake 0.3s ease-in-out;
            }

            .el-select .el-select__wrapper {
                border-color: #f56c6c;
                box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
                animation: shake 0.3s ease-in-out;
            }

            .el-textarea__inner {
                border-color: #f56c6c;
                box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
                animation: shake 0.3s ease-in-out;
            }

            .el-form-item__error {
                font-size: 12px;
                color: #f56c6c;
                margin-top: 4px; // 增加错误提示与输入框的间距 @author: hongdong.xie @date: 2025-06-05 15:43:50
                //line-height: 1.4; // 增加行高，确保多行错误文字不重叠 @author: hongdong.xie @date: 2025-06-05 15:43:50
                animation: fadeIn 0.3s ease-in-out;
                display: block; // 确保错误提示独占一行 @author: hongdong.xie @date: 2025-06-05 15:43:50
                //word-wrap: break-word; // 长文本自动换行 @author: hongdong.xie @date: 2025-06-05 15:43:50
            }
        }

        // 聚焦状态的增强样式
        :deep(.el-input__wrapper.is-focus) {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        :deep(.el-select .el-select__wrapper.is-focused) {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        :deep(.el-textarea__inner:focus) {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        // 费率区域分组样式
        .rate-section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 6px;
            border: 1px solid #e4e7ed;

            &:last-child {
                margin-bottom: 0;
            }

            .rate-section-title {
                font-size: 16px;
                font-weight: 600;
                color: #303133;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #409eff;
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: -2px;
                    width: 30px;
                    height: 2px;
                    background-color: #409eff;
                }
            }

            .el-row {
                margin-bottom: 16px;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .el-form-item {
                margin-bottom: 10px; // 为费率区域的表单项增加间距，避免错误提示被遮挡 @author: hongdong.xie @date: 2025-06-05 15:43:50

                // &:last-child {
                //     margin-bottom: 0; // 最后一个表单项不需要下边距 @author: hongdong.xie @date: 2025-06-05 15:43:50
                // }
            }
        }
    }
</style>

<style lang="less">
// 动画效果
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

@keyframes fadeIn {
    0% { opacity: 0; transform: translateY(-5px); }
    100% { opacity: 1; transform: translateY(0); }
}
</style>
