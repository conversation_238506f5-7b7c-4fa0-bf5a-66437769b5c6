/*
 * @Description: table表格展示
 * @Author: gang.zou
 * @Date: 2024-05-09 17:14:42
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatNumber, formatTableValue } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const stockProductFeeTableColumn: TableColumnItem[] = [
    {
        key: 'fundCode',
        label: '产品代码',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundName',
        label: '产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'subFundName',
        label: '所投子基金名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'manager',
        label: '管理人',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'startDt',
        label: '计提开始日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'endDt',
        label: '计提结束日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'feeType',
        label: '费用类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'feeTax',
        label: '计提金额',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fee',
        label: '去税计提金额',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ageSubject',
        label: '协议主体',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'realAmountTax',
        label: '实际结算金额',
        width: 120,
        formatter: ({ realAmountTax }: any) => {
            return formatNumber({ num: realAmountTax })
        }
    },
    {
        key: 'realAmount',
        label: '去税实际结算金额',
        width: 120,
        formatter: ({ realAmount }: any) => {
            return formatNumber({ num: realAmount })
        }
    },
    {
        key: 'deviation',
        label: '误差(%)',
        width: 120,
        formatter: ({ deviation }: any) => {
            if (deviation == null) {
                // 如果为 null，返回默认值
                return '-'
            }
            return `${deviation}%`
        }
    },
    {
        key: 'settlePeriod',
        label: '结算周期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'auditState',
        label: '审核状态',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
