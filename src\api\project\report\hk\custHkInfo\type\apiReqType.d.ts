export {}
declare module './apiReqType' {
    type queryCustHkInfoParam = {
        /**
         * 香港客户号
         */
        hkCustNo?: string
        /**
         * 手机号码
         */
        mobile?: string

        /**
         * 证件号码
         */
        idNo?: string

        /**
         * ebrokerID
         */
        ebrokerId?: string

        /**
         * 一账通号
         */
        hboneNo?: string

        /**
         * 投顾客户号
         */
        conscustno?: string

        /**
         * 香港开户日期
         */
        openDateStart?: string
        /**
         * 香港开户日期
         */
        openDateEnd?: string

        /**
         * 香港客户状态0-正常 1-注销  2-休眠  3-注册  4-开户申请成功
         */
        custStat?: string

        /**
         * 客户类型
         */
        invstType?: string

        /**
         * 投资者类型（香港）
         */
        investorQualification?: string

        /**
         * 专业投资者认证日期
         */
        investorQualificationDateStart?: string
        /**
         * 专业投资者认证日期
         */
        investorQualificationDateEnd?: string

        /**
         * 风险等级
         */
        riskToleranceLevel?: string

        /**
         * 风险测评日期
         */
        kycDtStart?: string
        /**
         * 风险测评日期
         */
        kycDtEnd?: string

        /**
         * 所属部门
         */
        orgCode?: string

        /**
         * 当前投顾代码
         */
        consCode?: string

        /**
         * 资产证明到期日期
         */
        assetCertExpiredDateStart?: string

        /**
         * 资产证明到期日期
         */
        assetCertExpiredDateEnd?: string

        /**
         * 是否有衍生工具知识
         */
        derivativeKnowledge?: string

        /**
         * 注册日期
         */
        regTimeStart?: string

        /**
         * 注册日期
         */
        regTimeEnd?: string

        /**
         * 开户渠道
         */
        openChannel?: string

        /**
         * 线上开户资料提交时间
         */
        submitTimeStart?: string

        /**
         * 线上开户资料提交时间
         */
        submitTimeEnd?: string

        /**
         * 线上开户资料审核通过时间
         */
        passDtStart?: string

        /**
         * 线上开户资料审核通过时间
         */
        passDtEnd?: string

        /**
         * 入金审核提交时间
         */
        depositSubmitTimeStart?: string

        /**
         * 入金审核提交时间
         */
        depositSubmitTimeEnd?: string

        /**
         * 入金审核通过时间
         */
        depositPassDtStart?: string

        /**
         * 入金审核通过时间
         */
        depositPassDtEnd?: string

        /**
         * 入金渠道
         */
        depositFundChannel?: string
        /**
         * 储蓄罐签约状态
         */
        signState?: string
        /**
         * 海外储蓄罐签约日期
         */
        agreementSignDtStart?: string
        /**
         * 海外储蓄罐签约日期
         */
        agreementSignDtEnd?: string

        /**
         * 海外储蓄罐签署方式
         */
        agreementSignType?: string
    }

    type viewDetailParam = {
        /**
         * 香港客户号
         */
        hkCustNo: string
    }

    type saveUserShowColumnRequest = {
        /**
         * 列名串
         */
        columnNameArr?: string[]
    }

    type viewRealTextRequest = {
        /**
         * 香港客户号
         */
        hkCustNo: string

        /**
         * 类型 1-手机号 2-证件号码
         */
        type: string

        /**
         * 文本
         */
        text: string
    }

    export { queryCustHkInfoParam, viewDetailParam, saveUserShowColumnRequest, viewRealTextRequest }
}
