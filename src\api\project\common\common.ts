import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import { TableColumnItem } from '@/type/tableColumn'

/**
/**
 * @description: 查询投顾列表
 * @param {object} params
 * @return {*}
 */
export const queryConsultant = (params: {
    orgCode: string
    status?: string
    recursive?: string //是否为递归查询 组织架构下的投顾 1-是 0-否。 默认1-是
}) => {
    return axiosRequest(
        paramsMerge({
            url: '/crmacct/consultant/queryconsultant',
            method: 'post',
            data: params,
            baseURL: window._msApiPrefixCGI,
            canclePending: true,
            loadingParams: {
                target: '.elForm'
            }
        })
    )
}

/**
 * @description: 查询当前登录用户，根据权限可见的部门机构列表
 * @param {*} params
 * @return {*}
 */
export const queryAuthOrgTree = (params: { module?: string; notAuthor?: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/crmacct/organization/queryauthorgtree2',
            method: 'post',
            data: params,
            baseURL: window._msApiPrefixCGI,
            loadingParams: {
                isCust: true
            }
        })
    )
}

/**
 * @description: 查询所有的部门机构列表
 * @param {object} params
 * @return {*}
 */
export const queryAllOrgTree = (params: {}) => {
    return axiosRequest(
        paramsMerge({
            url: '/crmacct/organization/queryallorgtree',
            method: 'post',
            data: params,
            baseURL: window._msApiPrefixCGI,
            loadingParams: {
                isCust: true
            }
        })
    )
}
/**
 * @description: 查菜单下用户拥有的操作权限列表
 * @return {*}
 */
export const getMenuPermission = (params: { menuCode: string }) => {
    return axiosRequest(
        paramsMerge({
            url: '/crmacct/pageauth/fetchauth',
            method: 'post',
            baseURL: window._msApiPrefixCGI,
            data: params
        })
    )
}
/**
 * @description: 产品名称自动补全
 * @return {*}
 */
export const autoProd = (params: {}) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/common/autoproduct/autoproduct',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 产品名称自动补全
 * @return {*}
 */
export const autoAllProd = (params: {}) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/common/autoproduct/autoallproduct',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    if (!keyArr) {
        return tableColumnList
    }
    const actualTableColumnList: TableColumnItem[] = []
    keyArr.forEach(item => {
        const tableColumn = tableColumnList.find(i => i.key === item)
        if (tableColumn) {
            actualTableColumnList.push(tableColumn)
        } else {
            console.log('tableColumn is undefined, skipped.')
        }
    })
    return actualTableColumnList
}

export const fetchConstant = (params: { typeCodeList: string[] }) => {
    return axiosRequest(
        paramsMerge({
            url: '/hbConstant/fetchConstant',
            method: 'post',
            data: params
        })
    )
}
