/*
 * @Description: table表格展示
 * @Author: gang.zou
 * @Date: 2024-04-17 17:14:42
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, formatTableValueBlank, formatNumber } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const assetRepDownloadTableColumn: TableColumnItem[] = [
    {
        key: 'u1Name',
        label: '中心',
        width: 110,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '投顾',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'consCode',
        label: '员工编码',
        width: 110,
        formatter: formatTableValue
    },
    {
        key: 'cl2024Cnt',
        label: '2024年底存量客户数',
        width: 130,
        formatter: formatTableValueBlank
    },
    {
        key: 'isKpi',
        label: '是否参与考核',
        width: 100,
        formatter: formatTableValue
    },
    {
        key: 'hkTarget',
        label: '好买香港(美元)-目标',
        width: 135,
        formatter: ({ hkTarget }: any) => {
            return formatNumber({ num: hkTarget })
        }
    },
    {
        key: 'hkResult',
        label: '好买香港(美元)-结果',
        width: 135,
        formatter: ({ hkResult }: any) => {
            return formatNumber({ num: hkResult })
        }
    },
    {
        key: 'nonATarget',
        label: '非A(RMB)-目标',
        width: 120,
        formatter: ({ nonATarget }: any) => {
            return formatNumber({ num: nonATarget })
        }
    },
    {
        key: 'nonAResult',
        label: '非A(RMB)-结果',
        width: 120,
        formatter: ({ nonAResult }: any) => {
            return formatNumber({ num: nonAResult })
        }
    },
    {
        key: 'newCustTarget',
        label: '新增客户-目标',
        width: 110,
        formatter: formatTableValueBlank
    },
    {
        key: 'newCustResult',
        label: '新增客户-结果',
        width: 110,
        formatter: formatTableValueBlank
    },
    {
        key: 'wechatTarget',
        label: '存量客户企微添加率-目标',
        width: 160,
        formatter: formatTableValueBlank
    },
    {
        key: 'wechatResult',
        label: '存量客户企微添加率-结果',
        width: 160,
        formatter: formatTableValueBlank
    },
    {
        key: 'bxTarget',
        label: '创新继续率-目标',
        width: 115,
        formatter: formatTableValueBlank
    },
    {
        key: 'bxResult',
        label: '创新继续率-结果',
        width: 115,
        formatter: formatTableValueBlank
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
