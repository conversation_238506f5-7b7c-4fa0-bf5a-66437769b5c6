<!--
 * @Description: 添加产品弹框
 * @Author: chaohui.wu
 * @Date: 2023-03-24 21:07:35
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-27 19:14:16
 * @FilePath: /crm-template/src/views/components/dialogCommon/adjustAmt/adjustAmtIndex.vue
 * @ 当前未解耦，有时间解耦后可提取到dialogCommomn中
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        height="500px"
        :border="true"
        :title="title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <!-- 产品费率的弹框 -->
            <el-form
                v-if="fixIncomeConfProductAdd"
                ref="ruleFormRef"
                :model="ruleForm"
                label-width="100px"
                :rules="rules"
                status-icon
            >
                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="fundCode" label="产品代码" style="margin-left: 40%">
                            <crm-input
                                v-model="ruleForm.fundCode"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="rateStartDate"
                            label-width="auto"
                            label="费率开始日期"
                            style="margin-left: 38.5%"
                        >
                            <el-date-picker
                                v-model="ruleForm.rateStartDate"
                                class="popperClass"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                type="date"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item
                            prop="closedPeriodDays"
                            label="封闭期天数"
                            style="margin-left: 40%"
                        >
                            <crm-input
                                v-model="ruleForm.closedPeriodDays"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item prop="agarement" label="协议主体" style="margin-left: 40%">
                            <crm-input
                                v-model="ruleForm.agarement"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="fundName" label="产品名称" style="margin-left: 18%">
                            <crm-input
                                v-model="ruleForm.fundName"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="rateEndDate"
                            label-width="auto"
                            label="费率结束日期"
                            style="margin-left: 16.5%"
                        >
                            <el-date-picker
                                v-model="ruleForm.rateEndDate"
                                class="popperClass"
                                type="date"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item prop="selfCust" label="自有客户" style="margin-left: 18%">
                            <crm-select
                                v-model="ruleForm.selfCust"
                                :placeholder="selfCustList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="selfCustList.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="manageFormula"
                            label="管理费公式"
                            style="margin-left: 40%"
                        >
                            <crm-select
                                v-model="ruleForm.manageFormula"
                                :placeholder="manageFormulaList2.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="manageFormulaList2.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="manageRate" label="管理费率" style="margin-left: 18%">
                            <crm-input
                                v-model="ruleForm.manageRate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="performanceFormula"
                            label="业绩报酬公式"
                            style="margin-left: 40%"
                        >
                            <crm-input
                                v-model="ruleForm.performanceFormula"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="hbPerformanceRate1"
                            label-width="auto"
                            label="好买业绩报酬费率1"
                            style="margin-left: 32%"
                        >
                            <crm-input
                                v-model="ruleForm.hbPerformanceRate1"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="performanceRate1"
                            label-width="auto"
                            label="业绩报酬费率1"
                            style="margin-left: 39%"
                        >
                            <crm-input
                                v-model="ruleForm.performanceRate1"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="performanceBase1"
                            label-width="auto"
                            label="业绩报酬基准1"
                            style="margin-left: 39%"
                        >
                            <crm-input
                                v-model="ruleForm.performanceBase1"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item prop="shareDate" label="分红日期" style="margin-left: 40%">
                            <crm-select
                                v-model="ruleForm.shareDate"
                                :placeholder="isShareDate2.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="isShareDate2.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item prop="settleDate" label="清算日期" style="margin-left: 40%">
                            <crm-select
                                v-model="ruleForm.settleDate"
                                :placeholder="isSettleDate2.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="isSettleDate2.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="hswType" label="高水位类型" style="margin-left: 18%">
                            <crm-select
                                v-model="ruleForm.hswType"
                                :placeholder="hswTypeList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="hswTypeList.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item
                            prop="hbPerformanceRate2"
                            label-width="auto"
                            label="好买业绩报酬费率2"
                            style="margin-left: 11.5%"
                        >
                            <crm-input
                                v-model="ruleForm.hbPerformanceRate2"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="performanceRate2"
                            label-width="auto"
                            label="业绩报酬费率2"
                            style="margin-left: 17%"
                        >
                            <crm-input
                                v-model="ruleForm.performanceRate2"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="performanceBase2"
                            label-width="auto"
                            label="业绩报酬基准2"
                            style="margin-left: 17%"
                        >
                            <crm-input
                                v-model="ruleForm.performanceBase2"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item prop="redemDate" label="赎回日期" style="margin-left: 18%">
                            <crm-select
                                v-model="ruleForm.redemDate"
                                :placeholder="isRedemDate2.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="isRedemDate2.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item
                            prop="performancejtType"
                            label-width="auto"
                            label="业绩报酬计提方式"
                            style="margin-left: 13.5%"
                        >
                            <crm-select
                                v-model="ruleForm.performancejtType"
                                :placeholder="performancejtTypeList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="performancejtTypeList.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="redemFormula"
                            label="赎回费公式"
                            style="margin-left: 40%"
                        >
                            <crm-select
                                v-model="ruleForm.redemFormula"
                                :placeholder="redemFormulaList2.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="redemFormulaList2.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item prop="redemDay" label="赎回天数" style="margin-left: 40%">
                            <crm-input
                                v-model="ruleForm.redemDay"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="redemRate" label="赎回费率" style="margin-left: 18%">
                            <crm-input
                                v-model="ruleForm.redemRate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="consultFormula"
                            label="咨询费公式"
                            style="margin-left: 40%"
                        >
                            <crm-select
                                v-model="ruleForm.consultFormula"
                                :placeholder="consultFormulaList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="consultFormulaList.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item
                            prop="valueStartDate"
                            label="产品起息日"
                            style="margin-left: 40%"
                        >
                            <el-date-picker
                                v-model="ruleForm.valueStartDate"
                                class="popperClass"
                                type="date"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item prop="adjustAmount" label="调整金额" style="margin-left: 40%">
                            <crm-input
                                v-model="ruleForm.adjustAmount"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="settleStartDate"
                            label="结算起始日期"
                            style="margin-left: 40%"
                        >
                            <el-date-picker
                                v-model="ruleForm.settleStartDate"
                                class="popperClass"
                                type="date"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>

                        <el-form-item prop="yearDay" label="年化天数" style="margin-left: 40%">
                            <crm-input
                                v-model="ruleForm.yearDay"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="consultRate" label="咨询费率" style="margin-left: 18%">
                            <crm-input
                                v-model="ruleForm.consultRate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="valueEndDate"
                            label="产品到期日"
                            style="margin-left: 18%"
                        >
                            <el-date-picker
                                v-model="ruleForm.valueEndDate"
                                class="popperClass"
                                type="date"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item prop="countDay" label="计费天数" style="margin-left: 18%">
                            <crm-input
                                v-model="ruleForm.countDay"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="settleEndDate"
                            label="结算结束日期"
                            style="margin-left: 18%"
                        >
                            <el-date-picker
                                v-model="ruleForm.settleEndDate"
                                class="popperClass"
                                type="date"
                                format="YYYYMMDD"
                                value-format="YYYYMMDD"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="remark" label="备注" style="margin-left: 40%">
                            <crm-input
                                v-model="ruleForm.remark"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12"> </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 43%">
                            <el-button type="primary" @click="submitForm(ruleFormRef)">
                                提交
                            </el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item style="margin-left: 10%">
                            <el-button type="primary" @click="resetForm(ruleFormRef)"
                                >重置</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { fetchRes } from '@/utils/index'
    import { dataList } from './data/labelData'
    import { fixIncomeConfProductRateInsert } from '@/api/project/report/finance/fixIncomeConfProductRate/fixIncomeConfProductRate'
    const {
        isRedemDate2,
        isSettleDate2,
        isShareDate2,
        manageFormulaList2,
        redemFormulaList2,
        consultFormulaList,
        selfCustList,
        hswTypeList,
        performancejtTypeList
    } = dataList
    const loadingFlag = ref<boolean>(false)
    // 表单数据共用的实体类
    interface RuleForm {
        fundCode: string
        fundName: string
        valueStartDate: string
        valueEndDate: string
        countDay: string
        yearDay: string
        consultRate: string
        manageRate: string
        redemRate: string
        redemDay: string
        consultFormula: string
        manageFormula: string
        redemFormula: string
        adjustAmount: string
        selfCust: string
        agarement: string
        settleStartDate: string
        settleEndDate: string
        remark: string
        performanceFormula: string
        hswType: string
        hbPerformanceRate1: string
        hbPerformanceRate2: string
        performanceRate1: string
        performanceRate2: string
        performancejtType: string
        redemDate: string
        settleDate: string
        shareDate: string
        closedPeriodDays: string
        performanceBase1: string
        performanceBase2: string
        rateStartDate: string
        rateEndDate: string
    }

    const ruleFormRef = ref<FormInstance>()
    const ruleForm = reactive<RuleForm>({
        fundCode: '',
        fundName: '',
        valueStartDate: '',
        valueEndDate: '',
        countDay: '',
        yearDay: '',
        consultRate: '',
        manageRate: '',
        redemRate: '',
        redemDay: '',
        consultFormula: '',
        manageFormula: '',
        redemFormula: '',
        adjustAmount: '',
        selfCust: '',
        agarement: '',
        settleStartDate: '',
        settleEndDate: '',
        remark: '',
        performanceFormula: '',
        hswType: '',
        hbPerformanceRate1: '',
        hbPerformanceRate2: '',
        performanceRate1: '',
        performanceRate2: '',
        performancejtType: '',
        redemDate: '',
        settleDate: '',
        shareDate: '',
        closedPeriodDays: '',
        performanceBase1: '',
        performanceBase2: '',
        rateStartDate: '20200101', // 费率开始日期
        rateEndDate: '29991231' // 费率结束日期
    })
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            transData?: {
                conscustNo: string
                amount: number
            }
            fixIncomeConfProductAdd?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            transData: () => {
                return {
                    conscustNo: '',
                    amount: 0
                }
            },
            fixIncomeConfProductAdd: false
        }
    )

    // 弹窗标题配置
    const title = computed(() => {
        if (props.fixIncomeConfProductAdd) {
            return '新增固收产品费率配置'
        }
    })
    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })
    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()
    const rules = reactive<FormRules<RuleForm>>({
        fundCode: [
            { required: true, message: '请输入产品代码', trigger: 'blur' },
            { pattern: /^.{0,6}$/, message: '产品代码不能超过6个字符', trigger: 'blur' }
        ],
        fundName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        hbPerformanceRate1: [
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '限数字,小于1,小数点后6位',
                trigger: 'blur'
            }
        ],
        hbPerformanceRate2: [
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '限数字,小于1,小数点后6位',
                trigger: 'blur'
            }
        ],
        performanceRate1: [
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '限数字,小于1,小数点后6位',
                trigger: 'blur'
            }
        ],
        performanceRate2: [
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '限数字,小于1,小数点后6位',
                trigger: 'blur'
            }
        ],
        performanceBase1: [
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '限数字,小于1,小数点后6位',
                trigger: 'blur'
            }
        ],
        performanceBase2: [
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '限数字,小于1,小数点后6位',
                trigger: 'blur'
            }
        ],
        consultRate: [
            { required: true, message: '请输入咨询费率', trigger: 'blur' },
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '限数字,小于1,小数点后6位',
                trigger: 'blur'
            }
        ],
        manageRate: [
            { required: true, message: '请输入管理费率', trigger: 'blur' },
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '限数字,小于1,小数点后6位',
                trigger: 'blur'
            }
        ],
        redemRate: [
            { required: true, message: '请输入赎回费率', trigger: 'blur' },
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '限数字,小于1,小数点后6位',
                trigger: 'blur'
            }
        ],
        consultFormula: [{ required: true, message: '请输入咨询费公式', trigger: 'blur' }],
        manageFormula: [{ required: true, message: '请输入管理费公式', trigger: 'blur' }],
        redemFormula: [{ required: true, message: '请输入赎回费公式', trigger: 'blur' }],
        closedPeriodDays: [
            { pattern: /^\d{0,8}$/, message: '只能输入数字且长度不能超过8', trigger: 'blur' }
        ],
        countDay: [
            { pattern: /^\d{0,8}$/, message: '只能输入数字且长度不能超过8', trigger: 'blur' }
        ],
        yearDay: [
            { pattern: /^\d{0,8}$/, message: '只能输入数字且长度不能超过8', trigger: 'blur' }
        ],
        redemDay: [
            { pattern: /^\d{0,8}$/, message: '只能输入数字且长度不能超过8', trigger: 'blur' }
        ],
        adjustAmount: [
            { pattern: /^-?\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        hswType: [{ required: true, message: '必填', trigger: 'blur' }],
        performancejtType: [{ required: true, message: '必填', trigger: 'blur' }],
        rateStartDate: [{ required: true, message: '必填', trigger: 'blur' }],
        rateEndDate: [{ required: true, message: '必填', trigger: 'blur' }]
    })
    //提交方法
    const submitForm = async (formEl: FormInstance | undefined) => {
        console.log('submit11111111111111')
        if (!formEl) {
            return
        }
        await formEl.validate((valid, fields) => {
            if (valid) {
                console.log('submit!', fields)
                fixIncomeConfProductRateSubmit(ruleForm)
            } else {
                console.log('error submit!', fields)
            }
        })
    }
    //重置方法
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        formEl.resetFields()
    }
    /**
     * 产品费率新增方法
     * @param params
     */
    function fixIncomeConfProductRateSubmit(params: any) {
        console.log('params' + JSON.stringify(params))
        //   ---  产品起息到期日  ---
        let startTime3 = new Date(params.valueStartDate).getTime()
        const startdt3 = String(params.valueStartDate)
        //解决日期格式转换问题  （2023-10 使用new date函数是从八点开始的， 通过控件获取的是从0点开始）
        if (startdt3.indexOf('-') !== -1) {
            const startDate3 = new Date(params.valueStartDate)
            startTime3 = startDate3.getTime() - 28880000
        }
        const endTime3 = new Date(params.valueEndDate).getTime()
        if (startTime3 - endTime3 > 0) {
            ElMessage({
                message: '选择的产品起息日大于产品到期日',
                type: 'warning',
                duration: 2000
            })
            return
        }
        //   ---  结算起息到期日  ---
        let startTime2 = new Date(params.settleStartDate).getTime()
        const startdt2 = String(params.settleStartDate)
        //解决日期格式转换问题  （2023-10 使用new date函数是从八点开始的， 通过控件获取的是从0点开始）
        if (startdt2.indexOf('-') !== -1) {
            const startDate2 = new Date(params.settleStartDate)
            startTime2 = startDate2.getTime() - 28880000
        }
        const endTime2 = new Date(params.settleEndDate).getTime()
        if (startTime2 - endTime2 > 0) {
            ElMessage({
                message: '选择的结算起息日大于结算到期日',
                type: 'warning',
                duration: 2000
            })
            return
        }
        //   ---  费率开始结束日  ---
        let startTime = new Date(params.rateStartDate).getTime()
        const startdt = String(params.rateStartDate)
        //解决日期格式转换问题  （2023-10 使用new date函数是从八点开始的， 通过控件获取的是从0点开始）
        if (startdt.indexOf('-') !== -1) {
            const startDate = new Date(params.rateStartDate)
            startTime = startDate.getTime() - 28880000
        }
        const endTime = new Date(params.rateEndDate).getTime()
        if (startTime - endTime > 0) {
            ElMessage({
                message: '选择的费率开始日期大于费率结束日期',
                type: 'warning',
                duration: 2000
            })
            return
        }

        if (params.performancejtType !== '' && params.hbPerformanceRate1 === '') {
            ElMessage({
                message: '若业绩报酬计提类型有值,好买业绩报酬费率1不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }
        if (params.performancejtType !== '' && params.performanceRate1 === '') {
            ElMessage({
                message: '业绩报酬公式有值,业绩报酬费率1不能为空',
                type: 'warning',
                duration: 2000
            })
            return
        }

        fetchRes(fixIncomeConfProductRateInsert(params), {
            successCB: (res: any) => {
                loadingFlag.value = false
                dialogVisible.value = false
                return emit('callback')
            },
            errorCB: (res: any) => {
                loadingFlag.value = false
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => {
                loadingFlag.value = false
            },
            successTxt: '添加成功',
            failTxt: '',
            fetchKey: ''
        })
    }
    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        dialogVisible.value = false
        // formEl.resetFields()
    }
</script>

<style lang="less" scoped>
    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
