/*
 * @Description: 定义搜索的label列表
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:36:23
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/labelData.ts
 *
 */
export const dataList = {
    startDt: {
        label: '开始日期',
        placeholder: '输入开始日期'
    },
    endDt: {
        label: '结束日期',
        placeholder: '输入结束日期'
    },
    fundCode: {
        label: '基金代码',
        placeholder: '输入基金代码'
    },
    fundMan: {
        label: '基金管理人',
        placeholder: '输入基金管理人'
    },
    // 审核状态
    auditState: {
        label: '审核状态',
        placeholder: '请选择审核状态',
        selectList: [
            {
                key: '0',
                label: '未提交'
            },
            {
                key: '1',
                label: '待审核'
            },
            {
                key: '2',
                label: '已审核'
            },
            {
                key: '3',
                label: '已驳回'
            }
        ]
    },
    // 费用类型
    feeType: {
        label: '费用类型',
        placeholder: '请选择费用类型',
        selectList: [
            {
                key: '7',
                label: '认购费/设立服务费(股权产品)'
            },
            {
                key: '8',
                label: '管理费/咨询服务费 (股权产品)'
            },
            {
                key: '9',
                label: '业绩报酬 (股权产品)'
            }
        ]
    },
    fundName: {
        label: '产品名称',
        placeholder: '输入产品名称'
    },
    custName: {
        label: '客户姓名',
        placeholder: '输入客户姓名'
    },
    conscustNo: {
        label: '投顾客户号',
        placeholder: '投顾客户号'
    },
    // 产品类型
    productType: {
        label: '产品类型',
        placeholder: '请选择产品类型',
        selectList: [
            {
                key: '1',
                label: '股权代销'
            },
            {
                key: '2',
                label: '股权自营-母基金'
            },
            {
                key: '3',
                label: '股权自营-底层'
            }
        ]
    },
    subFundName: {
        label: '子基金名称',
        placeholder: '子基金名称'
    },
    settlePeriod: {
        label: '结算周期',
        placeholder: '结算周期'
    },
    settleYear: {
        label: '周期年份',
        placeholder: '周期年份'
    },
    settlePeriodDetail: {
        label: '具体周期',
        placeholder: '具体周期'
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
