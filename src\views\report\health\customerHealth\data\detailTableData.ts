/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import {
    formatTableValue,
    formatTableValueBlank,
    numberWithThousandSeparatorTwoDecimalPlaces
} from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const custDetailTableColumn: TableColumnItem[] = [
    { key: 'custName', label: '客户姓名', width: 100, formatter: formatTableValue },
    { key: 'conscustno', label: '投顾客户号', width: 100, formatter: formatTableValue },
    { key: 'u1Name', label: '中心', width: 100, formatter: formatTableValue },
    { key: 'u2Name', label: '区域', width: 100, formatter: formatTableValue },
    { key: 'u3Name', label: '分公司', width: 120, formatter: formatTableValue },
    { key: 'consname', label: '投顾姓名', width: 100, formatter: formatTableValue },
    { key: 'conscode', label: '员工编码', width: 110, formatter: formatTableValue },
    { key: 'isKpi', label: '是否参与统计', width: 110, formatter: formatTableValue },
    {
        key: 'priMarketCap2024',
        label: '24年底高端总市值',
        width: 140,
        formatter: numberWithThousandSeparatorTwoDecimalPlaces
    },
    {
        key: 'priMarketCap',
        label: '高端总市值',
        width: 120,
        formatter: numberWithThousandSeparatorTwoDecimalPlaces
    },
    { key: 'starH2', label: '策略分散健康度', width: 110, formatter: formatTableValueBlank },
    { key: 'starH3', label: '基金分散健康度', width: 110, formatter: formatTableValueBlank },
    { key: 'starH4', label: '全球配置健康度', width: 110, formatter: formatTableValueBlank },
    { key: 'avgStar', label: '综合健康度', width: 90, formatter: formatTableValueBlank },
    { key: 'invstType', label: '客户类型', width: 90, formatter: formatTableValue },
    { key: 'isRelation2024', label: '24年底是否关联账户', width: 140, formatter: formatTableValue },
    { key: 'isRelationCurrent', label: '当前是否关联账户', width: 130, formatter: formatTableValue }
]

export const healthDetailTableColumn: TableColumnItem[] = [
    { key: 'custName', label: '客户姓名', width: 100, formatter: formatTableValue },
    { key: 'conscustno', label: '投顾客户号', width: 100, formatter: formatTableValue },
    { key: 'u1Name', label: '中心', width: 100, formatter: formatTableValue },
    { key: 'u2Name', label: '区域', width: 100, formatter: formatTableValue },
    { key: 'u3Name', label: '分公司', width: 120, formatter: formatTableValue },
    { key: 'consname', label: '投顾姓名', width: 100, formatter: formatTableValue },
    { key: 'conscode', label: '员工编码', width: 110, formatter: formatTableValue },
    { key: 'isKpi', label: '是否参与统计', width: 100, formatter: formatTableValue },
    {
        key: 'priMarketCap2024',
        label: '24年底高端总市值',
        width: 130,
        formatter: numberWithThousandSeparatorTwoDecimalPlaces
    },
    {
        key: 'starH22024',
        label: '24年底策略分散健康度',
        width: 150,
        formatter: formatTableValueBlank
    },
    {
        key: 'starH32024',
        label: '24年底基金分散健康度',
        width: 150,
        formatter: formatTableValueBlank
    },
    {
        key: 'starH42024',
        label: '24年底全球配置健康度',
        width: 150,
        formatter: formatTableValueBlank
    },
    { key: 'avgStar2024', label: '24年底综合健康度', width: 120, formatter: formatTableValueBlank },
    {
        key: 'priMarketCap',
        label: '高端总市值',
        width: 110,
        formatter: numberWithThousandSeparatorTwoDecimalPlaces
    },
    { key: 'starH2', label: '策略分散健康度', width: 110, formatter: formatTableValueBlank },
    { key: 'starH3', label: '基金分散健康度', width: 110, formatter: formatTableValueBlank },
    { key: 'starH4', label: '全球配置健康度', width: 110, formatter: formatTableValueBlank },
    { key: 'avgStar', label: '综合健康度', width: 90, formatter: formatTableValueBlank },
    { key: 'invstType', label: '客户类型', width: 90, formatter: formatTableValue },
    { key: 'isRelation2024', label: '24年底是否关联账户', width: 140, formatter: formatTableValue },
    { key: 'isRelationCurrent', label: '当前是否关联账户', width: 130, formatter: formatTableValue }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
