/*
 * @Description: 定义搜索的label列表
 * @Author: ji<PERSON><PERSON><PERSON>.yang
 * @Date: 2024-04-01 10:40:30
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-04-01 10:40:30
 * @FilePath: /src/views/report/kpi/assetRepDownload/data/labelData.ts
 *
 */
export const dataList = {
    combinationName: {
        label: '组合名称',
        placeholder: '请输入组合名称'
    },
    isOverEighty: {
        label: '海外占比≥80%',
        placeholder: '请选择',
        selectList: [
            {
                key: '1',
                label: '是'
            },
            {
                key: '0',
                label: '否'
            }
        ]
    },
    dateInterval: {
        label: '时间范围',
        placeholder: ['开始日期', '结束日期']
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
