<!--
 * @Description: 统计说明
 * @Author: jianjian.yang
 * @Date: 2024-04-01 19:46:04
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-04-01 19:46:04
 * @FilePath: /src/views/report/kpi/components/explainAssetRep.vue
 *  
-->
<template>
    <crm-dialog
        width="824px"
        title="统计说明"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
        @keyup.enter.stop="handleClose"
    >
        <template #default>
            <div style="height: 300px">
                <b>此报表用于统计投顾名下存量客户在每半年度中，资产配置报告的制作与下载情况。</b
                ><br />
                <span>①存量客户_总客户数：取统计结束时间投顾名下的时点存量客户</span><br />
                <span
                    >②完成报告制作：取在统计开始时间~统计结束时间内，新增制作并下载资产配置报告的客户</span
                >
            </div>
        </template>

        <template #footer>
            <div>
                <crm-button size="small" :radius="true" @click="handleClose">确定</crm-button>
            </div>
        </template>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { useVisible } from '@/views/common/scripts/useVisible'

    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true
        }
    )

    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callBack'): void
    }>()
    // hooks
    const { dialogVisible, handleClose } = useVisible({
        emit,
        props
    })
</script>

<style lang="less" scoped>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 8px;
        text-align: left;
        border: 1px solid black;
    }

    th {
        background-color: #f2f2f2;
    }

    .middle-content {
        padding: 10px 0;
    }
</style>
