import { axiosRequest } from '@/utils/index'
import { paramsMerge } from '@/api/mock.js'
import {
    performanceManageNodeConfParam,
    adjustParam,
    updateNodeConfParam,
    batchUpdateNodeConfParam
} from './type/apiReqType.js'
import { pa } from 'element-plus/es/locale/index.js'
/**
 * @description: 查询接口
 * @return {*}
 */
// eslint-disable-next-line camelcase
export const performanceManageNodeConfQuery = (params: performanceManageNodeConfParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/query',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 导出接口
 * @return {*}
 */
export const performanceManageNodeConfExport = (params: performanceManageNodeConfParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/export',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 列表初始化接口
 * @return {*}
 */
export const performanceManageNodeConfInit = () => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/initdata',
            method: 'post',
            data: null
        })
    )
}

/**
 * @description: 列表调整是否考核接口
 * @return {*}
 */
export const performanceManageNodeConfAdjustStatus = (params: adjustParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/adjuststatus',
            method: 'post',
            data: params
        })
    )
}

/**
 * @description: 修改页面保存接口
 * @return {*}
 */
export const performanceManageNodeConfUpdate = (params: updateNodeConfParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/update',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 修改页面查询接口
 * @return {*}
 */
export const queryEdit = (params: adjustParam) => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/queryedit',
            method: 'post',
            data: params
        })
    )
}
/**
 * @description: 批量修改页面保存接口
 * @return {*}
 */
export const performanceManageNodeConfBatchUpdate = (params: batchUpdateNodeConfParam): any => {
    return axiosRequest(
        paramsMerge({
            url: '/api/report/performance/manage/node/conf/batchupdate',
            method: 'post',
            data: params
        })
    )
}
