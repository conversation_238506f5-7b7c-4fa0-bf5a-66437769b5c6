/*
 * @Author: xing.zhou01
 * @Date: 2022-01-20 21:30:15
 * @LastEditTime: 2023-07-10 10:29:05
 * @LastEditors: chaohui.wu
 * @Description:
 * @FilePath: /crm-asset-web/stylelint.config.js
 */
module.exports = {
    defaultSeverity: 'warning',
    extends: [
        'stylelint-config-standard',
        'stylelint-config-html/vue',
        'stylelint-config-recess-order',
        'stylelint-config-prettier'
    ],
    plugins: ['stylelint-order'],
    // 指定不同文件对应的解析器
    overrides: [
        {
            files: ['**/*.{vue,html}'],
            customSyntax: 'postcss-html'
        },
        {
            files: ['**/*.{css,less}'],
            customSyntax: 'postcss-less'
        }
    ],
    rules: {
        // 禁止小于 1 的小数有一个前导零（避免与prettier冲突）
        // 'number-leading-zero': 'always', // 已弃用
        // 禁止空第一行
        // 'no-empty-first-line': true, // 已弃用
        // 缩进4
        // indentation: 2, // 已弃用
        // 颜色指定小写（避免与prettier配置冲突）
        // 'color-hex-case': 'lower', // 已弃用
        'function-no-unknown': null,
        'custom-property-empty-line-before': 'never', // 自定义标签自动加空行，去除兼容prettier
        'string-quotes': null,
        'no-empty-source': null,
        'selector-class-pattern': null,
        'no-duplicate-at-import-rules': null,
        'import-notation': 'string',
        // 允许双斜杠注释
        'no-invalid-double-slash-comments': null,
        // 允许小数
        'alpha-value-notation': null,
        // 允许rgb颜色
        'color-function-notation': null,
        // 禁止空块
        'block-no-empty': null,
        // 颜色6位长度
        'color-hex-length': 'long',
        // 兼容自定义标签名
        'selector-type-no-unknown': [
            true,
            {
                ignoreTypes: []
            }
        ],
        // 忽略伪类选择器 ::v-deep
        'selector-pseudo-element-no-unknown': [
            true,
            {
                ignorePseudoElements: ['v-deep', '::input-placeholder']
            }
        ],
        // Disallow unknown pseudo-class selectors.
        // https://stylelint.io/user-guide/rules/selector-pseudo-class-no-unknown/
        'selector-pseudo-class-no-unknown': [
            true,
            {
                ignorePseudoClasses: ['/deep/']
            }
        ],
        'font-family-no-missing-generic-family-keyword': null,
        'keyframes-name-pattern': null,
        'no-duplicate-selectors': null,
        // 禁止低优先级的选择器出现在高优先级的选择器之后。
        'no-descending-specificity': null,
        // 禁止空注释
        'comment-no-empty': true,
        // 禁止简写属性的冗余值
        'shorthand-property-no-redundant-values': true,
        // 禁止值的浏览器引擎前缀
        'value-no-vendor-prefix': null,
        // property-no-vendor-prefix
        'property-no-vendor-prefix': null,
        'function-url-quotes': null,
        'at-rule-no-unknown': [
            true,
            {
                ignoreAtRules: [
                    'mixin',
                    'include',
                    'if',
                    'else',
                    'extend',
                    'for',
                    '$',
                    'forward',
                    'use'
                ] // 忽略规则
            }
        ]
    },
    ignoreFiles: [
        '**/*.js',
        '**/*.jsx',
        '**/*.ts',
        '**/*.tsx',
        '**/*.json',
        '**/*.md',
        '**/*.yaml',
        '**/*.png',
        '**/*.svg',
        '**/*.jpg',
        '**/*.jpeg',
        '**/*.eot',
        '**/*.ttf',
        '**/*.woff',
        'dist/**/*.css',
        'node_modules/**/*.css',
        'node_modules/**/*.less',
        'node_modules/**/*.scss'
    ]
}
