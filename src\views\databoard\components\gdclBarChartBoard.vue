﻿<!-- eslint-disable no-undef -->
<!-- eslint-disable vue/require-default-prop -->
<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="board-s">
        <div :id="vId" style="width: 100%; height: 100%"></div>
    </div>
</template>

<script>
    import * as echarts from 'echarts'
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'GdclBarChartBoard',
        props: {
            // eslint-disable-next-line vue/require-default-prop
            vId: {
                type: String
            },
            title: {
                type: String,
                default: 'title'
            },
            // eslint-disable-next-line vue/require-default-prop
            thisYear: {
                type: String
            },
            // eslint-disable-next-line vue/require-default-prop
            lastYear: {
                type: String
            },
            // eslint-disable-next-line vue/require-default-prop
            thisYearData: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            lastYearData: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            dataX: {
                type: Array
            }
        },
        data() {
            return {
                // option: {
                //     title: {
                //         text: this.title,
                //         textStyle: {
                //             fontSize: 10
                //         }
                //     },
                //     grid: {
                //         left: '3%',
                //         right: '3%',
                //         bottom: '0px',
                //         top: '20%',
                //         containLabel: true
                //     },
                //     legend: {
                //         y: 'top',
                //         x: 'right',
                //         itemWidth: 4,
                //         itemHeight: 4,
                //         itemGap: 4,
                //         textStyle: {
                //             fontSize: 6
                //         },
                //         data: [this.lastYear, this.thisYear]
                //     },
                //     xAxis: {
                //         type: 'category',
                //         axisLabel: {
                //             interval: 0,
                //             rotate: 35,
                //             textStyle: {
                //                 fontSize: 6
                //             }
                //         },
                //         data: this.dataX
                //     },
                //     yAxis: {
                //         type: 'value',
                //         axisLabel: {
                //             textStyle: {
                //                 fontSize: 6
                //             }
                //         }
                //     },
                //     series: [
                //         {
                //             name: '去年',
                //             type: 'bar',
                //             barwidth: '40%', //柱的宽度
                //             barGap: 0,
                //             label: {
                //                 show: true, // 是否可见
                //                 rotate: 0, // 旋转角度
                //                 position: 'top', // 显示位置
                //                 fontSize: 6
                //             },
                //             data: this.lastYearData
                //         },
                //         {
                //             name: '今年',
                //             type: 'bar',
                //             label: {
                //                 show: true, // 是否可见
                //                 rotate: 0, // 旋转角度
                //                 position: 'top', // 显示位置
                //                 fontSize: 6
                //             },
                //             data: this.thisYearData
                //         }
                //     ]
                // }
            }
        },
        watch: {
            thisYearData: {
                handler(newVal) {
                    if (newVal) {
                        this.$nextTick(() => {
                            this.renderGaugeChart()
                        })
                    }
                },
                immediate: true,
                deep: true
            }
        },

        mounted() {
            this.renderGaugeChart()
        },
        methods: {
            renderGaugeChart() {
                const option = {
                    grid: {
                        left: '3%',
                        right: '3%',
                        bottom: '0px',
                        top: '8%',
                        containLabel: true
                    },
                    //下载图片
                    toolbox: {
                        show: true,
                        orient: 'vertical',
                        feature: {
                            saveAsImage: {
                                pixelRatio: 15
                            } //值越大分辨率越高
                        }
                    },
                    // 提示框
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        showContent: true,
                        // 自定义提示  显示千位符
                        formatter(params) {
                            let relVal = params[0].name + '(单位:万)'
                            for (let i = 0, l = params.length; i < l; i++) {
                                relVal += `<br/>${params[i].marker}${
                                    params[i].seriesName
                                } : ${params[i].value.toLocaleString()}`
                            }
                            return relVal
                        }
                    },
                    xAxis: {
                        type: 'category',
                        axisLabel: {
                            interval: 0,
                            rotate: 35,
                            textStyle: {
                                fontSize: 9
                            }
                        },
                        data: this.dataX
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                fontSize: 9
                            }
                        }
                    },
                    series: [
                        {
                            name: this.lastYear,
                            color: '#bfbfbf',
                            type: 'bar',
                            barwidth: '40%', //柱的宽度
                            barGap: 0,
                            label: {
                                show: true, // 是否可见
                                rotate: 40, // 旋转角度
                                position: 'top', // 显示位置
                                fontSize: 7
                            },
                            data: this.lastYearData
                        },
                        {
                            name: this.thisYear,
                            color: '#e54c5e',
                            type: 'bar',
                            label: {
                                show: true, // 是否可见
                                rotate: 40, // 旋转角度
                                position: 'top', // 显示位置
                                fontSize: 7
                            },
                            data: this.thisYearData
                        }
                    ]
                }
                this.myChart = echarts.init(document.getElementById(this.vId))
                this.myChart.setOption(option)
            }
        }
    })
</script>

<style scoped>
    .board-s {
        height: 1%;
        margin: 2px;

        /* border: 1px solid cornflowerblue; */
    }

    .box-card {
        height: 95%;
        font-size: 12px;
        background: #8cc5ff;
    }

    .card-title {
        display: block;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }
</style>
