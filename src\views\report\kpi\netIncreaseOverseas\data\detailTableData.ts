/*
 * @Description: table表格展示
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue,formatNumber } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const netIncreaseOverseasTableColumn: TableColumnItem[] = [
    {
        key: 'u1Name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'isKpi',
        label: '是否参与考核',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'kpiItem',
        label: 'KPI考核项',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'busiType',
        label: '业务类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'conscustno',
        label: '投顾客户号',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custName',
        label: '客户姓名',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'tradeType',
        label: '交易类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ackDt',
        label: '交易日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundCode',
        label: '产品代码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundName',
        label: '产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'tradeAmt',
        label: '交易金额（RMB）',
        width: 120,
        formatter: ({ tradeAmt }:any) => {
            return formatNumber({ num:tradeAmt })
        }
    },
    {
        key: 'hwzcxs',
        label: '海外占比系数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'cxhtje',
        label: '创新合同金额（RMB）',
        width: 120,
        formatter: ({ cxhtje }:any) => {
            if(!cxhtje && cxhtje !== 0) {
                return '—'
            }
            return formatNumber({ num:cxhtje })
        }
    },
    {
        key: 'renewalRate',
        label: '创新继续率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'kpiTradeAmt',
        label: 'KPI交易金额（RMB）',
        width: 120,
        formatter: ({ kpiTradeAmt }:any) => {
            return formatNumber({ num:kpiTradeAmt })
        }
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
