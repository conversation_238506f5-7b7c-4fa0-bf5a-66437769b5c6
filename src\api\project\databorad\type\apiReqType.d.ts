export {}
declare module './apiReqType' {
    type boardDataParam = {
        /**
         * 机构代码
         */
        orgCode?: String
        /**
         * 投顾代码
         */
        consCode?: String
        /**
         * 客户名称
         */
        conscustNo?: String
        /**
         * 交易年
         */
        tradeYear?: String
    }
    type getConsParam = {
        /**
         * 选择的组织
         */
        selectOrgCode?: String
        /**
         * 是否多选   0:单选 1:多选
         */
        multCheck?: String
    }
    type custVisitTableDataParam = {
        /**
         * 机构代码
         */
        orgCode?: String
        /**
         * 投顾代码
         */
        consCode?: String
        /**
         * 投顾客户号
         */
        conscustNo?: String
        /**
         * 交易年
         */
        tradeYear?: String
        /**
         * 页码
         */
        page?: Number
        /**
         * 每页显示多少条
         */
        rows?: Number
        /**
         * 每页显示多少条
         */
        flag?: String
    }
    type yxsAndLcjzBoardParam = {
        /**
         * 机构代码
         */
        orgCode?: String
        /**
         * 投顾代码
         */
        consCode?: String
        /**
         * 时间区间
         */
        tradeYearMonth?: String
        /**
         * 页码
         */
        page?: String
        /**
         * 每页显示多少条
         */
        rows?: String
    }
    type favorPvReportParam = {
        /**
         * 机构代码
         */
        orgCode?: String
        /**
         * 投顾代码
         */
        consCode?: String
        /**
         * 模块id
         */
        boardId?: String
        /**
         * 投顾客户号
         */
        conscustNo?: String
        /**
         * 页码
         */
        page?: String
        /**
         * 每页显示多少条
         */
        rows?: String
    }

    export {
        boardDataParam,
        yxsAndLcjzBoardParam,
        favorPvReportParam,
        custVisitTableDataParam,
        getConsParam
    }
}
