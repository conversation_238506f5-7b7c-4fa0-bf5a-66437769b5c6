<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <label-item label="查询时间">
                    <el-date-picker
                        v-model="queryForm.queryDt"
                        type="month"
                        size="small"
                        format="YYYYMM"
                        value-format="YYYYMM"
                        @change="handleQueryDt"
                    ></el-date-picker>
                </label-item>
                <!-- 投顾 -->
                <label-item label="当前投顾">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>
                <!-- 产品名称 -->
                <label-item label="产品名称">
                    <autocomplete
                        v-model="queryForm.label"
                        :search-func="autoProd"
                        @handleSet="handleProdCode"
                    />
                </label-item>
                <label-item label="客户姓名">
                    <crm-input v-model="queryForm.custName" :clearable="true" />
                </label-item>
                <label-item label="投顾客户号">
                    <crm-input v-model="queryForm.conscustno" :clearable="true" />
                </label-item>
                <label-item label="核算产品类型">
                    <crm-select
                        v-model="queryForm.accountProductType"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="accountProductTypeSelectList"
                        :style="{ width: '150px' }"
                        @change="handleAccountProductType"
                    />
                </label-item>
                <label-item label="订单号">
                    <crm-input v-model="queryForm.dealNo" :clearable="true" />
                </label-item>
            </template>
            <template #operationBtns>
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="exportHandle"
                    >导出</crm-button
                >
                <crm-button
                    size="small"
                    :radius="true"
                    :icon="InfoFilled"
                    plain
                    @click="handleExplain"
                    >说明
                </crm-button>
            </template>
            <template #tableContentMiddle>
                <div class="myclass">
                    <theme-table
                        :is-loading="listLoading"
                        :columns="tableColumn"
                        :data="tableData"
                        style="width: 100%"
                        :show-operation="false"
                        :no-select="false"
                        :no-index="false"
                        :stripe="true"
                        height="100%"
                        :border="true"
                        operation-width="100"
                        class-name="theme-gray"
                    >
                    </theme-table>
                </div>
            </template>
            <template #tableContentBottom>
                <pagination
                    :page="pageObj"
                    :page-sizes="[100, 200, 500, 1000, 2000]"
                    :total="pageObj.total"
                    @change="handleCurrentChange"
                />
            </template>
        </table-wrapper>
        <ExplainStock v-model="explainDialogVisiable"></ExplainStock>
    </div>
</template>

<script lang="ts" setup>
    import { Download, InfoFilled } from '@element-plus/icons-vue'
    import { fetchRes } from '@/utils'
    import { smStockFeedTableColumn, showTableColumn } from './data/tableData'
    import { dataList } from './data/labelData'
    import ExplainStock from '@/views/report/smStockFeed/explainStock.vue'
    import {
        smStockFeedQuery,
        smStockFeedExport
    } from '@/api/project/report/kpi/smStockFeed/smStockFeed'
    import { autoProd, getMenuPermission, fetchConstant } from '@/api/project/common/common'
    import LabelItem from '@/components/moduleBase/LabelItem.vue'
    import { ElMessage } from 'element-plus'
    import ThemeTable from '@/components/module/ThemeTable.vue'
    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import { useStockListData } from '@/views/common/scripts/stockListData'
    import Autocomplete from '@/components/moduleBase/Autocomplete.vue'
    import { ACCOUNT_PRODUCT_TYPE } from '@/constant/hbConstant'
    const listLoading = ref<boolean>(false)
    const consStatus = ref<string>('1') //不包含离职人员
    // 投顾管理层
    const stockListStore = useStockListData()
    const { getPageInit } = stockListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(stockListStore)
    const { accountProductType } = dataList
    const module = ref<string>('B140121')
    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        queryDt = ''
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        prodCode = ''
        label = ''
        custName = ''
        conscustno = ''
        accountProductType = ''
        dealNo = ''
    }

    const queryForm = reactive(new QueryForm())
    /**
     * @description: 说明展示隐藏
     * @param explainDialogVisiable 展示隐藏
     * @method handleExplain 触发方法
     * @return {*}
     */
    const explainDialogVisiable = ref<boolean>(false)
    const handleExplain = () => {
        explainDialogVisiable.value = true
    }
    const handleQueryDt = (val: string) => {
        queryForm.queryDt = val
    }
    const handleProdCode = (val: { label: string; productCode: string }) => {
        queryForm.prodCode = val.productCode
        queryForm.label = val.label
    }
    const handleCustName = (val: string) => {
        queryForm.custName = val
    }
    const handleConscustno = (val: string) => {
        queryForm.conscustno = val
    }
    const handleAccountProductType = (val: string) => {
        queryForm.accountProductType = val
    }
    const handleDealNo = (val: string) => {
        queryForm.dealNo = val
    }

    const exportHandle = () => {
        exportList()
    }
    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 100
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])
    const selectList = ref<object[]>([])

    const accountProductTypeSelectList = ref<object[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        return showTableColumn(
            smStockFeedTableColumn.map(item => item.key),
            smStockFeedTableColumn
        )
    })
    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        if (!queryForm.queryDt) {
            ElMessage.error('请选择查询时间')
            return
        }
        const params = {
            queryDt: queryForm.queryDt,
            orgCode:
                queryForm.orgvalue.orgCode && queryForm.orgvalue.orgCode === '0'
                    ? ''
                    : queryForm.orgvalue.orgCode,
            conscode: queryForm.orgvalue.consCode,
            prodCode: queryForm.prodCode,
            custName: queryForm.custName,
            conscustno: queryForm.conscustno,
            accountProductType: queryForm.accountProductType,
            dealNo: queryForm.dealNo
        }
        const res: any = await smStockFeedExport(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }
    //查詢
    const queryList = async () => {
        listLoading.value = true
        if (!queryForm.queryDt) {
            ElMessage.error('请选择查询时间')
            listLoading.value = false
            return
        }
        const params = {
            queryDt: queryForm.queryDt,
            orgCode:
                queryForm.orgvalue.orgCode && queryForm.orgvalue.orgCode === '0'
                    ? ''
                    : queryForm.orgvalue.orgCode,
            conscode: queryForm.orgvalue.consCode,
            prodCode: queryForm.prodCode,
            custName: queryForm.custName,
            conscustno: queryForm.conscustno,
            accountProductType: queryForm.accountProductType,
            dealNo: queryForm.dealNo,
            page: pageObj.value.page,
            rows: pageObj.value.size
        }
        fetchRes(smStockFeedQuery(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total } = resObj
                tableData.value = rows
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }
    const getLastMonth = () => {
        // 获取当前时间
        let now = new Date()
        // 获取当前月份
        let month = now.getMonth()
        // 获取当前年份
        let year = now.getFullYear()

        // 计算上个月的月份和年份
        if (month === 0) {
            month = 11
            year -= 1
        } else {
            month -= 1
        }

        // 使用计算出的年月创建新的Date对象，为了避免日期越界问题，这里直接使用1号
        now = new Date(year, month, 1)

        // 格式化日期为 yyyyMM 格式
        return (
            now.getFullYear().toString().padStart(4, '0') +
            (now.getMonth() + 1).toString().padStart(2, '0')
        )
    }
    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }
    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }
    const exportShow = ref<boolean>(false)
    const initData = async () => {
        queryForm.queryDt = getLastMonth()
        const params = {
            menuCode: 'B140121'
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (item.operateCode === '1' && item.display === '1') {
                            exportShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {},
            catchCB: () => {},
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })

        fetchRes(fetchConstant({ typeCodeList: [ACCOUNT_PRODUCT_TYPE] }), {
            successCB: (resObj: any) => {
                const { constantTypeMap } = resObj
                accountProductTypeSelectList.value = constantTypeMap[ACCOUNT_PRODUCT_TYPE]
            },
            errorCB: () => {},
            catchCB: () => {},
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }
    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        listLoading.value = true
        getPageInit(module.value)
        initData()
        listLoading.value = false
    })
</script>
<style lang="less" scoped>
    .myclass {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
</style>
