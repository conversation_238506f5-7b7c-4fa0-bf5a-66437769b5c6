import { axiosRequest } from '@/utils/index'
import { QueryHealthDetailParams } from './type/apiReqType'

// 查询健康客户统计列表
export const healthDetailQuery = (data: QueryHealthDetailParams) => {
    return axiosRequest({
        url: '/api/report/health/detail/healthDetailQuery',
        method: 'post',
        data
    })
}

// 导出健康客户统计列表
export const healthDetailExport = (data: QueryHealthDetailParams) => {
    return axiosRequest({
        url: '/api/report/health/detail/healthDetailExport',
        method: 'post',
        data
    })
}

// 查询健康客户统计列表
export const custDetailQuery = (data: QueryHealthDetailParams) => {
    return axiosRequest({
        url: '/api/report/health/detail/custDetailQuery',
        method: 'post',
        data
    })
}

// 导出健康客户统计列表
export const custDetailExport = (data: QueryHealthDetailParams) => {
    return axiosRequest({
        url: '/api/report/health/detail/custDetailExport',
        method: 'post',
        data
    })
}
