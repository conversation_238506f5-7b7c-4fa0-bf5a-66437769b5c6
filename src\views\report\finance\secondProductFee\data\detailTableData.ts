/*
 * @Description: table表格展示
 * @Author: gang.zou
 * @Date: 2024-05-09 17:14:42
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, formatNumber } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const secondProductFeeDetailTableColumn: TableColumnItem[] = [
    {
        key: 'hboneNo',
        label: '一账通号',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'custName',
        label: '客户姓名',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundCode',
        label: '产品代码',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fundName',
        label: '产品名称',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'manager',
        label: '管理人',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'feeType',
        label: '费用类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'days',
        label: '天数',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'balanceVol',
        label: '持仓份额',
        width: 120,
        formatter: ({ balanceVol }: any) => {
            return formatNumber({ num: balanceVol })
        }
    },
    {
        key: 'marketCap',
        label: '持仓市值',
        width: 120,
        formatter: ({ marketCap }: any) => {
            return formatNumber({ num: marketCap })
        }
    },
    {
        key: 'preNav',
        label: '计提日单位净值',
        width: 120,
        formatter: ({ preNav }: any) => {
            return formatNumber({ num: preNav })
        }
    },
    {
        key: 'preBeforeNav',
        label: '计提费前净值',
        width: 120,
        formatter: ({ preBeforeNav }: any) => {
            return formatNumber({ num: preBeforeNav })
        }
    },
    {
        key: 'preDt',
        label: '计提日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'tradeType',
        label: '交易类型',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'tradeDt',
        label: '交易日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'tradeVol',
        label: '交易份额',
        width: 120,
        formatter: ({ balanceVol }: any) => {
            return formatNumber({ num: balanceVol })
        }
    },
    {
        key: 'tradeAmt',
        label: '交易金额',
        width: 120,
        formatter: ({ marketCap }: any) => {
            return formatNumber({ num: marketCap })
        }
    },
    {
        key: 'feeRate',
        label: '费率',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'feeTax',
        label: '计提金额',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'fee',
        label: '去税计提金额',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consname',
        label: '所属投顾',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u1Name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '部门1',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '部门2',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'ageSubject',
        label: '协议主体',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'remark',
        label: '备注',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
