/*
 * @Description: 定义搜索的label列表
 * @Author: chao<PERSON>.wu
 * @Date: 2023-03-16 13:40:30
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 17:36:23
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/labelData.ts
 *
 */
export const dataList = {
    hkCustNo: {
        label: '香港客户号',
        placeholder: '请输入'
    },
    hboneNo: {
        label: '一账通号',
        placeholder: '请输入'
    },
    conscustno: {
        label: '投顾客户号',
        placeholder: '请输入'
    },
    // 个人、机构、产品
    invstType: {
        label: '客户类型',
        placeholder: '请选择',
        selectList: [
            {
                key: '个人',
                label: '个人'
            },
            {
                key: '机构',
                label: '机构'
            },
            {
                key: '产品',
                label: '产品'
            }
        ]
    },
    investorQualification: {
        label: '投资者类型(香港）',
        placeholder: '请选择',
        selectList: [
            {
                key: 'NORMAL',
                label: '普通'
            },
            {
                key: 'PRO',
                label: '专业'
            }
        ]
    },
    custStat: {
        label: '香港客户状态',
        placeholder: '请选择',
        selectList: [
            {
                key: '正常',
                label: '开户完成'
            },
            {
                key: '注销',
                label: '注销'
            },
            {
                key: '休眠',
                label: '休眠'
            },
            {
                key: '注册',
                label: '注册'
            },
            {
                key: '开户申请成功',
                label: '开户申请成功'
            }
        ]
    },
    riskToleranceLevel: {
        label: '风险等级',
        placeholder: '请选择',
        selectList: [
            {
                key: '1',
                label: '保守型(C1)'
            },
            {
                key: '2',
                label: '中度保守型C2)'
            },
            {
                key: '3',
                label: '平稳型(C3)'
            },
            {
                key: '4',
                label: '中度进取型(C4)'
            },
            {
                key: '5',
                label: '进取型(C5)'
            }
        ]
    },
    derivativeKnowledge: {
        label: '是否有衍生工具知识',
        placeholder: '请选择',
        selectList: [
            {
                key: '有',
                label: '有'
            },
            {
                key: '无',
                label: '无'
            }
        ]
    },
    regTime: {
        label: '注册日期',
        placeholder: ['开始日期', '结束日期']
    },
    openDate: {
        label: '香港开户日期',
        placeholder: ['开始日期', '结束日期']
    },
    openChannel: {
        label: '开户渠道',
        placeholder: '请选择',
        selectList: [
            {
                key: '柜台',
                label: '柜台'
            },
            {
                key: 'APP',
                label: 'APP'
            },
            {
                key: 'CRM-PC',
                label: 'CRM-PC'
            },
            {
                key: 'Wap/小程序',
                label: 'Wap/小程序'
            },
            {
                key: '网站',
                label: '网站'
            },
            {
                key: '电话',
                label: '电话'
            },
            {
                key: '储蓄罐',
                label: '储蓄罐'
            },
            {
                key: 'CRM移动端',
                label: 'CRM移动端'
            },
            {
                key: 'H5',
                label: 'H5'
            }
        ]
    },
    depositFundChannel: {
        label: '入金渠道',
        placeholder: '请选择',
        selectList: [
            {
                key: '柜台',
                label: '柜台'
            },
            {
                key: 'APP',
                label: 'APP'
            },
            {
                key: 'CRM-PC',
                label: 'CRM-PC'
            },
            {
                key: 'Wap/小程序',
                label: 'Wap/小程序'
            },
            {
                key: '网站',
                label: '网站'
            },
            {
                key: '电话',
                label: '电话'
            },
            {
                key: '储蓄罐',
                label: '储蓄罐'
            },
            {
                key: 'CRM移动端',
                label: 'CRM移动端'
            },
            {
                key: 'H5',
                label: 'H5'
            }
        ]
    },
    orgData: {
        label: '当前投顾',
        placeholder: '请选择'
    },
    signState: {
        label: '海外储蓄罐签约状态',
        placeholder: '请选择',
        selectList: [
            {
                key: '未签署',
                label: '未签署'
            },
            {
                key: '已签署',
                label: '已签署'
            },
            {
                key: '已终止',
                label: '已终止'
            }
        ]
    },
    agreementSignDt: {
        label: '海外储蓄罐签约日期',
        placeholder: ['开始日期', '结束日期']
    },
    agreementSignType: {
        label: '海外储蓄罐签署方式',
        placeholder: '请选择',
        selectList: [
            {
                key: '线下自主申请开通',
                label: '线下自主申请开通'
            },
            {
                key: '线上自主申请开通',
                label: '线上自主申请开通'
            },
            {
                key: '到期自动续期',
                label: '到期自动续期'
            },
            {
                key: '底层基金更换同意',
                label: '底层基金更换同意'
            }
        ]
    },
    investorQualificationDate: {
        label: '投资者资质认证日期',
        placeholder: ['开始日期', '结束日期']
    },
    assetCertExpiredDate: {
        label: '资产证明到期日期',
        placeholder: ['开始日期', '结束日期']
    },
    submitTime: {
        label: '线上开户资料提交时间',
        placeholder: ['开始日期', '结束日期']
    },
    passDt: {
        label: '线上开户资料审核通过时间',
        placeholder: ['开始日期', '结束日期']
    },
    depositSubmitTime: {
        label: '开户入金审核提交时间',
        placeholder: ['开始日期', '结束日期']
    },
    depositPassDt: {
        label: '开户入金审核通过时间',
        placeholder: ['开始日期', '结束日期']
    },
    kycDt: {
        label: '风险测评日期',
        placeholder: ['开始日期', '结束日期']
    },
    kycExpiredDt: {
        label: '风险测评到期日',
        placeholder: ['开始日期', '结束日期']
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
