<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="board-s">
        <!-- header -->
        <div class="header-s">
            <span style="margin: 5px 0 0 5px">{{ title }}</span>
        </div>

        <!-- 滚动看板 -->
        <el-row span="24" class="demo">
            <el-col :span="4">
                <el-card class="box-card">
                    <div>
                        <span style="font-size: 12px; font-weight: bold; white-space: nowrap">
                            APP访问人数
                        </span>
                        <span
                            class="ahover"
                            style="
                                display: block;
                                font-size: 10px;
                                text-decoration: underline;
                                white-space: nowrap;
                            "
                            @click="sendMsgToParent(8)"
                        >
                            {{ d1UsersSum }}人/近1天
                        </span>
                        <span
                            class="ahover"
                            style="
                                display: block;
                                font-size: 10px;
                                text-decoration: underline;
                                white-space: nowrap;
                            "
                            @click="sendMsgToParent(9)"
                        >
                            {{ d14UsersSum }}人/近14天
                        </span>
                        <span
                            class="ahover"
                            style="
                                display: block;
                                font-size: 10px;
                                text-decoration: underline;
                                white-space: nowrap;
                            "
                            @click="sendMsgToParent(10)"
                        >
                            {{ d30UsersSum }}人/近30天
                        </span>
                    </div>
                </el-card>
            </el-col>
            <el-col v-for="(item, index) in scrollBoardData" :key="index" :span="5">
                <el-card class="box-card">
                    <div slot="header">
                        <span style="font-size: 14px; font-weight: bold">
                            {{ item.value }}
                        </span>
                        <span v-if="item.note">{{ '/' + item.note }}</span>
                    </div>
                    <div>
                        <span
                            class="ahover"
                            style="
                                font-weight: bold;
                                text-decoration: underline;
                                white-space: nowrap;
                            "
                            @click="sendMsgToParent(item.id)"
                        >
                            {{ item.name }}
                        </span>
                    </div>
                    <div>
                        <el-popover placement="right" width="300" trigger="hover">
                            <el-table v-if="item.id == 11" :data="grid11Data">
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="date"
                                    label="全体客户"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="name"
                                    label="名下客户"
                                ></el-table-column>
                            </el-table>
                            <el-table v-if="item.id == 12" :data="grid12Data">
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="date"
                                    label="全体客户"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="name"
                                    label="名下客户"
                                ></el-table-column>
                            </el-table>
                            <el-table v-if="item.id == 13" :data="grid13Data">
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="date"
                                    label="全体客户"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="name"
                                    label="名下客户"
                                ></el-table-column>
                            </el-table>
                            <el-table v-if="item.id == 14" :data="grid14Data">
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="date"
                                    label="全体客户"
                                ></el-table-column>
                                <el-table-column
                                    width="150"
                                    style="font-size: 10px"
                                    property="name"
                                    label="名下客户"
                                ></el-table-column>
                            </el-table>
                            <template #reference>
                                <base-button
                                    slot="reference"
                                    custom-class="small-btn"
                                    type="primary"
                                    size="medium"
                                    >{{ item.top3_Name }}
                                </base-button>
                            </template>
                        </el-popover>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
    import { defineComponent } from 'vue'
    export default defineComponent({
        name: 'ScrollBoardLeft',
        props: {
            title: {
                type: String,
                default: 'title'
            },
            // eslint-disable-next-line vue/require-default-prop
            boardData: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            grid11Data: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            grid12Data: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            grid13Data: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            grid14Data: {
                type: Array
            },
            // eslint-disable-next-line vue/require-default-prop
            d1UsersSum: {
                type: String
            },
            // eslint-disable-next-line vue/require-default-prop
            d14UsersSum: {
                type: String
            },
            // eslint-disable-next-line vue/require-default-prop
            d30UsersSum: {
                type: String
            }
        },

        data() {
            return {
                beginIndex: 0,
                endIndex: 0,
                length: 0,
                scrollBoardData: []
            }
        },
        watch: {
            boardData: {
                handler(val, oldVal) {
                    this.initScroll()
                },
                deep: true //true 深度监听
            }
        },
        mounted() {
            this.initScroll()
        },
        methods: {
            initScroll() {
                this.beginIndex = 0
                this.endIndex = 4
                this.length = this.boardData.length
                console.log('thisboardData:' + JSON.stringify(this.boardData))
                this.scrollBoardData = this.boardData.slice(this.beginIndex, this.endIndex)
                console.log('scrollBoardData:' + JSON.stringify(this.scrollBoardData))
                console.log('grid11Data:' + JSON.stringify(this.grid11Data))
            },

            sendMsgToParent(item) {
                // eslint-disable-next-line vue/require-explicit-emits
                this.$emit('ievent', item)
                console.log(item)
            }
        }
    })
</script>

<style lang="less" scoped>
    .demo {
        :deep(.el-card__body) {
            padding: 5px;
        }
    }

    .board-s {
        height: 95%;
        margin: 5px;
        border: 1px solid cornflowerblue;
    }

    .box-card {
        display: block;
        height: 90%;
        margin: 2px;
        font-size: 12px;
        text-align: center;
        background: #bce8f1;
    }

    .box-card2 {
        display: inline-block;
        height: 100%;
        margin: 2px;
        font-size: 1px !important;
        text-align: center;
        background: #bce8f1;
    }

    .header-s {
        height: 20%;
        padding: 2px;
        margin: 2px;
        font-size: 16px;
        font-weight: bold;
        background-color: #d9edf7;
    }

    .ahover:hover {
        color: red;
    }
</style>
