<template>
    <div class="report-list-module">
        <table-wrapper
            class-name="crm_wraper"
            :show-operation-left="true"
            :show-tabs-panel="true"
            pick-up-height="210px"
            :defalult-top-area-expand="false"
            @searchFn="handleLoading"
        >
            <template #searchArea>
                <!-- 香港客户号 -->
                <label-item :label="hkCustNo.label">
                    <crm-input
                        v-model="queryForm.hkCustNo"
                        :placeholder="hkCustNo.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 一账通号 -->
                <label-item :label="hboneNo.label">
                    <crm-input
                        v-model="queryForm.hboneNo"
                        :placeholder="hboneNo.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 投顾客户号 -->
                <label-item :label="conscustno.label">
                    <crm-input
                        v-model="queryForm.conscustno"
                        :placeholder="conscustno.placeholder"
                        :clearable="true"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 客户类型 -->
                <label-item :label="invstType.label">
                    <crm-select
                        v-model="queryForm.invstType"
                        :placeholder="invstType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="invstType.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 投资者类型(香港） -->
                <label-item :label="investorQualification.label">
                    <crm-select
                        v-model="queryForm.investorQualification"
                        :placeholder="investorQualification.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="investorQualification.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 香港客户状态 -->
                <label-item :label="custStat.label">
                    <crm-select
                        v-model="queryForm.custStat"
                        :placeholder="custStat.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="custStat.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 风险等级 -->
                <label-item :label="riskToleranceLevel.label">
                    <crm-select
                        v-model="queryForm.riskToleranceLevel"
                        :placeholder="riskToleranceLevel.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="riskToleranceLevel.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 是否有衍生工具知识 -->
                <label-item :label="derivativeKnowledge.label">
                    <crm-select
                        v-model="queryForm.derivativeKnowledge"
                        :placeholder="derivativeKnowledge.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="derivativeKnowledge.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 注册日期 -->
                <label-item :label="regTime.label">
                    <date-range
                        v-model="queryForm.regTime"
                        show-format="YYYY-MM-DD"
                        :placeholder="regTime.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 香港开户日期 -->
                <label-item :label="openDate.label">
                    <date-range
                        v-model="queryForm.openDate"
                        show-format="YYYY-MM-DD"
                        :placeholder="openDate.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 开户渠道 -->
                <label-item :label="openChannel.label">
                    <crm-select
                        v-model="queryForm.openChannel"
                        :placeholder="openChannel.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="openChannel.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 入金渠道 -->
                <label-item :label="depositFundChannel.label">
                    <crm-select
                        v-model="queryForm.depositFundChannel"
                        :placeholder="depositFundChannel.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="depositFundChannel.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 当前投顾 -->
                <label-item label="当前投顾">
                    <ReleatedSelect
                        ref="newlySelect"
                        v-model="queryForm.orgvalue"
                        :organization-list="organizationList"
                        :cons-list-default="consultList"
                        :default-org-code="orgCodeDefault"
                        :default-cons-code="consCodeDefault"
                        :cons-status="consStatus"
                        :module="module"
                        :add-all="true"
                    ></ReleatedSelect>
                </label-item>

                <!-- 海外储蓄罐签约状态 -->
                <label-item :label="signState.label">
                    <crm-select
                        v-model="queryForm.signState"
                        :placeholder="signState.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="signState.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>
                <!-- 海外储蓄罐签约日期 -->
                <label-item :label="agreementSignDt.label">
                    <date-range
                        v-model="queryForm.agreementSignDt"
                        show-format="YYYY-MM-DD"
                        :placeholder="agreementSignDt.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 海外储蓄罐签署方式 -->
                <label-item :label="agreementSignType.label">
                    <crm-select
                        v-model="queryForm.agreementSignType"
                        :placeholder="agreementSignType.placeholder"
                        filterable
                        clearable
                        label-format="label"
                        value-format="key"
                        :option-list="agreementSignType.selectList"
                        :style="{ width: '130px' }"
                    />
                </label-item>

                <!-- 投资者资质认证日期 -->
                <label-item :label="investorQualificationDate.label">
                    <date-range
                        v-model="queryForm.investorQualificationDate"
                        show-format="YYYY-MM-DD"
                        :placeholder="investorQualificationDate.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 资产证明到期日期 -->
                <label-item :label="assetCertExpiredDate.label">
                    <date-range
                        v-model="queryForm.assetCertExpiredDate"
                        show-format="YYYY-MM-DD"
                        :placeholder="assetCertExpiredDate.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 线上开户资料提交时间 -->
                <label-item :label="submitTime.label">
                    <date-range
                        v-model="queryForm.submitTime"
                        show-format="YYYY-MM-DD"
                        :placeholder="submitTime.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 线上开户资料审核通过时间 -->
                <label-item :label="passDt.label">
                    <date-range
                        v-model="queryForm.passDt"
                        show-format="YYYY-MM-DD"
                        :placeholder="passDt.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 开户入金审核提交时间 -->
                <label-item :label="depositSubmitTime.label">
                    <date-range
                        v-model="queryForm.depositSubmitTime"
                        show-format="YYYY-MM-DD"
                        :placeholder="depositSubmitTime.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 开户入金审核通过时间 -->
                <label-item :label="depositPassDt.label">
                    <date-range
                        v-model="queryForm.depositPassDt"
                        show-format="YYYY-MM-DD"
                        :placeholder="depositPassDt.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 风险测评日期 -->
                <label-item :label="kycDt.label">
                    <date-range
                        v-model="queryForm.kycDt"
                        show-format="YYYY-MM-DD"
                        :placeholder="kycDt.placeholder"
                        style-type="fund"
                    />
                </label-item>
                <!-- 风险测评到期日 -->
                <label-item :label="kycExpiredDt.label">
                    <date-range
                        v-model="queryForm.kycExpiredDt"
                        show-format="YYYY-MM-DD"
                        :placeholder="kycExpiredDt.placeholder"
                        style-type="fund"
                    />
                </label-item>
            </template>
            <template #operationLeft>
                <Text type="info" size="small" style="padding-left: 10px; font-size: 14px"
                    >数据更新时间：{{ lastUpdateTimeRef }}
                </Text>
            </template>

            <template #operationBtns>
                <crm-button
                    v-if="exportShow"
                    size="small"
                    :radius="true"
                    :icon="Download"
                    plain
                    @click="exportHandle"
                    >导出</crm-button
                >
                <crm-button
                    v-if="selectColumnShow"
                    size="small"
                    :radius="true"
                    :icon="InfoFilled"
                    plain
                    @click="handleSelectColumn"
                    >显示字段</crm-button
                >
            </template>
            <template #tableContentMiddle>
                <base-table
                    :is-loading="listLoading"
                    :columns="tableColumn"
                    :data="tableData"
                    style="width: 100%"
                    :show-operation="true"
                    :no-index="false"
                    :no-select="false"
                    :stripe="true"
                    height="100%"
                    :border="true"
                    operation-width="150"
                    :pops="{ selectFixed: 'left' }"
                    :row-class-name="handleRowClassName"
                    @sortChange="handleSortChange"
                >
                    <template #operation="{ scope }">
                        <el-button
                            v-if="viewDetailShow"
                            size="small"
                            :text="true"
                            link
                            @click="handleViewDetail(scope.row)"
                            >查看详情</el-button
                        >
                    </template>
                    <!-- 
                    <template #mobileReal="{ scope }">
                        <el-text>{{ scope.row.mobile }}</el-text>
                        <el-button
                            v-if="viewMobileRealShow"
                            size="small"
                            :text="true"
                            link
                            @click="handleViewReal(scope.row, '1')"
                            >查看</el-button
                        >
                    </template>
                    <template #idNoReal="{ scope }">
                        <el-text>{{ scope.row.idNo }}</el-text>
                        <el-button
                            v-if="viewIdNoRealShow"
                            size="small"
                            :text="true"
                            link
                            @click="handleViewReal(scope.row, '2')"
                            >查看</el-button
                        >
                    </template> -->
                </base-table>
            </template>
            <template #tableContentBottom>
                <pagination :page="pageObj" :total="pageObj.total" @change="handleCurrentChange" />
            </template>
        </table-wrapper>

        <view-cust-hk-info
            v-if="viewDetailDialogVisible"
            v-model="viewDetailDialogVisible"
            :trans-data="viewObj"
            :organization-list="organizationList"
            :consult-list="consultList"
            :org-code="orgCodeDefault"
            :cons-code="consCodeDefault"
            @callback="handleClose()"
        >
        </view-cust-hk-info>

        <ModuleKeyDia
            v-if="selectColumnDialogVisible"
            v-model="dialogId"
            v-model:visible="selectColumnDialogVisible"
            model-type="bsConfMsg"
            title="显示字段"
            :table-columns="custHkInfoAllColumn"
            :check-list="columnList"
            :support-model="false"
            :max-num="custHkInfoAllColumn.length"
            @change="handleShowColumnChange"
        >
        </ModuleKeyDia>
    </div>
</template>

<script lang="ts" setup>
    import { Plus, Download, InfoFilled, ArrowDownBold } from '@element-plus/icons-vue'
    import { fetchRes, messageBox } from '@/utils'
    import { dataList } from './data/labelData'
    import { ElMessage, ElMessageBox } from 'element-plus'

    import ReleatedSelect from '@/views/common/releatedSelect.vue'
    import { defaultTableColumn } from './data/tableData'
    import { custHkInfoAllColumn } from './data/allColumnData'
    import ViewCustHkInfo from '@/views/report/hk/components/viewCustHkInfo.vue'
    import ModuleKeyDia from '@/views/modBusiness/keyDia/moduleKeyDia.vue'
    import { showTableColumn } from '@/api/project/common/common'
    import {
        queryCustHkInfo,
        exportCustHkInfo,
        getInitData,
        saveUserShowColumn,
        viewRealText
    } from '@/api/project/report/hk/custHkInfo/custHkInfo'
    import { CUST_HK_INFO_OPER_PERMISSION } from '@/constant/reportConst'
    import { getMenuPermission } from '@/api/project/common/common'
    import { useConsOrgListData } from '@/views/common/scripts/consOrgListData'
    import { SortOrderCumstom } from '@/type/index'

    // 投顾管理层
    const useConsOrgListStore = useConsOrgListData()
    const { fetchConsOrgList } = useConsOrgListStore
    const { organizationList, consultList, orgCodeDefault, consCodeDefault } =
        storeToRefs(useConsOrgListStore)

    const {
        hkCustNo,
        hboneNo,
        conscustno,
        invstType,
        investorQualification,
        custStat,
        riskToleranceLevel,
        derivativeKnowledge,
        regTime,
        openDate,
        openChannel,
        depositFundChannel,
        investorQualificationDate,
        assetCertExpiredDate,
        submitTime,
        passDt,
        depositSubmitTime,
        depositPassDt,
        kycDt,
        kycExpiredDt,
        signState,
        agreementSignDt,
        agreementSignType
    } = dataList

    const listLoading = ref<boolean>(false)
    const selectColumnDialogVisible = ref<boolean>(false)
    const viewDetailDialogVisible = ref<boolean>(false)
    const consStatus = ref<string>('1') //不包含离职人员

    const exportShow = ref<boolean>(false)
    const selectColumnShow = ref<boolean>(false)
    const viewDetailShow = ref<boolean>(false)

    const lastUpdateTimeRef = ref<string>('')

    const dialogId = ref('1111')

    const handleRowClassName = (rowObj: any, rowIndex: number) => {
        const { row } = rowObj
        if (row && row.ifExceptionData === '1') {
            return 'highlight-bg'
        }
    }

    const module = ref<string>('071701')
    /**
     * @description: 关闭弹框刷新数据
     * @param {*} val
     * @param {*} type
     * @return {*}
     */
    const handleClose = (): void => {
        queryList()
    }

    // 排序值映射
    const transOrder = (key: string) => {
        switch (key) {
            case 'ascending':
                return 'ASC'
            case 'descending':
                return 'DESC'
            default:
                return 'DESC'
        }
    }
    interface SortParams {
        order: SortOrderCumstom
        prop: string
    }
    // 排序联动
    const handleSortChange = (val: SortParams) => {
        queryForm.order = val.order
        queryForm.sort = val.prop
        nextTick(() => {
            queryList()
        })
    }

    /**
     * @description: 编辑
     * @return {*}
     */
    const viewObj = ref({
        hkCustNo: '',
        type: '',
        title: ''
    })

    const handleShowColumnChange = (val: any, type: string) => {
        console.log(val, type)
        fetchRes(saveUserShowColumn({ columnNameArr: val }), {
            successCB: (resObj: any) => {
                listLoading.value = false
                columnList.value = val
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 查询条件列表
     * @return {*}
     */
    class QueryForm {
        hkCustNo = ''
        hboneNo = ''
        conscustno = ''
        invstType = ''
        investorQualification = ''
        custStat = ''
        riskToleranceLevel = ''
        derivativeKnowledge = ''
        regTime = {
            startDate: '',
            endDate: ''
        }
        openDate = {
            startDate: '',
            endDate: ''
        }
        openChannel = ''
        depositFundChannel = ''
        investorQualificationDate = {
            startDate: '',
            endDate: ''
        }
        assetCertExpiredDate = {
            startDate: '',
            endDate: ''
        }
        submitTime = {
            startDate: '',
            endDate: ''
        }
        passDt = {
            startDate: '',
            endDate: ''
        }
        depositSubmitTime = {
            startDate: '',
            endDate: ''
        }
        depositPassDt = {
            startDate: '',
            endDate: ''
        }
        kycDt = {
            startDate: '',
            endDate: ''
        }
        kycExpiredDt = {
            startDate: '',
            endDate: ''
        }
        orgvalue = {
            orgCode: orgCodeDefault.value ?? '',
            consCode: consCodeDefault.value ?? ''
        }
        signState = ''
        agreementSignDt = {
            startDate: '',
            endDate: ''
        }
        agreementSignType = ''
        sort = ''
        order = 'descending'
    }
    const queryForm = reactive(new QueryForm())

    /**
     * @description: table表格切换
     * @return {*}
     */
    const tableData = ref<object[]>([])

    const columnList = ref<string[]>([])

    // table表格column数据展示
    const tableColumn = computed(() => {
        const keys: string[] = columnList.value
        return showTableColumn(keys, custHkInfoAllColumn)
    })

    /**
     * @description: 分页数据
     * @return {*}
     */
    class PageObj {
        page = 1
        size = 20
        total = 0
        perPage = 1
    }
    const pageObj = shallowRef(new PageObj())

    //查詢
    const queryList = async () => {
        listLoading.value = true
        const params = {
            hkCustNo: queryForm.hkCustNo,
            hboneNo: queryForm.hboneNo,
            conscustno: queryForm.conscustno,
            invstType: queryForm.invstType,
            investorQualification: queryForm.investorQualification,
            custStat: queryForm.custStat,
            riskToleranceLevel: queryForm.riskToleranceLevel,
            derivativeKnowledge: queryForm.derivativeKnowledge,
            regTimeStart: queryForm.regTime.startDate,
            regTimeEnd: queryForm.regTime.endDate,
            openDateStart: queryForm.openDate.startDate,
            openDateEnd: queryForm.openDate.endDate,
            openChannel: queryForm.openChannel,
            depositFundChannel: queryForm.depositFundChannel,
            investorQualificationDateStart: queryForm.investorQualificationDate.startDate,
            investorQualificationDateEnd: queryForm.investorQualificationDate.endDate,
            assetCertExpiredDateStart: queryForm.assetCertExpiredDate.startDate,
            assetCertExpiredDateEnd: queryForm.assetCertExpiredDate.endDate,
            submitTimeStart: queryForm.submitTime.startDate,
            submitTimeEnd: queryForm.submitTime.endDate,
            passDtStart: queryForm.passDt.startDate,
            passDtEnd: queryForm.passDt.endDate,
            depositSubmitTimeStart: queryForm.depositSubmitTime.startDate,
            depositSubmitTimeEnd: queryForm.depositSubmitTime.endDate,
            depositPassDtStart: queryForm.depositPassDt.startDate,
            depositPassDtEnd: queryForm.depositPassDt.endDate,
            kycDtStart: queryForm.kycDt.startDate,
            kycDtEnd: queryForm.kycDt.endDate,
            kycExpiredDtStart: queryForm.kycExpiredDt.startDate,
            kycExpiredDtEnd: queryForm.kycExpiredDt.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            signState: queryForm.signState,
            agreementSignDtStart: queryForm.agreementSignDt.startDate,
            agreementSignDtEnd: queryForm.agreementSignDt.endDate,
            agreementSignType: queryForm.agreementSignType,
            page: pageObj.value.page,
            rows: pageObj.value.size,
            sort: queryForm.sort,
            order: transOrder(queryForm.order)
        }
        fetchRes(queryCustHkInfo(params), {
            successCB: (resObj: any) => {
                listLoading.value = false
                const { rows, total, lastUpdateTime } = resObj
                tableData.value = rows
                lastUpdateTimeRef.value = lastUpdateTime
                pageObj.value.total = Number(total)
                pageObj.value.size = Number(pageObj.value.size)
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 分页联动
     * @param {*} current
     * @param {*} perPage
     * @return {*}
     */
    const handleCurrentChange = (current: number, perPage: number) => {
        pageObj.value = { ...pageObj.value, page: current, size: perPage }
        queryList()
    }

    /**
     * @description: 初始化
     * @return {*}
     */
    const handleLoading = () => {
        queryList()
    }

    const exportHandle = () => {
        exportList()
    }

    const handleSelectColumn = () => {
        selectColumnDialogVisible.value = true
    }

    const handleViewDetail = (row: any) => {
        const { hkCustNo } = row
        viewObj.value = {
            hkCustNo: hkCustNo,
            type: 'edit',
            title: '查看详情'
        }
        viewDetailDialogVisible.value = true
    }

    const handleViewReal = (row: any, type: string) => {
        console.log(row.hkCustNo)
        let text
        if (type === '2') {
            text = row.idNoCipher
        } else {
            text = row.mobileCipher
        }
        const params = {
            hkCustNo: row.hkCustNo,
            text: text,
            type: type
        }
        fetchRes(viewRealText(params), {
            successCB: (res: any) => {
                console.log(res)
                ElMessageBox.alert(res, '', {
                    center: true
                })
            },
            errorCB: null,
            catchCB: null,
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 导出请求
     * @return {*}
     */
    const exportList = async () => {
        const params = {
            hkCustNo: queryForm.hkCustNo,
            hboneNo: queryForm.hboneNo,
            conscustno: queryForm.conscustno,
            invstType: queryForm.invstType,
            investorQualification: queryForm.investorQualification,
            custStat: queryForm.custStat,
            riskToleranceLevel: queryForm.riskToleranceLevel,
            derivativeKnowledge: queryForm.derivativeKnowledge,
            regTimeStart: queryForm.regTime.startDate,
            regTimeEnd: queryForm.regTime.endDate,
            openDateStart: queryForm.openDate.startDate,
            openDateEnd: queryForm.openDate.endDate,
            openChannel: queryForm.openChannel,
            depositFundChannel: queryForm.depositFundChannel,
            investorQualificationDateStart: queryForm.investorQualificationDate.startDate,
            investorQualificationDateEnd: queryForm.investorQualificationDate.endDate,
            assetCertExpiredDateStart: queryForm.assetCertExpiredDate.startDate,
            assetCertExpiredDateEnd: queryForm.assetCertExpiredDate.endDate,
            submitTimeStart: queryForm.submitTime.startDate,
            submitTimeEnd: queryForm.submitTime.endDate,
            passDtStart: queryForm.passDt.startDate,
            passDtEnd: queryForm.passDt.endDate,
            depositSubmitTimeStart: queryForm.depositSubmitTime.startDate,
            depositSubmitTimeEnd: queryForm.depositSubmitTime.endDate,
            depositPassDtStart: queryForm.depositPassDt.startDate,
            depositPassDtEnd: queryForm.depositPassDt.endDate,
            kycDtStart: queryForm.kycDt.startDate,
            kycDtEnd: queryForm.kycDt.endDate,
            kycExpiredDtStart: queryForm.kycExpiredDt.startDate,
            kycExpiredDtEnd: queryForm.kycExpiredDt.endDate,
            orgCode: queryForm.orgvalue.orgCode ?? orgCodeDefault.value,
            consCode: queryForm.orgvalue.consCode ?? consCodeDefault.value,
            sort: queryForm.sort,
            order: transOrder(queryForm.order)
        }
        const res: any = await exportCustHkInfo(params)
        const { fileByte, name } = res.data
        const bstr = atob(fileByte)
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n)
        }
        const blob = new Blob([u8arr], {
            // 下载的文件类型
            type: 'application/vnd.ms-excel;chartset=UTF-8'
        })
        const link = document.createElement('a')
        link.download = name || '导出文件'
        link.href = window.URL.createObjectURL(blob)
        link.click()
    }

    const initData = async () => {
        const params = {
            menuCode: module.value
        }
        fetchRes(getMenuPermission(params), {
            successCB: (resObj: any) => {
                const { rows } = resObj
                if (rows.length > 0) {
                    rows.forEach((item: any) => {
                        if (
                            item.operateCode === CUST_HK_INFO_OPER_PERMISSION.EXPORT &&
                            item.display === '1'
                        ) {
                            exportShow.value = true
                        }
                        if (
                            item.operateCode === CUST_HK_INFO_OPER_PERMISSION.SELECT_COLUMN &&
                            item.display === '1'
                        ) {
                            selectColumnShow.value = true
                        }
                        if (
                            item.operateCode === CUST_HK_INFO_OPER_PERMISSION.VIEW_DETAIL &&
                            item.display === '1'
                        ) {
                            viewDetailShow.value = true
                        }
                    })
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
        fetchRes(getInitData(), {
            successCB: (resObj: any) => {
                const { headList } = resObj
                if (headList?.length > 0) {
                    columnList.value = headList
                } else {
                    columnList.value = defaultTableColumn
                }
            },
            errorCB: () => {
                listLoading.value = false
            },
            catchCB: () => {
                listLoading.value = false
            },
            successTxt: '',
            failTxt: '请求失败请重试！',
            fetchKey: ''
        })
    }

    /**
     * @description: 默认值初始化
     * @return {*}
     */
    watch(
        [orgCodeDefault, consCodeDefault],
        (newVal, oldVal) => {
            if (orgCodeDefault.value || consCodeDefault.value) {
                queryForm.orgvalue.orgCode = orgCodeDefault.value
                queryForm.orgvalue.consCode = consCodeDefault.value
            }
        },
        {
            immediate: true
        }
    )

    /**
     * @description 页面挂载的时候就获取组织投顾信息
     */
    onMounted(() => {
        console.log('onMounted')
        initData()
        fetchConsOrgList('', module.value, '1')
    })
</script>
<style lang="less">
    .crm_table_wrapper {
        .crm_table_top {
            &.pack_up {
                .search_area {
                    .left {
                        height: 215px;
                    }
                }
            }
        }
    }
</style>
