/*
 * @Description: 关键字弹窗提示
 * @Author: chaohui.wu
 * @Date: 2024-10-12 15:24:28
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2024-10-12 18:25:24
 * @FilePath: /ds-report-web/src/views/modBusiness/hooks/useKeyDia.ts
 *
 */

// utils函数
import { getString, flatTree, message } from '@/utils'
// 自定义hooks方法
export function useKeyDia({ props, emit, tree }: any) {
    const getDataList = () => {
        return flatTree(props.tableColumns)?.filter((item: any) => item.key) ?? []
    }

    const flatTableColumns = computed(() => getDataList())
    /**
     * @description: 指标弹框关闭
     * @return {*}
     */
    const handleClose = () => {
        emit('update:visible', false)
    }

    const rankListHasNoChildren = ref<Array<any>>([])

    /**
     * @description: 初始化数据结构
     * @return {*}
     */

    const initData = () => {
        debugger
        const checkedArr: any[] = []
        if (props.checkList) {
            props.checkList.forEach((key: any) => {
                const item = getDataList().find((item: any) => item.key === key)
                checkedArr.push(item)
            })
            rankListHasNoChildren.value = checkedArr
        }
    }
    /**
     * @description: 删除选中
     * @param {*} obj
     * @return {*}
     */
    const deleteSelectedColumn = (obj: any) => {
        const index = rankListHasNoChildren.value.findIndex((item: any) => item.key === obj.key)
        if (index !== -1) {
            rankListHasNoChildren.value.splice(index, 1)
        }
        if (tree.value) {
            tree.value?.setChecked(obj, false)
        }
    }

    /**
     * @description: 树形结构选中
     * @param {*} node
     * @param {*} checkedObj
     * @return {*}
     */
    const checkCb = (node: any, checkedObj: any) => {
        const checkedNodes =
            checkedObj.checkedNodes.filter((item: any) => !!item.key && !item?.children?.length) ||
            []

        if (checkedNodes.length > props.maxNum) {
            const messageTpl: any = message
            messageTpl?.closeAll()
            message({
                message: `支持最多选择${props.maxNum}个指标!`,
                type: 'error',
                duration: 1000
            })

            const selectedIdList = rankListHasNoChildren.value?.map((item: any) => item?.key) || []

            const clickedNodeKeys = flatTree([node])
                .filter((v: any) => !!v.key)
                .map((v: any) => v.key)

            clickedNodeKeys.forEach((item: any) => {
                if (!selectedIdList.includes(item)) {
                    if (tree.value) {
                        tree.value?.setChecked(item, false)
                    }
                }
            })
            return false
        }

        rankListHasNoChildren.value = checkedNodes
    }

    const generateTree = (
        list: Array<any>,
        options = { keyField: 'key', childField: 'children', parentField: 'parentKey' }
    ) => {
        const { keyField = 'key', childField = 'children', parentField = 'parentKey' } = options

        const tree: Array<any> = []
        const record: Record<string, any[]> = {}

        for (let i = 0, len = list.length; i < len; i++) {
            const item = list[i]
            const id = item[keyField]

            if (!id) {
                continue
            }

            if (record[id]) {
                item[childField] = record[id]
            } else {
                item[childField] = record[id] = []
            }

            if (item[parentField]) {
                const parentId = item[parentField]

                if (!record[parentId]) {
                    record[parentId] = []
                }

                record[parentId].push(item)
            } else {
                tree.push(item)
            }
        }

        return tree
    }

    // const filterRankList = () => rankList.value

    /**
     * @description: 指标筛选
     * @param {*} keyword
     * @param {*} data
     * @param {*} node
     * @return {*}
     */
    const filterNode = (keyword: string, data: any, node: any) => {
        if (!keyword) {
            return true
        }

        const label = getString(data.label)

        const parentVisible = node?.parent?.visible === true && node?.parent?.level > 0

        return label.includes(keyword) || parentVisible
    }

    /**
     * @description: 重置选中
     * @return {*}
     */
    const resetChecked = () => {
        const keyArr: Array<any> = []
        flatTableColumns.value.forEach((item: any) => {
            if (item.disabled) {
                keyArr.push(item.key)
            }
        })
        if (tree.value) {
            tree.value?.setCheckedKeys(keyArr)
        }
        rankListHasNoChildren.value = rankListHasNoChildren.value.filter((item: any) =>
            keyArr.includes(item.key)
        )
    }

    /**
     * @description: 全选
     * @return {*}
     */
    const handleCheckAll = () => {
        const allKeys = flatTree(props.tableColumns).reduce((prev: any, cur: any) => {
            if (cur.key) {
                prev.push(cur.key)
            }
            return prev
        }, [] as Array<any>)
        // 最多选中提示
        if (allKeys?.length > props.maxNum) {
            message({
                message: `支持最多选择${props.maxNum}个指标!`,
                type: 'error',
                duration: 1000
            })
            return
        }
        if (tree.value) {
            tree.value?.setCheckedKeys(allKeys)
        }
        rankListHasNoChildren.value = getDataList()
    }

    const filterText = ref('')
    /**
     * @description: 搜索高亮
     * @param {*} text
     * @return {*}
     */
    const highlightSearchKey = (text: string) => {
        if (!text) {
            return ''
        }
        text = getString(text)
        const searchKey = filterText.value || ''
        if (!searchKey) {
            return text
        }
        let replaceReg: any = ''
        try {
            replaceReg = new RegExp(searchKey, 'g')
        } catch (e) {
            return
        }
        const replaceString = `<span class="highlight_search_key">${searchKey}</span>`
        return text.replace(replaceReg, replaceString)
    }
    // 导出方法
    return {
        filterText,
        flatTableColumns,
        rankListHasNoChildren,
        initData,
        handleClose,
        deleteSelectedColumn,
        checkCb,
        generateTree,
        filterNode,
        resetChecked,
        handleCheckAll,
        highlightSearchKey
    }
}
