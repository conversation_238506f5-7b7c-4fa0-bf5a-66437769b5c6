/*
 * @Description: table表格展示
 * @Author: chaohui.wu
 * @Date: 2023-03-17 17:14:42
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-28 13:50:56
 * @FilePath: /crm-template/src/views/assetReport/reportList/data/tableData.ts
 *
 */
import { TableColumnItem } from '@/type/tableColumn'
import { formatTableValue, getString } from '@/utils/index.js'

/**
 * @description: table表格数据
 * @return {*}
 */
export const performanceManageBusinessTableColumn: TableColumnItem[] = [
    {
        key: 'u1Name',
        label: '中心',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u2Name',
        label: '区域',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'u3Name',
        label: '分公司',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consName',
        label: '管理人员',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'consCode',
        label: '投顾code',
        minWidth: 120,
        formatter: formatTableValue
    },
    {
        key: 'regDt',
        label: '入职日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'regularDt',
        label: '转正日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'promoteDate',
        label: '管理日期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'workState',
        label: '在职状态',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'periodExplain',
        label: '考核周期',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'userLevel',
        label: '层级',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'curMonthLevel',
        label: '当前职级',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'startDt',
        label: '开始时间',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'exaimneNode',
        label: '考核节点',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'exaimneMonth',
        label: '考核节点司龄月（管）',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'targetAsset',
        label: '①管理存续D目标',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'resultAsset',
        label: '①管理存续D结果',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'gapAsset',
        label: '①管理存续D目标差距',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'monthAddAsset',
        label: '①管理月均净新增存续D',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'humanTargetSelf',
        label: '②人力目标-本人',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'humanTargetCons',
        label: '②人力目标-理财师',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'humanResultSelf',
        label: '②人力结果-本人',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'humanResultCons',
        label: '②人力结果-理财师',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'exaimneResult',
        label: '预计考核结果',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'newRank',
        label: '预计新职级',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'istp',
        label: '是否TP',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'exaimneEndresult',
        label: '最终考核结果',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'newEndrank',
        label: '最终新职级',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'qualifiedDepartment',
        label: '非观察分公司-当前',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'competentDepartment',
        label: '合格分公司-当前',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'qualifiedDepartmentNew',
        label: '非观察分公司-预计',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'competentDepartmentNew',
        label: '合格分公司-预计',
        width: 120,
        formatter: formatTableValue
    },
    {
        key: 'remark',
        label: '备注',
        width: 120,
        formatter: formatTableValue
    }
]

/**
 * @description: 不同场景下展示的table数据
 * @param {string} keyArr
 * @param {TableColumnItem} tableColumnList
 * @return {*}
 */
export const showTableColumn = (keyArr: string[], tableColumnList: TableColumnItem[]) => {
    return tableColumnList.filter(item => {
        if (item.key) {
            return keyArr.includes(item.key)
        } else if (item.type === 'slot' && item.slotName) {
            return keyArr.includes(item.slotName)
        }
    })
}
