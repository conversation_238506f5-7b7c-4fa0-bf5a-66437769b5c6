<!--
 * @Description: label组件
 * @Author: chaohui.wu
 * @Date: 2023-02-28 14:32:29
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-04-23 13:05:24
 * @FilePath: /crm-asset-web/src/components/moduleBase/LabelItem.vue
-->
<template>
    <div class="crm_input_item" :class="boxClassName">
        <div class="label" :class="[classNameStr]">
            <aside v-if="custom">
                <slot name="labelCust"></slot>
            </aside>
            <aside v-else>
                {{ label ? `${label}：` : '' }}
            </aside>
        </div>
        <div class="value">
            <slot></slot>
        </div>
    </div>
</template>

<script>
    export default defineComponent({
        name: 'LabelItem',
        props: {
            label: {
                type: String,
                default: ''
            },
            custom: {
                type: [Boolean],
                default: false
            },
            className: {
                type: [String],
                default: ''
            },
            boxClassName: {
                type: [String],
                default: ''
            }
        },
        computed: {
            classNameStr() {
                switch (this.className) {
                    case 'white':
                        return 'color-white'
                    case 'red':
                        return 'color-red'
                    default:
                        return this.className
                }
            }
        }
    })
</script>
<style lang="less" scoped>
    .crm_input_item {
        display: flex;
        align-items: flex-start;
        margin: 15px 30px 0 0;

        &.no-margin {
            margin: 0;
        }

        &.col-center {
            display: flex;
            align-items: center;
        }

        .label {
            min-width: 84px;
            font-size: 12px;
            line-height: 26px;
            color: @font_color;
            text-align: right;
            white-space: nowrap;

            &.color-white {
                color: @font_color_01;
            }

            &.color-red {
                color: @theme_main;
            }

            &.bold {
                font-weight: bold;
            }

            &.text-left {
                text-align: left;
            }
        }

        .value {
            flex: 1;
            font-size: 12px;
        }
    }
</style>
