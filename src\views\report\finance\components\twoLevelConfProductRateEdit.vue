<!--
 * @Description: 添加产品弹框
 * @Author: chaohui.wu
 * @Date: 2023-03-24 21:07:35
 * @LastEditors: chaohui.wu
 * @LastEditTime: 2023-06-27 19:14:16
 * @FilePath: /crm-template/src/views/components/dialogCommon/adjustAmt/adjustAmtIndex.vue
 * @ 当前未解耦，有时间解耦后可提取到dialogCommomn中
 400px
-->
<template>
    <crm-dialog
        v-model="dialogVisible"
        width="1024px"
        height="500px"
        :border="true"
        :title="title"
        :slot-list="['default', 'footer']"
        :close-on-click-modal="false"
    >
        <div>
            <!-- 产品费率的弹框 -->
            <el-form
                v-if="isTwoProductConfAdd"
                ref="ruleFormRef"
                :model="ruleForm"
                label-width="100px"
                :rules="rules"
                status-icon
            >
                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="fundCode" label="产品代码" style="margin-left: 40%">
                            <crm-input
                                v-model="ruleForm.fundCode"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item prop="company" label="公司" style="margin-left: 40%">
                            <crm-input
                                v-model="ruleForm.company"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="signSubject"
                            label-width="auto"
                            label="好买签约主体"
                            style="margin-left: 38.3%"
                        >
                            <crm-input
                                v-model="ruleForm.signSubject"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="oppositeContact"
                            label="对方联系人"
                            style="margin-left: 40%"
                        >
                            <crm-input
                                v-model="ruleForm.oppositeContact"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="rateConfType"
                            label-width="auto"
                            label="费率配置类型"
                            style="margin-left: 39%"
                        >
                            <crm-select
                                v-model="ruleForm.rateConfType"
                                :placeholder="rateConfTypeList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="rateConfTypeList.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item
                            prop="rateStartDate"
                            label-width="auto"
                            label="费率开始日期"
                            style="margin-left: 38.3%"
                        >
                            <el-date-picker
                                v-model="ruleForm.rateStartDate"
                                class="popperClass"
                                type="date"
                                format="YYYYMMDD"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item
                            prop="feedRate"
                            label="存续D折算系数"
                            label-width="120px"
                            style="margin-left: 35%"
                        >
                            <crm-input
                                v-model="ruleForm.feedRate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="fixedType"
                            label="固定日期类型"
                            label-width="120px"
                            style="margin-left: 35%"
                        >
                            <crm-select
                                v-model="ruleForm.fixedType"
                                :placeholder="fixedTypeList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="fixedTypeList.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item
                            prop="performanceType"
                            label="高水位类型"
                            label-width="140px"
                            style="margin-left: 30%"
                        >
                            <crm-select
                                v-model="ruleForm.performanceType"
                                :placeholder="performanceTypeList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="performanceTypeList.selectList"
                                :style="{ width: '150px' }"
                            ></crm-select>
                        </el-form-item>
                        <el-form-item
                            prop="closedPeriodDays"
                            label="份额锁定期天数"
                            label-width="120px"
                            style="margin-left: 35%"
                        >
                            <crm-input
                                v-model="ruleForm.closedPeriodDays"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="transMinAmount"
                            label="配置下限"
                            label-width="120px"
                            style="margin-left: 35%"
                        >
                            <crm-input
                                v-model="ruleForm.transMinAmount"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="transStartDate"
                            label-width="120px"
                            label="交易开始日期"
                            style="margin-left: 35%"
                        >
                            <el-date-picker
                                v-model="ruleForm.transStartDate"
                                class="popperClass"
                                type="date"
                                format="YYYYMMDD"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="fundName" label="产品名称" style="margin-left: 18%">
                            <crm-input
                                v-model="ruleForm.fundName"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="productManage"
                            label="产品经理"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.productManage"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item prop="signDate" label="签约日期" style="margin-left: 18%">
                            <el-date-picker
                                v-model="ruleForm.signDate"
                                class="popperClass"
                                type="date"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item prop="subRate" label="认购费率" style="margin-left: 18%">
                            <crm-input
                                v-model="ruleForm.subRate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="economicIncome"
                            label="经纪收入"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.economicIncome"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="rateEndDate"
                            label="费率结束日期"
                            label-width="auto"
                            style="margin-left: 17%"
                        >
                            <el-date-picker
                                v-model="ruleForm.rateEndDate"
                                class="popperClass"
                                type="date"
                                format="YYYYMMDD"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item prop="feedRemark" label="存续D备注" style="margin-left: 18%">
                            <crm-input
                                v-model="ruleForm.feedRemark"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item prop="fixeDate" label="固定日日期" style="margin-left: 18%">
                            <crm-select
                                v-model="ruleForm.fixeDate"
                                :placeholder="fixedDateList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="fixedDateList.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item
                            prop="performanceBenchmark"
                            label="业绩报酬计提基准"
                            label-width="140px"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="ruleForm.performanceBenchmark"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="closedPeriodType"
                            label="份额锁定期类型"
                            label-width="140px"
                            style="margin-left: 10%"
                        >
                            <crm-select
                                v-model="ruleForm.closedPeriodType"
                                :placeholder="closedPeriodTypeList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="closedPeriodTypeList.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item
                            prop="transMaxAmount"
                            label="配置上限"
                            label-width="140px"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="ruleForm.transMaxAmount"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="transEndDate"
                            label="交易结束日期"
                            label-width="140px"
                            style="margin-left: 10%"
                        >
                            <el-date-picker
                                v-model="ruleForm.transEndDate"
                                class="popperClass"
                                type="date"
                                format="YYYYMMDD"
                                size="small"
                                style="width: 150px; font-size: 12px"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item prop="manageRate" label="管理费率" style="margin-left: 40%">
                            <crm-input
                                v-model="ruleForm.manageRate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item prop="manageRemark" label="管理备注" style="margin-left: 40%">
                            <crm-input
                                v-model="ruleForm.manageRemark"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="manageFormula"
                            label="管理费公式"
                            style="margin-left: 18%"
                        >
                            <crm-input
                                v-model="ruleForm.manageFormula"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="performanceRate"
                            label-width="auto"
                            label="业绩报酬费率"
                            style="margin-left: 38.5%"
                        >
                            <crm-input
                                v-model="ruleForm.performanceRate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="performanceDate"
                            label-width="auto"
                            label="业绩报酬提取日"
                            style="margin-left: 35%"
                        >
                            <crm-input
                                v-model="ruleForm.performanceDate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="performanceRemark"
                            label-width="auto"
                            label="业绩报酬备注"
                            style="margin-left: 38.3%"
                        >
                            <crm-input
                                v-model="ruleForm.performanceRemark"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <!--                        <el-form-item
                            prop="closedPeriodDays"
                            label="封闭期天数"
                            style="margin-left: 40%"
                        >
                            <crm-input
                                v-model="ruleForm.closedPeriodDays"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>-->
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="performanceShareRate"
                            label-width="140px"
                            label="业绩报酬分成费率"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="ruleForm.performanceShareRate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                        <el-form-item
                            prop="performanceFormula"
                            label-width="auto"
                            label="业绩报酬公式"
                            style="margin-left: 16.8%"
                        >
                            <crm-select
                                v-model="ruleForm.performanceFormula"
                                :placeholder="performanceFormulaList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="performanceFormulaList.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <!--                        <el-form-item
                            prop="closedPeriodType"
                            label-width="140px"
                            label="封闭期类型"
                            style="margin-left: 10%"
                        >
                            <crm-select
                                v-model="ruleForm.closedPeriodType"
                                :placeholder="closedPeriodTypeList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="closedPeriodTypeList.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>-->
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item
                            prop="redemFormula"
                            label="赎回费公式"
                            style="margin-left: 40%"
                        >
                            <crm-select
                                v-model="ruleForm.redemFormula"
                                :placeholder="redemFormulaList.placeholder"
                                filterable
                                clearable
                                label-format="label"
                                value-format="key"
                                :option-list="redemFormulaList.selectList"
                                :style="{ width: '150px' }"
                            />
                        </el-form-item>
                        <el-form-item
                            prop="redemRemark"
                            label="赎回费备注"
                            style="margin-left: 40%"
                        >
                            <crm-input
                                v-model="ruleForm.redemRemark"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            prop="redemRate"
                            label="赎回费率"
                            label-width="140px"
                            style="margin-left: 10%"
                        >
                            <crm-input
                                v-model="ruleForm.redemRate"
                                :clearable="true"
                                :style="{ width: '150px' }"
                            ></crm-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <br />
                <br />
                <el-row>
                    <el-col :span="10">
                        <el-form-item style="margin-left: 43%">
                            <el-button type="primary" @click="submitForm(ruleFormRef)">
                                提交
                            </el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item style="margin-left: 10%">
                            <el-button type="primary" @click="resetForm(ruleFormRef)"
                                >重置</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </crm-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'
    import { fetchRes } from '@/utils/index'
    import { dataList } from './data/labelData'
    import { twoLevelConfProductRateUpdate } from '@/api/project/report/finance/twoLevelConfProductRate/twoLevelConfProductRate'
    const {
        isRedemDate,
        isSettleDate,
        isShareDate,
        manageFormulaList,
        redemFormulaList,
        rateConfTypeList,
        closedPeriodTypeList,
        performanceFormulaList,
        fixedTypeList,
        performanceTypeList
    } = dataList
    const loadingFlag = ref<boolean>(false)
    // 表单数据共用的实体类
    // 表单数据共用的实体类
    interface RuleForm {
        taskId: string
        fundCode: string // 基金code
        fundName: string // 基金名称
        company: string // 公司
        subRate: string // 认购费率
        rateConfContent: string //费率配置内容
        manageRate: string // 管理费率
        feedRate: string // 存续D折算系数
        feedRemark: string // 存续D备注
        manageFormula: string // 管理费公式
        manageRemark: string // 管理费备注
        performanceRate: string // 业绩报酬费率
        performanceShareRate: string // 业绩报酬分成费率
        redemRate: string // 赎回费率
        performanceDate: string // 业绩报酬提取日
        fixedType: string //固定日期类型
        fixeDate: string //固定日日期
        redemDate: string //赎回日 1-是2-否
        settleDate: string //清算日 1-是2-否
        shareDate: string //分红日 1-是2-否
        performanceFormula: string //业绩报酬公式
        performanceType: string //业绩报酬计提类型
        performanceBenchmark: string //业绩报酬计提基准
        performanceRemark: string //业绩报酬备注
        redemFormula: string // 赎回费公式
        redemRemark: string // 赎回费备注
        economicIncome: string // 经纪收入
        productManage: string // 产品经理
        signDate: string // 签约日期
        signSubject: string // 好买签约主体
        oppositeContact: string // 对方联系人
        rateStartDate: string
        rateEndDate: string
        rateConfType: string
        transMinAmount: string
        transMaxAmount: string
        transStartDate: string
        transEndDate: string
        closedPeriodDays: string
        closedPeriodType: string
        closedPeriodFirstDay: string
    }

    const ruleFormRef = ref<FormInstance>()
    const ruleForm = reactive<RuleForm>({
        taskId: '',
        fundCode: '', // 基金code
        fundName: '', // 基金名称
        company: '', // 公司
        subRate: '', // 认购费率
        rateConfContent: '', //费率配置内容
        manageRate: '', // 管理费率
        feedRate: '', // 存续D折算系数
        feedRemark: '', // 存续D备注
        manageFormula: '1', // 管理费公式
        manageRemark: '', // 管理费备注
        performanceRate: '', // 业绩报酬费率
        performanceShareRate: '', // 业绩报酬分成费率
        redemRate: '', // 赎回费率
        performanceDate: '', // 业绩报酬提取日
        fixedType: '', //固定日期类型
        fixeDate: '', //固定日日期
        redemDate: '1', //赎回日 1-是2-否
        settleDate: '1', //清算日 1-是2-否
        shareDate: '1', //分红日 1-是2-否
        performanceFormula: '', //业绩报酬公式
        performanceType: '', //业绩报酬计提类型
        performanceBenchmark: '', //业绩报酬计提基准
        performanceRemark: '', //业绩报酬备注
        redemFormula: '1', // 赎回费公式
        redemRemark: '', // 赎回费备注
        economicIncome: '', // 经纪收入
        productManage: '', // 产品经理
        signDate: '', // 签约日期
        signSubject: '', // 好买签约主体
        oppositeContact: '', // 对方联系人
        rateStartDate: '20200101', // 费率开始日期
        rateEndDate: '29991231', // 费率结束日期
        rateConfType: '', // 费率配置类型
        transMinAmount: '', // 配置下限
        transMaxAmount: '', // 配置上限
        transStartDate: '20200101', // 交易开始日期
        transEndDate: '29991231', // 交易结束日期
        closedPeriodDays: '', // 份额锁定期天数
        closedPeriodType: '', // 份额锁定期类型
        closedPeriodFirstDay: '' // 封闭期后首个日期
    })
    /**
     * @description: 组件传参
     * @return {*}
     */
    const props = withDefaults(
        defineProps<{
            dialogType?: string
            visibleCus?: boolean
            twoProductConfData?: {
                taskId: string
                fundCode: string // 基金code
                fundName: string // 基金名称
                company: string // 公司
                subRate: string // 认购费率
                manageRate: string // 管理费率
                feedRate: string // 存续D折算系数
                feedRemark: string // 存续D备注
                manageFormula: string // 管理费公式
                manageRemark: string // 管理费备注
                performanceRate: string // 业绩报酬费率
                performanceShareRate: string // 业绩报酬分成费率
                redemRate: string // 赎回费率
                performanceDate: string // 业绩报酬提取日
                fixedType: string //固定日期类型
                fixeDate: string //固定日日期
                redemDate: string //赎回日 1-是2-否
                settleDate: string //清算日 1-是2-否
                shareDate: string //分红日 1-是2-否
                performanceFormula: string //业绩报酬公式
                performanceRemark: string //业绩报酬备注
                performanceBenchmark: string //业绩报酬计提基准
                performanceType: string //业绩报酬计提类型
                redemFormula: string // 赎回费公式
                redemRemark: string // 赎回费备注
                economicIncome: string // 经纪收入
                productManage: string // 产品经理
                signDate: string // 签约日期
                signSubject: string // 好买签约主体
                oppositeContact: string // 对方联系人
                rateStartDate: string
                rateEndDate: string
                rateConfType: string
                rateConfContent: string
                transMinAmount: string
                transMaxAmount: string
                transStartDate: string
                transEndDate: string
                closedPeriodDays: string
                closedPeriodType: string
                closedPeriodFirstDay: string
            }
            isTwoProductConfAdd?: boolean
        }>(),
        {
            dialogType: 'add',
            visibleCus: true,
            twoProductConfData: () => {
                return {
                    taskId: '',
                    fundCode: '', // 基金code
                    fundName: '', // 基金名称
                    company: '', // 公司
                    subRate: '', // 认购费率
                    rateConfContent: '', //费率配置内容
                    manageRate: '', // 管理费率
                    feedRate: '', // 存续D折算系数
                    feedRemark: '', // 存续D备注
                    manageFormula: '1', // 管理费公式
                    manageRemark: '', // 管理费备注
                    performanceRate: '', // 业绩报酬费率
                    performanceShareRate: '', // 业绩报酬分成费率
                    redemRate: '', // 赎回费率
                    performanceDate: '', // 业绩报酬提取日
                    fixedType: '', //固定日期类型
                    fixeDate: '', //固定日日期
                    redemDate: '1', //赎回日 1-是2-否
                    settleDate: '1', //清算日 1-是2-否
                    shareDate: '1', //分红日 1-是2-否
                    performanceFormula: '', //业绩报酬公式
                    performanceType: '', //业绩报酬计提类型
                    performanceBenchmark: '', //业绩报酬计提基准
                    performanceRemark: '', //业绩报酬备注
                    redemFormula: '1', // 赎回费公式
                    redemRemark: '', // 赎回费备注
                    economicIncome: '', // 经纪收入
                    productManage: '', // 产品经理
                    signDate: '', // 签约日期
                    signSubject: '', // 好买签约主体
                    oppositeContact: '', // 对方联系人
                    rateStartDate: '20200101', // 费率开始日期
                    rateEndDate: '29991231', // 费率结束日期
                    rateConfType: '', // 费率配置类型
                    transMinAmount: '', // 配置下限
                    transMaxAmount: '', // 配置上限
                    transStartDate: '20200101', // 交易开始日期
                    transEndDate: '29991231', // 交易结束日期
                    closedPeriodDays: '', // 份额锁定期天数
                    closedPeriodType: '', // 份额锁定期类型
                    closedPeriodFirstDay: '' // 封闭期后首个日期
                }
            },
            isTwoProductConfAdd: false
        }
    )

    watchEffect(() => {
        console.log('props.twoProductConfData', props.twoProductConfData)
        ruleForm.taskId = props.twoProductConfData.taskId
        ruleForm.fundCode = props.twoProductConfData.fundCode
        ruleForm.fundName = props.twoProductConfData.fundName
        ruleForm.company = props.twoProductConfData.company
        ruleForm.subRate = props.twoProductConfData.subRate
        ruleForm.manageRate = props.twoProductConfData.manageRate
        ruleForm.feedRate = props.twoProductConfData.feedRate
        ruleForm.feedRemark = props.twoProductConfData.feedRemark
        ruleForm.manageFormula = props.twoProductConfData.manageFormula
        ruleForm.manageRemark = props.twoProductConfData.manageRemark
        ruleForm.performanceRate = props.twoProductConfData.performanceRate
        ruleForm.performanceShareRate = props.twoProductConfData.performanceShareRate
        ruleForm.redemRate = props.twoProductConfData.redemRate
        ruleForm.performanceDate = props.twoProductConfData.performanceDate
        ruleForm.fixedType = props.twoProductConfData.fixedType
        ruleForm.fixeDate = props.twoProductConfData.fixeDate
        ruleForm.redemDate = props.twoProductConfData.redemDate
        ruleForm.settleDate = props.twoProductConfData.settleDate
        ruleForm.shareDate = props.twoProductConfData.shareDate
        ruleForm.performanceFormula = props.twoProductConfData.performanceFormula
        ruleForm.performanceType = props.twoProductConfData.performanceType
        ruleForm.performanceBenchmark = props.twoProductConfData.performanceBenchmark
        ruleForm.performanceRemark = props.twoProductConfData.performanceRemark
        ruleForm.redemFormula = props.twoProductConfData.redemFormula
        ruleForm.redemRemark = props.twoProductConfData.redemRemark
        ruleForm.economicIncome = props.twoProductConfData.economicIncome
        ruleForm.productManage = props.twoProductConfData.productManage
        ruleForm.signDate = props.twoProductConfData.signDate
        ruleForm.signSubject = props.twoProductConfData.signSubject
        ruleForm.oppositeContact = props.twoProductConfData.oppositeContact
        ruleForm.rateStartDate = props.twoProductConfData.rateStartDate
        ruleForm.rateEndDate = props.twoProductConfData.rateEndDate
        ruleForm.rateConfType = props.twoProductConfData.rateConfType
        ruleForm.rateConfContent = props.twoProductConfData.rateConfContent
        ruleForm.transMinAmount = props.twoProductConfData.transMinAmount
        ruleForm.transMaxAmount = props.twoProductConfData.transMaxAmount
        ruleForm.transStartDate = props.twoProductConfData.transStartDate
        ruleForm.transEndDate = props.twoProductConfData.transEndDate
        ruleForm.rateConfType = props.twoProductConfData.rateConfType
        ruleForm.closedPeriodDays = props.twoProductConfData.closedPeriodDays
        ruleForm.closedPeriodType = props.twoProductConfData.closedPeriodType
        ruleForm.closedPeriodFirstDay = props.twoProductConfData.closedPeriodFirstDay
    })

    // 弹窗标题配置
    const title = computed(() => {
        if (props.isTwoProductConfAdd) {
            return '修改二级产品费率配置'
        }
    })
    /**
     * @description: 双向数据绑定弹框显示
     * @return {*}
     */
    const dialogVisible = computed({
        get() {
            return props.visibleCus
        },
        set(newVal) {
            return emit('update:modelValue', newVal)
        }
    })

    interface OptionItem {
        key: string
        label: string
    }
    const fixedDateList = ref({
        label: '固定日日期',
        placeholder: '请选择',
        selectList: [] as OptionItem[]
    })
    const generateDailyOptions = computed<OptionItem[]>(() => {
        const options: OptionItem[] = []
        for (let i = 1; i <= 31; i++) {
            options.push({
                key: `${i}`,
                label: `${i}`.padStart(2, '0') + '日'
            })
        }
        return options
    })
    // 特殊日期选项，使用 ref 使其响应式
    const specialDateOptions = ref<OptionItem[]>([
        { key: '0', label: '无' },
        { key: '41', label: '月最后一个T日' },
        { key: '42', label: '年最后一个T日' },
        { key: '43', label: '月第一个T日' },
        { key: '44', label: '年倒数第二个T日' },
        { key: '51', label: '月第三个T日' },
        { key: '61', label: '15日and最后一个T日' },
        { key: '71', label: '月第一个周五' },
        { key: '72', label: '月第三个周二' },
        { key: '73', label: '月最后一个周五' },
        { key: '74', label: '月第三个周五' },
        { key: '75', label: '月第四个周五' },
        { key: '76', label: '月第四个周三' }
    ])

    onMounted(() => {
        // 合并日常日期选项和特殊日期选项
        fixedDateList.value.selectList = [
            ...generateDailyOptions.value,
            ...specialDateOptions.value
        ]
    })
    /**
     * @description: $emit 平替
     * @return {*}
     */
    const emit = defineEmits<{
        (e: 'update:modelValue', modelValue: boolean): void
        (e: 'callback'): void
    }>()
    const rules = reactive<FormRules<RuleForm>>({
        fundCode: [
            { required: true, message: '请输入产品代码', trigger: 'blur' },
            { pattern: /^.{0,6}$/, message: '产品代码不能超过6个字符', trigger: 'blur' }
        ],
        fundName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        company: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
        subRate: [
            { required: true, message: '请输入认购费率', trigger: 'blur' },
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '请输入正确的格式',
                trigger: 'blur'
            }
        ],
        manageRate: [
            { required: true, message: '请输入管理费率', trigger: 'blur' },
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '请输入正确的数字格式',
                trigger: 'blur'
            }
        ],
        feedRate: [
            { required: true, message: '请输入存续D折算系数', trigger: 'blur' },
            {
                pattern: /^(0(\.\d{0,6})?|1(\.\d{0,6})?|2(\.0{1,6})?)$/,
                message: '请输入正确的格式',
                trigger: 'blur'
            }
        ],
        feedRemark: [{ required: true, message: '请输入存续D备注', trigger: 'blur' }],

        performanceRate: [
            { required: true, message: '请输入业绩报酬费率', trigger: 'blur' },
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '请输入正确的数字格式',
                trigger: 'blur'
            }
        ],
        performanceShareRate: [
            { required: true, message: '请输入业绩报酬分成费率', trigger: 'blur' },
            {
                pattern: /^(?:0(\.\d{1,6})?|1(\.0*)?)$/,
                message: '请输入正确的数字格式',
                trigger: 'blur'
            }
        ],
        fixedType: [{ required: true, message: '请输入固定日期类型', trigger: 'blur' }],
        fixeDate: [{ required: true, message: '请输入固定日日期', trigger: 'blur' }],
        performanceDate: [{ required: true, message: '请输入业绩报酬提取日', trigger: 'blur' }],
        performanceFormula: [{ required: true, message: '请输入业绩报酬公式', trigger: 'blur' }],
        performanceType: [{ required: true, message: '请输入业绩报酬计提类型', trigger: 'blur' }],
        performanceRemark: [{ required: true, message: '请输入业绩报酬备注', trigger: 'blur' }],
        performanceBenchmark: [
            { required: true,message: '业绩报酬计提基准',trigger: 'blur' }
        ],
        closedPeriodDays: [
            { pattern: /^[0-9]\d*$/, message: '只能输入数字且长度不能超过4', trigger: 'blur' }
        ],
        closedPeriodType: [{ required: true, message: '份额锁定期类型不能为空', trigger: 'blur' }],
        rateConfType: [{ required: true, message: '请选择费率配置类型', trigger: 'blur' }],
        redemRate: [
            {
                pattern: /^(0(\.\d{0,6})?|1(\.\d{0,6})?|2(\.0{1,6})?)$/,
                message: '请输入正确的数字格式',
                trigger: 'blur'
            }
        ],
        rateStartDate: [{ required: true, message: '请选择费率开始日期', trigger: 'blur' }],
        rateEndDate: [{ required: true, message: '请选择费率结束日期', trigger: 'blur' }]
    })
    //提交方法
    const submitForm = async (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        await formEl.validate((valid, fields) => {
            if (valid) {
                twoLevelConfProductRateSubmit(ruleForm)
            } else {
                console.log('error submit!', fields)
            }
        })
    }
    //重置方法
    const resetForm = (formEl: FormInstance | undefined) => {
        if (!formEl) {
            return
        }
        formEl.resetFields()
    }
    /**
     * 产品费率新增方法
     * @param params
     */
    function twoLevelConfProductRateSubmit(params: any) {
        console.log('params' + JSON.stringify(params))
        let startTime = new Date(params.rateStartDate).getTime()
        const startdt = String(params.rateStartDate)
        //解决日期格式转换问题  （2023-10 使用new date函数是从八点开始的， 通过控件获取的是从0点开始）
        if (startdt.indexOf('-') !== -1) {
            const startDate = new Date(params.rateStartDate)
            startTime = startDate.getTime() - 28880000
        }
        const endTime = new Date(params.rateEndDate).getTime()
        if (startTime - endTime > 0) {
            ElMessage({
                message: '选择的费率开始日期大于费率结束日期',
                type: 'warning',
                duration: 2000
            })
            return
        }
        fetchRes(twoLevelConfProductRateUpdate(params), {
            successCB: (res: any) => {
                loadingFlag.value = false
                dialogVisible.value = false
                return emit('callback')
            },
            errorCB: (res: any) => {
                loadingFlag.value = false
                ElMessage({
                    type: 'error',
                    message: res?.description || '请求失败'
                })
            },
            catchCB: () => {
                loadingFlag.value = false
            },
            successTxt: '修改成功',
            failTxt: '',
            fetchKey: ''
        })
    }
    /**
     * @description: 关闭
     * @param {*} formEl
     * @return {*}
     */
    const handleClose = (formEl: FormInstance | undefined) => {
        ElMessage({
            type: 'info',
            message: '取消'
        })
        dialogVisible.value = false
        // formEl.resetFields()
    }
</script>

<style lang="less" scoped>
    .el-row {
        margin-bottom: -30px; /* 负边距，减小垂直间距 */
    }

    .bordered-column1,
    .bordered-column2 {
        line-height: normal; /* 调整行高 */
        border: 1px solid #cccccc; /* 设置列的边框样式 */
    }

    .bordered-column1 > .el-form-item,
    .bordered-column2 > .el-form-item {
        margin-bottom: 10px; /* 添加垂直间距 */
        line-height: normal; /* 调整行高 */
        border-bottom: 1px solid black;
    }

    .bordered-column1 > .el-form-item:first-child,
    .bordered-column2 > .el-form-item:first-child {
        border-top: 1px solid black;
    }

    :deep(.el-form) {
        &.form-top {
            .el-input__wrapper {
                margin-top: 4px;
            }
        }
    }
</style>
