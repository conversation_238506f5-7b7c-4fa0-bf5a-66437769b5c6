/*
 * @Description: 定义搜索的label列表
 * @Author: ji<PERSON><PERSON><PERSON>.yang
 * @Date: 2024-04-01 10:40:30
 * @LastEditors: jianjian.yang
 * @LastEditTime: 2024-04-01 10:40:30
 * @FilePath: /src/views/report/kpi/assetRepDownload/data/labelData.ts
 *
 */
export const dataList = {
    //香港基金、海外创新、CTA、FOF、不良、海外私募(非香港)、海外公募(非香港)
    busiType: {
        label: '业务类型',
        placeholder: '请选择业务类型',
        selectList: [
            {
                key: '香港基金',
                label: '香港基金'
            },
            {
                key: '海外创新',
                label: '海外创新'
            },
            {
                key: 'CTA',
                label: 'CTA'
            },
            {
                key: 'FOF',
                label: 'FOF'
            },
            {
                key: '不良',
                label: '不良'
            },
            {
                key: '海外私募(非香港)',
                label: '海外私募(非香港)'
            },
            {
                key: '海外公募(非香港)',
                label: '海外公募(非香港)'
            }
        ]
    },
    // 0-离职，1-在职
    workState: {
        label: '在职状态',
        placeholder: '请选择在职状态',
        selectList: [
            {
                key: '0',
                label: '离职'
            },
            {
                key: '1',
                label: '在职'
            }
        ]
    },
    // 是否参与考核
    isKpi: {
        label: '是否参与考核',
        placeholder: '请选择是否参与考核',
        selectList: [
            {
                key: '是',
                label: '是'
            },
            {
                key: '否',
                label: '否'
            }
        ]
    },
    // 考核项
    kpiItem: {
        label: '考核项',
        placeholder: '请选择考核项',
        selectList: [
            {
                key: '好买香港',
                label: '好买香港'
            },
            {
                key: '非A',
                label: '非A'
            }
        ]
    },
    userId: {
        label: '员工编码',
        placeholder: '请输入员工编码'
    },
    orgData: {
        label: '投顾管理层',
        placeholder: '请选择所在组织'
    },
    ackDt: {
        label: '交易日期',
        placeholder: ['开始日期', '结束日期']
    },
    sortOrderMap: {
        ascending: 'ASC',
        descending: 'DESC',
        default: 'DESC'
    }
}
